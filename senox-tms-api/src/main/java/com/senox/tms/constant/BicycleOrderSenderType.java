package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/18 10:53
 */
@Getter
public enum BicycleOrderSenderType {

    /**
     * 冷藏客户
     */
    REFRIGERATE_CUSTOMER(1, "冷藏客户");

    private final int number;
    private final String name;

    BicycleOrderSenderType(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static BicycleOrderSenderType fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (BicycleOrderSenderType item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static BicycleOrderSenderType fromName(String name) {
        if (null == name) {
            return null;
        }
        for (BicycleOrderSenderType item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

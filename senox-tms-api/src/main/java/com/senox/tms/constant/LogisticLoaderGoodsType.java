package com.senox.tms.constant;

import lombok.Getter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
public enum LogisticLoaderGoodsType {

    /**
     * 干调
     */
    DRY_SPICES(1, "干调", BigDecimal.ZERO),

    /**
     * 重货
     */
    HEAVY_CARGO(2, "重货", BigDecimal.ZERO),

    /**
     * 贴箱
     */
    BOX_PACKING(3, "贴箱", BigDecimal.ZERO),

    /**
     * 印章
     */
    YIN_ZHANG(4, "印章", BigDecimal.ZERO),

    /**
     * 分拣
     */
    SORTING(5, "分拣", BigDecimal.ZERO),

    /**
     * 扫码出库
     */
    SCAN_CODE_OUT_WAREHOUSE(6, "扫码出库", new BigDecimal("0.1")),

    /**
     * 一面码
     */
    ONE_SIDE_CODE(7, "一面码", new BigDecimal("0.02")),

    /**
     * 四面码
     */
    FOUR_SIDED_CODE(8, "四面码", new BigDecimal("0.12")),

    /**
     * 轻抛货补贴
     */
    LIGHTWEIGHT_CARGO_SUBSIDY(9, "轻抛货补贴", BigDecimal.ZERO),

    /**
     * 转货上楼
     */
    TRANSFER_CARGO_UPSTAIRS(10, "转货上楼", new BigDecimal("1.5")),

    /**
     * 打板
     */
    PALLETIZING(11, "打板", new BigDecimal("40")),

    /**
     * 业务车分货
     */
    BUSINESS_VEHICLE_SORTING(12, "业务车分货", new BigDecimal("25")),

    /**
     * 收卡板
     */
    COLLECT_PALLET(13, "收卡板", new BigDecimal("1.2")),

    /**
     * 仓库借用
     */
    WAREHOUSE_BORROW(14, "仓库借用", BigDecimal.ZERO),
    ;

    private final int number;
    private final String name;
    private final BigDecimal unitPrice;

    LogisticLoaderGoodsType(int number, String name, BigDecimal unitPrice) {
        this.number = number;
        this.name = name;
        this.unitPrice = unitPrice;
    }

    public static LogisticLoaderGoodsType fromNumber(Integer number) {
        if (null == number) {
            return null;
        }
        for (LogisticLoaderGoodsType item : values()) {
            if (number.equals(item.number)) {
                return item;
            }
        }
        return null;
    }
}

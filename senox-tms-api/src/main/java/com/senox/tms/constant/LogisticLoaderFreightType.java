package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
public enum LogisticLoaderFreightType {

    /**
     * 其它装卸
     */
    OTHER(0, "其它装卸"),

    /**
     * 装卸入库
     */
    LOADING_TO_WAREHOUSE(1, "装卸入库"),

    /**
     * 装卸出库
     */
    UNLOADING_FROM_WAREHOUSE(2, "装卸出库"),

    /**
     * 分拣
     */
    SORTING(3, "分拣"),

    /**
     * 配送跟车
     */
    DELIVERY_CAR_TRACKING(4, "配送跟车"),;

    private final int number;
    private final String name;

    LogisticLoaderFreightType(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static LogisticLoaderFreightType fromNumber(Integer number) {
        if (null == number) {
            return null;
        }
        for (LogisticLoaderFreightType item : values()) {
            if (number.equals(item.number)) {
                return item;
            }
        }
        return null;
    }
}

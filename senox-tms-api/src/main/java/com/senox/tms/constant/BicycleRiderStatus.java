package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/13 15:43
 */
@Getter
public enum BicycleRiderStatus {

    /**
     * 离线
     */
    OFFLINE(0, "离线"),

    /**
     * 在线
     */
    ONLINE(1, "在线"),

    /**
     * 离职
     */
    DEPART(2, "离职");

    private final int number;
    private final String name;


    BicycleRiderStatus(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static BicycleRiderStatus fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (BicycleRiderStatus item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static BicycleRiderStatus fromName(String name) {
        if (null == name) {
            return null;
        }
        for (BicycleRiderStatus item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

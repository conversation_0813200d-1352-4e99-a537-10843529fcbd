package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/20 8:35
 */
@Getter
public enum BicycleOrderStatus {

    /**
     * 正常
     */
    FINALIZE(0, "正常"),

    /**
     * 取消
     */
    CANCEL(1, "取消");

    private final int number;
    private final String name;


    BicycleOrderStatus(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static BicycleOrderStatus fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (BicycleOrderStatus item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static BicycleOrderStatus fromName(String name) {
        if (null == name) {
            return null;
        }
        for (BicycleOrderStatus item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

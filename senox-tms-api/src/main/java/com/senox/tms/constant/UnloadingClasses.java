package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/10 8:23
 */
@Getter
public enum UnloadingClasses {

    /**
     * 早班
     */
    MORNING_SHIFT(0, "早班"),

    /**
     * 晚班
     */
    NIGHT_SHIFT(1, "晚班");

    private final int number;
    private final String name;


    UnloadingClasses(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static UnloadingClasses fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (UnloadingClasses item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static UnloadingClasses fromName(String name) {
        if (null == name) {
            return null;
        }
        for (UnloadingClasses item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

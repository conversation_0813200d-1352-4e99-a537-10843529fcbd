package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/19 9:34
 */
@Getter
public enum UnloadingWorkerLeave {

    /**
     * 未请假
     */
    NO(0, "未请假"),

    /**
     * 请假
     */
    YES(1, "请假");

    private final int number;
    private final String name;


    UnloadingWorkerLeave(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static UnloadingWorkerLeave fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (UnloadingWorkerLeave item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static UnloadingWorkerLeave fromName(String name) {
        if (null == name) {
            return null;
        }
        for (UnloadingWorkerLeave item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

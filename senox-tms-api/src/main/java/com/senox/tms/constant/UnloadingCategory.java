package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/12 13:31
 */
@Getter
public enum UnloadingCategory {

    /**
     * 冻品
     */
    FROZEN_PRODUCTS(0, "冻品"),

    /**
     * 干货
     */
    DRY_GOODS(1, "干货"),

    /**
     * 车型
     */
    VEHICLE_MODEL(2, "车型"),
    /**
     * 其他
     */
    OTHER(3, "其他"),
    /**
     * 客户
     */
    CUSTOMER(4, "客户"),
    /**
     * 加急费
     */
    URGENT_FEE(5, "加急费");

    private final int number;
    private final String name;


    UnloadingCategory(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static UnloadingCategory fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (UnloadingCategory item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static UnloadingCategory fromName(String name) {
        if (null == name) {
            return null;
        }
        for (UnloadingCategory item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

package com.senox.tms.constant;


import lombok.Getter;

/**
 * 收费标准状态
 *
 * <AUTHOR>
 * @date 2023-9-12
 */
@Getter
public enum BicycleStatus {

    /**
     * 未启用
     */
    DISABLED(0, "未启用"),

    /**
     * 启用未生效
     */
    ENABLED_NOT_EFFECTIVE(1, "启用未生效"),

    /**
     * 生效
     */
    ENABLED(2, "生效"),

    /**
     * 失效
     */
    EXPIRED(10, "失效"),
    ;

    private final int number;
    private final String name;


    BicycleStatus(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static BicycleStatus fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (BicycleStatus item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static BicycleStatus fromName(String name) {
        if (null == name) {
            return null;
        }
        for (BicycleStatus item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

package com.senox.tms.constant;

import lombok.Getter;

/**
 * 收款人类型
 *
 * <AUTHOR>
 * @date 2023-9-22
 */
@Getter
public enum BicyclePayeeType {

    UNKNOWN(0, "未知"),
    RIDER(1, "骑手"),
    ;

    private final int number;
    private final String name;

    BicyclePayeeType(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static BicyclePayeeType fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (BicyclePayeeType item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static BicyclePayeeType fromName(String name) {
        if (null == name) {
            return null;
        }
        for (BicyclePayeeType item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }

}

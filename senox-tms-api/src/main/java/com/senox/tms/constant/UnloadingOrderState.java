package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/12 13:31
 */
@Getter
public enum UnloadingOrderState {

    /**
     * 草稿
     */
    DRAFT(0, "草稿"),

    /**
     * 干货
     */
    NORMAL(1, "正常");

    private final int number;
    private final String name;


    UnloadingOrderState(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static UnloadingOrderState fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (UnloadingOrderState item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static UnloadingOrderState fromName(String name) {
        if (null == name) {
            return null;
        }
        for (UnloadingOrderState item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

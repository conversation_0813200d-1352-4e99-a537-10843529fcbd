package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/29 8:30
 */
@Getter
public enum BicycleOrderGoodsType {

    /**
     * 货物类型
     */
    OTHER(0, "其他"),
    HEAVY_GOODS(1, "重货"),
    THROW_GOODS(2, "抛货");

    private final int number;
    private final String name;

    BicycleOrderGoodsType(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static BicycleOrderGoodsType fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (BicycleOrderGoodsType item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static BicycleOrderGoodsType fromName(String name) {
        if (null == name) {
            return null;
        }
        for (BicycleOrderGoodsType item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

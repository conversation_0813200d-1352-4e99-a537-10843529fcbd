package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-05-26
 **/
@Getter
public enum LogisticTransportOrderPayer {

    /**
     * 发货方
     */
    CONSIGNOR(1, "发货方"),

    /**
     * 到付
     */
    CONSIGNEE(2, "到付");

    private final int number;
    private final String name;

    LogisticTransportOrderPayer(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static LogisticTransportOrderPayer fromNumber(Integer status) {
        if (null == status) {
            return null;
        }
        for (LogisticTransportOrderPayer item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static LogisticTransportOrderPayer fromName(String name) {
        if (null == name) {
            return null;
        }
        for (LogisticTransportOrderPayer item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

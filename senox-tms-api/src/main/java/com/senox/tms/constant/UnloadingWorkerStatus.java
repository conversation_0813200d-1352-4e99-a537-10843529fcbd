package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/13 11:41
 */
@Getter
public enum UnloadingWorkerStatus {

    /**
     * 未挂牌
     */
    NOT_LISTED(0, "未挂牌"),

    /**
     * 已挂牌
     */
    ALREADY_LISTED(1, "已挂牌"),

    /**
     * 搬运中
     */
    DURING_TRANSPORTATION(2, "搬运中"),
    /**
     * 请假
     */
    WORK_LEAVE(3, "请假");

    private final int number;
    private final String name;


    UnloadingWorkerStatus(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static UnloadingWorkerStatus fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (UnloadingWorkerStatus item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static UnloadingWorkerStatus fromName(String name) {
        if (null == name) {
            return null;
        }
        for (UnloadingWorkerStatus item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/20 8:35
 */
@Getter
public enum BicycleDeliveryOrderStatus {

    /**
     * 未分派
     */
    UN_ASSIGN(0, "未分派"),

    /**
     * 待揽货
     */
    UNPICKED_GOODS(1, "待揽货"),

    /**
     * 到达揽货点
     */
    ARRIVING_PICKUP_POINT(2, "到达揽货点"),

    /**
     * 已揽货
     */
    PICKED(3, "已揽货"),

    /**
     * 配送中
     */
    SEND(4, "配送中"),

    /**
     * 到达送货点
     */
    ARRIVED_DELIVERY_POINT(5, "到达送货点"),

    /**
     * 配送完成
     */
    SEND_COMPLETED(6, "配送完成"),

    /**
     * 确认完成
     */
    CONFIRM_COMPLETION(7, "确认完成");

    private final int number;
    private final String name;


    BicycleDeliveryOrderStatus(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static BicycleDeliveryOrderStatus fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (BicycleDeliveryOrderStatus item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static BicycleDeliveryOrderStatus fromName(String name) {
        if (null == name) {
            return null;
        }
        for (BicycleDeliveryOrderStatus item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

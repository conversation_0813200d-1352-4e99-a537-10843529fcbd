package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/12/20 9:19
 */
@Getter
public enum BicycleFee {

    /**
     * 配送费
     */
    DELIVERY(21L, "三轮车配送费"),
    /**
     * 其他费用
     */
    OTHER(22L, "三轮车其他费用"),
    /**
     * 装卸费
     */
    BICYCLE_HANDLING(23L, "三轮车装卸费"),
    /**
     * 上楼费
     */
    BICYCLE_UPSTAIRS(24L, "三轮车上楼费"),
    ;

    private final Long feeId;
    private final String name;

    BicycleFee(Long feeId, String name) {
        this.feeId = feeId;
        this.name = name;
    }

    public Long getFeeId() {
        return feeId;
    }

    public String getName() {
        return name;
    }
}

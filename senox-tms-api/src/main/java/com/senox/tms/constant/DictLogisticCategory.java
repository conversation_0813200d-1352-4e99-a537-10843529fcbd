package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023-12-21
 */
@Getter
public enum DictLogisticCategory {

    LOADER(1, "搬运工"),
    CUSTOMER(2, "客户"),
    DRIVER(3, "司机"),
    DELIVERY_CAR_NO(4, "配送车牌号"),
    ;
    private final int number;
    private final String name;

    DictLogisticCategory(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static DictLogisticCategory fromNumber(Integer number) {
        if (null == number) {
            return null;
        }
        for (DictLogisticCategory item : values()) {
            if (number.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static DictLogisticCategory fromName(String name) {
        if (null == name) {
            return null;
        }
        for (DictLogisticCategory item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

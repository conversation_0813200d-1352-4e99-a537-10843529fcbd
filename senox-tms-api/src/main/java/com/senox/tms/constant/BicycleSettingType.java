package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/10/25 10:12
 */
@Getter
public enum BicycleSettingType {

    AUTO_DISPATCH("AUTO_DISPATCH", "自动派工开关"),
    AUTO_MERGED("AUTO_MERGED", "自动合并订单开关"),
    UNPICKED("UNPICKED", "超时未揽货开关"),
    UN_ASSIGN("UN_ASSIGN", "未分派提醒开关"),
    HEAVY_GOODS("HEAVY_GOODS", "重货"),
    THROW_GOODS("THROW_GOODS", "抛货"),
    MULTIPLE_START_PRICE("MULTIPLE_START_PRICE", "多起步价"),
    MERCHANT_AUTO_AUDIT("MERCHANT_AUTO_AUDIT", "商户自动审核"),
    RIDER_AUTO_TAKING_ORDER("RIDER_AUTO_TAKING_ORDER", "骑手空闲自动接推荐商户单"),
    ORDER_COMPLETE_REFERRAL_FEE("ORDER_COMPLETE_REFERRAL_FEE", "订单完成产生推荐费"),
    RIDER_UPDATE_PART_GOODS("RIDER_UPDATE_PART_GOODS", "骑手改货物信息开关");

    private final String alias;
    private final String name;

    BicycleSettingType(String alias, String name) {
        this.alias = alias;
        this.name = name;
    }

    public static BicycleSettingType fromStatus(String alias) {
        if (null == alias) {
            return null;
        }
        for (BicycleSettingType item : values()) {
            if (alias.equals(item.alias)) {
                return item;
            }
        }
        return null;
    }

    public static BicycleSettingType fromName(String name) {
        if (null == name) {
            return null;
        }
        for (BicycleSettingType item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

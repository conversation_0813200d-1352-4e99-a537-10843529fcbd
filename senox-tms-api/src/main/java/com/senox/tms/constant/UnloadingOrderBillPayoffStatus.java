package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/22 14:35
 */
@Getter
public enum UnloadingOrderBillPayoffStatus {

    /**
     * 初始化
     */
    INIT(0,"未收"),
    /**
     * 已支付
     */
    PAID(1, "已收")
    ;

    private final int number;
    private final String name;


    UnloadingOrderBillPayoffStatus(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static UnloadingOrderBillPayoffStatus fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (UnloadingOrderBillPayoffStatus item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static UnloadingOrderBillPayoffStatus fromName(String name) {
        if (null == name) {
            return null;
        }
        for (UnloadingOrderBillPayoffStatus item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

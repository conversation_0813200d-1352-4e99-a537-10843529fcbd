package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/10/30 14:50
 */
@Getter
public enum UnloadingSharesStatus {

    /**
     * 未启用
     */
    DISABLED(0, "未启用"),

    /**
     * 生效
     */
    ENABLED(1, "生效"),

    /**
     * 失效
     */
    EXPIRED(2, "失效"),
    ;

    private final int number;
    private final String name;


    UnloadingSharesStatus(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static UnloadingSharesStatus fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (UnloadingSharesStatus item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static UnloadingSharesStatus fromName(String name) {
        if (null == name) {
            return null;
        }
        for (UnloadingSharesStatus item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

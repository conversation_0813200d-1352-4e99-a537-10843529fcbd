package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/18 14:34
 */
@Getter
public enum BicycleOrderState {

    /**
     * 草稿
     */
    DRAFT(0, "草稿"),

    /**
     * 正常
     */
    FINALIZE(1, "正常");

    private final int number;
    private final String name;


    BicycleOrderState(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static BicycleOrderState fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (BicycleOrderState item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static BicycleOrderState fromName(String name) {
        if (null == name) {
            return null;
        }
        for (BicycleOrderState item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/22 17:23
 */
@Getter
public enum BicycleStatisticsSearchUnit {

    DAY(0, "日"),
    MONTH(1, "月"),
    YEAR(2, "年");

    private final int number;
    private final String name;

    BicycleStatisticsSearchUnit(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static BicycleStatisticsSearchUnit fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (BicycleStatisticsSearchUnit item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static BicycleStatisticsSearchUnit fromName(String name) {
        if (null == name) {
            return null;
        }
        for (BicycleStatisticsSearchUnit item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

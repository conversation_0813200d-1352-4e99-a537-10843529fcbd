package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/19 10:53
 */
@Getter
public enum LogisticStatisticsIncomeType {

    /**
     * 冻品线
     */
    FROZEN_PRODUCT_WIRE(1, "冻品线"),
    /**
     * 干货线
     */
    DRY_CARGO_WIRE(2, "干货线"),
    /**
     * 电子线
     */
    ELECTRONIC_WIRE(3, "电子线"),
    /**
     * 云仓
     */
    CLOUD_WAREHOUSE(4, "云仓");

    private final int number;
    private final String name;

    LogisticStatisticsIncomeType(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static LogisticStatisticsIncomeType fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (LogisticStatisticsIncomeType item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static LogisticStatisticsIncomeType fromName(String name) {
        if (null == name) {
            return null;
        }
        for (LogisticStatisticsIncomeType item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/24 8:13
 */
@Getter
public enum UnloadingVersion {

    /**
     * V1
     */
    VERSION_1("V1"),

    /**
     * V2
     */
    VERSION_2("V2");

    private final String name;


    UnloadingVersion(String name) {
        this.name = name;
    }

    public static UnloadingVersion fromName(String name) {
        if (null == name) {
            return null;
        }
        for (UnloadingVersion item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/9/12 13:31
 */
@Getter
public enum UnloadingOrderWorkerStatus {

    /**
     * 初始化
     */
    INIT(0, "初始化"),

    /**
     * 搬运中
     */
    TRANSPORTING(1, "搬运中"),
    /**
     * 已完成
     */
    DONE(2, "已完成")
    ;

    private final int number;
    private final String name;


    UnloadingOrderWorkerStatus(int number, String name) {
        this.number = number;
        this.name = name;
    }

    public static UnloadingOrderWorkerStatus fromStatus(Integer status) {
        if (null == status) {
            return null;
        }
        for (UnloadingOrderWorkerStatus item : values()) {
            if (status.equals(item.number)) {
                return item;
            }
        }
        return null;
    }

    public static UnloadingOrderWorkerStatus fromName(String name) {
        if (null == name) {
            return null;
        }
        for (UnloadingOrderWorkerStatus item : values()) {
            if (name.equals(item.name)) {
                return item;
            }
        }
        return null;
    }
}

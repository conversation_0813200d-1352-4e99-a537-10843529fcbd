package com.senox.tms.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-05-15 10:43
 */
@Getter
public enum LogisticFee {

    /**
     * 配送费
     */
    LOGISTIC_FREIGHT(71L, "珠三角费用");

    private final Long feeId;
    private final String name;

    LogisticFee(Long feeId, String name) {
        this.feeId = feeId;
        this.name = name;
    }

    public Long getFeeId() {
        return feeId;
    }

    public String getName() {
        return name;
    }
}


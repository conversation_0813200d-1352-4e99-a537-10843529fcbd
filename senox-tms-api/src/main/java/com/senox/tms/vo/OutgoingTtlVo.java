package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/4/8 11:04
 */
@ApiModel("太太乐外发")
@Data
public class OutgoingTtlVo implements Serializable {
    private static final long serialVersionUID = -1126523800968843998L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 收货日期
     */
    @ApiModelProperty("收货日期")
    private LocalDate receivingDate;

    /**
     * 托运方客户
     */
    @ApiModelProperty("托运方客户")
    private String shipperName;

    /**
     * 收货方客户
     */
    @ApiModelProperty("收货方客户")
    private String recipientName;

    /**
     * 收货方地址
     */
    @ApiModelProperty("收货方地址")
    private String recipientAddress;

    /**
     * 收货方电话
     */
    @ApiModelProperty("收货方电话")
    private String recipientContact;

    /**
     * 物流公司
     */
    @ApiModelProperty("物流公司")
    private String logisticsCompany;

    /**
     * 物流单号
     */
    @ApiModelProperty("物流单号")
    private String logisticsNo;

    /**
     * 托运方件数
     */
    @ApiModelProperty("托运方件数")
    private Integer shipperPieces;

    /**
     * 实收运费
     */
    @ApiModelProperty("实收运费")
    private BigDecimal actualFreightAmount;

    /**
     * 实发运费
     */
    @ApiModelProperty("实发运费")
    private BigDecimal actualShippingAmount;

    /**
     * 利润
     */
    @ApiModelProperty("利润")
    private BigDecimal profitAmount;

    /**
     * 结算类型(1:到付;2:现付)
     */
    @ApiModelProperty("结算类型(1:到付;2:现付)")
    private Integer settlementType;

}

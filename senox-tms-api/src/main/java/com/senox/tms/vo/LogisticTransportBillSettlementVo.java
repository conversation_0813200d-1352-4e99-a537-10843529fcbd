package com.senox.tms.vo;

import com.senox.tms.constant.LogisticTransportOrderPayer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-06-09
 **/
@Getter
@Setter
@ApiModel("城际运输结算单")
public class LogisticTransportBillSettlementVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 账单日期
     */
    @ApiModelProperty("账单日期")
    private LocalDate billDate;

    /**
     * 账单年月
     */
    @ApiModelProperty("账单年月")
    private String billYearMonth;

    /**
     * 年
     */
    @ApiModelProperty("年")
    private Integer billYear;

    /**
     * 月
     */
    @ApiModelProperty("月")
    private Integer billMonth;

    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;

    /**
     * 商户名称
     */
    @ApiModelProperty("商户名称")
    private String merchantName;

    /**
     * 付款人
     */
    @ApiModelProperty("付款人")
    private LogisticTransportOrderPayer payer;

    /**
     * 商户编号
     */
    @ApiModelProperty("商户编号")
    private String merchantSerial;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /**
     * 支付金额
     */
    @ApiModelProperty("支付金额")
    private BigDecimal paidAmount;

    /**
     * 待支付金额
     */
    @ApiModelProperty("待支付金额")
    private BigDecimal paidStillAmount;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private Integer payWay;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private Long remoteOrderId;

    /**
     * 下发状态
     */
    @ApiModelProperty("下发状态")
    private Boolean send;

    /**
     * 下发时间
     */
    @ApiModelProperty("下发时间")
    private LocalDateTime sendTime;

    /**
     * 收费员
     */
    @ApiModelProperty("收费员")
    private Long tollManId;

    /**
     * 收费员
     */
    @ApiModelProperty("收费员姓名")
    private String tollManName;

    /**
     * 账单状态
     */
    @ApiModelProperty("账单状态")
    private Boolean status;
}

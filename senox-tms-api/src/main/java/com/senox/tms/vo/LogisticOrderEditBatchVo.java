package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/4 15:50
 */
@Getter
@Setter
@ToString
@ApiModel("物流订单批量修改信息")
public class LogisticOrderEditBatchVo implements Serializable {

    private static final long serialVersionUID = -2419942985971281218L;

    @ApiModelProperty("商品订单id列表")
    private List<Long> productOrderIds;

    @ApiModelProperty("司机")
    private String driver;

    @ApiModelProperty("预付款人")
    private String orderPrePaidMan;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "操作人id", hidden = true)
    private Long operatorId;

    @ApiModelProperty(value = "操作人名", hidden = true)
    private String operatorName;
}

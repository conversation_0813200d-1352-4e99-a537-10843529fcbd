package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 15:49
 */
@ApiModel("鹏翔搬运工排期计划批量添加")
@Data
public class UnloadingNightScheduleBatchVo implements Serializable {
    private static final long serialVersionUID = 7090803548216499948L;

    /**
     * 排期日期起
     */
    @ApiModelProperty("排期日期起")
    private LocalDate scheduleDateStart;

    /**
     * 排期日期止
     */
    @ApiModelProperty("排期日期止")
    private LocalDate scheduleDateEnd;

    /**
     * 搬运工id集合
     */
    @ApiModelProperty("搬运工id集合")
    private List<Long> workerIdList;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:07
 */
@ApiModel("装卸搬运工考勤")
@Data
public class UnloadingAttendanceVo implements Serializable {
    private static final long serialVersionUID = 9029474429293145377L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 搬运工id
     */
    @ApiModelProperty("搬运工id")
    private Long workerId;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String workerName;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private LocalDateTime operateTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}

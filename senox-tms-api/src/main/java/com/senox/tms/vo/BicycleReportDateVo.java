package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/16 16:46
 */
@ApiModel("报表生成")
@Getter
@Setter
public class BicycleReportDateVo implements Serializable {

    private static final long serialVersionUID = 947690676305145730L;
    /**
     * 年
     */
    @ApiModelProperty("年")
    private Integer year;

    /**
     * 月
     */
    @ApiModelProperty("月")
    private Integer month;

    /**
     * 日
     */
    @ApiModelProperty("日")
    private Integer day;

    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;
}

package com.senox.tms.vo;

import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/11/4 11:03
 */
@ApiModel("三轮车提货信息详细")
@Data
public class BicycleDeliveryInfoDetailVo implements Serializable {
    private static final long serialVersionUID = 864400255688501279L;

    /**
     * 骑手姓名
     */
    @ApiModelProperty("骑手姓名")
    private String riderName;

    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    @ApiModelProperty("状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    private Integer detailStatus;
}

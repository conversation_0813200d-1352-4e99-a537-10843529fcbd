package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/8 14:18
 */
@Getter
@Setter
@ApiModel("鹏翔月应付查询参数")
public class UnloadingOrderMonthPayoffSearchVo extends PageRequest {
    private static final long serialVersionUID = -1181713998253529318L;

    @ApiModelProperty("年")
    private Integer year;

    @ApiModelProperty("月")
    private Integer month;

    @ApiModelProperty("搬运工id")
    private Long workerId;

    @ApiModelProperty("关键字")
    private String keywords;

    @ApiModelProperty("应付状态 0初始化 1已支付")
    private Integer status;

    @ApiModelProperty("应付时间起")
    private LocalDateTime payoffTimeStart;

    @ApiModelProperty("应付时间止")
    private LocalDateTime payoffTimeEnd;

}

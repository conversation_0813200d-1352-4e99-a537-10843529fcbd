package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/13 9:25
 */
@ApiModel("鹏翔账单月份")
@Data
public class UnloadingMonthVo implements Serializable {

    private static final long serialVersionUID = -989768313577624234L;

    @ApiModelProperty("年")
    private Integer year;

    @ApiModelProperty("月")
    private Integer month;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("订单号")
    private List<String> orderNoList;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/12/15 16:52
 */
@Getter
@Setter
@ToString
@ApiModel("物流客户应付生成")
public class LogisticPayoffGenerateVo implements Serializable {


    @NotNull(message = "无效的账单日")
    @ApiModelProperty("账单日")
    private LocalDate billDate;

    @ApiModelProperty("商户")
    private String merchant;

    @ApiModelProperty("是否覆盖")
    private Boolean overwrite;

    @ApiModelProperty(value = "操作人id", hidden = true)
    private Long operatorId;

    @ApiModelProperty(value = "操作人姓名", hidden = true)
    private String operateName;
}

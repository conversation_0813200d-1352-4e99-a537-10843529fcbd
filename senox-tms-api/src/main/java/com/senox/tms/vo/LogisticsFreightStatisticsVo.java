package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-12-26
 */
@Getter
@Setter
@ApiModel("物流货运统计")
public class LogisticsFreightStatisticsVo {

    /**
     * 总寄件数
     */
    @ApiModelProperty("总寄件数")
    private BigDecimal totalSenderPieces;

    /**
     * 总寄件运费
     */
    @ApiModelProperty("总寄件运费")
    private BigDecimal totalSenderFreightCharge;

    /**
     * 总中转运费
     */
    @ApiModelProperty("总中转运费")
    private BigDecimal totalTransferCharge;

    /**
     * 总利润
     */
    @ApiModelProperty("总利润")
    private BigDecimal totalProfitAmount;

    /**
     * 总实付金额
     */
    @ApiModelProperty("总实付金额")
    private BigDecimal totalPaidAmount;
}

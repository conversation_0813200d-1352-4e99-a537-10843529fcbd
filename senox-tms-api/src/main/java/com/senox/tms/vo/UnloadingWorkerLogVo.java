package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/3/24 8:24
 */
@ApiModel("鹏翔搬运工异常日志")
@Data
public class UnloadingWorkerLogVo implements Serializable {
    private static final long serialVersionUID = 7518652941394438933L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 搬运工id
     */
    @ApiModelProperty("搬运工id")
    private Long workerId;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String workerName;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 操作时间
     */
    @ApiModelProperty("操作时间")
    private LocalDateTime modifiedTime;
}

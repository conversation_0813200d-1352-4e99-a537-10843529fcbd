package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/19 16:59
 */
@ApiModel("骑手超时未拣货消息")
@Data
public class BicycleRiderUnpickedMessageVo implements Serializable {
    private static final long serialVersionUID = 2719614123448370404L;

    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;

    /**
     * 骑手名
     */
    @ApiModelProperty("骑手名")
    private String riderName;

    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    /**
     * 状态描述
     */
    @ApiModelProperty("状态描述")
    private String statusDescription;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;
}

package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/10/17 9:09
 */
@Getter
@Setter
@ToString
@ApiModel("月账单查询")
public class BicycleOrderMonthReportSearchVo extends PageRequest {
    private static final long serialVersionUID = 236999479122262306L;

    @ApiModelProperty("年份")
    private Integer reportYear;

    @ApiModelProperty("月份")
    private Integer reportMonth;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("商户名")
    private String merchantName;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-10
 **/
@Getter
@Setter
@ApiModel("城际运输结算单详情")
public class LogisticTransportBillSettlementDetailVo {

    /**
     * 总件数
     */
    @ApiModelProperty("总件数")
    private Integer totalPieces;

    /**
     * 总装载重量
     */
    @ApiModelProperty("总装载重量")
    private BigDecimal totalLoadingWeight;

    /**
     * 总运费
     */
    @ApiModelProperty("总运费")
    private BigDecimal totalFreightCharge;

    /**
     * 总其他费用
     */
    @ApiModelProperty("总其他费用")
    private BigDecimal totalOtherCharge;

    /**
     * 总应收运费
     */
    @ApiModelProperty("总应收运费")
    private BigDecimal totalReceivableFreightCharge;

    /**
     * 账单列表
     */
    @ApiModelProperty("账单列表")
    private List<LogisticTransportBillVo> bills;
}

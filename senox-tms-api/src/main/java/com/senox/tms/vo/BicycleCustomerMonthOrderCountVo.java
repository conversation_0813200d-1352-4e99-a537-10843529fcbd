package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/3/21 14:30
 */
@Getter
@Setter
@ApiModel("下单统计")
public class BicycleCustomerMonthOrderCountVo implements Serializable {
    private static final long serialVersionUID = -8616356378355251466L;

    @ApiModelProperty("年月")
    private LocalDate yearMonth;

    @ApiModelProperty("下单件数")
    private BigDecimal pieces;

    @ApiModelProperty("下单数量")
    private Integer count;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/14 14:10
 */
@ApiModel("三轮车骑手登录信息")
@Data
public class BicycleRiderLoginVo implements Serializable {
    private static final long serialVersionUID = 8058368874968101995L;

    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号不能为空")
    private String contact;

    @ApiModelProperty("密码")
    @NotBlank(message = "密码不能为空")
    private String password;
}

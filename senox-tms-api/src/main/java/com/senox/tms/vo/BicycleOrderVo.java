package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.constant.BicycleOrderSenderType;
import com.senox.tms.dto.BicycleRiderImportDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18 9:42
 */
@Data
@ApiModel("三轮车配送订单")
public class BicycleOrderVo implements Serializable {
    private static final long serialVersionUID = 1918079476963393747L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 订单流水号
     */
    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    /**
     * 起点id
     */
    @ApiModelProperty("起点id")
    @NotNull(message = "无效的起点id", groups = Add.class)
    private Long startPointId;

    /**
     * 起点
     */
    @ApiModelProperty("起点")
    private String startPointName;

    /**
     * 详细起点地址
     */
    @ApiModelProperty("详细起点地址")
    private String startPointDetailName;

    /**
     * 终点id
     */
    @ApiModelProperty("终点id")
    @NotNull(message = "无效的终点id", groups = Add.class)
    private Long endPointId;

    /**
     * 终点
     */
    @ApiModelProperty("终点")
    private String endPointName;

    /**
     * 详细终点地址
     */
    @ApiModelProperty("详细终点地址")
    private String endPointDetailName;

    /**
     * 预约配送时间起
     */
    @ApiModelProperty("预约配送时间起")
    @NotNull(message = "无效的预约配送时间起", groups = Add.class)
    private LocalDateTime sendTimeStart;

    /**
     * 预约配送时间止
     */
    @ApiModelProperty("预约配送时间止")
    @NotNull(message = "无效的预约配送时间止", groups = Add.class)
    private LocalDateTime sendTimeEnd;

    /**
     * 寄件人id
     */
    @NotNull(message = "无效的寄件人id",groups = Add.class)
    @Min(1)
    @ApiModelProperty("寄件人id")
    private Long senderId;

    /**
     * 寄件人
     */
    @ApiModelProperty("寄件人")
    @NotBlank(message = "无效的寄件人", groups = Add.class)
    private String sender;

    /**
     * 发货人冷藏编号
     */
    @ApiModelProperty("发货人冷藏编号")
    private String senderSerialNo;

    /**
     * 寄件人联系方式
     */
    @ApiModelProperty("寄件人联系方式")
    @NotBlank(message = "无效的寄件人联系方式", groups = Add.class)
    private String senderContact;

    /**
     * 寄件人类别
     * @see BicycleOrderSenderType
     */
    @ApiModelProperty("寄件人类别 1冷藏客戶")
    private Integer senderType;

    /**
     * 收件人
     */
    @ApiModelProperty("收件人")
    @NotBlank(message = "无效的收件人", groups = Add.class)
    private String recipient;

    /**
     * 收件人联系方式
     */
    @ApiModelProperty("收件人联系方式")
    @NotBlank(message = "无效的收件人联系方式", groups = Add.class)
    private String recipientContact;

    /**
     * 车牌
     */
    @ApiModelProperty("车牌")
    private String carNo;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 其他备注
     */
    @ApiModelProperty("其他备注")
    private String otherRemark;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal pieces;

    /**
     * 配送费用
     */
    @ApiModelProperty("配送费用")
    private BigDecimal deliveryCharge;

    /**
     * 其他费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 装卸费
     */
    @ApiModelProperty("装卸费")
    private BigDecimal handlingCharge;

    /**
     * 上楼费
     */
    @ApiModelProperty("上楼费")
    private BigDecimal upstairsCharge;

    /**
     * 总费用
     */
    @ApiModelProperty("总费用")
    private BigDecimal totalCharge;

    /**
     * 收费标准id
     */
    @ApiModelProperty("收费标准id")
    private Long chargesId;

    /**
     * 状态 0：草稿 1：正常
     */
    @NotNull(message = "无效的状态", groups = Add.class)
    private Integer state;

    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    private LocalDateTime orderTime;

    /**
     * 来源(0:系统生成;1:导入)
     */
    @ApiModelProperty("来源(0:系统生成;1:导入)")
    private Integer origin;

    /**
     * 状态 0：正常 1：取消
     */
    @ApiModelProperty("订单状态 0：正常 1：取消")
    private Integer orderStatus;

    /**
     * 订单状态备注
     */
    @ApiModelProperty("订单状态备注")
    private String statusRemark;

    /**
     * 微信创建用户
     */
    @ApiModelProperty("微信创建用户")
    private String createOpenid;

    /**
     * 三轮车配送订单货物明细
     */
    @ApiModelProperty("三轮车配送订单货物明细")
    @NotNull(message = "无效的订单货物明细", groups = {Add.class, Update.class})
    private List<@Valid BicycleOrderGoodsDetailVo> orderGoodsDetailVos;

    /**
     * 多媒体资料访问连接集合
     */
    @ApiModelProperty("多媒体资料访问连接集合")
    private List<String> mediaUrlList;

    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    @ApiModelProperty("状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    private Integer status;

    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;

    /**
     * 骑手姓名
     */
    @ApiModelProperty("骑手姓名")
    private String riderName;

    @ApiModelProperty("揽货件数")
    private Integer pickedPieces;

    /**
     * 配送单详细
     */
    @ApiModelProperty("配送单详细")
    private List<BicycleDeliveryOrderDetailVo> detailVos;

    /**
     * 低消金额
     */
    @ApiModelProperty("低消金额")
    private BigDecimal minAmount;

    /**
     * 明细列表
     */
    private List<BicycleChargesDetailVo> chargesDetails;

    /**
     * 订单信息
     */
    private List<BicycleOrderInfoVo> orderInfoVos;

    /**
     * 配送单Id
     */
    @ApiModelProperty("配送单Id")
    private Long deliveryOrderId;

    /**
     * 推荐分配
     */
    @ApiModelProperty("推荐分配")
    private Boolean referralDelivery;

    /**
     * 结算周期
     */
    @ApiModelProperty("结算周期：0单结 1日结 2月结")
    private Integer settlePeriod;

    /**
     * 合并序号
     */
    @ApiModelProperty(hidden = true)
    private Integer mergeNumber;

    /**
     * 骑手集
     */
    @ApiModelProperty(hidden = true)
    private List<BicycleRiderImportDto> riders;

}

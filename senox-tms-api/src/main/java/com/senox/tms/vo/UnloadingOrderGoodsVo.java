package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/14 13:50
 */
@ApiModel("装卸订单明细")
@Data
public class UnloadingOrderGoodsVo implements Serializable {
    private static final long serialVersionUID = -2370781384135370056L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("类型 0冻品，1干货，3其他")
    private Integer category;

    @ApiModelProperty("货物名")
    private String goodsName;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("数量")
    private BigDecimal quantity;
}

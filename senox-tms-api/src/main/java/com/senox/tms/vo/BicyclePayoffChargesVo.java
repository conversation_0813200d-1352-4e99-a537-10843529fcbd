package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-4-1
 */
@Getter
@Setter
@ApiModel("三轮车应付费用")
public class BicyclePayoffChargesVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 基础金额
     */
    @ApiModelProperty("基础金额")
    private BigDecimal basicAmount;

    /**
     * 基础件数
     */
    @ApiModelProperty("基础件数")
    private BigDecimal basicPieces;

    /**
     * 超量价格
     */
    @ApiModelProperty("超量金额")
    private BigDecimal excessPrice;
}

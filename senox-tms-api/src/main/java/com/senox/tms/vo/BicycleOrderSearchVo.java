package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/9/18 17:03
 */
@ApiModel("三轮车配送订单查询参数")
@Getter
@Setter
public class BicycleOrderSearchVo extends PageRequest {
    private static final long serialVersionUID = 4505321404611199994L;

    /**
     * 订单流水号
     */
    @ApiModelProperty("订单流水号")
    private String orderSerialNo;
    /**
     * 起点id
     */
    @ApiModelProperty("起点id")
    private Long startPointId;
    /**
     * 终点id
     */
    @ApiModelProperty("终点id")
    private Long endPointId;
    /**
     * 状态 0：草稿 1：正常
     */
    @ApiModelProperty("状态 0：草稿 1：正常")
    private Integer state;
    /**
     * 下单时间起
     */
    @ApiModelProperty("下单时间起")
    private LocalDateTime orderTimeStart;
    /**
     * 下单时间止
     */
    @ApiModelProperty("下单时间止")
    private LocalDateTime orderTimeEnd;

    /**
     * 来源(0:系统生成;1:导入)
     */
    @ApiModelProperty("来源(0:系统生成;1:导入)")
    private Integer origin;

    /**
     * 订单状态 0：正常 1：取消
     */
    @ApiModelProperty("订单状态 0：正常 1：取消")
    public Integer orderStatus;

    /**
     * 微信创建用户
     */
    @ApiModelProperty("微信创建用户")
    private String createOpenid;
    /**
     * 包含配送单
     */
    @ApiModelProperty("查询未派发的订单")
    private Boolean delivery;
    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;
    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    @ApiModelProperty("状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    private List<Integer> status;
    /**
     * 寄件人id
     */
    @ApiModelProperty("寄件人id")
    private Long senderId;
    /**
     * 寄件人
     */
    @ApiModelProperty("寄件人")
    private String sender;
    /**
     * 收件人
     */
    @ApiModelProperty("收件人")
    private String recipient;
    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;
    /**
     * 发货人冷藏编号
     */
    @ApiModelProperty("发货人冷藏编号")
    private String senderSerialNo;

    /**
     * 配送单开始时间启
     */
    @ApiModelProperty("配送单开始时间启")
    private LocalDateTime modifiedTimeStart;

    /**
     * 配送单开始时间止
     */
    @ApiModelProperty("配送单开始时间止")
    private LocalDateTime modifiedTimeEnd;

    /**
     * 是否分配骑手
     */
    @ApiModelProperty("是否分配骑手")
    private Boolean assignRider;

    /**
     * 结算周期
     */
    @ApiModelProperty("结算周期：0单结 1日结 2月结")
    private List<Integer> settlePeriodList;

    /**
     * 合并
     */
    @ApiModelProperty("查询合并的订单")
    private Boolean merged;


}

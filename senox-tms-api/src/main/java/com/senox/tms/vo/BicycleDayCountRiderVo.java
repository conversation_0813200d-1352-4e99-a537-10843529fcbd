package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/26 10:31
 */
@ApiModel("当天在线和离线骑手数量")
@Data
public class BicycleDayCountRiderVo implements Serializable {

    private static final long serialVersionUID = 7289848367076398432L;

    /**
     * 在线数量
     */
    @ApiModelProperty("在线数量")
    private Integer onlineCount;

    /**
     * 离线数量
     */
    @ApiModelProperty("离线数量")
    private Integer offlineCount;

    /**
     * 当天最佳骑手集合
     */
    @ApiModelProperty("当天最佳骑手集合")
    private List<BicycleDayBestRiderVo> bestRiderVoList;
}

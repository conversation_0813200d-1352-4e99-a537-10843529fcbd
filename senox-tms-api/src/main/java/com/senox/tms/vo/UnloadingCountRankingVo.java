package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/28 15:27
 */
@ApiModel("排行榜")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UnloadingCountRankingVo implements Serializable {
    private static final long serialVersionUID = 5834578544666047164L;

    /**
     * 下单数量排行榜
     */
    @ApiModelProperty("下单数量排行榜")
    private List<UnloadingOrderCountRankingVo> orderCountRankingVoList;

    /**
     * 下单货物数量排行榜
     */
    @ApiModelProperty("下单货物数量排行榜")
    private List<UnloadingQuantityCountRankingVo> quantityCountRankingVoList;

    /**
     * 下单金额排行榜
     */
    @ApiModelProperty("下单金额排行榜")
    private List<UnloadingAmountCountRankingVo> amountCountRankingVoList;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/27 14:06
 */
@ApiModel("配送地点使用统计")
@Data
public class BicyclePointCountVo implements Serializable {
    private static final long serialVersionUID = 6371070250025553228L;

    /**
     * 地点名
     */
    @ApiModelProperty("地点名")
    private String pointName;

    /**
     * 使用次数
     */
    @ApiModelProperty("使用次数")
    private Integer useCount;
}

package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 9:07
 */
@ApiModel("鹏翔应收账单查询参数")
@Getter
@Setter
public class UnloadingOrderBillSearchVo extends PageRequest {
    private static final long serialVersionUID = -5455194703299036825L;


    /**
     * 账单日起
     */
    @ApiModelProperty("账单日起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate billDateStart;

    /**
     * 账单日止
     */
    @ApiModelProperty("账单日止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate billDateEnd;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 支付状态
     * @see UnloadingOrderBillPayoffStatus
     */
    @ApiModelProperty("支付状态 0初始化 1已支付")
    private Integer status;

    /**
     * 支付时间起
     */
    @ApiModelProperty("支付时间起")
    private LocalDateTime paidTimeStart;

    /**
     * 支付时间止
     */
    @ApiModelProperty("支付时间止")
    private LocalDateTime paidTimeEnd;

    /**
     * 订单号集合
     */
    @ApiModelProperty("订单号集合")
    private List<String> orderNoList;
}

package com.senox.tms.vo;

import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/19 11:01
 */
@ApiModel("货物统计")
@Data
public class LogisticStatisticsDayReportVo implements Serializable {
    private static final long serialVersionUID = 7634218078305352324L;

    @ApiModelProperty("id")
    @NotNull(message = "无效的id", groups = Update.class)
    private Long id;

    @ApiModelProperty("日期")
    private LocalDate reportDate;

    @ApiModelProperty("运营部门")
    private String operationsDepartment;

    @ApiModelProperty("发货人")
    private String shipper;

    @ApiModelProperty("物流单号")
    private String logisticsNo;

    @ApiModelProperty("收入类别")
    private Integer incomeType;

    @ApiModelProperty("司机名")
    private String driverName;

    @ApiModelProperty("车牌")
    private String carNo;

    @ApiModelProperty("是否包车")
    private Boolean charteredBus;

    @ApiModelProperty("始发站")
    private String departureStation;

    @ApiModelProperty("目的站")
    private String destinationStation;

    @ApiModelProperty("件数")
    private BigDecimal pieces;

    @ApiModelProperty("装载重量")
    private BigDecimal loadingWeight;

    @ApiModelProperty("入库重量")
    private BigDecimal storageWeight;

    @ApiModelProperty("未入库重量")
    private BigDecimal unStockedWeight;

    @ApiModelProperty("体积")
    private BigDecimal volume;

    @ApiModelProperty("运费收入金额")
    private BigDecimal freightIncomeAmount;

    @ApiModelProperty("收款日期")
    private LocalDateTime paymentTime;

    @ApiModelProperty("实收运费金额")
    private BigDecimal actualFreightAmount;

    @ApiModelProperty("进仓单号")
    private String warehousingNo;

    @ApiModelProperty("进口冻品优惠")
    private BigDecimal frozenGoodsDiscounts;

    @ApiModelProperty("未收款金额")
    private BigDecimal unpaidAmount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建人")
    private String createName;
}

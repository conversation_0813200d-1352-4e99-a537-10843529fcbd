package com.senox.tms.vo;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-12-26
 */
@Getter
@Setter
public class LogisticsDailyOrderDeliveryTotalAmountVo {

    /**
     * 总件数
     */
    private BigDecimal totalPieces;

    /**
     * 总千克
     */
    private BigDecimal totalKilograms;

    /**
     * 总应收运费
     */
    private BigDecimal totalReceivableFreightCharge;

    /**
     * 总实收运费
     */
    private BigDecimal totalReceivedFreightCharge;
}

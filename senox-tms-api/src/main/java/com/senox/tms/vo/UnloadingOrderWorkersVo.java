package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/20 16:46
 */
@ApiModel("装卸订单搬运工")
@Data
public class UnloadingOrderWorkersVo implements Serializable {
    private static final long serialVersionUID = 5575700324687140203L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private Long orderId;

    /**
     * 搬运工id
     */
    @ApiModelProperty("搬运工id")
    private Long workerId;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String workerName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 状态 0：非正常轮 1：正常轮
     */
    @ApiModelProperty("状态 0：非正常轮 1：正常轮")
    private Boolean flag;
}

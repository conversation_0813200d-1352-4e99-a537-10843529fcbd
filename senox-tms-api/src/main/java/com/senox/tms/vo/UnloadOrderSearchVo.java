package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/18 8:42
 */
@ApiModel("装卸搬运订单查询参数")
@Getter
@Setter
public class UnloadOrderSearchVo extends PageRequest {

    private static final long serialVersionUID = -6971626720981603652L;


    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("关键字")
    private String keywords;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("openid")
    private String openid;

    @ApiModelProperty("状态 0：草稿 1：正常")
    private Integer state;

    @ApiModelProperty("搬运状态 0：初始化 1：搬运中 2：已完成")
    private Integer workerStatus;

    @ApiModelProperty("搬运状态 0：初始化 1：搬运中 2：已完成")
    private List<Integer> workerStatusList;

    @ApiModelProperty("是否拼车")
    private Boolean carpool;

    /**
     * 是否预约单
     */
    @ApiModelProperty("是否预约单")
    private Boolean reservationOrder;

    /**
     * 预约时间
     */
    @ApiModelProperty("预约时间起")
    private LocalDateTime reservationTimeStart;

    /**
     * 预约时间
     */
    @ApiModelProperty("预约时间止")
    private LocalDateTime reservationTimeEnd;

    /**
     * 加急单
     */
    @ApiModelProperty("加急单")
    private Boolean urgentOrder;

    @ApiModelProperty("订单时间起")
    private LocalDateTime orderTimeStart;

    @ApiModelProperty("订单时间止")
    private LocalDateTime orderTimeEnd;

    @ApiModelProperty("搬运时间起")
    private LocalDateTime workTimeStart;

    @ApiModelProperty("搬运时间止")
    private LocalDateTime workTimeEnd;

    @ApiModelProperty("完成时间起")
    private LocalDateTime finishTimeStart;

    @ApiModelProperty("完成时间止")
    private LocalDateTime finishTimeEnd;

    @ApiModelProperty("订单号")
    private List<String> orderNoList;

    @ApiModelProperty("是否有账单")
    private Boolean bill;

    @ApiModelProperty("是否有应付")
    private Boolean payoff;

    @ApiModelProperty("是否管理员")
    private Boolean isManager;

    @ApiModelProperty("是否0元")
    private Boolean isZero;
}

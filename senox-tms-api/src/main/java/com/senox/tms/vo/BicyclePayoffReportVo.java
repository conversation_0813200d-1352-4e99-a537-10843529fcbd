package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-10-13
 */
@ApiModel("应付账单报表")
@Getter
@Setter
public class BicyclePayoffReportVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private String date;

    /**
     * 年
     */
    @ApiModelProperty("年")
    private Integer year;

    /**
     * 月
     */
    @ApiModelProperty("月")
    private Integer month;

    /**
     * 日
     */
    @ApiModelProperty("日")
    private Integer day;

    /**
     * 收款人
     */
    @ApiModelProperty("收款人")
    private String payeeName;

    /**
     * 推荐费
     */
    @ApiModelProperty("推荐费")
    private BigDecimal referralAmount;

    /**
     * 推荐数量
     */
    @ApiModelProperty("推荐数量")
    private Integer referralCount;

    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;
}

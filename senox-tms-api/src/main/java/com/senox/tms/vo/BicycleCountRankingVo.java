package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/27 14:50
 */
@ApiModel("排行榜")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BicycleCountRankingVo implements Serializable {
    private static final long serialVersionUID = 2093684275045184185L;

    /**
     * 下单数量排行榜
     */
    @ApiModelProperty("下单数量排行榜")
    private List<BicycleOrderCountRankingVo> orderCountRankingVoList;

    /**
     * 下单件数排行榜
     */
    @ApiModelProperty("下单件数排行榜")
    private List<BicyclePiecesCountRankingVo> piecesCountRankingVoList;

    /**
     * 下单金额排行榜
     */
    @ApiModelProperty("下单金额排行榜")
    private List<BicycleAmountCountRankingVo> amountCountRankingVoList;
}

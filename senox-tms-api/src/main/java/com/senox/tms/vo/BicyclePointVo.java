package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/12 15:07
 */
@Data
@ApiModel("三轮车配送地点")
public class BicyclePointVo implements Serializable {

    private static final long serialVersionUID = -5554708204399653262L;
    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "无效的id", groups = Update.class)
    private Long id;
    /**
     * 名称
     */
    @NotBlank(message = "无效的名称", groups = Add.class)
    @ApiModelProperty("名称")
    private String name;
}

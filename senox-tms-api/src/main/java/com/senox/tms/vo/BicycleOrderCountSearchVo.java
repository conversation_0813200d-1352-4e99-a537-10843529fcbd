package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/18 11:29
 */
@ApiModel("三轮车订单统计查询参数")
@Data
public class BicycleOrderCountSearchVo implements Serializable {
    private static final long serialVersionUID = -5955143582912388010L;

    @ApiModelProperty("日期起")
    private LocalDateTime startDate;

    @ApiModelProperty("日期止")
    private LocalDateTime endDate;
}

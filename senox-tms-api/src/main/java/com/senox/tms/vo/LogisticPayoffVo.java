package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/12/15 15:56
 */
@Getter
@Setter
@ToString
@ApiModel("物流客户应付款")
public class LogisticPayoffVo implements Serializable {

    private static final long serialVersionUID = -8090317399455417692L;

    @ApiModelProperty("id")
    private Long id;

    @NotNull(message = "无效的账单日")
    @ApiModelProperty("账单日")
    private LocalDate billDate;

    @NotBlank(message = "无效的商户")
    @ApiModelProperty("商户")
    private String merchant;

    @ApiModelProperty("订单件数")
    private BigDecimal productCount;

    @ApiModelProperty("订单总价")
    private BigDecimal productAmount;

    @ApiModelProperty("满减额")
    private BigDecimal productFullReduction;

    @ApiModelProperty("应收货款")
    private BigDecimal productToPaid;

    @ApiModelProperty("已收货款")
    private BigDecimal productPaid;

    @ApiModelProperty("欠款货款")
    private BigDecimal productOwe;

    @ApiModelProperty("其他减款")
    private BigDecimal productDeduction;

    @ApiModelProperty("差异金额")
    private BigDecimal productDiversity;

    @ApiModelProperty("物流费")
    private BigDecimal shipAmount;

    @ApiModelProperty("应结算金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("备注")
    private String remark;
}

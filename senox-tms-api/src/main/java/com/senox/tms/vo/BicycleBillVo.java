package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/22 10:16
 */
@Getter
@Setter
@ApiModel("三轮车配送应收账单")
public class BicycleBillVo implements Serializable {
    private static final long serialVersionUID = 2160244175437067072L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 账单日
     */
    @ApiModelProperty("账单日")
    private LocalDate billDate;

    /**
     * 账单年份
     */
    @ApiModelProperty("账单年份")
    private Integer billYear;

    /**
     * 账单月份
     */
    @ApiModelProperty("账单月份")
    private Integer billMonth;

    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    /**
     * 订单流水号
     */
    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;

    /**
     * 商户名
     */
    @ApiModelProperty("商户名")
    private String merchantName;

    /**
     * 冷藏编号
     */
    @ApiModelProperty("冷藏编号")
    private String rcSerial;

    /**
     * 结算id
     */
    @ApiModelProperty("结算id")
    private Long settlementId;

    /**
     * 结算周期
     */
    @ApiModelProperty("结算周期")
    private Integer settlePeriod;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /**
     * 起点
     */
    @ApiModelProperty("起点")
    private String startPoint;

    /**
     * 终点
     */
    @ApiModelProperty("终点")
    private String endPoint;

    /**
     * 配送完成时间
     */
    @ApiModelProperty("配送完成时间")
    private LocalDateTime deliveryFinishTime;
    /**
     * 配送金额
     */
    @ApiModelProperty("配送金额")
    private BigDecimal deliveryAmount;

    /**
     * 其他金额
     */
    @ApiModelProperty("其他金额")
    private BigDecimal otherAmount;

    /**
     * 装卸费
     */
    @ApiModelProperty("装卸费")
    private BigDecimal handlingAmount;

    /**
     * 上楼费
     */
    @ApiModelProperty("上楼费")
    private BigDecimal upstairsAmount;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal pieces;

    /**
     * 创建用户
     */
    @ApiModelProperty("创建用户")
    private String createOpenid;
}

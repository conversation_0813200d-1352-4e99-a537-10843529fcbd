package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-09
 **/
@Getter
@Setter
@ApiModel("城际运输结算单下发参数")
public class LogisticTransportBillSettlementSendVo {

    /**
     * 是否下发
     */
    @ApiModelProperty("是否下发")
    private Boolean send;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private String endDate;

    /**
     * 结算单id集
     */
    @ApiModelProperty("结算单id集")
    private List<Long> settlementIds;

    /**
     * 商户id集
     */
    @ApiModelProperty("商户id集")
    private List<Long> merchantIds;
}

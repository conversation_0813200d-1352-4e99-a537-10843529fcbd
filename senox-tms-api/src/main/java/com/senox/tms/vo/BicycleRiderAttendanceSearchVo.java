package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/14 16:59
 */
@ApiModel("三轮车配送骑手考勤查询参数")
@Getter
@Setter
public class BicycleRiderAttendanceSearchVo extends PageRequest {
    private static final long serialVersionUID = -6247138067219943242L;

    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;
    /**
     * 骑手编号
     */
    @ApiModelProperty("骑手编号")
    private String riderNo;
    /**
     * 骑手名
     */
    @ApiModelProperty("骑手名")
    private String riderName;
    /**
     * 上线时间起
     */
    @ApiModelProperty("上线时间起")
    private LocalDateTime onlineTimeStart;
    /**
     * 上线时间止
     */
    @ApiModelProperty("上线时间止")
    private LocalDateTime onlineTimeEnd;
    /**
     * 下线时间起
     */
    @ApiModelProperty("下线时间起")
    private LocalDateTime offlineTimeStart;
    /**
     * 下线时间止
     */
    @ApiModelProperty("下线时间止")
    private LocalDateTime offlineTimeEnd;
}

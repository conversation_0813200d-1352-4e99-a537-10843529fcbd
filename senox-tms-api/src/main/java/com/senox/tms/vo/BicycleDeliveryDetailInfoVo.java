package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/4 16:08
 */
@Data
@ApiModel("三轮车配送订单详细信息")
public class BicycleDeliveryDetailInfoVo {

    @ApiModelProperty("配送单详细id")
    private Long deliveryOrderDetailId;

    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    @ApiModelProperty("配送子单号")
    private String deliveryOrderSerialNoItem;

    @ApiModelProperty("揽货件数")
    private BigDecimal pickedPieces;

    @ApiModelProperty("骑手Id")
    private Long riderId;

    @ApiModelProperty("骑手名称")
    private String riderName;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("配送单子详细")
    private List<BicycleDeliveryOrderDetailItemVo> detailItemVos;
}

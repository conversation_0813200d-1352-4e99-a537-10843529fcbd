package com.senox.tms.vo;

import com.senox.tms.constant.LogisticTransportCategory;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-05-23
 **/
@Getter
@Setter
@ApiModel("物流运输账单")
public class LogisticTransportBillVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 结算单id
     */
    @ApiModelProperty("结算单id")
    private Long settlementId;

    /**
     * 账单日期
     */
    @ApiModelProperty("账单日期")
    private LocalDate billDate;

    /**
     * 年份
     */
    @ApiModelProperty("年份")
    private Integer billYear;

    /**
     * 月份
     */
    @ApiModelProperty("月份")
    private Integer billMonth;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderSerialNo;

    /**
     * 订单年月日
     */
    @ApiModelProperty("订单年月日")
    private String orderYearMonthDay;

    /**
     * 订单备注
     */
    @ApiModelProperty("订单备注")
    private String orderRemark;

    /**
     * 付款人
     */
    @ApiModelProperty("付款人")
    private LogisticTransportOrderPayer payer;

    /**
     * 类别
     * @see LogisticTransportCategory
     */
    @ApiModelProperty("类别")
    private LogisticTransportCategory category;

    /**
     * 司机姓名
     */
    @ApiModelProperty("司机姓名")
    private String driverName;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号")
    private String licensePlateNumber;

    /**
     * 是否包车
     */
    @ApiModelProperty("是否包车")
    private Boolean charter;

    /**
     * 始发站
     */
    @ApiModelProperty("始发站")
    private String departureStation;

    /**
     * 目的站
     */
    @ApiModelProperty("目的站")
    private String destinationStation;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private Integer pieces;

    /**
     * 装载重量(kg)
     */
    @ApiModelProperty("装载重量(kg)")
    private BigDecimal loadingWeight;

    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;

    /**
     * 商户名
     */
    @ApiModelProperty("商户名")
    private String merchantName;

    /**
     * 运费
     */
    @ApiModelProperty("运费")
    private BigDecimal freightCharge;

    /**
     * 其他费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 应收运费
     */
    @ApiModelProperty("应收运费")
    private BigDecimal receivableFreightCharge;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Boolean status;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private Integer payWay;
}

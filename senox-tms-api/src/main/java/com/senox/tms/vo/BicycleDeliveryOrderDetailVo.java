package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20 8:55
 */
@Data
@ApiModel("三轮车配送单详细")
public class BicycleDeliveryOrderDetailVo implements Serializable {
    private static final long serialVersionUID = -2873857147676280973L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 配送单id
     */
    @ApiModelProperty("配送单id")
    private Long deliveryOrderId;
    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;
    /**
     * 子配送单流水号
     */
    @ApiModelProperty("子配送单流水号")
    private String deliveryOrderSerialNoItem;
    /**
     * 订单流水号
     */
    @ApiModelProperty("订单流水号")
    @NotBlank(message = "无效的订单流水号", groups = Add.class)
    private String orderSerialNo;
    /**
     * 收费标准id
     */
    @ApiModelProperty("收费标准id")
    private Long chargesId;
    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    @ApiModelProperty("状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    @NotNull(message = "无效的状态", groups = Add.class)
    private Integer status;
    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    @NotNull(message = "无效的骑手", groups = {Add.class, Update.class})
    private Long riderId;
    /**
     * 揽货件数
     */
    @ApiModelProperty("揽货件数")
    @Min(value = 1, message = "揽货件数不能小于0")
    @NotNull(message = "无效的揽货件数")
    private BigDecimal pickedPieces;

    /**
     * 配送单子详细
     */
    @ApiModelProperty("配送单子详细")
    private List<BicycleDeliveryOrderDetailItemVo> detailItemVos;
    /**
     * 揽货点经纬度
     */
    @ApiModelProperty("揽货点经纬度")
    private String pickingPoint;
    /**
     * 送货点经纬度
     */
    @ApiModelProperty("送货点经纬度")
    private String deliveryPoint;
    /**
     * 揽货时间
     */
    @ApiModelProperty("揽货时间")
    private LocalDateTime pickingTime;
    /**
     * 配送时间
     */
    @ApiModelProperty("配送时间")
    private LocalDateTime sendTime;
    /**
     * 收货时间
     */
    @ApiModelProperty("收货时间")
    private LocalDateTime receivingTime;
    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    private LocalDateTime finishTime;
    /**
     * 评分星级
     */
    @ApiModelProperty("评分星级")
    private Integer rating;
    /**
     * 配送单任务
     */
    @ApiModelProperty(value = "配送单任务", hidden = true)
    private List<BicycleDeliveryOrderJobVo> jobVoList;
    /**
     * 起点id
     */
    @ApiModelProperty(value = "起点id", hidden = true)
    private Long startPointId;
    /**
     * 起点
     */
    @ApiModelProperty(value = "起点", hidden = true)
    private String startPointName;
    /**
     * 详细起点地址
     */
    @ApiModelProperty(value = "详细起点地址", hidden = true)
    private String startPointDetailName;
    /**
     * 终点id
     */
    @ApiModelProperty(value = "终点id", hidden = true)
    private Long endPointId;
    /**
     * 终点
     */
    @ApiModelProperty(value = "终点", hidden = true)
    private String endPointName;
    /**
     * 详细终点地址
     */
    @ApiModelProperty(value = "详细终点地址", hidden = true)
    private String endPointDetailName;
    /**
     * 预约配送时间起
     */
    @ApiModelProperty(value = "预约配送时间起", hidden = true)
    private LocalDateTime sendTimeStart;
    /**
     * 预约配送时间止
     */
    @ApiModelProperty(value = "预约配送时间止", hidden = true)
    private LocalDateTime sendTimeEnd;
    /**
     * 寄件人id
     */
    @ApiModelProperty("寄件人id")
    private Long senderId;
    /**
     * 寄件人
     */
    @ApiModelProperty(value = "寄件人", hidden = true)
    private String sender;
    /**
     * 寄件人冷藏编号
     */
    @ApiModelProperty("寄件人冷藏编号")
    private String senderSerialNo;
    /**
     * 寄件人联系方式
     */
    @ApiModelProperty(value = "寄件人联系方式", hidden = true)
    private String senderContact;
    /**
     * 收件人
     */
    @ApiModelProperty(value = "收件人", hidden = true)
    private String recipient;
    /**
     * 车牌
     */
    @ApiModelProperty("车牌")
    private String carNo;
    /**
     * 收件人联系方式
     */
    @ApiModelProperty(value = "收件人联系方式", hidden = true)
    private String recipientContact;
    /**
     * 客户备注
     */
    @ApiModelProperty(value = "客户备注", hidden = true)
    private String remark;
    /**
     * 件数
     */
    @ApiModelProperty(value = "件数", hidden = true)
    private BigDecimal pieces;
    /**
     * 总费用
     */
    @ApiModelProperty(value = "总费用", hidden = true)
    private BigDecimal totalCharge;
    /**
     * 配送费
     */
    @ApiModelProperty(value = "配送费", hidden = true)
    private BigDecimal deliveryCharge;
    /**
     * 其他费用
     */
    @ApiModelProperty(value = "其他费用", hidden = true)
    private BigDecimal otherCharge;

    /**
     * 装卸费
     */
    @ApiModelProperty(value = "装卸费", hidden = true)
    private BigDecimal handlingCharge;

    /**
     * 上楼费
     */
    @ApiModelProperty(value = "上楼费", hidden = true)
    private BigDecimal upstairsCharge;
    /**
     * 其他备注
     */
    @ApiModelProperty(value = "其他备注", hidden = true)
    private String otherRemark;
    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间", hidden = true)
    private LocalDateTime orderTime;
    /**
     * 状态 0：正常 1：取消
     */
    @ApiModelProperty("订单状态 0：正常 1：取消")
    private Integer orderStatus;
    /**
     * 订单状态备注
     */
    @ApiModelProperty("订单状态备注")
    private String statusRemark;
    /**
     * 微信创建用户
     */
    @ApiModelProperty(value = "微信创建用户", hidden = true)
    private String createOpenid;
    /**
     * 三轮车配送订单货物明细
     */
    @ApiModelProperty(value = "三轮车配送订单货物明细", hidden = true)
    private List<BicycleOrderGoodsDetailVo> orderGoodsDetailVos;
    /**
     * 多媒体资料访问连接集合
     */
    @ApiModelProperty("多媒体资料访问连接集合")
    private List<String> mediaUrlList;
    /**
     * 骑手姓名
     */
    @ApiModelProperty(value = "骑手姓名", hidden = true)
    private String riderName;
    /**
     * 骑手联系方式
     */
    @ApiModelProperty(value = "骑手联系方式", hidden = true)
    private String riderContact;

    /**
     * 推荐码
     */
    @ApiModelProperty(value = "推荐码", hidden = true)
    private String referralCode;
}

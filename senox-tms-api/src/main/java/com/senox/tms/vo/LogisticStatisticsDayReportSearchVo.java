package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/12/19 10:48
 */
@ApiModel("货物统计查询参数")
@Getter
@Setter
public class LogisticStatisticsDayReportSearchVo extends PageRequest {
    private static final long serialVersionUID = 5675415777570644615L;

    @ApiModelProperty("日期起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportDateStart;

    @ApiModelProperty("日期止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportDateEnd;

    @ApiModelProperty("发货人")
    private String shipper;

    @ApiModelProperty("物流单号")
    private String logisticsNo;

    @ApiModelProperty("收入类别")
    private Integer incomeType;

    @ApiModelProperty("司机")
    private String driverName;

    @ApiModelProperty("车牌")
    private String carNo;

    @ApiModelProperty("是否包车")
    private Boolean charteredBus;

    @ApiModelProperty("始发站")
    private String departureStation;

    @ApiModelProperty("目的站")
    private String destinationStation;

    @ApiModelProperty("收款时间起")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentTimeStart;

    @ApiModelProperty("收款时间止")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime paymentTimeEnd;
}

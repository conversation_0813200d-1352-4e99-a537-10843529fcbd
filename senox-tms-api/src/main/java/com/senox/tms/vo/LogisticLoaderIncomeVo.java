package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@ApiModel("搬运工收入")
public class LogisticLoaderIncomeVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private LocalDate date;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String loaderNumber;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String loaderName;

    /**
     * 结算id
     */
    @ApiModelProperty("结算单id")
    private Long settlementId;

    /**
     * 总金额
     */
    @ApiModelProperty("金额")
    private BigDecimal amount;
}

package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/2/19 8:31
 */
@ApiModel("三轮车配送订单费用添加")
@Data
public class BicycleOrderChargeVo implements Serializable {
    private static final long serialVersionUID = 4391666575808971916L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "无效的id", groups = Add.class)
    private Long id;

    /**
     * 装卸费
     */
    @ApiModelProperty("装卸费")
    private BigDecimal handlingCharge;

    /**
     * 上楼费
     */
    @ApiModelProperty("上楼费")
    private BigDecimal upstairsCharge;

    /**
     * 费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 其他备注
     */
    @ApiModelProperty("其他备注")
    private String otherRemark;

    /**
     * 配送单id
     */
    @ApiModelProperty("配送单id")
    private Long deliveryDetailId;
}

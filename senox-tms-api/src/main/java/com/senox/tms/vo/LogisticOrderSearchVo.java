package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/12/1 10:37
 */
@Getter
@Setter
@ToString
@ApiModel("物流订单查询")
public class LogisticOrderSearchVo extends PageRequest {

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("商户")
    private String merchant;

    @ApiModelProperty("商品")
    private String product;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("市场")
    private String market;

    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("车牌号")
    private String vehicleNo;

    @ApiModelProperty("发货时间起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate shipDateStart;

    @ApiModelProperty("发货时间止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate shipDateEnd;

    @ApiModelProperty("运费单价")
    private BigDecimal shipPrice;

    @ApiModelProperty("运费单价起")
    private BigDecimal shipPriceStart;

    @ApiModelProperty("运费单价止")
    private BigDecimal shipPriceEnd;

    @ApiModelProperty("运费折扣率（小于）")
    private BigDecimal shipDiscountLt;

    @ApiModelProperty("运费折扣率起")
    private BigDecimal shipDiscountStart;

    @ApiModelProperty("运费折扣率止")
    private BigDecimal shipDiscountEnd;

    @ApiModelProperty("欠款金额起")
    private BigDecimal productOweStart;

    @ApiModelProperty("欠款金额止")
    private BigDecimal productOweEnd;

    @ApiModelProperty("交款人")
    private String productPaidMan;

}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/18 10:00
 */
@Data
@ApiModel("三轮车配送订单货物明细")
public class BicycleOrderGoodsDetailVo implements Serializable {
    private static final long serialVersionUID = 8830267382614050694L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private Long orderId;
    /**
     * 货物名
     */
    @ApiModelProperty("货物名")
    @NotBlank(message = "无效的货物名")
    private String goodsName;
    /**
     * 货物类型 0其他，1重货，2抛货
     */
    @ApiModelProperty("货物类型 0其他，1重货，2抛货")
    private Integer goodsType;
    /**
     * 件数
     */
    @ApiModelProperty("件数")
    @Min(value = 1, message = "件数不能小于0")
    @NotNull(message = "无效的件数")
    private BigDecimal pieces;
    /**
     * 重量
     */
    @ApiModelProperty("重量")
    private BigDecimal weight;
    /**
     * 体积
     */
    @ApiModelProperty("体积")
    private BigDecimal size;

    /**
     * 子配送单流水号
     */
    @ApiModelProperty("子配送单流水号")
    private String deliveryOrderSerialNoItem;
}

package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/12/15 15:01
 */
@Getter
@Setter
@ToString
@ApiModel("物流应付查询")
public class LogisticPayoffSearchVo extends PageRequest {

    @ApiModelProperty("送货日期起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate shipDateStart;

    @ApiModelProperty("送货日期止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate shipDateEnd;

    @ApiModelProperty("商户")
    private String merchant;

    @ApiModelProperty("欠款起")
    private BigDecimal productOweStart;

    @ApiModelProperty("欠款止")
    private BigDecimal productOweEnd;

    @ApiModelProperty("应结算货款起")
    private BigDecimal totalAmountStart;

    @ApiModelProperty("应结算货款止")
    private BigDecimal totalAmountEnd;


}

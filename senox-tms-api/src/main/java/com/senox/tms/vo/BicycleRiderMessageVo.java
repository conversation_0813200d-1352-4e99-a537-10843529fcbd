package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/9 9:20
 */
@ApiModel("骑手接单消息")
@Data
public class BicycleRiderMessageVo implements Serializable {
    private static final long serialVersionUID = -4771325137116294606L;

    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;

    /**
     * 骑手名
     */
    @ApiModelProperty("骑手名")
    private String riderName;

    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;
}

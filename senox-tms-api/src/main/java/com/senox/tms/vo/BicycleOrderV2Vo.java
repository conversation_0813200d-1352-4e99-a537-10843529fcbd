package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/19 16:23
 */
@ApiModel("三轮车订单V2")
@Data
public class BicycleOrderV2Vo implements Serializable {
    private static final long serialVersionUID = -1342877248356721116L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    @ApiModelProperty("发货人冷藏编号")
    private String senderSerialNo;

    @ApiModelProperty("寄件人")
    private String sender;

    @ApiModelProperty("收件人")
    private String recipient;

    @ApiModelProperty("起点")
    private String startPointName;

    @ApiModelProperty("详细起点地址")
    private String startPointDetailName;

    @ApiModelProperty("终点")
    private String endPointName;

    @ApiModelProperty("详细终点地址")
    private String endPointDetailName;

    @ApiModelProperty("收费标准id")
    private Long chargesId;

    @ApiModelProperty("其他备注")
    private String otherRemark;

    @ApiModelProperty("件数")
    private BigDecimal pieces;

    @ApiModelProperty("总费用")
    private BigDecimal totalCharge;

    @ApiModelProperty("配送费用")
    private BigDecimal deliveryCharge;

    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    @ApiModelProperty("装卸费")
    private BigDecimal handlingCharge;

    @ApiModelProperty("上楼费")
    private BigDecimal upstairsCharge;

    @ApiModelProperty("下单时间")
    private LocalDateTime orderTime;

    @ApiModelProperty("状态 0：草稿 1：正常")
    private Integer state;

    @ApiModelProperty("来源(0:系统生成;1:导入)")
    private Integer origin;

    @ApiModelProperty("订单状态 0：正常 1：取消")
    private Integer orderStatus;

    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    @ApiModelProperty("结算周期：0单结 1日结 2月结")
    private Integer settlePeriod;

    @ApiModelProperty("配送状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    private Integer deliveryStatus;

    @ApiModelProperty("骑手姓名组合")
    private String composeRiderName;

    @ApiModelProperty("三轮车配送信息")
    private List<BicycleDeliveryOrderInfoV2Vo> infoV2Vos;
}

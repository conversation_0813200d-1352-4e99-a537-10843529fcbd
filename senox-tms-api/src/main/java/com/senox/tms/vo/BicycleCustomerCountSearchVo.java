package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/10/13 13:34
 */
@ApiModel("三轮车客户下单统计查询参数")
@Getter
@Setter
public class BicycleCustomerCountSearchVo extends PageRequest {
    private static final long serialVersionUID = 6018008441081454085L;


    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;


    /**
     * 商户名
     */
    @ApiModelProperty("商户名")
    private String merchantName;

}

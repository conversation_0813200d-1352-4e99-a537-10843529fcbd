package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21 14:31
 */
@Getter
@Setter
@ApiModel("客户下单信息")
public class BicycleCustomerMonthInfoVo implements Serializable {
    private static final long serialVersionUID = -6482463627607960664L;

    @ApiModelProperty("商户Id")
    private Long senderId;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("收费标准")
    private String chargesName;

    @ApiModelProperty("下单数量")
    private List<BicycleCustomerMonthOrderCountVo> countVoList;
}

package com.senox.tms.vo;

import com.senox.tms.constant.LogisticTransportOrderPayer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-03
 **/
@ApiModel("城际运输账单生成")
@Getter
@Setter
public class LogisticTransportBillGenerateVo {

    /**
     * 订单编号集
     */
    @ApiModelProperty("订单编号集")
    private List<String> orderSerialNos;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    /**
     * 收费员
     */
    @ApiModelProperty("收费员")
    private Long tollManId;

    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;

    /**
     * 商户编号
     */
    @ApiModelProperty("商户编号")
    private String merchantSerial;

    /**
     * 是否下发
     */
    @ApiModelProperty("是否下发")
    private Boolean send;

    /**
     * 付款人
     */
    @ApiModelProperty("付款人")
    private LogisticTransportOrderPayer payer;
}

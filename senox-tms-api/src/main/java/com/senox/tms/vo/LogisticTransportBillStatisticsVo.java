package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-04
 **/
@Getter
@Setter
@ApiModel("城际运输账单统计")
public class LogisticTransportBillStatisticsVo {

    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    /**
     * 总支付金额
     */
    @ApiModelProperty("总支付金额")
    private BigDecimal totalPaidAmount;

    /**
     * 总待支付金额
     */
    @ApiModelProperty("总待支付金额")
    private BigDecimal paidStillAmount;
}

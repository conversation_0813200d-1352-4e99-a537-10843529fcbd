package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/15 14:34
 */
@Getter
@Setter
@ApiModel("配送订单查询参数")
public class BicycleDeliverySearchVo extends PageRequest {
    private static final long serialVersionUID = -5088336799816911321L;

    /**
     * 状态 0：草稿 1：正常
     */
    @ApiModelProperty("状态 0：草稿 1：正常")
    private Integer state;

    /**
     * 订单状态 0：正常 1：取消
     */
    @ApiModelProperty("订单状态 0：正常 1：取消")
    public Integer orderStatus;

    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    @ApiModelProperty("状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    private List<Integer> status;

    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;

    /**
     * 下单时间起
     */
    @ApiModelProperty("下单时间起")
    private LocalDateTime orderTimeStart;
    /**
     * 下单时间止
     */
    @ApiModelProperty("下单时间止")
    private LocalDateTime orderTimeEnd;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/31 15:35
 */
@ApiModel("鹏翔分佣")
@Data
public class UnloadingSharesVo implements Serializable {
    private static final long serialVersionUID = -3358447269824984066L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 分佣比例
     */
    @ApiModelProperty("分佣比例")
    private BigDecimal sharesRate;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    private LocalDateTime effectiveTime;

    /**
     * 失效时间
     */
    @ApiModelProperty("失效时间")
    private LocalDateTime ineffectiveTime;

    /**
     * 状态
     * @see com.senox.tms.constant.UnloadingSharesStatus
     */
    @ApiModelProperty("状态 0未生效 1已生效 2已失效")
    private Integer status;
}

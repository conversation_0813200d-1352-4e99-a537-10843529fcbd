package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-9
 */
@Getter
@Setter
@ApiModel("三轮车结算单查询参数")
public class BicycleBillSettlementSearchVo extends PageRequest {

    /**
     * 商户id集
     */
    @ApiModelProperty("商户id集")
    private List<Long> merchantIds;

    /**
     * 用户
     */
    @ApiModelProperty("用户")
    private String openid;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keyword;

    /**
     * 收费员
     */
    @ApiModelProperty("收费员")
    private Long tollManId;

    /**
     * 收费方式
     */
    @ApiModelProperty("收费方式")
    private String payWay;

    /**
     * 下发状态
     */
    @ApiModelProperty("下发状态")
    private Boolean send;

    /**
     * 账单状态
     */
    @ApiModelProperty("账单状态")
    private Boolean status;

    /**
     * 结算周期
     */
    @ApiModelProperty("结算周期")
    private Integer settlePeriod;

    /**
     * 账单开始时间
     */
    @ApiModelProperty("账单开始时间")
    private String startDate;

    /**
     * 账单结束时间
     */
    @ApiModelProperty("账单结束时间")
    private String endDate;

    /**
     * 支付开始时间
     */
    @ApiModelProperty("支付开始时间")
    private LocalDateTime paidStartDate;

    /**
     * 支付结束时间
     */
    @ApiModelProperty("支付结束时间")
    private LocalDateTime paidEndDate;
}

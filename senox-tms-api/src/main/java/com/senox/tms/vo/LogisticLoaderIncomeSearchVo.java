package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@ApiModel("搬运工收益")
public class LogisticLoaderIncomeSearchVo extends PageRequest {

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String loaderNumber;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String loaderName;

    /**
     * 结算id
     */
    @ApiModelProperty("结算id")
    private Long settlementId;


}

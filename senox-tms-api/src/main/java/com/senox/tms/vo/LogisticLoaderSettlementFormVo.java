package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.tms.constant.LogisticLoaderFreightType;
import com.senox.tms.constant.LogisticLoaderGoodsType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@ApiModel("搬运工结算表单")
public class LogisticLoaderSettlementFormVo {

    @NotNull(message = "id不能为空", groups = {Update.class})
    @Min(value = 1, message = "id格式不合法", groups = {Update.class})
    @ApiModelProperty("id")
    private Long id;

    /**
     * 日期
     */
    @NotNull(message = "日期不能为空", groups = {Add.class})
    @ApiModelProperty("日期")
    private LocalDate date;

    /**
     * 装卸类型
     */
    @NotNull(message = "装卸类型不能为空", groups = {Add.class})
    @ApiModelProperty("装卸类型")
    private LogisticLoaderFreightType freightType;

    /**
     * 客户名
     */
    @NotNull(message = "客户不能为空", groups = {Add.class})
    @Min(value = 1, message = "客户格式不合法", groups = {Add.class})
    @ApiModelProperty("客户id")
    private Long customerId;

    /**
     * 商品类型
     */
    @NotNull(message = "商品类型不能为空", groups = {Add.class})
    @ApiModelProperty("商品类型")
    private LogisticLoaderGoodsType goodsType;

    /**
     * 平均搬运量
     */
    @ApiModelProperty("平均搬运量")
    private BigDecimal transportAvg;

    /**
     * 总搬运量
     */
    @NotNull(message = "总搬运量不能为空", groups = {Add.class})
    @ApiModelProperty("总搬运量")
    private BigDecimal transportTotal;

    /**
     * 参与人数
     */
    @ApiModelProperty("参与人数")
    private Integer participationNumber;

    /**
     * 装卸单价
     */
    @NotNull(message = "装卸单价不能为空", groups = {Add.class})
    @ApiModelProperty("装卸单价")
    private BigDecimal freightUnitPrice;

    /**
     * 装卸总金额
     */
    @ApiModelProperty("装卸总金额")
    private BigDecimal freightTotalAmount;

    /**
     * 分拣费
     */
    @NotNull(message = "分拣费不能为空", groups = {Add.class})
    @ApiModelProperty("分拣费")
    private BigDecimal sortingFee;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal pieces;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号")
    private String carNo;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 工时
     */
    @NotNull(message = "工时不能为空", groups = {Add.class})
    @ApiModelProperty("工时")
    private BigDecimal workingHours;

    /**
     * 餐补
     */
    @NotNull(message = "餐补不能为空", groups = {Add.class})
    @ApiModelProperty("餐补")
    private BigDecimal mealAllowanceAmount;

    /**
     * 其他费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 小计
     */
    @ApiModelProperty("小计")
    private BigDecimal subtotalAmount;

    /**
     * 合计
     */
    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    /**
     * 搬运工id集
     */
    @NotEmpty(message = "搬运工id集不能为空",groups = {Add.class})
    @ApiModelProperty("搬运工id集")
    public List<Long> loaderIds;

}

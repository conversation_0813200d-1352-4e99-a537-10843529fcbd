package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20 10:03
 */
@Data
@ApiModel("三轮车配送单任务")
public class BicycleDeliveryOrderJobVo implements Serializable {
    private static final long serialVersionUID = -5650964141458011062L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 配送单详细id
     */
    @ApiModelProperty("配送单详细id")
    @NotNull(message = "无效的配送单详细id", groups = Add.class)
    private Long deliveryOrderDetailId;
    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    @ApiModelProperty("状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    @NotNull(message = "无效的状态", groups = Add.class)
    private Integer status;
    /**
     * 地点经纬度
     */
    @ApiModelProperty("地点经纬度")
    private String point;
    /**
     * 骑手备注
     */
    @ApiModelProperty("骑手备注")
    private String remark;
    /**
     * 多媒体资料访问连接集合
     */
    @ApiModelProperty("多媒体资料访问连接集合")
    private List<String> mediaUrlList;
    /**
     * 星级评价
     */
    @ApiModelProperty("星级评价")
    private Integer rating;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;
}

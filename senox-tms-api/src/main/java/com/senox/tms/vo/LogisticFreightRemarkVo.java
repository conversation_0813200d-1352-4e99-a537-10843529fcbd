package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-15 14:03
 */
@ApiModel("物流货运支付备注参数")
@Data
public class LogisticFreightRemarkVo implements Serializable {
    private static final long serialVersionUID = -6066057104500400487L;

    @ApiModelProperty("ids")
    private List<Long> ids;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "操作人id", hidden = true)
    private Long operatorId;

    @ApiModelProperty(value = "操作人姓名", hidden = true)
    private String operatorName;
}


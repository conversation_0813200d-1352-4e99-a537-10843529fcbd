package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.BicycleStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2023-9-12
 */
@Getter
@Setter
@ApiModel("收费标准-查询")
public class BicycleChargesSearchVo extends PageRequest {

    private static final long serialVersionUID = 3287655199834026646L;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    private LocalDateTime effectiveStartTime;

    /**
     * 失效时间
     */
    @ApiModelProperty("失效时间")
    private LocalDateTime effectiveEndTime;

    /**
     * 状态
     *
     * @see BicycleStatus
     */
    @ApiModelProperty("状态")
    private BicycleStatus status;
}

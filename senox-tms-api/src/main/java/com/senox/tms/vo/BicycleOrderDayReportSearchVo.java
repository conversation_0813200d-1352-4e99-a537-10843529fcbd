package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/10/16 15:13
 */
@Getter
@Setter
@ToString
@ApiModel("日账单查询")
public class BicycleOrderDayReportSearchVo extends PageRequest {
    private static final long serialVersionUID = 5679552415244028638L;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("商户名")
    private String merchantName;

    @ApiModelProperty("报表日起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportDateStart;

    @ApiModelProperty("报表日止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate reportDateEnd;

}

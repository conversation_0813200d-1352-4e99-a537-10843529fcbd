package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/19 15:51
 */
@ApiModel("三轮订单V2查询参数")
@Getter
@Setter
public class BicycleOrderV2SearchVo extends PageRequest {

    private static final long serialVersionUID = 4505321404611199994L;

    @ApiModelProperty("状态 0：草稿 1：正常")
    private Integer state;

    @ApiModelProperty("是否分派")
    private Boolean delivery;

    @ApiModelProperty("是否分配骑手")
    private Boolean assignRider;

    @ApiModelProperty("订单状态 0：正常 1：取消")
    private Integer orderStatus;

    @ApiModelProperty("寄件人id")
    private Long senderId;

    @ApiModelProperty("配送状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    private List<Integer> deliveryStatusList;

    @ApiModelProperty("下单时间起")
    private LocalDateTime orderTimeStart;

    @ApiModelProperty("下单时间止")
    private LocalDateTime orderTimeEnd;

    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    @ApiModelProperty("配送单流水号集合")
    private List<String> deliveryOrderSerialNoList;

    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    @ApiModelProperty("起点id")
    private Long startPointId;

    @ApiModelProperty("终点id")
    private Long endPointId;

    @ApiModelProperty("寄件人")
    private String sender;

    @ApiModelProperty("发货人冷藏编号")
    private String senderSerialNo;

    @ApiModelProperty("结算周期：0单结 1日结 2月结")
    private List<Integer> settlePeriodList;
}

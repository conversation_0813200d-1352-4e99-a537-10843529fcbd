package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 14:31
 */
@ApiModel("装卸搬运工查询参数")
@Getter
@Setter
public class UnloadingWorkerSearchVo extends PageRequest {
    private static final long serialVersionUID = -3888246811376465223L;

    /**
     * 搬运工状态
     */
    @ApiModelProperty("状态 0 下班; 1 已挂牌; 2 搬运中 3 请假")
    private List<Integer> statusList;

    /**
     * 班次
     */
    @ApiModelProperty("班次 0 早班； 1 晚班")
    private Integer classes;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keyword;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 返回行数
     */
    @ApiModelProperty("返回行数")
    private boolean rows;

    /**
     * 轮次
     */
    @ApiModelProperty("轮次")
    private Integer roundNum;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/1 9:20
 */
@Getter
@Setter
@ApiModel("三轮车订单取消参数")
public class BicycleOrderCancelVo implements Serializable {
    private static final long serialVersionUID = 2369061902339015134L;

    @ApiModelProperty("订单id集合")
    private List<Long> orderIds;

    @ApiModelProperty("订单状态 0：正常 1：取消")
    public Integer status;

    @ApiModelProperty("状态备注")
    public String statusRemark;
}

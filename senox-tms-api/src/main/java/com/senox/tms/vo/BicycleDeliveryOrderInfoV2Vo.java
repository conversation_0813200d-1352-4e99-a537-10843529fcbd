package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/11/19 16:35
 */
@ApiModel("三轮车配送信息V2")
@Data
public class BicycleDeliveryOrderInfoV2Vo implements Serializable {
    private static final long serialVersionUID = 7484303007893207101L;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("详细配送状态")
    private Integer deliveryDetailStatus;

    @ApiModelProperty("骑手id")
    private Long riderId;

    @ApiModelProperty("配送件数")
    private BigDecimal pickedPieces;

    @ApiModelProperty("骑手姓名")
    private String riderName;
}

package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023-12-5
 */
@Getter
@Setter
@ApiModel("物流每日订单配送查询")
public class LogisticsDailyOrderDeliverySearchVo extends PageRequest {

    /**
     * 配送单号
     */
    @ApiModelProperty("配送单号")
    private String orderDeliveryNo;

    /**
     * 配送车牌
     */
    @ApiModelProperty("配送车牌")
    private String orderDeliveryCarNo;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 送货开始日期
     */
    @ApiModelProperty("送货开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate sendStartDate;

    /**
     * 送货结束日期
     */
    @ApiModelProperty("送货结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate sendEndDate;

    /**
     * 类型(1:干货线;2:冻品线)
     */
    @ApiModelProperty("类型(1:干货线;2:冻品线)")
    private Integer type;


}

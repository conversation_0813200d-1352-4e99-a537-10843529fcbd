package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/25 13:37
 */
@Getter
@Setter
@ApiModel("三轮车应付账单查询")
public class BicyclePayoffSearchVo extends PageRequest {
    private static final long serialVersionUID = 6691160138697952052L;

    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    /**
     * 订单流水号
     */
    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    /**
     * 账单状态：0初始化；1已支付
     */
    @ApiModelProperty("账单状态：0初始化；1已支付")
    private Integer status;

    /**
     * 支付时间起
     */
    @ApiModelProperty("支付时间起")
    private LocalDateTime paidTimeBegin;

    /**
     * 支付时间止
     */
    @ApiModelProperty("支付时间止")
    private LocalDateTime paidTimeEnd;

    /**
     * 收款人姓名
     */
    @ApiModelProperty("收款人姓名")
    private String payeeName;

    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;

    /**
     * 开始时间启
     */
    @ApiModelProperty("时间起")
    private LocalDateTime startTime;

    /**
     * 开始时间止
     */
    @ApiModelProperty("时间止")
    private LocalDateTime endTime;

    /**
     * 是否推荐费
     */
    @ApiModelProperty("是否推荐费")
    private Boolean referral;

    /**
     * 排除零元
     */
    @ApiModelProperty("排除零元")
    private Boolean excludeZero;
}

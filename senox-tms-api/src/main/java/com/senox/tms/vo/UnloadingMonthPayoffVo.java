package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/8 10:34
 */
@ApiModel("鹏翔应付月账单生成")
@Data
public class UnloadingMonthPayoffVo implements Serializable {

    private static final long serialVersionUID = 7706605238526210920L;

    @ApiModelProperty("年")
    private Integer year;

    @ApiModelProperty("月")
    private Integer month;

    @ApiModelProperty("搬运工id")
    private Long workerId;

    @ApiModelProperty("备注")
    private String remark;
}

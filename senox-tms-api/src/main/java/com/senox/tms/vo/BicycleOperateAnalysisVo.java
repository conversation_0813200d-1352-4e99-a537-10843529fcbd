package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/9/27 13:37
 */
@ApiModel("三轮车运营分析")
@Data
public class BicycleOperateAnalysisVo implements Serializable {
    private static final long serialVersionUID = 7665210237674173756L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private LocalDate operateAnalysisDate;

    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    /**
     * 总件数
     */
    @ApiModelProperty("总件数")
    private BigDecimal totalPieces;

    /**
     * 平均单价
     */
    @ApiModelProperty("平均单价")
    private BigDecimal avgUnitPrice;

    /**
     * 总单数
     */
    @ApiModelProperty("总单数")
    private Integer totalCount;

    /**
     * 平均每单用时
     */
    @ApiModelProperty("平均每单用时")
    private Integer avgSecond;

    /**
     * 寄件人id
     */
    @ApiModelProperty("寄件人id")
    private Long bestSenderId;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String bestCustomerName;

    /**
     * 最佳骑手id
     */
    @ApiModelProperty("最佳骑手id")
    private Long bestRiderId;

    /**
     * 最佳骑手
     */
    @ApiModelProperty("最佳骑手")
    private String bestRiderName;

    public BicycleOperateAnalysisVo() {
        setTotalAmount(BigDecimal.ZERO);
        setTotalPieces(BigDecimal.ZERO);
        setAvgUnitPrice(BigDecimal.ZERO);
    }
}

package com.senox.tms.vo;

import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/10/17 9:06
 */
@Getter
@Setter
@ToString
@ApiModel("三轮车应收月账单")
public class BicycleOrderMonthReportVo implements Serializable {
    private static final long serialVersionUID = 8696077540598673496L;

    @ApiModelProperty("id")
    @NotNull(message = "无效的id", groups = Update.class)
    private Long id;

    @ApiModelProperty("月报年月")
    private String reportYearMonth;

    @ApiModelProperty("月报年份")
    private Integer reportYear;

    @ApiModelProperty("月报月份")
    private Integer reportMonth;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("商户名")
    private String merchantName;

    @ApiModelProperty("总件数")
    private BigDecimal totalPieces;

    @ApiModelProperty("总单数")
    private Integer totalCount;

    @ApiModelProperty("配送费用")
    private BigDecimal deliveryCharge;

    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    @ApiModelProperty("装卸费")
    private BigDecimal handlingCharge;

    @ApiModelProperty("上楼费")
    private BigDecimal upstairsCharge;

    @ApiModelProperty("合计")
    private BigDecimal totalCharge;

}

package com.senox.tms.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/19 11:31
 */
@Getter
@Setter
@ApiModel("货物统计分页结果")
public class LogisticStatisticsDayReportPageResult<T> extends PageResult<T> {

    @ApiModelProperty("件数")
    private BigDecimal pieces;

    @ApiModelProperty("装载重量")
    private BigDecimal loadingWeight;

    @ApiModelProperty("入库重量")
    private BigDecimal storageWeight;

    @ApiModelProperty("未入库重量")
    private BigDecimal unStockedWeight;

    @ApiModelProperty("体积")
    private BigDecimal volume;

    @ApiModelProperty("运费收入金额")
    private BigDecimal freightIncomeAmount;

    @ApiModelProperty("实收运费金额")
    private BigDecimal actualFreightAmount;

    @ApiModelProperty("进口冻品优惠")
    private BigDecimal frozenGoodsDiscounts;

    @ApiModelProperty("未收款金额")
    private BigDecimal unpaidAmount;

    public LogisticStatisticsDayReportPageResult() {
        init();
    }

    public LogisticStatisticsDayReportPageResult(int pageNo, int pageSize) {
        super(pageNo, pageSize);
        init();
    }

    private void init() {
        this.pieces = BigDecimal.ZERO;
        this.loadingWeight = BigDecimal.ZERO;
        this.storageWeight = BigDecimal.ZERO;
        this.unStockedWeight = BigDecimal.ZERO;
        this.volume = BigDecimal.ZERO;
        this.freightIncomeAmount = BigDecimal.ZERO;
        this.actualFreightAmount = BigDecimal.ZERO;
        this.frozenGoodsDiscounts = BigDecimal.ZERO;
        this.unpaidAmount = BigDecimal.ZERO;
    }

    /**
     * 空白页
     * @param <T>
     * @return
     */
    public static <T> LogisticStatisticsDayReportPageResult<T> emptyPage() {
        return new LogisticStatisticsDayReportPageResult<>();
    }
}

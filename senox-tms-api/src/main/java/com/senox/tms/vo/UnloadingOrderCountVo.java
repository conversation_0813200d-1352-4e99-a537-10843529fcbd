package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/3/28 16:06
 */
@ApiModel("鹏翔订单统计")
@Data
public class UnloadingOrderCountVo implements Serializable {
    private static final long serialVersionUID = 8699006270940823175L;

    /**
     * 时间区间
     */
    @ApiModelProperty("时间")
    private LocalDateTime hours;

    /**
     * 未处理数量
     */
    @ApiModelProperty("未处理数量")
    private Integer undoCount;

    /**
     * 正在处理数量
     */
    @ApiModelProperty("正在处理数量")
    private Integer doingCount;

    /**
     * 处理完成数量
     */
    @ApiModelProperty("处理完成数量")
    private Integer doneCount;

    /**
     * 货物数量
     */
    @ApiModelProperty("货物数量")
    private BigDecimal quantity;
}

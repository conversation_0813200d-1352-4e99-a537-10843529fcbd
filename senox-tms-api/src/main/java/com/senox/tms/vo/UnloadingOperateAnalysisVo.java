package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/28 14:29
 */
@ApiModel("鹏翔运营分析")
@Data
public class UnloadingOperateAnalysisVo implements Serializable {
    private static final long serialVersionUID = -4214264398148796624L;

    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    /**
     * 总数量
     */
    @ApiModelProperty("总数量")
    private Integer totalQuantity;

    /**
     * 平均单价
     */
    @ApiModelProperty("平均单价")
    private BigDecimal avgUnitPrice;

    /**
     * 总单数
     */
    @ApiModelProperty("总单数")
    private Integer totalCount;

    /**
     * 平均每单用时
     */
    @ApiModelProperty("平均每单用时")
    private Integer avgSecond;
}

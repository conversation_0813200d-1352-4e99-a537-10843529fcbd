package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/12/25 13:51
 */
@Getter
@Setter
@ToString
@ApiModel("发货折扣率查询")
public class ShipOrderDiscountSearchVo implements Serializable {

    private static final long serialVersionUID = 4506388985341344147L;

    @NotNull(message = "无效的发货日期")
    @ApiModelProperty("发货日期")
    private LocalDate shipDate;

    @NotBlank(message = "无效的订单好")
    @ApiModelProperty("订单号")
    private String orderNo;

    @NotNull(message = "无效的实发重量")
    @ApiModelProperty("实发重量")
    private BigDecimal shipWeight;

    @ApiModelProperty("排除id")
    private Long excludeId;


}

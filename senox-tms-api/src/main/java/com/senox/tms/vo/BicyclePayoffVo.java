package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/25 13:33
 */
@Data
@ApiModel("三轮车应付账单")
public class BicyclePayoffVo implements Serializable {
    private static final long serialVersionUID = 9178917926753025460L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 账单年份
     */
    @ApiModelProperty("账单年份")
    private Integer billYear;

    /**
     * 账单月份
     */
    @ApiModelProperty("账单月份")
    private Integer billMonth;

    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    /**
     * 订单流水号
     */
    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    /**
     * 收款人姓名
     */
    @ApiModelProperty("收款人姓名")
    private String payeeName;

    /**
     * 收款人类型
     */
    @ApiModelProperty("收款人类型 0未知 1骑手")
    private Integer payeeType;

    /**
     * 收款人联系方式
     */
    @ApiModelProperty("收款人联系方式")
    private String payeeContact;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private Integer payway;

    /**
     * 分佣金额
     */
    @ApiModelProperty("分佣金额")
    private BigDecimal shareAmount;

    /**
     * 推荐费
     */
    @ApiModelProperty("推荐费")
    private BigDecimal referralAmount;

    /**
     * 是否推荐费
     */
    @ApiModelProperty("是否推荐费")
    private Boolean referral;

    /**
     * 费用
     */
    @ApiModelProperty("费用")
    private BigDecimal amount;

    /**
     * 状态(0:未支付;1:已支付)
     */
    @ApiModelProperty("状态(0:未支付;1:已支付)")
    private Integer status;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private Integer pieces;

    /**
     * 交货时间
     */
    @ApiModelProperty("交货时间")
    private Integer deliveryTime;

    /**
     * 商户名
     */
    @ApiModelProperty("商户名")
    private String merchantName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 订单总金额
     */
    @ApiModelProperty("订单总件数")
    private BigDecimal orderTotalPieces;

    /**
     * 订单总金额
     */
    @ApiModelProperty("订单总金额")
    private BigDecimal orderTotalCharge;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/10/12 9:18
 */
@Data
@ApiModel("三轮车客户下单当日当月统计")
public class BicycleCustomerCountVo implements Serializable {
    private static final long serialVersionUID = -2944126644049470538L;

    @ApiModelProperty("日期")
    private LocalDate todayDate;

    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;


    /**
     * 商户名
     */
    @ApiModelProperty("商户名")
    private String merchantName;

    /**
     * 当天下单量
     */
    @ApiModelProperty("当天下单量")
    private Integer todayCount;


    /**
     * 当天下单件数
     */
    @ApiModelProperty("当天下单件数")
    private BigDecimal todayPieces;


    /**
     * 当天下单金额
     */
    @ApiModelProperty("当天下单金额")
    private BigDecimal todayCost;
}

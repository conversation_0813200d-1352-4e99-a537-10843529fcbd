package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-12-6
 */
@Getter
@Setter
@ApiModel("物流货运查询")
public class LogisticsFreightSearchVo extends PageRequest {

    /**
     * 寄件客户名
     */
    @ApiModelProperty("寄件客户名")
    private String senderCustomerName;

    /**
     * 收货客户名
     */
    @ApiModelProperty("收货客户名")
    private String receivingCustomerName;

    /**
     * 收货开始日期
     */
    @ApiModelProperty("收货开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate receivingStartDate;

    /**
     * 收货日期
     */
    @ApiModelProperty("收货开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate receivingEndDate;

    /**
     * 收货单号
     */
    @ApiModelProperty("收货单号")
    private String receivingNo;

    /**
     * 中转物流公司
     */
    @ApiModelProperty("中转物流公司")
    private String transferLogisticsCompany;

    /**
     * 寄件结算类型
     */
    @ApiModelProperty("寄件结算类型")
    private Integer senderSettlementType;

    /**
     * 账单状态
     */
    @ApiModelProperty("账单状态：0初始化 1已支付")
    private Integer status;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private Integer payWay;

    /**
     * 支付时间起
     */
    @ApiModelProperty("支付时间起")
    private LocalDateTime paidTimeStart;

    /**
     * 支付时间止
     */
    @ApiModelProperty("支付时间止")
    private LocalDateTime paidTimeEnd;
}

package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.LogisticLoaderFreightType;
import com.senox.tms.constant.LogisticLoaderGoodsType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@ApiModel("搬运工结算查询")
public class LogisticLoaderSettlementSearchVo extends PageRequest {

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 客户姓名
     */
    @ApiModelProperty("客户姓名")
    private String customerName;

    /**
     * 装卸类型
     */
    @ApiModelProperty("装卸类型")
    private LogisticLoaderFreightType freightType;

    /**
     * 商品类型
     */
    @ApiModelProperty("商品类型")
    private LogisticLoaderGoodsType goodsType;

    /**
     * 车牌
     */
    @ApiModelProperty("车牌")
    private String carNo;
}

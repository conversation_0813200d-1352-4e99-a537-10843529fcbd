package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/30 15:08
 */
@ApiModel("鹏翔分佣查询参数")
@Getter
@Setter
public class UnloadingSharesSearchVo extends PageRequest {
    private static final long serialVersionUID = -3881135522388156461L;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;
    /**
     * 生效时间启
     */
    @ApiModelProperty("生效时间启")
    private LocalDateTime effectiveTimeStart;
    /**
     * 生效时间止
     */
    @ApiModelProperty("生效时间止")
    private LocalDateTime effectiveTimeEnd;
    /**
     * 失效时间启
     */
    @ApiModelProperty("失效时间启")
    private LocalDateTime ineffectiveTimeStart;
    /**
     * 失效时间止
     */
    @ApiModelProperty("失效时间止")
    private LocalDateTime ineffectiveTimeEnd;
    /**
     * 状态 0未生效 1已生效 2已失效
     */
    @ApiModelProperty("状态 0未生效 1已生效 2已失效")
    private Integer status;
}

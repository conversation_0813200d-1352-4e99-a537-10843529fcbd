package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4 10:53
 */
@ApiModel("三轮车提货信息查询参数")
@Getter
@Setter
public class BicycleDeliveryInfoSearchVo extends PageRequest {
    private static final long serialVersionUID = -1784868910399843593L;

    /**
     * 起点id
     */
    @ApiModelProperty("起点id")
    private Long startPointId;

    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    @ApiModelProperty("状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    private List<Integer> statusList;
}

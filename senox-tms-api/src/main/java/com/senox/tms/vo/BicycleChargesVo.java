package com.senox.tms.vo;


import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.tms.constant.BicycleStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 收费标准视图
 *
 * <AUTHOR>
 * @date 2023-9-12
 */
@ApiModel("收费标准")
@Data
public class BicycleChargesVo implements Serializable {

    private static final long serialVersionUID = -1481502317811641638L;

    /**
     * id
     */
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 1, message = "无效的id", groups = Update.class)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "无效的名称", groups = Add.class)
    @ApiModelProperty("名称")
    private String name;

    /**
     * 生效时间
     */
    @NotNull(message = "生效时间不能为空", groups = Add.class)
    @ApiModelProperty("生效时间")
    private LocalDateTime effectiveTime;

    /**
     * 失效时间
     */
    @NotNull(message = "失效时间不能为空", groups = Add.class)
    @ApiModelProperty("失效时间")
    private LocalDateTime ineffectiveTime;

    /**
     * 状态
     *
     * @see BicycleStatus
     */
    @ApiModelProperty("状态")
    private BicycleStatus status;

    /**
     * 默认生效
     */
    @ApiModelProperty("默认生效")
    private Boolean defaultEffective;

    /**
     * 灵活计算
     */
    @ApiModelProperty("灵活计算")
    private Boolean flexible;

    /**
     * 低消
     */
    @ApiModelProperty("低消")
    private BigDecimal minAmount;

    /**
     * 明细列表
     */
    private List<BicycleChargesDetailVo> chargesDetails;
}

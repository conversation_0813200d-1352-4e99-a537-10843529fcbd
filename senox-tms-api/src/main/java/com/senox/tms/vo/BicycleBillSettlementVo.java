package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-11-10
 */
@Getter
@Setter
@ApiModel("三轮车结算单")
public class BicycleBillSettlementVo {

    /**
     * id
     */
    private Long id;

    /**
     * 账单年月
     */
    private String billYearMonth;

    /**
     * 年
     */
    @ApiModelProperty("年")
    private Integer billYear;

    /**
     * 月
     */
    @ApiModelProperty("月")
    private Integer billMonth;

    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;

    /**
     * 商户名
     */
    @ApiModelProperty("商户名")
    private String merchantName;

    /**
     * 冷藏客户编号
     */
    @ApiModelProperty("冷藏客户编号")
    private String rcSerial;

    /**
     * 结算周期
     */
    @ApiModelProperty("结算周期")
    private Integer settlePeriod;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private Integer payWay;

    /**
     * 支付金额
     */
    @ApiModelProperty("支付金额")
    private BigDecimal paidAmount;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 支付订单id
     */
    @ApiModelProperty("支付订单id")
    private Long remoteOrderId;

    /**
     * 下发状态
     */
    @ApiModelProperty("下发状态")
    private Boolean send;

    /**
     * 下发时间
     */
    @ApiModelProperty("下发时间")
    private LocalDateTime sendTime;

    /**
     * 收费员
     */
    @ApiModelProperty("收费员")
    private String tollManName;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Boolean status;

    /**
     * 用户
     */
    @ApiModelProperty("用户")
    private String openid;
}

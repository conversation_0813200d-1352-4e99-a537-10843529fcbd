package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19 10:25
 */
@ApiModel("搬运工订单搬运工派单")
@Data
public class UnloadingOrderWorkersAssignVo implements Serializable {
    private static final long serialVersionUID = -8229870506431792198L;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("分派的订单搬运工")
    private List<UnloadingOrderWorkersVo> assignList;

    @ApiModelProperty("忙的订单搬运工")
    private List<UnloadingOrderWorkersVo> busyList;

    @ApiModelProperty("惩罚的订单搬运工")
    private List<UnloadingOrderWorkersVo> punishList;

    @ApiModelProperty("请假的订单搬运工")
    private List<UnloadingOrderWorkersVo> leaveList;
}

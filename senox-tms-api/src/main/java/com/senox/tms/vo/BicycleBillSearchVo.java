package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22 10:25
 */
@Getter
@Setter
@ApiModel("三轮车配送应收账单查询参数")
public class BicycleBillSearchVo extends PageRequest {
    private static final long serialVersionUID = -3261382246448776348L;

    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    /**
     * 订单流水号
     */
    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;

    /**
     * 商户名
     */
    @ApiModelProperty("商户名")
    private String merchantName;

    /**
     *  结算单id集
     */
    @ApiModelProperty("结算单id集")
    private List<Long> settlementIds;

    /**
     * 包含结算单
     */
    @ApiModelProperty("包含结算单")
    private Boolean containSettlement;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private LocalDate endDate;

    /**
     * 状态集
     * @see BicycleDeliveryOrderStatus
     */
    @ApiModelProperty("状态集")
    private Collection<Integer> statusSet;

    /**
     * 账单id集
     */
    @ApiModelProperty("账单id集")
    private Collection<Long> billIds;

    /**
     * 结算周期
     */
    @ApiModelProperty("结算周期")
    private Integer settlePeriod;
}

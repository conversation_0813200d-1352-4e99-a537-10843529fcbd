package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/8 9:36
 */
@ApiModel("三轮车订单预估金额")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BicycleOrderCalculateEstimateVo implements Serializable {
    private static final long serialVersionUID = -2944471209394137430L;

    @ApiModelProperty("车辆数")
    private int carNum;

    @ApiModelProperty("总金额")
    private BigDecimal totalCharge;
}

package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 应付账单报表查询
 *
 * <AUTHOR>
 * @date 2023-10-13
 */
@Getter
@Setter
@ApiModel("应付账单报表")
public class BicyclePayoffReportSearchVo extends PageRequest {

    private static final long serialVersionUID = 4695237694560919897L;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private String startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private String endTime;

    /**
     * 收款人
     */
    @ApiModelProperty("收款人")
    private String payeeName;
}

package com.senox.tms.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/4/28 15:17
 */
@Getter
@Setter
@ApiModel("订单下单结果")
public class OrderResultVo implements Serializable {

    private static final long serialVersionUID = -8160570716535216184L;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("订单号")
    private String tradeNo;

    @ApiModelProperty("随机字符串")
    private String nonceStr;

    @ApiModelProperty("微信签名时间戳")
    private String timeStamp;

    @ApiModelProperty("微信签名")
    private String sign;

    @ApiModelProperty("微信签名方式")
    private String signType;

    @ApiModelProperty("JSAPI支付下单返回package")
    @JsonProperty("package")
    private String packageResp;

    @ApiModelProperty("二维码支付url")
    private String wxCodeUrl;

    @ApiModelProperty("支付地址")
    private String payUrl;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    @ApiModelProperty("订单状态")
    private Integer status;

    @ApiModelProperty("订单时间")
    private LocalDateTime orderTime;

    @ApiModelProperty("订单页面")
    private String orderHtml;

    public OrderResultVo() {

    }

    public OrderResultVo(Long orderId, String tradeNo) {
        this.orderId = orderId;
        this.tradeNo = tradeNo;
    }
}

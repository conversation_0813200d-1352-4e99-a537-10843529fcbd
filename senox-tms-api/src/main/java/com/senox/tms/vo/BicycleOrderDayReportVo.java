package com.senox.tms.vo;

import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/10/16 9:32
 */
@Data
@ToString
@ApiModel("三轮车日账单")
public class BicycleOrderDayReportVo implements Serializable {
    private static final long serialVersionUID = -2083494196634938212L;

    @ApiModelProperty("id")
    @NotNull(message = "无效的id", groups = Update.class)
    private Long id;

    @ApiModelProperty("报表日期")
    private LocalDate reportDate;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("商户名")
    private String merchantName;

    @ApiModelProperty("总件数")
    private BigDecimal totalPieces;

    @ApiModelProperty("总单数")
    private Integer totalCount;

    @ApiModelProperty("配送费用")
    private BigDecimal deliveryCharge;

    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    @ApiModelProperty("装卸费")
    private BigDecimal handlingCharge;

    @ApiModelProperty("上楼费")
    private BigDecimal upstairsCharge;

    @ApiModelProperty("合计")
    private BigDecimal totalCharge;

}

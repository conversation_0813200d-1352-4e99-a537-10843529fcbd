package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/10 8:18
 */
@ApiModel("货物统计批量收款参数")
@Data
public class LogisticStatisticsDayReportBatchUpdateVo implements Serializable {
    private static final long serialVersionUID = -569759344074242505L;

    @ApiModelProperty("id集合")
    private List<Long> ids;

    @ApiModelProperty("收款时间")
    private LocalDateTime paymentTime;
}

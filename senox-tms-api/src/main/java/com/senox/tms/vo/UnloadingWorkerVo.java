package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:07
 */
@ApiModel("装卸搬运工")
@Data
public class UnloadingWorkerVo implements Serializable {
    private static final long serialVersionUID = 9029474429293145377L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 搬运工状态
     */
    @ApiModelProperty("状态 0 下班; 1 已挂牌; 2 搬运中 3 请假")
    private Integer status;

    /**
     * 班次
     */
    @ApiModelProperty("班次 0 早班； 1 晚班")
    private Integer classes;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 搬运工标识
     */
    @ApiModelProperty("搬运工标识")
    private String workerSign;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 出生日期
     */
    @ApiModelProperty("出生日期")
    private LocalDate bornDate;

    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    private String contact;

    /**
     * 人脸
     */
    @ApiModelProperty("人脸")
    private String faceUrl;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer orderNum;

    /**
     * 轮次
     */
    @ApiModelProperty("轮次")
    private Integer roundNum;

    /**
     * 惩罚轮次
     */
    @ApiModelProperty("惩罚轮次")
    private Integer punishRoundNum;

    /**
     * 序号
     */
    @ApiModelProperty("序号")
    private int rowNum;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}

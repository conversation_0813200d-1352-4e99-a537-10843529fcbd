package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/27 14:49
 */
@ApiModel("管理员未分配提醒消息")
@Data
public class BicycleManagerUnAssignMessageVo implements Serializable {
    private static final long serialVersionUID = 6098177724669871191L;

    /**
     * 管理员id
     */
    @ApiModelProperty("管理员id")
    private Long adminUserId;
    /**
     * 未分配数量
     */
    @ApiModelProperty("未分配数量")
    private Integer unAssignCount;
    /**
     * 通知时间
     */
    @ApiModelProperty("通知时间")
    private LocalDateTime noticeTime;
}

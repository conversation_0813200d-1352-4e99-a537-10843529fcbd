package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/28 15:26
 */
@ApiModel("下单金额排行榜")
@Data
public class UnloadingAmountCountRankingVo implements Serializable {
    private static final long serialVersionUID = 1246178722261115337L;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String customerName;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal totalAmount;
}

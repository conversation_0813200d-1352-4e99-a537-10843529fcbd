package com.senox.tms.vo;

import com.senox.common.vo.PageResult;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @param <T>
 * <AUTHOR>
 * @date 2023-10-17
 */
@Getter
@Setter
public class BicycleTotalPageResult<T> extends PageResult<T> {

    public BicycleTotalPageResult() {
    }

    public BicycleTotalPageResult(PageResult<T> result) {
        setData(result);
    }

    public BicycleTotalPageResult(PageResult<T> result, BigDecimal totalAmount) {
        this(result);
        this.totalAmount = totalAmount;
    }

    public BicycleTotalPageResult(PageResult<T> result, BigDecimal totalPieces, BigDecimal totalReceivableFreightCharge, BigDecimal totalReceivedFreightCharge) {
        this(result);
        this.totalPieces = totalPieces;
        this.totalReceivableFreightCharge = totalReceivableFreightCharge;
        this.totalReceivedFreightCharge = totalReceivedFreightCharge;
    }

    public static <T> BicycleTotalPageResult<T> emptyPage() {
        return new BicycleTotalPageResult<>();
    }

    public void setData(PageResult<T> result) {
        setPageNo(result.getPageNo());
        setPageSize(result.getPageSize());
        setTotalSize(result.getTotalSize());
        setTotalPages(result.getTotalPages());
        setDataList(result.getDataList());
    }

    public static <E, T> BicycleTotalPageResult<T> convertPage(BicycleTotalPageResult<E> page1, Function<E, T> convertor) {
        BicycleTotalPageResult<T> result = new BicycleTotalPageResult<>();
        result.setPageNo(page1.getPageNo());
        result.setPageSize(page1.getPageSize());
        result.setTotalSize(page1.getTotalSize());
        result.setTotalPages(page1.getTotalPages());
        result.setTotalAmount(page1.getTotalAmount());
        if (!CollectionUtils.isEmpty(page1.getDataList())) {
            result.setDataList(page1.getDataList().stream().map(convertor).collect(Collectors.toList()));
        }
        return result;
    }

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 总件数
     */
    private BigDecimal totalPieces;

    /**
     * 总应收运费
     */
    private BigDecimal totalReceivableFreightCharge;

    /**
     * 总实收运费
     */
    private BigDecimal totalReceivedFreightCharge;
}

package com.senox.tms.vo;

import com.senox.tms.constant.LogisticLoaderFreightType;
import com.senox.tms.constant.LogisticLoaderGoodsType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@ApiModel("搬运工结算")
public class LogisticLoaderSettlementVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private LocalDate date;

    /**
     * 装卸类型
     */
    @ApiModelProperty("装卸类型")
    private LogisticLoaderFreightType freightType;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private Long customerId;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String customerName;

    /**
     * 商品类型
     */
    @ApiModelProperty("商品类型")
    private LogisticLoaderGoodsType goodsType;

    /**
     * 商品单价
     */
    @ApiModelProperty("商品单价")
    private BigDecimal goodsUnitPrice;

    /**
     * 平均搬运量
     */
    @ApiModelProperty("平均搬运量")
    private BigDecimal transportAvg;

    /**
     * 总搬运量
     */
    @ApiModelProperty("总搬运量")
    private BigDecimal transportTotal;

    /**
     * 参与人数
     */
    @ApiModelProperty("参与人数")
    private Integer participationNumber;

    /**
     * 装卸单价
     */
    @ApiModelProperty("装卸单价")
    private BigDecimal freightUnitPrice;

    /**
     * 装卸总金额
     */
    @ApiModelProperty("装卸总金额")
    private BigDecimal freightTotalAmount;

    /**
     * 分拣费
     */
    @ApiModelProperty("分拣费")
    private BigDecimal sortingFee;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal pieces;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号")
    private String carNo;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 工时
     */
    @ApiModelProperty("工时")
    private BigDecimal workingHours;

    /**
     * 餐补
     */
    @ApiModelProperty("餐补")
    private BigDecimal mealAllowanceAmount;

    /**
     * 其他费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 小计
     */
    @ApiModelProperty("小计")
    private BigDecimal subtotalAmount;

    /**
     * 合计
     */
    @ApiModelProperty("合计")
    private BigDecimal totalAmount;

    /**
     * 搬运工列表
     */
    @ApiModelProperty("搬运工列表")
    public List<DictLogisticVo> loaders;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}

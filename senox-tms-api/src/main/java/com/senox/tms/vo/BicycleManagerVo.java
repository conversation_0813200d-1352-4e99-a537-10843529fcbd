package com.senox.tms.vo;

import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/27 10:23
 */
@Data
@ApiModel("三轮车管理员信息")
public class BicycleManagerVo implements Serializable {
    private static final long serialVersionUID = -6655569159512767407L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 管理员id
     */
    @ApiModelProperty("管理员id")
    @NotNull(message = "无效的管理员id", groups = Update.class)
    private Long adminUserId;

    /**
     * 状态 0 离线; 1 在线;
     */
    @ApiModelProperty("状态 0 离线; 1 在线;")
    @NotNull(message = "无效的状态", groups = Update.class)
    private Integer status;
}

package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.LogisticTransportCategory;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-23
 **/
@Getter
@Setter
@ApiModel("物流运输账单查询参数")
public class LogisticTransportBillSearchVo extends PageRequest {

    /**
     * id集
     */
    @ApiModelProperty("id集")
    private List<Long> ids;

    /**
     * 账单开始日期
     */
    @ApiModelProperty("账单开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate billDateStart;

    /**
     * 账单结束日期
     */
    @ApiModelProperty("账单结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate billDateEnd;

    /**
     * 支付时间开始日期
     */
    @ApiModelProperty("支付时间开始日期")
    private LocalDateTime paidDateStart;

    /**
     * 支付时间结束日期
     */
    @ApiModelProperty("支付时间结束日期")
    private LocalDateTime paidDateEnd;


    /**
     * 订单编号集
     */
    @ApiModelProperty("订单编号集")
    private List<String> orderSerialNos;

    /**
     * 商户id集
     */
    @ApiModelProperty("商户id集")
    private List<Long> merchantIds;

    /**
     * 类别
     *
     * @see LogisticTransportCategory
     */
    @ApiModelProperty("类别")
    private LogisticTransportCategory category;

    /**
     * 付款人
     */
    @ApiModelProperty("付款人")
    private LogisticTransportOrderPayer payer;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keywords;

    /**
     * 结算单id
     */
    @ApiModelProperty("结算单id")
    private Long settlementId;

    /**
     * 支付状态
     */
    @ApiModelProperty("支付状态")
    private Boolean status;

    /**
     * 收费员id集
     */
    @ApiModelProperty("收费员id集")
    private List<Long> tollManIds;

    /**
     * 支付方式集
     */
    @ApiModelProperty("支付方式集")
    private List<Integer> paWays;

}

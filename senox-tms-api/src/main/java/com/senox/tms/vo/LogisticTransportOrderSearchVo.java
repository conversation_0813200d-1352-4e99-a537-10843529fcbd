package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.LogisticTransportCategory;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-20
 **/
@Getter
@Setter
@ApiModel("物流运输订单查询")
public class LogisticTransportOrderSearchVo extends PageRequest {

    /**
     * id集
     */
    @ApiModelProperty("id集")
    private List<Long> ids;

    /**
     * 编号集
     */
    @ApiModelProperty("编号集")
    private List<String> serialNos;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    /**
     * 发货人姓名
     */
    @ApiModelProperty("发货人姓名")
    private String consignorName;

    /**
     * 发货人编号
     */
    @ApiModelProperty("发货人编号")
    private String consignorCode;

    /**
     * 发货人编号集
     */
    @ApiModelProperty("发货人编号集")
    private List<String> consignorCodes;

    /**
     * 存在帐单
     */
    @ApiModelProperty("存在账单")
    private Boolean hasBill;

    /**
     * 类别
     *
     * @see LogisticTransportCategory
     */
    @ApiModelProperty("类别")
    private LogisticTransportCategory category;

    /**
     * 始发站
     */
    @ApiModelProperty("始发站")
    private String departureStation;

    /**
     * 目的站
     */
    @ApiModelProperty("目的站")
    private String destinationStation;

    /**
     * 司机姓名
     */
    @ApiModelProperty("司机姓名")
    private String driverName;

    /**
     * 司机手机
     */
    @ApiModelProperty("司机手机")
    private String driverPhone;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号")
    private String licensePlateNumber;

    /**
     * 车牌号集
     */
    @ApiModelProperty("车牌号集")
    private List<String> licensePlateNumbers;

    /**
     * 是否包车
     */
    @ApiModelProperty("是否包车")
    private Boolean charter;

    /**
     * 付款人
     */
    @ApiModelProperty("付款人")
    private LogisticTransportOrderPayer payer;

    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private Boolean auditStatus;

    /**
     * 支付状态
     */
    @ApiModelProperty("支付状态")
    private Boolean paidStatus;

    /**
     * 应收运费集
     */
    @ApiModelProperty("应收运费集")
    private List<BigDecimal> freightCharges;
}

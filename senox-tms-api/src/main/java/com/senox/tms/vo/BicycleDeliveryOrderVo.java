package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20 9:02
 */
@Data
@ApiModel("三轮车配送单")
public class BicycleDeliveryOrderVo implements Serializable {
    private static final long serialVersionUID = 1918079476963393747L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "无效的id", groups = Update.class)
    private Long id;
    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;
    /**
     * 是否合并订单
     */
    @ApiModelProperty("是否合并订单")
    private Boolean merged;

    /**
     * 推荐分配
     */
    @ApiModelProperty("推荐分配")
    private Boolean referralDelivery;
    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    @NotNull(message = "无效的骑手id", groups = Update.class)
    private Long riderId;
    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    @ApiModelProperty("状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    private Integer status;
    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    private LocalDateTime finishTime;

    /**
     * 配送单明细，后续会改为集合
     */
    @ApiModelProperty("配送单明细")
    @NotNull(message = "无效的配送单明细", groups = Add.class)
    private List<@Valid BicycleDeliveryOrderDetailVo> detailVos;
}

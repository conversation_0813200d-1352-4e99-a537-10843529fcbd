package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.BicycleStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-9-15
 */
@Getter
@Setter
@ApiModel("分佣查询")
public class BicycleSharesSearchVo extends PageRequest {

    private static final long serialVersionUID = 4471988339891982886L;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;
    /**
     * 生效时间
     */
    @ApiModelProperty("生效时间")
    private LocalDateTime effectiveStartTime;

    /**
     * 失效时间
     */
    @ApiModelProperty("失效时间")
    private LocalDateTime effectiveEndTime;

    /**
     * 状态
     *
     * @see BicycleStatus
     */
    @ApiModelProperty("状态")
    private BicycleStatus status;

}

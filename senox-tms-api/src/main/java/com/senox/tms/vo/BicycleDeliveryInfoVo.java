package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/4 11:01
 */
@ApiModel("三轮车提货信息")
@Data
public class BicycleDeliveryInfoVo implements Serializable {
    private static final long serialVersionUID = 947735269238960024L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("寄件人")
    private String sender;

    @ApiModelProperty("寄件人编号")
    private String senderSerialNo;

    @ApiModelProperty("配送单号")
    private String deliveryOrderSerial;

    @ApiModelProperty("起点")
    private String startPointName;

    @ApiModelProperty("终点")
    private String endPointName;

    @ApiModelProperty("件数")
    private BigDecimal pieces;

    @ApiModelProperty("总费用")
    private BigDecimal totalCharge;

    @ApiModelProperty("状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成")
    private Integer status;

    @ApiModelProperty("到达揽货点时间")
    private LocalDateTime deliveryTime;

    @ApiModelProperty("商品数量")
    private Integer goodsCount;

    @ApiModelProperty("到达揽货点时间")
    private LocalDateTime arrivingPickupTime;

    @ApiModelProperty("用时：单位分钟")
    private Integer useTime;

    @ApiModelProperty("提货信息详细")
    private List<BicycleDeliveryInfoDetailVo> infoDetailVoList;
}

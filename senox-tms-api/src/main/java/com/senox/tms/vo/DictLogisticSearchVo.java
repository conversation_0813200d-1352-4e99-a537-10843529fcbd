package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.DictLogisticCategory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@ApiModel("物流字典查询")
public class DictLogisticSearchVo extends PageRequest {

    /**
     * id集
     */
    private Collection<Long> ids;

    /**
     * key集
     */
    @ApiModelProperty("keys")
    private Collection<String> keys;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * attr1
     */
    @ApiModelProperty("attr1")
    private String attr1;

    /**
     * attr2
     */
    @ApiModelProperty("attr2")
    private String attr2;

    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private DictLogisticCategory category;
}

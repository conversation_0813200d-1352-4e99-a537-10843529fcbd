package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/9/12 14:31
 */
@ApiModel("装卸字典查询参数")
@Getter
@Setter
public class UnloadingDictSearchVo extends PageRequest {
    private static final long serialVersionUID = -3888246811376465223L;

    /**
     * 字典类型
     */
    @ApiModelProperty("字典类型 0冻品，1干货，2车型，3其他，4客户")
    private Integer category;

    /**
     * 字典名称
     */
    @ApiModelProperty("字典名称")
    private String name;
}

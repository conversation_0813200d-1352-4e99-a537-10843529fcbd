package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/28 9:15
 */
@ApiModel("当天最佳搬运工")
@Data
public class UnloadingDayBestWorkerVo implements Serializable {
    private static final long serialVersionUID = 1869413496935288098L;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String workerName;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /**
     * 配送单数
     */
    @ApiModelProperty("搬运单数")
    private Integer orderCount;
}

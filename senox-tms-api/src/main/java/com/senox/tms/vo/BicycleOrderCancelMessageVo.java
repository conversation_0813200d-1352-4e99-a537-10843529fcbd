package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/4 10:09
 */
@ApiModel("三轮车订单取消消息")
@Getter
@Setter
public class BicycleOrderCancelMessageVo implements Serializable {
    private static final long serialVersionUID = 7950940547454243641L;

    @ApiModelProperty("openid")
    private String openid;

    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    @ApiModelProperty("取消原因")
    private String cancelReason;
}

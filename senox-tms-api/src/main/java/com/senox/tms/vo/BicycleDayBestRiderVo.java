package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/26 10:35
 */
@ApiModel("当天最佳骑手")
@Data
public class BicycleDayBestRiderVo implements Serializable {
    private static final long serialVersionUID = 9094842160376407279L;

    /**
     * 骑手名
     */
    @ApiModelProperty("骑手名")
    private String riderName;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private Integer pieces;

    /**
     * 配送单数
     */
    private Integer orderCount;
}

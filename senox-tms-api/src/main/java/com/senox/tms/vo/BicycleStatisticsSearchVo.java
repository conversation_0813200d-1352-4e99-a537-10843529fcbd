package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/4/22 15:40
 */
@ApiModel("三轮车大屏统计查询参数")
@Getter
@Setter
public class BicycleStatisticsSearchVo implements Serializable {
    private static final long serialVersionUID = 1759948573039835465L;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("搜索单位:0 日 1月 2年")
    private Integer searchUnit;

    @ApiModelProperty(value = "搜索类型", hidden = true)
    private String searchType;

    @ApiModelProperty(value = "搜索类型格式", hidden = true)
    private String searchTypeFormat;

    @ApiModelProperty(value = "搜索结果集格式", hidden = true)
    private String searchResultFormat;
}

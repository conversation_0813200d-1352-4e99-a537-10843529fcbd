package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/10/13 10:59
 */
@ApiModel("三轮车骑手单数统计查询参数")
@Getter
@Setter
public class BicycleRiderCountSearchVo extends PageRequest {
    private static final long serialVersionUID = -1279588957856031072L;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String riderName;

    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;

    @ApiModelProperty("日期起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate billDateStart;

    @ApiModelProperty("日期止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate billDateEnd;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/3/10 11:00
 */
@ApiModel("鹏翔搬运工排期计划")
@Data
public class UnloadingNightScheduleVo implements Serializable {
    private static final long serialVersionUID = -5723460788743126444L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 排期日期
     */
    @ApiModelProperty("排期日期")
    private LocalDate scheduleDate;

    /**
     * 搬运工id
     */
    @ApiModelProperty("搬运工id")
    private Long workerId;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String workerName;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/28 15:15
 */
@ApiModel("下单数量排行榜")
@Data
public class UnloadingOrderCountRankingVo implements Serializable {
    private static final long serialVersionUID = 107997077357107947L;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String customerName;

    /**
     * 下单数量
     */
    @ApiModelProperty("下单数量")
    private Integer totalCount;
}

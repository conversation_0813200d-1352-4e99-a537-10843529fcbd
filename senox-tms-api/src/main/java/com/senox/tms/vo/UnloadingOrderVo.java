package com.senox.tms.vo;

import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/14 11:26
 */
@ApiModel("装卸订单")
@Data
public class UnloadingOrderVo implements Serializable {

    private static final long serialVersionUID = 5393540298808207436L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("车牌")
    private String licensePlate;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态 0：草稿 1：正常")
    private Integer state;

    @ApiModelProperty("搬运状态 0：初始化 1：搬运中 2：已完成")
    private Integer workerStatus;

    @ApiModelProperty("是否拼车")
    private Boolean carpool;

    @ApiModelProperty("车型")
    private Integer carCategory;

    @ApiModelProperty("车型名")
    private String carCategoryName;

    @ApiModelProperty("车辆数")
    private Integer carNum;

    @ApiModelProperty("搬运工人数")
    private Integer workerNum;

    /**
     * 是否预约单
     */
    @ApiModelProperty("是否预约单")
    private Boolean reservationOrder;

    /**
     * 预约时间
     */
    @ApiModelProperty("预约时间")
    private LocalDateTime reservationTime;

    @ApiModelProperty("下单时间")
    private LocalDateTime orderTime;

    @ApiModelProperty("搬运时间")
    private LocalDateTime workTime;

    @ApiModelProperty("完成时间")
    private LocalDateTime finishTime;

    /**
     * 加急单
     */
    @ApiModelProperty("加急单")
    private Boolean urgentOrder;

    /**
     * 加急金额
     */
    @ApiModelProperty("加急金额")
    private BigDecimal urgentAmount;

    @ApiModelProperty("金额")
    private BigDecimal amount;

    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("收益金额")
    private BigDecimal shareAmount;

    @ApiModelProperty("应付金额")
    private BigDecimal payoffAmount;

    @ApiModelProperty("补录")
    private Boolean supplement;

    @ApiModelProperty(value = "openid", hidden = true)
    private String openid;

    /**
     * 应收状态
     * @see UnloadingOrderBillPayoffStatus
     */
    @ApiModelProperty("应收状态 0初始化 1已支付")
    private Integer billStatus;

    /**
     * 收费人
     */
    @ApiModelProperty("收费人")
    private String tollMan;

    /**
     * 应收支付时间
     */
    @ApiModelProperty("应收支付时间")
    private LocalDateTime billPaidTime;

    /**
     * 应付状态
     * @see UnloadingOrderBillPayoffStatus
     */
    @ApiModelProperty("应付状态 0初始化 1已支付")
    private Integer payoffStatus;

    @ApiModelProperty("货物明细")
    private List<UnloadingOrderGoodsVo> goodsVoList;

    @ApiModelProperty("搬运工信息")
    private List<UnloadingOrderWorkersVo> workersVoList;
}

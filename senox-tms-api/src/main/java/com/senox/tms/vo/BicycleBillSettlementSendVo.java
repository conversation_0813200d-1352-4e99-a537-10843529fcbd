package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023-11-17
 */
@Getter
@Setter
@ApiModel("结算单下发")
public class BicycleBillSettlementSendVo implements Serializable {

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private String endDate;

    /**
     * 结算单id集
     */
    @ApiModelProperty("结算单id集")
    private Collection<Long> settlementIds;

    /**
     * 商户id集
     */
    @ApiModelProperty("商户id集")
    private Collection<Long> merchantIds;

    /**
     * 账单id集
     */
    @ApiModelProperty("账单id集")
    private Collection<Long> billIds;
}

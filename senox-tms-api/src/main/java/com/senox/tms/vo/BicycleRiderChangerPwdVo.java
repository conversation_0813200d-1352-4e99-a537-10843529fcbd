package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/14 10:11
 */
@ApiModel("三轮车配送骑手密码修改")
@Data
public class BicycleRiderChangerPwdVo implements Serializable {
    private static final long serialVersionUID = 3556580766843557363L;

    @ApiModelProperty("手机号")
    @NotBlank(message = "手机号不能为空")
    private String contact;

    @ApiModelProperty("旧密码")
    @NotBlank(message = "旧密码不能为空")
    private String oldPassword;

    @ApiModelProperty("新密码")
    @NotBlank(message = "新密码不能为空")
    private String newPassword;
}

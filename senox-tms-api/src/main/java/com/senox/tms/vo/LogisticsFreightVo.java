package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-12-6
 */
@Getter
@Setter
@ApiModel("物流货运")
public class LogisticsFreightVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 运营部门
     */
    @ApiModelProperty("运营部门")
    private String operationsDepartment;

    /**
     * 寄件客户名
     */
    @ApiModelProperty("寄件客户名")
    private String senderCustomerName;

    /**
     * 寄件客户联系方式
     */
    @ApiModelProperty("寄件客户联系方式")
    private String senderCustomerContact;

    /**
     * 寄件数
     */
    @ApiModelProperty("寄件数")
    private Integer senderPieces;

    /**
     * 寄件运费
     */
    @ApiModelProperty("寄件运费")
    private BigDecimal senderFreightCharge;

    /**
     * 寄件结算类型
     */
    @ApiModelProperty("寄件结算类型")
    private Integer senderSettlementType;

    /**
     * 收货日期
     */
    @ApiModelProperty("收货日期")
    private LocalDate receivingDate;

    /**
     * 收货单号
     */
    @ApiModelProperty("收货单号")
    private String receivingNo;

    /**
     * 收货客户名
     */
    @ApiModelProperty("收货客户名")
    private String receivingCustomerName;

    /**
     * 收货客户地址
     */
    @ApiModelProperty("收货客户地址")
    private String receivingCustomerAddress;

    /**
     * 收货客户联系方式
     */
    @ApiModelProperty("收货客户联系方式")
    private String receivingCustomerContact;

    /**
     * 中转物流公司
     */
    @ApiModelProperty("中转物流公司")
    private String transferLogisticsCompany;

    /**
     * 中转物流单号
     */
    @ApiModelProperty("中转物流单号")
    private String transferLogisticsNo;

    /**
     * 中转运费
     */
    @ApiModelProperty("中转运费")
    private BigDecimal transferCharge;

    /**
     * 利润
     */
    @ApiModelProperty("利润")
    private BigDecimal profitAmount;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 录入员
     */
    @ApiModelProperty("录入员")
    private String creatorName;

    /**
     * 录入时间
     */
    @ApiModelProperty("录入时间")
    private LocalDateTime createTime;


    /**
     * 支付金额
     */
    @ApiModelProperty("支付金额")
    private BigDecimal paidAmount;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 账单状态：0初始化 1已支付
     */
    @ApiModelProperty("账单状态：0初始化 1已支付")
    private Integer status;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private Integer payWay;

    /**
     * 支付备注
     */
    @ApiModelProperty("支付备注")
    private String paidRemark;

    /**
     * 收费员
     */
    @ApiModelProperty("收费员")
    private String tollMan;
}

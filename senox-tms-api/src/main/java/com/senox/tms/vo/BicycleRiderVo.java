package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.tms.constant.BicycleRiderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/13 15:56
 */
@Data
@ApiModel("三轮车配送骑手")
public class BicycleRiderVo implements Serializable {
    private static final long serialVersionUID = -1947689892747258750L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "无效的id", groups = Update.class)
    private Long id;
    /**
     * 骑手编号
     */
    @ApiModelProperty("骑手编号")
    private String riderNo;
    /**
     * 密码
     */
    @ApiModelProperty("密码")
    @NotBlank(message = "密码不能为空", groups = Add.class)
    private String password;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    @NotBlank(message = "无效的姓名", groups = Add.class)
    private String name;

    /**
     * 推荐码
     */
    @ApiModelProperty("推荐码")
    private String referralCode;
    /**
     * 联系方式
     */
    @ApiModelProperty("联系方式")
    @NotBlank(message = "无效的联系方式", groups = Add.class)
    private String contact;
    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatar;
    /**
     * 出生日期
     */
    @ApiModelProperty("出生日期")
    private LocalDate birthday;
    /**
     * 状态
     * @see BicycleRiderStatus
     */
    @ApiModelProperty("状态 0 离线; 1 在线; 2 离职")
    private Integer status;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;

    /**
     * 上一次修改密码时间
     */
    @ApiModelProperty("上一次修改密码时间")
    private LocalDateTime lastModifyPasswordTime;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-21
 */
@Getter
@Setter
@ApiModel("三轮车结算单详情")
public class BicycleBillSettlementDetailVo {

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal pieces;

    /**
     * 配送费
     */
    @ApiModelProperty("配送费")
    private BigDecimal deliveryCharge;

    /**
     * 其他费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 总费用
     */
    @ApiModelProperty("总费用")
    private BigDecimal totalCharge;

    /**
     * 装卸费
     */
    @ApiModelProperty("装卸费")
    private BigDecimal handlingCharge;

    /**
     * 上楼费
     */
    @ApiModelProperty("上楼费")
    private BigDecimal upstairsCharge;

    /**
     * 总金额
     */
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    /**
     * 总支付金额
     */
    @ApiModelProperty("总支付金额")
    private BigDecimal totalPaidAmount;

    /**
     * 账单列表
     */
    @ApiModelProperty("账单列表")
    List<BicycleBillVo> billList;
}

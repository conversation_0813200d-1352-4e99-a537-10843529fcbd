package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
@Getter
@Setter
@ApiModel("三轮车费用标准计划任务查询")
public class BicycleChargesScheduledTaskSearchVo extends PageRequest {

    /**
     * 任务次数
     */
    @ApiModelProperty("最大任务次数")
    private Integer maxTaskCount;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;


    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/9/26 9:11
 */
@Data
@ApiModel("三轮车骑手当日当月统计")
public class BicycleRiderCountVo implements Serializable {
    private static final long serialVersionUID = 6606531165177553884L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private LocalDate billDate;

    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;

    /**
     * 骑手姓名
     */
    @ApiModelProperty("骑手姓名")
    private String riderName;

    /**
     * 当天完成单量
     */
    @ApiModelProperty("当天完成单量")
    private Integer todayCount;

    /**
     * 当天完成详细单量
     */
    @ApiModelProperty("当天完成详细单量")
    private Integer todayDetailCount;

    /**
     * 当月完成单量
     */
    @ApiModelProperty("当月完成单量")
    private Integer monthCount;

    /**
     * 当天推荐费收益
     */
    @ApiModelProperty("当天推荐费数量")
    private Integer todayReferralCount;

    /**
     * 当天推荐费收益
     */
    @ApiModelProperty("当天推荐费收益")
    private BigDecimal todayReferralIncome;

    /**
     * 当天配送收益
     */
    @ApiModelProperty("当天配送收益")
    private BigDecimal todayDeliveryIncome;

    /**
     * 当天收益
     */
    @ApiModelProperty("当天收益")
    private BigDecimal todayIncome;

    /**
     * 当月收益
     */
    @ApiModelProperty("当月收益")
    private BigDecimal monthIncome;

    /**
     * 当天配送件数
     */
    @ApiModelProperty("当天配送件数")
    private BigDecimal todayPieces;

    /**
     * 当月配送件数
     */
    @ApiModelProperty("当月配送件数")
    private BigDecimal monthPieces;

    /**
     * 平均每单交货时间
     */
    @ApiModelProperty("平均每单交货时间")
    private int avgDeliveryTime;

    /**
     * 平均每单交货时间描述
     */
    @ApiModelProperty("平均每单交货时间描述")
    private String avgDeliveryTimeDescribe;


}

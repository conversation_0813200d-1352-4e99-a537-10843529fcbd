package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/27 14:44
 */
@ApiModel("下单数量排行榜")
@Data
public class BicycleOrderCountRankingVo implements Serializable {
    private static final long serialVersionUID = 6292504139291561594L;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String sender;

    /**
     * 下单数量
     */
    @ApiModelProperty("下单数量")
    private Integer totalCount;
}

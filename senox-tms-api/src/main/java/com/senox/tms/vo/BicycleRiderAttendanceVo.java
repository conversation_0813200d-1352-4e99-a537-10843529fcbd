package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/14 16:52
 */
@ApiModel("三轮车骑手考勤")
@Data
public class BicycleRiderAttendanceVo implements Serializable {
    private static final long serialVersionUID = 2925748534223685447L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;
    /**
     * 上线时间
     */
    @ApiModelProperty("上线时间")
    private LocalDateTime onlineTime;
    /**
     * 下线时间
     */
    @ApiModelProperty("下线时间")
    private LocalDateTime offlineTime;
    /**
     * 在线时长
     */
    @ApiModelProperty("在线时长")
    private Integer duration;
    /**
     * 完成单数
     */
    @ApiModelProperty("完成单数")
    private Integer completeCount;
    /**
     * 完成件数
     */
    @ApiModelProperty("完成件数")
    private BigDecimal completePieces;
    /**
     * 收益金额
     */
    @ApiModelProperty("收益金额")
    private BigDecimal shareAmount;
    /**
     * 骑手编号
     */
    @ApiModelProperty("骑手编号")
    private String riderNo;
    /**
     * 骑手名
     */
    @ApiModelProperty("骑手名")
    private String riderName;
}

package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-9-14
 */
@Getter
@Setter
@ApiModel("收费标准明细")
public class BicycleChargesDetailVo implements Serializable {

    private static final long serialVersionUID = -2429571860611371846L;

    /**
     * id
     */
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 1, message = "无效的id", groups = Update.class)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 收费标准id
     */
    @NotNull(message = "收费标准id不能为空", groups = Add.class)
    @Min(value = 1, message = "无效的收费标准id", groups = Add.class)
    @ApiModelProperty("收费标准id")
    private Long chargesId;

    /**
     * 货物类型 0其他，1重货，2抛货
     */
    @ApiModelProperty("货物类型 0其他，1重货，2抛货")
    private Integer goodsType;

    /**
     * 最少件数
     */
    @ApiModelProperty("最少件数")
    private Integer minCount;

    /**
     * 最多件数
     */
    @ApiModelProperty("最多件数")
    private Integer maxCount;

    /**
     * 最少重量
     */
    @ApiModelProperty("最少单位")
    private BigDecimal minUnit;

    /**
     * 最多重量
     */
    @ApiModelProperty("最多单位")
    private BigDecimal maxUnit;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 区间计算
     */
    @ApiModelProperty("区间计算")
    private Boolean calAnyway;

    /**
     * 默认标准
     */
    @ApiModelProperty("默认标准")
    private Boolean defaultCharges;

}

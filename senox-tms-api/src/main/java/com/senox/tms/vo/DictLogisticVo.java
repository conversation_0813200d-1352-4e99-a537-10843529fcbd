package com.senox.tms.vo;

import com.senox.tms.constant.DictLogisticCategory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@ApiModel("物流字典")
public class DictLogisticVo {

    /**
     * id
     */
    private Long id;

    /**
     * key
     */
    @ApiModelProperty("key")
    private String key;

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private DictLogisticCategory category;

    /**
     * 保留字段1
     */
    @ApiModelProperty("保留字段1")
    private String attr1;

    /**
     * 保留字段2
     */
    @ApiModelProperty("保留字段2")
    private String attr2;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

}

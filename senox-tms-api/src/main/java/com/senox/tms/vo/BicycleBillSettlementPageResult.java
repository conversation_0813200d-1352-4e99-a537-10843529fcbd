package com.senox.tms.vo;

import com.senox.common.vo.PageResult;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@ApiModel("三轮车结算单分页结果")
public class BicycleBillSettlementPageResult<T> extends PageResult<T> {
    private BigDecimal totalAmount;
    private BigDecimal totalPaidAmount;

    public BicycleBillSettlementPageResult(){

    }
    public BicycleBillSettlementPageResult(PageResult<T> result){
        if (null != result) {
            setPageNo(result.getPageNo());
            setPageSize(result.getPageSize());
            setTotalSize(result.getTotalSize());
            setTotalPages(result.getTotalPages());
            setDataList(result.getDataList());
        }
    }

    public static <T> BicycleBillSettlementPageResult<T> emptyPage() {
        return new BicycleBillSettlementPageResult<>();
    }

}

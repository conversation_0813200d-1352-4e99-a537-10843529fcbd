package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/11 8:25
 */
@ApiModel("三轮车订单完成消息")
@Data
public class BicycleOrderCompleteMessageVo implements Serializable {
    private static final long serialVersionUID = 6306905866944960735L;

    /**
     * 订单流水号
     */
    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    /**
     * 寄件人
     */
    @ApiModelProperty("寄件人")
    private String sender;

    /**
     * 骑手名
     */
    @ApiModelProperty("骑手名")
    private String riderName;

    /**
     * 骑手联系方式
     */
    @ApiModelProperty("骑手联系方式")
    private String riderContact;

    /**
     * 微信创建用户
     */
    @ApiModelProperty("微信创建用户")
    private String createOpenid;

    /**
     * 完成时间
     */
    @ApiModelProperty("完成时间")
    private LocalDateTime completeTime;

}

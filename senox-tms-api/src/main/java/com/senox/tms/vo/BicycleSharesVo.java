package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.tms.constant.BicycleStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分佣 domain
 *
 * <AUTHOR>
 * @date 2023-9-15
 */
@Getter
@Setter
@ApiModel("佣金")
public class BicycleSharesVo implements Serializable {

    private static final long serialVersionUID = 2339036870362108950L;


    /**
     * id
     */
    @NotNull(message = "id 不能为空", groups = Update.class)
    @Min(value = 1, message = "无效的id", groups = Update.class)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 名称
     */
    @NotBlank(message = "无效的名称", groups = Add.class)
    @ApiModelProperty("名称")
    private String name;

    /**
     * 比例
     */
    @ApiModelProperty("比例")
    private BigDecimal riderShares;

    /**
     * 默认比例
     */
    @ApiModelProperty("默认比例")
    private BigDecimal riderDefaultShares;

    /**
     * 推荐金额
     */
    @ApiModelProperty("推荐金额")
    private BigDecimal referralAmount;

    /**
     * 生效时间
     */
    @NotNull(message = "无效的生效时间",groups = Add.class)
    @ApiModelProperty("生效时间")
    private LocalDateTime effectiveTime;

    /**
     * 失效时间
     */
    @NotNull(message = "无效的失效时间",groups = Add.class)
    @ApiModelProperty("失效时间")
    private LocalDateTime ineffectiveTime;

    /**
     * 状态
     *
     * @see BicycleStatus
     */
    @ApiModelProperty("状态")
    private BicycleStatus status;
}

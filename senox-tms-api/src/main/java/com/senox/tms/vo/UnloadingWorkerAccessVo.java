package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/29 9:27
 */
@ApiModel("搬运工设备权限")
@Data
public class UnloadingWorkerAccessVo implements Serializable {
    private static final long serialVersionUID = -8375235757163730050L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 搬运工id
     */
    @ApiModelProperty("搬运工id")
    private Long workerId;

    /**
     * 搬运工标识
     */
    @ApiModelProperty("搬运工标识")
    private String workerSign;

    /**
     * 设备id
     */
    @ApiModelProperty("设备id")
    private Long deviceId;

    /**
     * ip
     */
    @ApiModelProperty("ip")
    private String deviceIp;

    /**
     * 拥有权限
     */
    @ApiModelProperty("拥有权限")
    private Boolean access;

    /**
     * 是否生效（0：未生效，1：已生效）
     */
    @ApiModelProperty("是否生效（0：未生效，1：已生效）")
    private Boolean state;
}

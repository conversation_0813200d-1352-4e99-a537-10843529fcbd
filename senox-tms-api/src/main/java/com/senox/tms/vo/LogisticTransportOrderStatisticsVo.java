package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025-06-04
 **/
@Getter
@Setter
@ApiModel("城际运输订单统计")
public class LogisticTransportOrderStatisticsVo {

    /**
     * 总件数
     */
    @ApiModelProperty("总件数")
    private Integer totalPieces;

    /**
     * 总装载重量
     */
    @ApiModelProperty("总装载重量")
    private BigDecimal totalLoadingWeight;

    /**
     * 总运费
     */
    @ApiModelProperty("总运费")
    private BigDecimal totalFreightCharge;

    /**
     * 总其他费用
     */
    @ApiModelProperty("总其他费用")
    private BigDecimal totalOtherCharge;

    /**
     * 总应收运费
     */
    @ApiModelProperty("总应收运费")
    private BigDecimal totalReceivableFreightCharge;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/28 9:09
 */
@ApiModel("当天搬运工信息")
@Data
public class UnloadingDayCountWorkerVo implements Serializable {

    private static final long serialVersionUID = -4889398885582172647L;

    /**
     * 未挂牌数量
     */
    @ApiModelProperty("未挂牌数量")
    private Integer notListedCount;

    /**
     * 已挂牌数量
     */
    @ApiModelProperty("已挂牌数量")
    private Integer alreadyListedCount;

    /**
     * 搬运中数量
     */
    @ApiModelProperty("搬运中数量")
    private Integer duringTransportationCount;

    /**
     * 请假数量
     */
    @ApiModelProperty("请假数量")
    private Integer workerLeaveCount;

    /**
     * 当天最佳搬运工集合
     */
    @ApiModelProperty("当天最佳搬运工集合")
    private List<UnloadingDayBestWorkerVo> bestWorkerVoList;
}

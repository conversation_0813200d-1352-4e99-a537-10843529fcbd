package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/18 15:57
 */
@ApiModel("三轮车配送订单附加费用")
@Data
public class BicycleOrderOtherChargeVo implements Serializable {
    private static final long serialVersionUID = -1576210317292216645L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "无效的id", groups = Add.class)
    private Long id;

    /**
     * 其他费用
     */
    @ApiModelProperty("附加费用")
    @NotNull(message = "附加费用不能为空", groups = Add.class)
    private BigDecimal otherCharge;

    /**
     * 其他备注
     */
    @ApiModelProperty("其他备注")
    private String otherRemark;

    /**
     * 配送单id
     */
    @ApiModelProperty("配送单id")
    private Long deliveryDetailId;
}

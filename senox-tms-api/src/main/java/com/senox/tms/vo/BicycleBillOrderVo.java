package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-9
 */
@Getter
@Setter
@ApiModel("三轮车订单")
public class BicycleBillOrderVo {

    /**
     * 微信openid
     */
    @ApiModelProperty(value = "微信openid", hidden = true)
    private String wxOpenid;

    /**
     * 标题
     */
    @NotBlank(message = "无效的标题")
    @ApiModelProperty("标题")
    private String title;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private Integer payWay;

    /**
     * 收费员
     */
    @ApiModelProperty("收费员")
    private Long tollManId;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private String latitude;

    /**
     * 请求ip
     */
    @ApiModelProperty(value = "请求ip", hidden = true)
    private String requestIp;

    /**
     * 结算单id集
     */
    @ApiModelProperty("结算单id集")
    private List<Long> settlementIds;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/27 10:18
 */
@ApiModel("骑手当前信息")
@Data
public class BicycleRiderInfoVo implements Serializable {
    private static final long serialVersionUID = 351756150229786412L;

    @ApiModelProperty("骑手编号前缀")
    private Integer riderNoPrefix;

    /**
     * 骑手id
     */
    @ApiModelProperty("骑手id")
    private Long riderId;

    /**
     * 骑手名
     */
    @ApiModelProperty("骑手名")
    private String riderName;

    /**
     * 单量
     */
    @ApiModelProperty("单量")
    private Integer orderCount;

    /**
     * 状态 0 空闲 1 忙碌
     */
    @ApiModelProperty("状态 0 空闲 1 忙碌")
    private Integer riderStatus;
}

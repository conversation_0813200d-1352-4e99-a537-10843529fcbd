package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/28 15:24
 */
@ApiModel("下单货物数量排行榜")
@Data
public class UnloadingQuantityCountRankingVo implements Serializable {
    private static final long serialVersionUID = -3852158478944858522L;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String customerName;

    /**
     * 下单货物数量
     */
    @ApiModelProperty("下单货物数量")
    private Integer totalQuantity;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:07
 */
@ApiModel("装卸字典")
@Data
public class UnloadingDictVo implements Serializable {
    private static final long serialVersionUID = 9029474429293145377L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("字典类型 0冻品，1干货，2车型，3其他，4客户，5加急费")
    private Integer category;

    @ApiModelProperty("字典名称")
    private String name;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("保留字段1")
    private String attr1;

    @ApiModelProperty("保留字段2")
    private String attr2;

    @ApiModelProperty("保留字段3")
    private String attr3;

    @ApiModelProperty("保留字段4")
    private String attr4;
}

package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/12 14:31
 */
@ApiModel("装卸搬运工考勤查询参数")
@Getter
@Setter
public class UnloadingAttendanceSearchVo extends PageRequest {
    private static final long serialVersionUID = -3888246811376465223L;

    /**
     * 操作时间起
     */
    @ApiModelProperty("操作时间起")
    private LocalDateTime operateTimeStart;

    /**
     * 操作时间止
     */
    @ApiModelProperty("操作时间止")
    private LocalDateTime operateTimeEnd;

    /**
     * 搬运工id
     */
    @ApiModelProperty("搬运工id")
    private Long workerId;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;
}

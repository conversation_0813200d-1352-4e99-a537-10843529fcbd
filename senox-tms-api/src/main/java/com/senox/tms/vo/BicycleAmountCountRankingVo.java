package com.senox.tms.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/27 14:49
 */
@ApiOperation("下单金额排行榜")
@Data
public class BicycleAmountCountRankingVo implements Serializable {
    private static final long serialVersionUID = -5891179813390363399L;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String sender;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal totalAmount;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
@Getter
@Setter
@ApiModel("三轮车费用标准计划任务")
public class BicycleChargesScheduledTaskVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 任务名
     */
    @ApiModelProperty("任务名")
    private String taskName;

    /**
     * 任务次数
     */
    @ApiModelProperty("任务次数")
    private Integer taskCount;

    /**
     * 模式(0:增量更新;1:全量更新)
     */
    @ApiModelProperty("模式(0:增量更新;1:全量更新)")
    private Integer model;

    /**
     * 费用标准id
     */
    @ApiModelProperty("费用标准id")
    private Long chargesId;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    /**
     * 费用标准名
     */
    @ApiModelProperty("费用标准名")
    private String chargesName;

    /**
     * 商户信息集
     */
    @ApiModelProperty("商户信息集")
    private Collection<MerchantInfoVo> merchantInfos;
}

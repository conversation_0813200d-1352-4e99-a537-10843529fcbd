package com.senox.tms.vo;

import com.senox.tms.constant.LogisticTransportCategory;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-05-20
 **/
@Getter
@Setter
@ApiModel("物流运输订单")
public class LogisticTransportOrderVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private String serialNo;

    /**
     * 订单年月日
     */
    @ApiModelProperty("订单年月日")
    private String yearMonthDay;

    /**
     * 发货人id
     */
    @ApiModelProperty("发货人id")
    private Long consignorId;

    /**
     * 发货人姓名
     */
    @ApiModelProperty("发货人姓名")
    private String consignorName;

    /**
     * 发货人编码
     */
    @ApiModelProperty("发货人编码")
    private String consignorCode;

    /**
     * 发货人电话
     */
    @ApiModelProperty("发货人电话")
    private String consignorPhone;

    /**
     * 收货人姓名
     */
    @ApiModelProperty("收货人姓名")
    private String consigneeName;

    /**
     * 收货人电话
     */
    @ApiModelProperty("收货人电话")
    private String consigneePhone;

    /**
     * 类别
     *
     * @see LogisticTransportCategory
     */
    @ApiModelProperty("类别")
    private LogisticTransportCategory category;

    /**
     * 司机姓名
     */
    @ApiModelProperty("司机姓名")
    private String driverName;

    /**
     * 司机手机
     */
    @ApiModelProperty("司机手机")
    private String driverPhone;

    /**
     * 车牌号
     */
    @ApiModelProperty("车牌号")
    private String licensePlateNumber;

    /**
     * 是否包车
     */
    @ApiModelProperty("是否包车")
    private Boolean charter;

    /**
     * 始发站
     */
    @ApiModelProperty("始发站")
    private String departureStation;

    /**
     * 目的站
     */
    @ApiModelProperty("目的站")
    private String destinationStation;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private Integer pieces;

    /**
     * 装载重量(kg)
     */
    @ApiModelProperty("装载重量(kg)")
    private BigDecimal loadingWeight;

    /**
     * 运费
     */
    @ApiModelProperty("运费")
    private BigDecimal freightCharge;

    /**
     * 其他费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 应收运费
     */
    @ApiModelProperty("应收运费")
    private BigDecimal receivableFreightCharge;

    /**
     * 付款人
     */
    @ApiModelProperty("付款人")
    private LogisticTransportOrderPayer payer;

    /**
     * 存在账单
     */
    @ApiModelProperty("存在账单")
    private Boolean hasBill;

    /**
     * 支付状态
     */
    @ApiModelProperty("支付状态")
    private Boolean payStatus;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private Boolean auditStatus;

    /**
     * 审核人ID
     */
    @ApiModelProperty("审核人ID")
    private Long auditorId;

    /**
     * 审核人姓名
     */
    @ApiModelProperty("审核人姓名")
    private String auditorName;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    private LocalDateTime auditTime;

    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人姓名
     */
    @ApiModelProperty("修改人姓名")
    private String modifierName;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime modifiedTime;
}

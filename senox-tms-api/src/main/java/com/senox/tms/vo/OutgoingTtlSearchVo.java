package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/4/8 11:08
 */
@ApiModel("太太乐外发查询参数")
@Getter
@Setter
public class OutgoingTtlSearchVo extends PageRequest {
    private static final long serialVersionUID = -2476700373594590717L;

    /**
     * 收货日期起
     */
    @ApiModelProperty("收货日期起")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate receivingDateStart;

    /**
     * 收货日期起
     */
    @ApiModelProperty("收货日期止")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate receivingDateEnd;

    /**
     * 托运方客户
     */
    @ApiModelProperty("托运方客户")
    private String shipperName;

    /**
     * 收货方客户
     */
    @ApiModelProperty("收货方客户")
    private String recipientName;

    /**
     * 物流单号
     */
    @ApiModelProperty("物流单号")
    private String logisticsNo;

    /**
     * 物流公司
     */
    @ApiModelProperty("物流公司")
    private String logisticsCompany;

    /**
     * 结算类型(1:到付;2:现付)
     */
    @ApiModelProperty("结算类型(1:到付;2:现付)")
    private Integer settlementType;
}

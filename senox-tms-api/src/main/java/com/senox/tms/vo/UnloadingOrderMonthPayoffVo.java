package com.senox.tms.vo;

import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/8 14:23
 */
@Data
@ApiModel("鹏翔月应付账单")
public class UnloadingOrderMonthPayoffVo implements Serializable {
    private static final long serialVersionUID = -1541224996132406828L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 应付年份
     */
    @ApiModelProperty("应付年份")
    private Integer payoffYear;

    /**
     * 应付月份
     */
    @ApiModelProperty("应付月份")
    private Integer payoffMonth;

    /**
     * 搬运工id
     */
    @ApiModelProperty("搬运工id")
    private Long workerId;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String workerName;

    /**
     * 佣金
     */
    @ApiModelProperty("佣金")
    private BigDecimal payoffAmount;

    /**
     * 收益
     */
    @ApiModelProperty("收益")
    private BigDecimal shareAmount;

    /**
     * 支付状态
     * @see UnloadingOrderBillPayoffStatus
     */
    @ApiModelProperty("应付状态 0初始化 1已支付")
    private Integer status;

    /**
     * 应付时间
     */
    @ApiModelProperty("应付时间")
    private LocalDateTime payoffTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}

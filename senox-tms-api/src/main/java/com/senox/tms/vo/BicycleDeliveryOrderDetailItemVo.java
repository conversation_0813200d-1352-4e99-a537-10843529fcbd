package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/4/3 11:19
 */
@Data
@ApiModel("三轮车配送单子详细")
public class BicycleDeliveryOrderDetailItemVo implements Serializable {
    private static final long serialVersionUID = 1586381991363891083L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    /**
     * 子配送单流水号
     */
    @ApiModelProperty("子配送单流水号")
    private String deliveryOrderSerialNoItem;

    /**
     * 订单流水号
     */
    @ApiModelProperty("订单流水号")
    private String orderSerialNo;
    /**
     * 货物id
     */
    @ApiModelProperty("货物id")
    private Long goodsId;
    /**
     * 货物类型 0其他，1重货，2抛货
     */
    @ApiModelProperty("货物类型 0其他，1重货，2抛货")
    private Integer goodsType;
    /**
     * 货物名
     */
    @ApiModelProperty("货物名")
    private String goodsName;
    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal pieces;
    /**
     * 重量
     */
    @ApiModelProperty("重量")
    private BigDecimal weight;
    /**
     * 体积
     */
    @ApiModelProperty("体积")
    private BigDecimal size;
}

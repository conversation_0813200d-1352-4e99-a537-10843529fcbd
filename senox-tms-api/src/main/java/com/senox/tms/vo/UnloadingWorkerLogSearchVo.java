package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/3/24 8:31
 */
@ApiModel("鹏翔搬运工异常日志查询参数")
@Getter
@Setter
public class UnloadingWorkerLogSearchVo extends PageRequest {
    private static final long serialVersionUID = -4110144972128625119L;

    /**
     * 搬运工id
     */
    @ApiModelProperty("搬运工id")
    private Long workerId;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String workerName;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 开始时间起
     */
    @ApiModelProperty("开始时间起")
    private LocalDateTime modifiedTimeStart;

    /**
     * 开始时间止
     */
    @ApiModelProperty("开始时间止")
    private LocalDateTime modifiedTimeEnd;
}

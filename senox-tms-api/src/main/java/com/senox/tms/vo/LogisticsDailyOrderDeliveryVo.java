package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-12-5
 */
@Getter
@Setter
@ApiModel("物流每日订单配送")
public class LogisticsDailyOrderDeliveryVo {

    /**
     * id
     */
    private Long id;

    /**
     * 配送单号
     */
    @ApiModelProperty("配送单号")
    private String orderDeliveryNo;

    /**
     * 配送车牌
     */
    @ApiModelProperty("配送车牌")
    private String orderDeliveryCarNo;

    /**
     * 订单件数
     */
    @ApiModelProperty("订单件数")
    private Integer orderPieces;

    /**
     * 订单总千克
     */
    @ApiModelProperty("订单总千克")
    private BigDecimal orderTotalKilograms;

    /**
     * 订单时间
     */
    @ApiModelProperty("订单时间")
    private LocalDate orderTime;

    /**
     * 下单人
     */
    @ApiModelProperty("下单人")
    private String orderPerson;

    /**
     * 送货时间
     */
    @ApiModelProperty("送货时间")
    private LocalDate sendTime;

    /**
     * 类型(1:干货线;2:冻品线)
     */
    @ApiModelProperty("类型(1:干货线;2:冻品线)")
    private Integer type;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private Integer pieces;

    /**
     * 千克
     */
    @ApiModelProperty("千克")
    private BigDecimal kilograms;

    /**
     * 运费单价
     */
    @ApiModelProperty("运费单价")
    private BigDecimal freightUnitPrice;

    /**
     * 应收运费
     */
    @ApiModelProperty("应收运费")
    private BigDecimal receivableFreightCharge;

    /**
     * 实收运费
     */
    @ApiModelProperty("实收运费")
    private BigDecimal receivedFreightCharge;

    /**
     * 差额
     */
    @ApiModelProperty("差额")
    private BigDecimal discrepancyAmount;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}

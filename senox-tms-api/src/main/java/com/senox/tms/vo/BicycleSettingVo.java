package com.senox.tms.vo;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/25 9:33
 */
@Data
@ApiModel("三轮车配置")
public class BicycleSettingVo implements Serializable {
    private static final long serialVersionUID = 8289372703099434767L;

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "无效的id", groups = Update.class)
    private Long id;

    /**
     * 配置名
     */
    @ApiModelProperty("配置名")
    @NotBlank(message = "无效的配置名", groups = Add.class)
    private String name;

    /**
     * 别名
     */
    @ApiModelProperty("别名")
    @NotBlank(message = "无效的别名", groups = Add.class)
    private String alias;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean enable;

    /**
     * 单位间隔，单位：分钟
     */
    @ApiModelProperty("单位间隔，单位：分钟")
    private Integer intervalMinute;
}

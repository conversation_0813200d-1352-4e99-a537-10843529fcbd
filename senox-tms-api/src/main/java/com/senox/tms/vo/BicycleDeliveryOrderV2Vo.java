package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/27 14:43
 */
@ApiModel("三轮车配送订单V2")
@Data
public class BicycleDeliveryOrderV2Vo implements Serializable {
    private static final long serialVersionUID = 3571605173321635541L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("配送单号")
    private String deliveryOrderSerialNo;

    @ApiModelProperty("是否合并")
    private Boolean merged;

    @ApiModelProperty("配送状态")
    private Integer status;

    @ApiModelProperty("三轮车订单V2集合")
    private List<BicycleOrderV2Vo> orderV2VoList;

    public BicycleDeliveryOrderV2Vo() {
    }

    public BicycleDeliveryOrderV2Vo(List<BicycleOrderV2Vo> orderV2VoList) {
        this.orderV2VoList = orderV2VoList;
        this.id = 0L;
        this.deliveryOrderSerialNo = "";
    }
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:28
 */
@Getter
@Setter
@ToString
@ApiModel("物流订单")
public class LogisticOrderVo implements Serializable {

    private static final long serialVersionUID = 7914975598551582997L;

    @ApiModelProperty("商品订单id")
    private Long productOrderId;

    @NotBlank(message = "无效的订单编号")
    @ApiModelProperty("订单编号")
    private String orderNo;

    @NotBlank(message = "无效的商户")
    @ApiModelProperty("商户")
    private String merchant;

    @ApiModelProperty("会员")
    private String member;

    @NotBlank(message = "无效的货品")
    @ApiModelProperty("货品")
    private String product;

    @ApiModelProperty("商品总类")
    private String productType1;

    @ApiModelProperty("商品分类")
    private String productType2;

    @ApiModelProperty("商品单价")
    private BigDecimal productPrice;

    //@DecimalMin(value = "0", message = "无效的商品件数")
    @NotNull(message = "无效的商品件数")
    @ApiModelProperty("商品件数")
    private BigDecimal productCount;

    @ApiModelProperty("商品总价")
    private BigDecimal productAmount;

    //@DecimalMin(value = "0", message = "无效的商品重量")
    @ApiModelProperty("商品总重量")
    private BigDecimal productWeight;

    //@DecimalMin(value = "0", message = "无效的商品体积")
    @ApiModelProperty("商品总体积")
    private BigDecimal productSize;

    @ApiModelProperty("商品优惠金额")
    private BigDecimal productDeduction;

    @ApiModelProperty("商品满减额")
    private BigDecimal productFullReduction;

    @ApiModelProperty("商品合计")
    private BigDecimal productTotalAmount;

    @ApiModelProperty("备注")
    private String remark;


    @ApiModelProperty("配送单id")
    private Long shipId;

    @NotNull(message = "无效的发货日期")
    @ApiModelProperty("发货日期")
    private LocalDate shipDate;

    @ApiModelProperty("收货人")
    private String receiver;

    @ApiModelProperty("收货人联系方式")
    private String receiverContact;

    @ApiModelProperty("送货地址")
    private String destination;

    @ApiModelProperty("市场")
    private String market;

    @ApiModelProperty("区域")
    private String area;

    @ApiModelProperty("车牌")
    private String vehicleNo;

    @ApiModelProperty("司机")
    private String driver;

    @ApiModelProperty("实发件数")
    private BigDecimal shipCount;

    @ApiModelProperty("发货差异")
    private BigDecimal shipDiversity;

    @ApiModelProperty("件重")
    private BigDecimal pieceWeight;

    @ApiModelProperty("实发重量")
    private BigDecimal shipWeight;

    @ApiModelProperty("件体积")
    private BigDecimal pieceSize;

    @ApiModelProperty("实发体积")
    private BigDecimal shipSize;

    @DecimalMin(value = "0", message = "无效的运费单价")
    @ApiModelProperty("运费单价")
    private BigDecimal shipPrice;

    @ApiModelProperty("运费倍率")
    private BigDecimal shipMultiplying;

    @ApiModelProperty("物流费")
    private BigDecimal shipAmount;

    @ApiModelProperty("折扣率")
    private BigDecimal shipDiscount;

    @ApiModelProperty("分拣费")
    private BigDecimal sortAmount;

    @ApiModelProperty("是否收取分拣费")
    private Boolean sortCharge;

    @ApiModelProperty("实际运费")
    private BigDecimal shipTotalAmount;

    @ApiModelProperty("账单id")
    private Long billId;

    @ApiModelProperty("已收货款金额")
    private BigDecimal productPaid;

    @ApiModelProperty("手工调整已收货款金额")
    private Boolean productPaidManual;

    @ApiModelProperty("预付款人")
    private String productPaidMan;

    @ApiModelProperty("已收货款差异金额")
    private BigDecimal productDiversity;

    @ApiModelProperty("已收货款欠款金额")
    private BigDecimal productOwe;

    @ApiModelProperty("应付合计")
    private BigDecimal totalAmount;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}

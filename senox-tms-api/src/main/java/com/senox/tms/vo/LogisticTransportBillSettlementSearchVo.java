package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-09
 **/
@Getter
@Setter
@ApiModel("城际运输结算单查询参数")
public class LogisticTransportBillSettlementSearchVo extends PageRequest {

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keywords;

    /**
     * id集
     */
    @ApiModelProperty("id集")
    private List<Long> ids;

    /**
     * 账单开始日期
     */
    @ApiModelProperty("结算开始日期")
    private LocalDate settlementDateStart;

    /**
     * 账单结束日期
     */
    @ApiModelProperty("结算结束日期")
    private LocalDate settlementDateEnd;

    /**
     * 结算支付时间开始日期
     */
    @ApiModelProperty("结算支付时间开始日期")
    private LocalDateTime settlementPaidDateStart;

    /**
     * 结算支付时间结束日期
     */
    @ApiModelProperty("结算支付时间结束日期")
    private LocalDateTime settlementPaidDateEnd;

    /**
     * 结算支付状态
     */
    @ApiModelProperty("结算支付状态")
    private Boolean settlementPaidStatus;

    /**
     * 结算支付方式集
     */
    @ApiModelProperty("结算支付方式集")
    private List<Integer> paWays;

    /**
     * 收费员id集
     */
    @ApiModelProperty("收费员id集")
    private List<Long> tollManIds;

    /**
     * 商户id集
     */
    @ApiModelProperty("商户id集")
    private List<Long> merchantIds;

    /**
     * 付款人
     */
    @ApiModelProperty("付款人")
    private LogisticTransportOrderPayer payer;

    /**
     * 是否下发
     */
    @ApiModelProperty("是否下发")
    private Boolean send;

    /**
     * 订单编号集
     */
    @ApiModelProperty("订单编号集")
    private List<String> orderSerialNos;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private String endDate;
}

package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/10/12 15:20
 */
@ApiModel("三轮车运营分析记录查询参数")
@Getter
@Setter
public class BicycleOperateAnalysisSearchVo extends PageRequest {
    private static final long serialVersionUID = -9063010680805149217L;

    /**
     * 时间启
     */
    @ApiModelProperty("时间启")
    private LocalDate operateAnalysisDateStart;

    /**
     * 时间止
     */
    @ApiModelProperty("时间止")
    private LocalDate operateAnalysisDateEnd;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private LocalDate operateAnalysisDate;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/9/27 14:46
 */
@ApiModel("下单件数排行榜")
@Data
public class BicyclePiecesCountRankingVo implements Serializable {
    private static final long serialVersionUID = -3433873550305266397L;

    /**
     * 客户名
     */
    @ApiModelProperty("客户名")
    private String sender;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal totalPieces;
}

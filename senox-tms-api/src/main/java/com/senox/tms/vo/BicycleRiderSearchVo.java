package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import com.senox.tms.constant.BicycleRiderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/9/13 16:01
 */
@ApiModel("三轮车配送骑手查询参数")
@Getter
@Setter
public class BicycleRiderSearchVo extends PageRequest {
    private static final long serialVersionUID = -2882158151782690787L;

    /**
     * 骑手编号
     */
    @ApiModelProperty("骑手编号")
    private String riderNo;
    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;
    /**
     * 状态
     * @see BicycleRiderStatus
     */
    @ApiModelProperty("状态 0 离线; 1 在线; 2 离职")
    private Integer status;
}

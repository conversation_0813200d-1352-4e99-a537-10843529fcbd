package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/3/10 11:06
 */
@ApiModel("鹏翔搬运工排期计划查询参数")
@Getter
@Setter
public class UnloadingNightScheduleSearchVo extends PageRequest {

    private static final long serialVersionUID = -836030231862715315L;

    /**
     * 排期日期
     */
    @ApiModelProperty("排期日期")
    private LocalDate scheduleDate;

    /**
     * 排期日期起
     */
    @ApiModelProperty("排期日期起")
    private LocalDate scheduleDateStart;

    /**
     * 排期日期止
     */
    @ApiModelProperty("排期日期止")
    private LocalDate scheduleDateEnd;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String workerName;
}

package com.senox.tms.vo;

import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/10/8 9:46
 */
@Data
@ApiModel("三轮车配送订单信息")
public class BicycleOrderInfoVo implements Serializable {
    private static final long serialVersionUID = 4709532135812745733L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 订单流水号
     */
    @ApiModelProperty("订单流水号")
    private String orderSerialNo;

    /**
     * 配送单Id
     */
    @ApiModelProperty("配送单Id")
    private Long deliveryOrderId;

    /**
     * 发货人冷藏编号
     */
    @ApiModelProperty("发货人冷藏编号")
    private String senderSerialNo;

    /**
     * 预约配送时间起
     */
    @ApiModelProperty("预约配送时间起")
    private LocalDateTime sendTimeStart;

    /**
     * 预约配送时间止
     */
    @ApiModelProperty("预约配送时间止")
    private LocalDateTime sendTimeEnd;

    /**
     * 下单时间
     */
    @ApiModelProperty("下单时间")
    private LocalDateTime orderTime;

    /**
     * 其他备注
     */
    @ApiModelProperty("其他备注")
    private String otherRemark;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal pieces;

    /**
     * 配送费用
     */
    @ApiModelProperty("配送费用")
    private BigDecimal deliveryCharge;

    /**
     * 其他费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 装卸费
     */
    @ApiModelProperty("装卸费")
    private BigDecimal handlingCharge;

    /**
     * 上楼费
     */
    @ApiModelProperty("上楼费")
    private BigDecimal upstairsCharge;

    /**
     * 总费用
     */
    @ApiModelProperty("总费用")
    private BigDecimal totalCharge;

    /**
     * 配送单流水号
     */
    @ApiModelProperty("配送单流水号")
    private String deliveryOrderSerialNo;

    /**
     * 寄件人
     */
    @ApiModelProperty("寄件人")
    private String sender;

    /**
     * 收件人
     */
    @ApiModelProperty("收件人")
    private String recipient;

    /**
     * 起点
     */
    @ApiModelProperty("起点")
    private String startPointName;

    /**
     * 详细起点地址
     */
    @ApiModelProperty("详细起点地址")
    private String startPointDetailName;

    /**
     * 终点
     */
    @ApiModelProperty("终点")
    private String endPointName;

    /**
     * 详细终点地址
     */
    @ApiModelProperty("详细终点地址")
    private String endPointDetailName;

    /**
     * 结算周期
     */
    @ApiModelProperty("结算周期：0单结 1日结 2月结")
    private Integer settlePeriod;

    /**
     * 状态 0：正常 1：取消
     */
    @ApiModelProperty("订单状态 0：正常 1：取消")
    private Integer orderStatus;

    /**
     * 订单状态备注
     */
    @ApiModelProperty("订单状态备注")
    private String statusRemark;

    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    @ApiModelProperty(value = "状态 1 待揽货；2 已揽货；3 配送中；4 配送完成；5 确认完成", hidden = true)
    private Integer status;

    /**
     * 收费标准id
     */
    @ApiModelProperty("收费标准id")
    private Long chargesId;

    /**
     * 配送详细单
     */
    @ApiModelProperty("配送详细单")
    private List<BicycleDeliveryDetailInfoVo> detailInfoVos;

    /**
     * 订单货物明细
     */
    @ApiModelProperty("订单货物明细")
    private List<BicycleOrderGoodsDetailVo> orderGoodsDetailVos;

    public BicycleOrderInfoVo() {
    }

    public BicycleOrderInfoVo(BicycleOrderVo orderVo, BicycleOrderSearchVo searchVo) {
        this.id = orderVo.getId();
        this.deliveryOrderId = orderVo.getDeliveryOrderId();
        this.orderSerialNo = orderVo.getOrderSerialNo();
        this.senderSerialNo = orderVo.getSenderSerialNo();
        this.deliveryOrderSerialNo = orderVo.getDeliveryOrderSerialNo();
        this.sendTimeStart = orderVo.getSendTimeStart();
        this.sendTimeEnd = orderVo.getSendTimeEnd();
        this.orderTime = orderVo.getOrderTime();
        this.otherRemark = orderVo.getOtherRemark();
        this.pieces = orderVo.getPieces();
        this.deliveryCharge = orderVo.getDeliveryCharge();
        this.otherCharge = orderVo.getOtherCharge();
        this.totalCharge = orderVo.getTotalCharge();
        this.handlingCharge = orderVo.getHandlingCharge();
        this.upstairsCharge = orderVo.getUpstairsCharge();
        this.sender = orderVo.getSender();
        this.recipient = orderVo.getRecipient();
        this.startPointName = orderVo.getStartPointName();
        this.startPointDetailName = orderVo.getStartPointDetailName();
        this.endPointName = orderVo.getEndPointName();
        this.endPointDetailName = orderVo.getEndPointDetailName();
        this.settlePeriod = orderVo.getSettlePeriod();
        this.orderStatus = orderVo.getOrderStatus();
        this.statusRemark = orderVo.getStatusRemark();
        this.status = orderVo.getStatus();
        this.chargesId = orderVo.getChargesId();
        List<BicycleDeliveryDetailInfoVo> detailInfoVos = new ArrayList<>(orderVo.getDetailVos().size());
        for (BicycleDeliveryOrderDetailVo detailVo : orderVo.getDetailVos()) {
            if (searchVo.getRiderId() == null || searchVo.getRiderId() == 0L) {
                detailInfoVos.add(new BicycleDeliveryDetailInfoVo(detailVo));
            } else {
                if (Objects.equals(detailVo.getRiderId(), searchVo.getRiderId())) {
                    detailInfoVos.add(new BicycleDeliveryDetailInfoVo(detailVo));
                }
            }
        }
        this.detailInfoVos = detailInfoVos;
    }

    @Getter
    @Setter
    public static class BicycleDeliveryDetailInfoVo {
        /**
         * 骑手名称
         */
        @ApiModelProperty("骑手名称")
        private String riderName;

        /**
         * 状态
         * @see BicycleDeliveryOrderStatus
         */
        @ApiModelProperty(value = "状态 1 待揽货；2 已揽货；3 配送中；4 配送完成；5 确认完成", hidden = true)
        private Integer status;

        /**
         * 订单流水号
         */
        @ApiModelProperty("订单流水号")
        private String orderSerialNo;

        /**
         * 配送单详细id
         */
        @ApiModelProperty("配送单详细id")
        private Long deliveryOrderDetailId;

        /**
         * 骑手Id
         */
        @ApiModelProperty("骑手Id")
        private Long riderId;

        /**
         * 揽货件数
         */
        @ApiModelProperty("揽货件数")
        private BigDecimal pickedPieces;

        /**
         * 揽货时间
         */
        @ApiModelProperty("揽货时间")
        private LocalDateTime pickingTime;
        /**
         * 配送时间
         */
        @ApiModelProperty("配送时间")
        private LocalDateTime sendTime;
        /**
         * 收货时间
         */
        @ApiModelProperty("收货时间")
        private LocalDateTime receivingTime;
        /**
         * 完成时间
         */
        @ApiModelProperty("完成时间")
        private LocalDateTime finishTime;

        /**
         * 配送单子详细
         */
        @ApiModelProperty("配送单子详细")
        private List<BicycleDeliveryOrderDetailItemVo> detailItemVos;

        public BicycleDeliveryDetailInfoVo() {
        }

        public BicycleDeliveryDetailInfoVo(BicycleDeliveryOrderDetailVo detailVo) {
            this.status = detailVo.getStatus();
            this.orderSerialNo = detailVo.getOrderSerialNo();
            this.riderName = detailVo.getRiderName();
            this.deliveryOrderDetailId = detailVo.getId();
            this.riderId = detailVo.getRiderId();
            this.pickedPieces = detailVo.getPickedPieces();
            this.pickingTime = detailVo.getPickingTime();
            this.sendTime = detailVo.getSendTime();
            this.receivingTime = detailVo.getReceivingTime();
            this.finishTime = detailVo.getFinishTime();
            this.detailItemVos = detailVo.getDetailItemVos();
        }
    }
}

package com.senox.tms.vo;

import com.senox.common.vo.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@ApiModel("搬运工查询")
public class LogisticLoaderSearchVo extends PageRequest {

    @ApiModelProperty("编号集")
    private Collection<String> numbers;

    @ApiModelProperty("姓名")
    private String name;
}

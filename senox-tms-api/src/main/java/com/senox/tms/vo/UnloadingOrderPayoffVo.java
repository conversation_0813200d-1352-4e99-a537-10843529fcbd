package com.senox.tms.vo;

import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/11/1 11:37
 */
@ApiModel("鹏翔搬运工应付")
@Data
public class UnloadingOrderPayoffVo implements Serializable {
    private static final long serialVersionUID = -6191155130187736852L;


    @ApiModelProperty("id")
    private Long id;

    /**
     * 应付日期
     */
    @ApiModelProperty("应付日期")
    private LocalDate payoffDate;

    /**
     * 应付年份
     */
    @ApiModelProperty("应付年份")
    private Integer payoffYear;

    /**
     * 应付月份
     */
    @ApiModelProperty("应付月份")
    private Integer payoffMonth;


    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private Long orderId;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 搬运工id
     */
    @ApiModelProperty("搬运工id")
    private Long workerId;

    /**
     * 搬运工编号
     */
    @ApiModelProperty("搬运工编号")
    private String workerNo;

    /**
     * 搬运工姓名
     */
    @ApiModelProperty("搬运工姓名")
    private String workerName;

    /**
     * 排序
     */
    @ApiModelProperty("排序")
    private Integer orderNum;

    /**
     * 佣金
     */
    @ApiModelProperty("佣金")
    private BigDecimal payoffAmount;

    /**
     * 月应付id
     */
    @ApiModelProperty("月应付id")
    private Long monthPayoffId;

    /**
     * 应付状态
     * @see UnloadingOrderBillPayoffStatus
     */
    @ApiModelProperty("应付状态 0初始化 1已支付")
    private Integer status;

    /**
     * 应付时间
     */
    @ApiModelProperty("应付时间")
    private LocalDateTime payoffTime;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("车牌")
    private String licensePlate;

    @ApiModelProperty("联系方式")
    private String contact;
}

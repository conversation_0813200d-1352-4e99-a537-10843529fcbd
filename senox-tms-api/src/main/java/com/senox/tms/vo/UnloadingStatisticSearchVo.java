package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/3/28 9:25
 */
@ApiModel("鹏翔大屏统计查询参数")
@Data
public class UnloadingStatisticSearchVo implements Serializable {
    private static final long serialVersionUID = -2856616564381896381L;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("搜索单位:0 日 1月 2年")
    private Integer searchUnit;

    @ApiModelProperty(value = "搜索类型", hidden = true)
    private String searchType;

    @ApiModelProperty(value = "搜索类型格式", hidden = true)
    private String searchTypeFormat;

    @ApiModelProperty(value = "搜索结果集格式", hidden = true)
    private String searchResultFormat;
}

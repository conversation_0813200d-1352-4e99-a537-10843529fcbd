package com.senox.tms.vo;

import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 8:55
 */
@ApiModel("鹏翔应收账单")
@Data
public class UnloadingOrderBillVo implements Serializable {
    private static final long serialVersionUID = 6885426883021014551L;

    @ApiModelProperty("id")
    private Long id;

    /**
     * 账单日
     */
    @ApiModelProperty("账单日")
    private LocalDate billDate;

    /**
     * 年份
     */
    @ApiModelProperty("年份")
    private Integer billYear;

    /**
     * 月份
     */
    @ApiModelProperty("月份")
    private Integer billMonth;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    private Long orderId;

    /**
     * 金额
     */
    @ApiModelProperty("金额")
    private BigDecimal amount;

    /**
     * 支付状态
     * @see UnloadingOrderBillPayoffStatus
     */
    @ApiModelProperty("支付状态 0初始化 1已支付")
    private Integer status;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    private LocalDateTime paidTime;

    /**
     * 收费人
     */
    @ApiModelProperty("收费人")
    private String tollMan;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("客户名")
    private String customerName;

    @ApiModelProperty("地址")
    private String address;

    @ApiModelProperty("车牌")
    private String licensePlate;

    @ApiModelProperty("联系方式")
    private String contact;

    @ApiModelProperty("货物数量")
    private BigDecimal quantity;

    @ApiModelProperty("货物明细")
    private List<UnloadingOrderGoodsVo> goodsVoList;
}

package com.senox.tms.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/26 11:48
 */
@ApiModel("订单统计")
@Data
public class BicycleOrderCountVo implements Serializable {
    private static final long serialVersionUID = -9027274638399967396L;

    /**
     * 时间区间
     */
    @ApiModelProperty("时间")
    private LocalDateTime hours;

    /**
     * 总订单数
     */
    @ApiModelProperty("总订单数")
    private Integer totalCount;

    /**
     * 未处理数量
     */
    @ApiModelProperty("未处理数量")
    private Integer undoCount;

    /**
     * 正在处理数量
     */
    @ApiModelProperty("正在处理数量")
    private Integer doingCount;

    /**
     * 处理完成数量
     */
    @ApiModelProperty("处理完成数量")
    private Integer doneCount;

    /**
     * 件数
     */
    @ApiModelProperty("件数")
    private BigDecimal pieces;

    public BicycleOrderCountVo() {
    }

    public BicycleOrderCountVo(Integer totalCount) {
        this.totalCount = totalCount;
        this.undoCount = 0;
        this.doingCount = 0;
        this.doneCount = 0;
        this.pieces = BigDecimal.ZERO;
    }
}

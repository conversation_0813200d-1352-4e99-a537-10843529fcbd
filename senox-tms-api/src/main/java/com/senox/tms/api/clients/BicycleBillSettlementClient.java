package com.senox.tms.api.clients;

import com.senox.common.vo.BillPaidVo;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.tms.api.BicycleBillSettlementServiceUrl.*;

/**
 * <AUTHOR>
 * @date 2023-11-13
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicycleBillSettlementClient {

    /**
     * 根据id查询结算单
     * @param id id
     * @return 返回结算单
     */
    @GetMapping(BICYCLE_BILL_SETTLEMENT_FIND_BY_ID)
    BicycleBillSettlementVo findById(@PathVariable Long id);

    /**
     * 账单支付
     *
     * @param billOrderVo 账单
     * @return 返回支付结果
     */
    @PostMapping(BICYCLE_BILL_SETTLEMENT_PAY)
    OrderResultVo payBill(@Validated @RequestBody BicycleBillOrderVo billOrderVo);

    /**
     * 账单列表
     *
     * @param searchVo 查询参数
     * @return 返回账单列表
     */
    @PostMapping(BICYCLE_BILL_SETTLEMENT_LIST)
    List<BicycleBillSettlementVo> list(@RequestBody BicycleBillSettlementSearchVo searchVo);

    /**
     * 账单列表统计
     *
     * @param searchVo 查询参数
     * @return 返回账单列表统计
     */
    @PostMapping(BICYCLE_BILL_SETTLEMENT_LIST_COUNT)
    Integer listCount(@RequestBody BicycleBillSettlementSearchVo searchVo);

    /**
     * 账单分页列表
     *
     * @param searchVo 查询参数
     * @return 返回分页后的列表
     */
    @PostMapping(BICYCLE_BILL_SETTLEMENT_LIST_PAGE)
    BicycleBillSettlementPageResult<BicycleBillSettlementVo> listPage(@RequestBody BicycleBillSettlementSearchVo searchVo);

    /**
     * 账单状态更新
     *
     * @param billPaidVo 账单支付信息
     */
    @PostMapping(BICYCLE_BILL_SETTLEMENT_PAID_UPDATE)
    void updateStatus(@RequestBody BillPaidVo billPaidVo);

    /**
     * 根据结算单id查询详情
     *
     * @param settlementId 结算单id
     * @return 返回查询到的详情
     */
    @GetMapping(BICYCLE_BILL_SETTLEMENT_DETAIL)
    BicycleBillSettlementDetailVo detailBySettlement(@PathVariable Long settlementId);

    /**
     * 结算单下发
     *
     * @param sendVo 下发参数
     */
    @PostMapping(BICYCLE_BILL_SETTLEMENT_SEND)
    void send(@RequestBody BicycleBillSettlementSendVo sendVo);

    /**
     * 结算单下发通知
     *
     * @param sendVo 下发参数
     */
    @PostMapping(BICYCLE_BILL_SETTLEMENT_SEND_NOTIFY)
    void notifySend(@RequestBody BicycleBillSettlementSendVo sendVo);

    /**
     * 结算单生成
     *
     * @param sendVo 参数
     */
    @PostMapping(BICYCLE_BILL_SETTLEMENT_GENERATE)
    void generate(@RequestBody BicycleBillSettlementSendVo sendVo);
}

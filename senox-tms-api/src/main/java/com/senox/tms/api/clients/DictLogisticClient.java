package com.senox.tms.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.constant.DictLogisticCategory;
import com.senox.tms.vo.DictLogisticSearchVo;
import com.senox.tms.vo.DictLogisticVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import static com.senox.tms.api.DictLogisticServiceUrl.*;

/**
 * <AUTHOR>
 * @date 2023-11-30
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface DictLogisticClient {

    /**
     * 添加
     *
     * @param dictLogisticVo 物流字典
     */
    @PostMapping(DICT_LOGISTIC_ADD)
    void add(@PathVariable DictLogisticCategory category, @RequestBody DictLogisticVo dictLogisticVo);

    /**
     * 更新
     *
     * @param dictLogisticVo 物流字典
     */
    @PostMapping(DICT_LOGISTIC_UPDATE)
    void update(@RequestBody DictLogisticVo dictLogisticVo);

    /**
     * 删除
     *
     * @param id 物流字典id
     */
    @GetMapping(DICT_LOGISTIC_DELETE_BY_ID)
    void deleteById(@PathVariable Long id);

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    @PostMapping(DICT_LOGISTIC_LIST_PAGE)
    PageResult<DictLogisticVo> listPage(@PathVariable DictLogisticCategory category, @RequestBody DictLogisticSearchVo searchVo);
}

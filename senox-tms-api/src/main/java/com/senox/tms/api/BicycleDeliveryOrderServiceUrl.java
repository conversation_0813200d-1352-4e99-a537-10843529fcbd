package com.senox.tms.api;

/**
 * <AUTHOR>
 * @date 2023/9/21 13:35
 */
public class BicycleDeliveryOrderServiceUrl {

    private BicycleDeliveryOrderServiceUrl(){
    }

    public static final String DELIVERY_ORDER_ADD = "/bicycle/delivery/order/add";
    public static final String DELIVERY_ORDER_UPDATE_GOODS_DETAIL = "/bicycle/delivery/order/update/goodsDetail";
    public static final String DELIVERY_ORDER_UPDATE_PART_GOODS_DETAIL = "/bicycle/delivery/order/update/part/goodsDetail";
    public static final String ORDER_ADD_OTHER_CHARGE = "/bicycle/delivery/order/addOtherCharge";
    public static final String ORDER_ADD_CHARGE = "/bicycle/delivery/order/addCharge";
    public static final String DELIVERY_ORDER_GET = "/bicycle/delivery/order/get/{id}";
    public static final String DELIVERY_ORDER_DETAIL_GET = "/bicycle/delivery/order/detail/get/{id}";
    public static final String DELIVERY_ORDER_DETAIL_GETBYORDERSERIALNO = "/bicycle/delivery/order/detail/getByOrderSerialNo/{orderSerialNo}";
    public static final String DELIVERY_ORDER_APPOINT = "/bicycle/delivery/order/appoint";
    public static final String DELIVERY_ORDER_CANCEL = "/bicycle/delivery/order/cancel/{id}";
    public static final String DELIVERY_ORDER_JOB_ADD = "/bicycle/delivery/order/job/add";
    public static final String DELIVERY_ORDER_JOB_BATCH_ADD = "/bicycle/delivery/order/job/batchAdd";
    public static final String DELIVERY_ORDER_COUNT = "/bicycle/delivery/order/count/{riderId}";
    public static final String DELIVERY_ORDER_RIDER_COUNT = "/bicycle/delivery/order/rider/count";
    public static final String DELIVERY_ORDER_DAY_COUNT = "/bicycle/delivery/order/day/count";
    public static final String DELIVERY_ORDER_RIDER_STATISTICS = "/bicycle/delivery/order/rider/statistics";
    public static final String DELIVERY_ORDER_STATISTICS = "/bicycle/delivery/order/statistics";
    public static final String DELIVERY_ORDER_MERGED = "/bicycle/delivery/order/merged";
    public static final String DELIVERY_ORDER_CANCEL_MERGED = "/bicycle/delivery/order/cancel/merged";
    public static final String DELIVERY_ORDER_UNDO_COUNT = "/bicycle/delivery/order/undoCount/{riderId}";
    public static final String DELIVERY_ORDER_RIDER_COUNT_LIST = "/bicycle/delivery/order/rider/count/list";
    public static final String DELIVERY_ORDER_RIDER_COUNT_SUM = "/bicycle/delivery/order/rider/count/sum";
    public static final String DELIVERY_ORDER_RIDER_HISTORY_COUNT_LIST = "/bicycle/delivery/order/rider/history/count/list";
    public static final String DELIVERY_ORDER_RIDER_HISTORY_COUNT_SUM = "/bicycle/delivery/order/rider/history/count/sum";
    public static final String DELIVERY_ORDER_DETAIL_LIST = "/bicycle/delivery/order/detail/list";
    public static final String DELIVERY_ORDER_INFO_PAGE = "/bicycle/delivery/order/info/page";
    public static final String DELIVERY_ORDER_ORDER_DELIVERY_DETAIL_INFO = "/bicycle/delivery/order/deliveryDetailInfo";

}

package com.senox.tms.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

import static com.senox.tms.api.TmsServiceUrl.SERVICE_NAME;

/**
 * <AUTHOR>
 * @date 2023/12/4 14:28
 */
@FeignClient(SERVICE_NAME)
public interface LogisticOrderClient {

    /**
     * 添加物流单
     * @param order
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_ORDER_ADD)
    Long addLogisticOrder(@RequestBody LogisticOrderVo order);

    /**
     * 更新物流单
     * @param order
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_ORDER_UPDATE)
    void updateLogisticOrder(@RequestBody LogisticOrderVo order);

    /**
     * 批量添加物流单
     * @param list
     * @param overwrite
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_ORDER_SAVE_BATCH)
    void saveLogisticOrderBatch(@RequestBody List<LogisticOrderVo> list, @RequestParam(required = false) Boolean overwrite);

    /**
     * 批量更新物流单信息
     * @param editInfo
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_ORDER_EDIT_BATCH)
    void editLogisticOrderBatch(@RequestBody LogisticOrderEditBatchVo editInfo);

    /**
     * 删除物流配送单
     * @param orderIds
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_ORDER_DELETE)
    void deleteLogisticOrder(@RequestBody List<Long> orderIds);

    /**
     * 批量更新物流单信息
     * @param search
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_ORDER_SHIP_DISCOUNT_CAL)
    BigDecimal calShipDiscount(@RequestBody ShipOrderDiscountSearchVo search);

    /**
     * 获取物流单详情
     * @param id
     * @return
     */
    @GetMapping(TmsServiceUrl.LOGISTIC_ORDER_GET)
    LogisticOrderVo findLogisticOrderById(@PathVariable Long id);

    /**
     * 物流单合计
     * @param search
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_ORDER_SUM)
    LogisticOrderVo sumLogisticOrder(@RequestBody LogisticOrderSearchVo search);

    /**
     * 物流单列表（无分页）
     * @param search
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_ORDER_LIST)
    List<LogisticOrderVo> listLogisticOrder(@RequestBody LogisticOrderSearchVo search);

    /**
     * 物流单列表
     * @param search
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_ORDER_PAGE)
    PageResult<LogisticOrderVo> listLogisticOrderPage(@RequestBody LogisticOrderSearchVo search);

    /**
     * 添加物流客户应付账单
     * @param payoff
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_PAYOFF_ADD)
    Long addPayoff(@RequestBody LogisticPayoffVo payoff);

    /**
     * 更新物流客户应付账单
     * @param payoff
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_PAYOFF_UPDATE)
    void updatePayoff(@RequestBody LogisticPayoffVo payoff);

    /**
     * 删除应付账单
     * @param ids
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_PAYOFF_DELETE)
    void deletePayoff(@RequestBody List<Long> ids);

    /**
     * 生成物流客户应付账单
     * @param generate
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_PAYOFF_GENERATE)
    void generatePayoff(@RequestBody LogisticPayoffGenerateVo generate);

    /**
     * 获取物流客户应付账单
     * @param id
     * @return
     */
    @GetMapping(TmsServiceUrl.LOGISTIC_PAYOFF_GET)
    LogisticPayoffVo findPayoffById(@PathVariable Long id);

    /**
     * 获取物流客户应付账单合计
     * @param search
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_PAYOFF_SUM)
    LogisticPayoffVo sumPayoff(@RequestBody LogisticPayoffSearchVo search);

    /**
     * 物流客户应付账单列表
     * @param search
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_PAYOFF_LIST)
    List<LogisticPayoffVo> listPayoff(@RequestBody LogisticPayoffSearchVo search);

    /**
     * 物流客户应付账单页
     * @param search
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_PAYOFF_PAGE)
    PageResult<LogisticPayoffVo> listPayoffPage(@RequestBody LogisticPayoffSearchVo search);
}

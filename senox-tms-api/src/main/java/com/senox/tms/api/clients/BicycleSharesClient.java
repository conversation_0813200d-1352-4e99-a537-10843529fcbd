package com.senox.tms.api.clients;


import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.tms.vo.BicycleSharesSearchVo;
import com.senox.tms.vo.BicycleSharesVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import static com.senox.tms.api.BicycleSharesServiceUrl.*;
import static com.senox.tms.api.TmsServiceUrl.SERVICE_NAME;

/**
 * <AUTHOR>
 * @date 2023-9-18
 */
@FeignClient(SERVICE_NAME)
public interface BicycleSharesClient {

    /**
     * 添加佣金
     *
     * @param sharesVo 佣金
     */
    @PostMapping(BICYCLE_SHARES_ADD)
    void add(@Validated({Add.class}) @RequestBody BicycleSharesVo sharesVo);

    /**
     * 删除佣金
     *
     * @param id 佣金id
     */
    @GetMapping(BICYCLE_SHARES_DELETE)
    void delete(@PathVariable Long id);

    /**
     * 修改收费标准
     *
     * @param sharesVo 佣金
     */
    @PostMapping(BICYCLE_SHARES_UPDATE)
    void update(@Validated({Update.class})@RequestBody BicycleSharesVo sharesVo);

    /**
     * 佣金列表
     *
     * @param searchVo 佣金
     * @return 分页结果
     */
    @PostMapping(BICYCLE_SHARES_LIST)
    PageResult<BicycleSharesVo> listPage(@RequestBody BicycleSharesSearchVo searchVo);
}

package com.senox.tms.api.clients;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.tms.vo.BicycleChargesDetailVo;
import com.senox.tms.vo.BicycleChargesSearchVo;
import com.senox.tms.vo.BicycleChargesVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.tms.api.BicycleChargesServiceUrl.*;
import static com.senox.tms.api.TmsServiceUrl.SERVICE_NAME;

/**
 * <AUTHOR>
 * @date 2023-9-18
 */
@FeignClient(SERVICE_NAME)
public interface BicycleChargesClient {

    /**
     * 添加收费标准
     *
     * @param chargesVo 收费标准
     */
    @PostMapping(BICYCLE_CHARGES_ADD)
    void addCharges(@Validated({Add.class}) @RequestBody BicycleChargesVo chargesVo);

    /**
     * 根据id查询收费标准
     *
     * @param id 收费标准id
     */
    @GetMapping(BICYCLE_CHARGES_GET)
    BicycleChargesVo getChargesById(@PathVariable Long id);


    /**
     * 删除收费标准
     *
     * @param id 收费标准id
     */
    @GetMapping(BICYCLE_CHARGES_DELETE)
    void deleteCharges(@PathVariable Long id);

    /**
     * 修改收费标准
     *
     * @param chargesVo 收费标准
     */
    @PostMapping(BICYCLE_CHARGES_UPDATE)
    void updateCharges(@Validated({Update.class}) @RequestBody BicycleChargesVo chargesVo);

    /**
     * 收费标准分页列表
     *
     * @param searchVo 查询参数
     * @return 分页结果
     */
    @PostMapping(BICYCLE_CHARGES_LIST)
    PageResult<BicycleChargesVo> listPage(@RequestBody BicycleChargesSearchVo searchVo);

    /**
     * 添加收费标准明细
     *
     * @param chargesDetailVo 收费标准明细
     */
    @PostMapping(BICYCLE_CHARGES_DETAIL_ADD)
    void addChargesDetail(@Validated({Add.class}) @RequestBody BicycleChargesDetailVo chargesDetailVo);

    /**
     * 修改收费标准明细
     *
     * @param chargesDetailVo 收费标准明细
     */
    @PostMapping(BICYCLE_CHARGES_DETAIL_UPDATE)
    void updateChargesDetail(@Validated({Update.class}) @RequestBody BicycleChargesDetailVo chargesDetailVo);

    /**
     * 删除收费标准明细
     *
     * @param chargesDetailIds 要删除明细id集
     */
    @PostMapping(BICYCLE_CHARGES_DETAIL_DELETE)
    void deleteChargesDetail(@RequestBody List<Long> chargesDetailIds);

    /**
     * 根据收费标准查询明细列表
     *
     * @param chargesId 收费标准id
     * @return 查询到的结果列表
     */
    @GetMapping(BICYCLE_CHARGES_DETAIL_LIST)
    List<BicycleChargesDetailVo> listDetailByCharges(@PathVariable Long chargesId);

    /**
     * 获取当前默认收费标准
     * @return
     */
    @GetMapping(BICYCLE_CHARGES_CURRENT_CHARGES)
    BicycleChargesVo getCurrentEffectiveCharges();
}

package com.senox.tms.api.clients;

import com.senox.tms.api.BicyclePayoffChargesServiceUrl;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.BicyclePayoffChargesVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2024-4-2
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicyclePayoffChargesClient {

    /**
     * 更新
     *
     * @param chargesVo 费用
     */
    @PostMapping(BicyclePayoffChargesServiceUrl.PAYOFF_CHARGES_UPDATE)
    void update(@RequestBody BicyclePayoffChargesVo chargesVo);

    /**
     * 查找
     */
    @GetMapping(BicyclePayoffChargesServiceUrl.PAYOFF_CHARGES_FIND)
    BicyclePayoffChargesVo find();
}

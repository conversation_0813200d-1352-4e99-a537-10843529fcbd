package com.senox.tms.api.clients;

import com.senox.common.validation.groups.Update;
import com.senox.tms.api.LogisticStatisticsServiceUrl;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.LogisticStatisticsDayReportBatchUpdateVo;
import com.senox.tms.vo.LogisticStatisticsDayReportPageResult;
import com.senox.tms.vo.LogisticStatisticsDayReportSearchVo;
import com.senox.tms.vo.LogisticStatisticsDayReportVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 15:38
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface LogisticStatisticsClient {

    /**
     * 批量添加货物统计报表
     * @param reportVoList
     */
    @PostMapping(LogisticStatisticsServiceUrl.LOGISTIC_STATISTICS_BATCH_ADD)
    void batchAddLogisticStatisticsDayReport(@RequestBody List<LogisticStatisticsDayReportVo> reportVoList);

    /**
     * 添加货物统计报表
     * @param logisticStatisticsDayReportVo
     * @return
     */
    @PostMapping(LogisticStatisticsServiceUrl.LOGISTIC_STATISTICS_ADD)
    Long addLogisticStatisticsDayReport(@RequestBody LogisticStatisticsDayReportVo logisticStatisticsDayReportVo);

    /**
     * 更新货物统计报表
     * @param logisticStatisticsDayReportVo
     */
    @PostMapping(LogisticStatisticsServiceUrl.LOGISTIC_STATISTICS_UPDATE)
    void updateLogisticStatisticsDayReport(@Validated({Update.class}) @RequestBody LogisticStatisticsDayReportVo logisticStatisticsDayReportVo);

    /**
     * 根据Id获取货物统计报表
     * @param id
     * @return
     */
    @GetMapping(LogisticStatisticsServiceUrl.LOGISTIC_STATISTICS_GET)
    LogisticStatisticsDayReportVo findLogisticStatisticsDayReportById(@PathVariable Long id);

    /**
     * 根据Id删除货物统计报表
     * @param id
     */
    @PostMapping(LogisticStatisticsServiceUrl.LOGISTIC_STATISTICS_DELETE)
    void deleteLogisticStatisticsDayReportById(@PathVariable Long id);

    /**
     * 货物统计报表分页
     * @param searchVo
     * @return
     */
    @PostMapping(LogisticStatisticsServiceUrl.LOGISTIC_STATISTICS_PAGE)
    LogisticStatisticsDayReportPageResult<LogisticStatisticsDayReportVo> page(@RequestBody LogisticStatisticsDayReportSearchVo searchVo);

    /**
     * 货物统计批量收款
     * @param batchUpdateVo
     */
    @PostMapping(LogisticStatisticsServiceUrl.LOGISTIC_STATISTICS_BATCH_UPDATE)
    void batchUpdate(@RequestBody LogisticStatisticsDayReportBatchUpdateVo batchUpdateVo);
}

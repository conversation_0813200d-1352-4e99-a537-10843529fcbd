package com.senox.tms.api.clients;

import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.BicycleTotalPageResult;
import com.senox.tms.vo.LogisticsDailyOrderDeliverySearchVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryTotalAmountVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.tms.api.LogisticsDailyOrderDeliveryUrl.*;


/**
 * <AUTHOR>
 * @date 2023-12-26
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface LogisticsDailyOrderDeliveryClient {

    /**
     * 批量添加
     *
     * @param dailyOrderDeliveryVos 物流日订单配送列表
     */
    @PostMapping(LOGISTICS_DAILY_ORDER_DELIVERY_BATCH_ADD)
    void addBatch(@RequestBody List<LogisticsDailyOrderDeliveryVo> dailyOrderDeliveryVos);

    /**
     * 根据id查找配送
     *
     * @param id id
     * @return 返回查找到的配送
     */
    @GetMapping(LOGISTICS_DAILY_ORDER_DELIVERY_FIND_BY_ID)
    LogisticsDailyOrderDeliveryVo findById(@PathVariable Long id);

    /**
     * 修改
     *
     * @param dailyOrderDeliveryVo 物流日订单配送
     */
    @PostMapping(LOGISTICS_DAILY_ORDER_DELIVERY_UPDATE)
    void update(@RequestBody LogisticsDailyOrderDeliveryVo dailyOrderDeliveryVo);

    /**
     * 删除
     *
     * @param id 物流日订单配送id
     */
    @GetMapping(LOGISTICS_DAILY_ORDER_DELIVERY_DELETE_BY_ID)
    void deleteById(@PathVariable Long id);

    /**
     * 列表查询
     *
     * @param searchVo 查询
     * @return 返回列表
     */
    @PostMapping(LOGISTICS_DAILY_ORDER_DELIVERY_LIST)
    List<LogisticsDailyOrderDeliveryVo> list(@RequestBody LogisticsDailyOrderDeliverySearchVo searchVo);

    /**
     * 列表分页查询
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    @PostMapping(LOGISTICS_DAILY_ORDER_DELIVERY_LIST_PAGE)
    PageStatisticsResult<LogisticsDailyOrderDeliveryVo, LogisticsDailyOrderDeliveryTotalAmountVo> listPage(@RequestBody LogisticsDailyOrderDeliverySearchVo searchVo);

}

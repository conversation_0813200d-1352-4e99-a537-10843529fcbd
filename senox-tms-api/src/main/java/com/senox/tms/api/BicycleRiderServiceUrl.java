package com.senox.tms.api;

/**
 * <AUTHOR>
 * @date 2023/9/15 11:51
 */
public class BicycleRiderServiceUrl {

    private BicycleRiderServiceUrl(){
    }

    public static final String RIDER_ADD = "/bicycle/rider/add";
    public static final String RIDER_UPDATE = "/bicycle/rider/update";
    public static final String RIDER_GENERATE_REFERRAL_CODE = "/bicycle/rider/generate/referralCode/{id}";
    public static final String RIDER_PASSWORD_MODIFY = "/bicycle/rider/password/modify";
    public static final String RIDER_GET = "/bicycle/rider/get/{id}";
    public static final String RIDER_PASSWORD_CHECK = "/bicycle/rider/password/check";
    public static final String RIDER_DELETE = "/bicycle/rider/delete/{id}";
    public static final String RIDER_LIST = "/bicycle/rider/list";
    public static final String RIDER_ATTENDANCE_ONLINE = "/bicycle/rider/attendance/online/{riderId}";
    public static final String RIDER_ATTENDANCE_OFFLINE = "/bicycle/rider/attendance/offline/{riderId}";
    public static final String RIDER_ATTENDANCE_GET = "/bicycle/rider/attendance/get/{id}";
    public static final String RIDER_ATTENDANCE_LIST = "/bicycle/rider/attendance/list";
    public static final String RIDER_ATTENDANCE_LIST_INFO = "/bicycle/rider/list/info";
}

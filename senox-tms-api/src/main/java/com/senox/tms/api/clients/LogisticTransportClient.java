package com.senox.tms.api.clients;

import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.tms.api.LogisticTransportServiceUrl.*;

/**
 * <AUTHOR>
 * @date 2025-05-27
 **/
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface LogisticTransportClient {

    /**
     * 添加订单
     *
     * @param orders 订单集
     */
    @PostMapping(LOGISTIC_TRANSPORT_ORDER_ADD)
    void addOrder(@RequestBody List<LogisticTransportOrderVo> orders);

    /**
     * 更新订单
     *
     * @param orders 订单集
     */
    @PostMapping(LOGISTIC_TRANSPORT_ORDER_UPDATE)
    void updateOrder(@RequestBody List<LogisticTransportOrderVo> orders);

    /**
     * 订单审核
     *
     * @param orderAudit 订单审核参数
     */
    @PostMapping(LOGISTIC_TRANSPORT_ORDER_AUDIT)
    void auditOrder(@RequestBody LogisticTransportOrderAuditVo orderAudit);

    /**
     * 根据id查找订单
     *
     * @param orderId 订单id
     * @return 返回订单
     */
    @GetMapping(LOGISTIC_TRANSPORT_ORDER_FIND_BY_ID)
    LogisticTransportOrderVo findOrderById(@PathVariable Long orderId);

    /**
     * 根据编号查找订单
     *
     * @param orderSerialNo 订单编号
     * @return 返回订单
     */
    @GetMapping(LOGISTIC_TRANSPORT_ORDER_FIND_BY_SERIAL_NO)
    LogisticTransportOrderVo findOrderBySerialNo(@PathVariable String orderSerialNo);

    /**
     * 根据编号删除订单
     *
     * @param orderSerialNo 订单编号
     */
    @GetMapping(LOGISTIC_TRANSPORT_ORDER_DELETE_BY_SERIAL_NO)
    void deleteOrderBySerialNo(@PathVariable String orderSerialNo);

    /**
     * 订单列表
     *
     * @param search 查询参数
     * @return 返回列表
     */
    @PostMapping(LOGISTIC_TRANSPORT_ORDER_LIST)
    List<LogisticTransportOrderVo> orderList(@RequestBody LogisticTransportOrderSearchVo search);

    /**
     * 订单分页列表
     *
     * @param search 查询参数
     * @return 返回分页列表
     */
    @PostMapping(LOGISTIC_TRANSPORT_ORDER_LIST_PAGE)
    PageResult<LogisticTransportOrderVo> orderPageList(@RequestBody LogisticTransportOrderSearchVo search);

    /**
     * 订单统计
     *
     * @return 返回订单统计
     */
    @PostMapping(LOGISTIC_TRANSPORT_ORDER_STATISTICS)
    LogisticTransportOrderStatisticsVo orderStatistics(@RequestBody LogisticTransportOrderSearchVo search);

    /**
     * 订单数量
     *
     * @param search 查询参数
     * @return 返回订单数量
     */
    @PostMapping(LOGISTIC_TRANSPORT_ORDER_COUNT)
    Integer orderCount(@RequestBody LogisticTransportOrderSearchVo search);

    /**
     * 生成账单结算单
     *
     * @param billGenerate 账单生成信息
     * @return 返回结算单id
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_GENERATE_SETTLEMENT)
    List<Long> generateBillSettlement(@RequestBody LogisticTransportBillGenerateVo billGenerate);

    /**
     * 账单列表
     *
     * @param search 查询参数
     * @return 返回账单列表
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_LIST)
    List<LogisticTransportBillVo> billList(@RequestBody LogisticTransportBillSearchVo search);

    /**
     * 根据账单id获取账单列表
     *
     * @param billIds 账单id集
     * @return 返回账单列表
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_LIST_BY_ID)
    List<LogisticTransportBillVo> billListById(@RequestBody List<Long> billIds);

    /**
     * 根据订单编号查询账单
     *
     * @param orderSerialNo 订单编号
     * @return 返回账单
     */
    @GetMapping(LOGISTIC_TRANSPORT_BILL_FIND_BY_ORDER_SERIAL_NO)
    LogisticTransportBillVo findByOrderSerialNo(@PathVariable String orderSerialNo);

    /**
     * 根据账单id删除账单
     *
     * @param billId 账单id
     */
    @GetMapping(LOGISTIC_TRANSPORT_BILL_DELETE_BY_ID)
    void billDeleteById(@PathVariable Long billId);

    /**
     * 账单分页列表
     *
     * @param search 查询参数
     * @return 返回账单分页列表
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_LIST_PAGE)
    PageResult<LogisticTransportBillVo> billPageList(@RequestBody LogisticTransportBillSearchVo search);

    /**
     * 账单结算列表
     *
     * @param search 查询参数
     * @return 返回账单结算列表
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_SETTLEMENT_LIST)
    List<LogisticTransportBillSettlementVo> billSettlementList(@RequestBody LogisticTransportBillSettlementSearchVo search);

    /**
     * 账单结算分页列表
     *
     * @param search 查询参数
     * @return 返回账单结算分页列表
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_SETTLEMENT_LIST_PAGE)
    PageResult<LogisticTransportBillSettlementVo> billSettlementPageList(@RequestBody LogisticTransportBillSettlementSearchVo search);

    /**
     * 账单结算统计
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_SETTLEMENT_COUNT)
    Integer billSettlementCount(@RequestBody LogisticTransportBillSettlementSearchVo search);

    /**
     * 结算单统计
     *
     * @param search 查询参数
     * @return 返回结算单统计
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_SETTLEMENT_STATISTICS)
    LogisticTransportBillSettlementStatisticsVo statisticsSettlement(@RequestBody LogisticTransportBillSettlementSearchVo search);

    /**
     * 结算单支付结果
     *
     * @param billPaid 账单支付信息
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_SETTLEMENT_PAID_UPDATE)
    void billSettlementStatus(@Validated @RequestBody BillPaidVo billPaid);

    /**
     * 结算单更新支付订单
     *
     * @param billPaid 账单支付信息
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_SETTLEMENT_PAID_ORDER_UPDATE)
    void billSettlementUpdatePaidOrder(@RequestBody BillPaidVo billPaid);

    /**
     * 结算单下发
     *
     * @param send 下发参数
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_SETTLEMENT_SEND)
    void billSettlementSend(@RequestBody LogisticTransportBillSettlementSendVo send);

    /**
     * 结算单删除
     *
     * @param ids id集
     */
    @PostMapping(LOGISTIC_TRANSPORT_BILL_SETTLEMENT_DELETE_BY_ID)
    void billSettlementDeleteById(@RequestBody List<Long> ids);

    /**
     * 结算单详情
     *
     * @param settlementId 结算单id
     * @return 返回结算单详情
     */
    @GetMapping(LOGISTIC_TRANSPORT_BILL_SETTLEMENT_DETAIL)
    LogisticTransportBillSettlementDetailVo billSettlementDetail(@PathVariable Long settlementId);

    /**
     * 根据id查找结算单
     *
     * @param settlementId 结算单id
     * @return 返回查找到的结算单
     */
    @GetMapping(LOGISTIC_TRANSPORT_BILL_SETTLEMENT_FIND_BY_ID)
    LogisticTransportBillSettlementVo billSettlementFindById(@PathVariable Long settlementId);


}

package com.senox.tms.api.clients;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.BicycleOrderServiceUrl;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.dto.BicycleOrderImportDto;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/9/19 10:44
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicycleOrderClient {

    /**
     * 添加三轮车配送订单
     * @param orderVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_ADD)
    Long addBicycleOrder(@Validated(Add.class) @RequestBody BicycleOrderVo orderVo);

    /**
     * 批量添加三轮车配送订单
     * @param orderVoList
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_BATCH_ADD)
    List<String> batchAddBicycleOrder(@Validated(Add.class) @RequestBody List<BicycleOrderVo> orderVoList);

    /**
     * 更新配送订单状态
      * @param id
     */
    @GetMapping(BicycleOrderServiceUrl.ORDER_UPDATE_STATE)
    void updateBicycleOrderState(@PathVariable Long id, @RequestParam Integer state);

    /**
     * 根据id获取配送订单
     * @param id
     * @return
     */
    @GetMapping(BicycleOrderServiceUrl.ORDER_GET)
    BicycleOrderVo findOrderVoById(@PathVariable Long id, @RequestParam(required = false) Boolean containStatus);

    /**
     * 根据订单号查询订单
     * @param orderSerialNo
     * @return
     */
    @GetMapping(BicycleOrderServiceUrl.ORDER_BY_ORDER_SERIAL_NO)
    BicycleOrderVo findOrderVoByOrderSerialNo(@PathVariable String orderSerialNo);

    /**
     * 根据id删除配送订单
     * @param id
     */
    @GetMapping(BicycleOrderServiceUrl.ORDER_DELETE)
    void deleteBicycleOrder(@PathVariable Long id);

    /**
     * 添加附加费用
     * @param otherChargeVo
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_ADD_OTHER_CHARGE)
    void addBicycleOrderOtherCharge(@Validated(Add.class) @RequestBody BicycleOrderOtherChargeVo otherChargeVo);

    /**
     * 配送订单列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_LIST)
    PageResult<BicycleOrderVo> listOrder(@RequestBody BicycleOrderSearchVo searchVo);

    /**
     * 配送订单列表视图
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_LIST_VIEW)
    PageResult<BicycleOrderVo> listOrderView(@RequestBody BicycleOrderSearchVo searchVo);

    /**
     * 导出订单列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_EXPORT_LIST)
    List<BicycleOrderVo> exportOrderList(@RequestBody BicycleOrderSearchVo searchVo);

    /**
     * 配送订单合计
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_SUM)
    BicycleOrderVo sumOrder(@RequestBody BicycleOrderSearchVo searchVo);

    /**
     * 配送订单分页
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_PAGE)
    PageResult<BicycleOrderVo> page(@RequestBody BicycleOrderSearchVo searchVo);

    /**
     * 三轮车配送订单合计
     * @param search
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_DELIVERY_SUM)
    BicycleOrderVo sumDeliveryOrder(@RequestBody BicycleOrderSearchVo search);

    /**
     * 三轮车配送订单页
     * @param search
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_DELIVERY_PAGE)
    PageResult<BicycleOrderVo> listDeliveryPage(@RequestBody BicycleOrderSearchVo search);

    /**
     * 计算配送费用
     * @param orderVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_CALCULATE)
    BigDecimal calculateCharges(@Validated(Update.class) @RequestBody BicycleOrderVo orderVo, @RequestParam Boolean useChargesId);

    /**
     * 预估配送费用
     * @param orderVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_CALCULATE)
    BicycleOrderCalculateEstimateVo calculateEstimateCharges(@Validated(Update.class) @RequestBody BicycleOrderVo orderVo, @RequestParam Boolean useChargesId);

    /**
     * 运营分析
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_OPERATE_ANALYSIS)
    @Deprecated
    BicycleOperateAnalysisVo findOperateAnalysis();

    /**
     * 配送地点使用统计
     * @param isStart 查看终点还是起点
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_POINT_COUNT)
    List<BicyclePointCountVo> listPointCount(@RequestParam Boolean isStart);

    /**
     * 排行榜
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_RANKING)
    @Deprecated
    BicycleCountRankingVo countRanking();

    /**
     * 运营分析
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_OPERATE_ANALYSIS_STATISTICS)
    BicycleOperateAnalysisVo operateAnalysisStatistics(@RequestBody BicycleStatisticsSearchVo searchVo);

    /**
     * 排行榜
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_RANKING_STATISTICS)
    BicycleCountRankingVo rankingStatistics(@RequestBody BicycleStatisticsSearchVo searchVo);

    /**
     * 未处理的订单数量
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_UNDO_COUNT)
    Integer undoOrderCount();

    /**
     * 历史运营分析记录列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_ANALYSIS_LIST)
    PageResult<BicycleOperateAnalysisVo> listOperateAnalysis(@RequestBody BicycleOperateAnalysisSearchVo searchVo);

    /**
     * 历史运营分析记录合计
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_ANALYSIS_SUM)
    BicycleOperateAnalysisVo sumOperateAnalysis(@RequestBody  BicycleOperateAnalysisSearchVo searchVo);

    /**
     * 客户下单统计列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_CUSTOMER_COUNT_LIST)
    PageResult<BicycleCustomerCountVo> customerCountList(@RequestBody BicycleCustomerCountSearchVo searchVo);

    /**
     * 客户下单统计合计
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_CUSTOMER_COUNT_SUM)
    BicycleCustomerCountVo sumCustomerCount(@RequestBody BicycleCustomerCountSearchVo searchVo);

    /**
     * 查询订单统计数量情况
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_COUNT)
    BicycleOrderCountVo orderCount(@RequestBody BicycleOrderCountSearchVo searchVo);

    /**
     * 删除未生成结算单的订单
     * @param id
     */
    @GetMapping(BicycleOrderServiceUrl.ORDER_CANCEL)
    void cancelBicycleOrderById(@PathVariable Long id);

    /**
     * 批量删除未生成结算单的订单
     * @param ids
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_BATCH_CANCEL)
    void cancelBicycleOrderByIds(@RequestBody List<Long> ids);

    /**
     * 订单导入
     *
     * @param orderImportDtoList 订单导入列表
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_IMPORT)
    void orderImport(@RequestBody List<BicycleOrderImportDto> orderImportDtoList);

    /**
     * 根据id删除未配送的订单
     * @param id
     */
    @GetMapping(BicycleOrderServiceUrl.ORDER_DELETE_UNDELIVERED)
    void deleteUndeliveredBicycleOrder(@PathVariable Long id);

    /**
     * 取消订单
     * @param cancelVo
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_CANCEL_ORDER)
    void cancelBicycleOrder(@RequestBody BicycleOrderCancelVo cancelVo);

    /**
     * 客户每月下单统计
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_CUSTOMER_INFO_LIST)
    List<BicycleCustomerMonthInfoVo> customerMonthInfoList(@RequestBody BicycleCustomerMonthInfoSearchVo searchVo);


    /**
     * 三轮车配送订单数量V2
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_COUNT_ORDER_V2)
    int countOrderV2(@RequestBody BicycleOrderV2SearchVo searchVo);

    /**
     * 三轮车配送订单列表V2
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_LIST_ORDER_V2)
    List<BicycleOrderV2Vo> listOrderV2(@RequestBody BicycleOrderV2SearchVo searchVo);

    /**
     * 三轮车配送订单视图分页V2
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_PAGE_ORDER_VIEW_V2)
    PageResult<BicycleOrderV2Vo> pageOrderViewV2(@RequestBody BicycleOrderV2SearchVo searchVo);

    /**
     * 三轮车配送订单分页V2
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_PAGE_ORDER_V2)
    PageResult<BicycleDeliveryOrderV2Vo> pageOrderV2(@RequestBody BicycleOrderV2SearchVo searchVo);

    /**
     * 根据订单id查询货物信息
     * @param orderId
     * @return
     */
    @GetMapping(BicycleOrderServiceUrl.ORDER_GOODS_DETAIL)
    List<BicycleOrderGoodsDetailVo> goodsDetailByOrderId(@PathVariable Long orderId);

    /**
     * 查询订单统计数量情况V2
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_COUNT_V2)
    BicycleOrderCountVo orderCountV2(@RequestBody BicycleOrderCountSearchVo searchVo);

    /**
     * 三轮车配送订单合计V2
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderServiceUrl.ORDER_SUM_ORDER_V2)
    BicycleOrderV2Vo sumOrderV2(@RequestBody BicycleOrderV2SearchVo searchVo);
}

package com.senox.tms.api.clients;


import com.senox.common.vo.PageResult;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.BicycleChargesScheduledTaskSearchVo;
import com.senox.tms.vo.BicycleChargesScheduledTaskVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.tms.api.BicycleChargesScheduledTaskUrl.*;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicycleChargesScheduledTaskClient {

    /**
     * 添加
     *
     * @param task 任务
     */
    @PostMapping(BICYCLE_CHARGES_TASK_ADD)
    void add(@RequestBody BicycleChargesScheduledTaskVo task);

    /**
     * 更新
     *
     * @param task 任务
     */
    @PostMapping(BICYCLE_CHARGES_TASK_UPDATE)
    void update(@RequestBody BicycleChargesScheduledTaskVo task);

    /**
     * 删除
     *
     * @param taskId 任务id
     */
    @GetMapping(BICYCLE_CHARGES_TASK_DELETE)
    void delete(@PathVariable Long taskId);

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    @PostMapping(BICYCLE_CHARGES_TASK_LIST)
    List<BicycleChargesScheduledTaskVo> list(@RequestBody BicycleChargesScheduledTaskSearchVo searchVo);

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    @PostMapping(BICYCLE_CHARGES_TASK_LIST_PAGE)
    PageResult<BicycleChargesScheduledTaskVo> listPage(@RequestBody BicycleChargesScheduledTaskSearchVo searchVo);


    /**
     * 根据id查找计划任务
     * @param id id
     * @return 返回查找到的计划任务
     */
    @GetMapping(BICYCLE_CHARGES_TASK_FIND_BY_ID)
    BicycleChargesScheduledTaskVo find(@PathVariable Long id);
}

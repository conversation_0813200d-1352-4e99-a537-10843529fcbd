package com.senox.tms.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.api.UnloadingServiceUrl;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/11 8:27
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface UnloadingClient {

    /**
     * 批量添加字典
     * @param dictVos
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_DICT_BATCH_ADD)
    void batchAddDict(@RequestBody List<UnloadingDictVo> dictVos);

    /**
     * 添加字典
     * @param dictVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_DICT_ADD)
    void addDict(@RequestBody UnloadingDictVo dictVo);

    /**
     * 更新字典
     * @param dictVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_DICT_UPDATE)
    void updateDict(@RequestBody UnloadingDictVo dictVo);

    /**
     * 根据id查询字典
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_DICT_GET)
    UnloadingDictVo findById(@PathVariable Long id);

    /**
     * 根据id删除字典
     * @param id
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_DICT_DELETE)
    void deleteDict(@PathVariable Long id);

    /**
     * 字典分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_DICT_PAGE)
    PageResult<UnloadingDictVo> dictPageResult(@RequestBody UnloadingDictSearchVo searchVo);

    /**
     * 保存搬运工
     * @param workerVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_SAVE)
    void saveWorker(@RequestBody UnloadingWorkerVo workerVo);

    /**
     * 更新搬运工状态
     * @param id
     * @param status
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_UPDATE_STATUS)
    void updateWorkerStatus(@PathVariable Long id, @RequestParam Integer status);

    /**
     * 根据搬运工编号更新搬运工状态
     * @param workerNo
     * @param status
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_UPDATE_STATUS_BY_WORKER_NO)
    void updateWorkerStatusByWorkerNo(@RequestParam String workerNo, @RequestParam Integer status);

    /**
     * 只更新人脸
     * @param faceUrlVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_UPDATE_FACE_URL)
    void updateFaceUrl(@RequestBody UnloadingWorkerFaceUrlVo faceUrlVo);

    /**
     * 重新排序
     * @param id
     * @param targetId
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_RESET_ORDER_NUM)
    void resetOrderNum(@PathVariable Long id, @RequestParam Long targetId);

    /**
     * 指定位置排序
     * @param id
     * @param num
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_APPOINT_RESET_ORDER_NUM)
    void appointResetOrderNum(@PathVariable Long id, @RequestParam Integer num);

    /**
     * 搬运工排序置底
     * @param id
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_APPOINT_BOTTOM_UP)
    void bottomUp(@PathVariable Long id);

    /**
     * 根据id查询搬运工
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_GET)
    UnloadingWorkerVo findWorkById(@PathVariable Long id);

    /**
     * 搬运工请假
     * @param workerVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_LEAVE)
    void workerLeave(@RequestBody UnloadingWorkerVo workerVo);

    /**
     * 根据id删除搬运工
     * @param id
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_DELETE)
    void deleteWorker(@PathVariable Long id);

    /**
     * 搬运工总数
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_COUNT)
    int countWorker(@RequestBody UnloadingWorkerSearchVo searchVo);

    /**
     * 搬运工列表
      * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_LIST)
    List<UnloadingWorkerVo> listWorker(@RequestBody UnloadingWorkerSearchVo searchVo);

    /**
     * 搬运工分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_PAGE)
    PageResult<UnloadingWorkerVo> pageWorker(@RequestBody UnloadingWorkerSearchVo searchVo);

    /**
     * 搬运工考勤记录分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_ATTENDANCE_PAGE)
    PageResult<UnloadingAttendanceVo> pageAttendance(@RequestBody UnloadingAttendanceSearchVo searchVo);

    /**
     * 更新考勤记录备注
     * @param id
     * @param remark
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_ATTENDANCE_UPDATE_REMARK)
    void updateRemark(@PathVariable Long id, @RequestParam String remark);

    /**
     * 添加搬运工设备权限
     * @param accessVoList
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_ACCESS_SAVE)
    void addWorkerAccess(@RequestBody List<UnloadingWorkerAccessVo> accessVoList);

    /**
     * 根据id删除搬运工设备权限
     * @param id
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_ACCESS_DELETE)
    void deleteAccessById(@PathVariable Long id);

    /**
     * 根据Id获取搬运工设备权限
     * @param workerId
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_ACCESS_GET)
    List<UnloadingWorkerAccessVo> listAccessByWorkerId(@PathVariable Long workerId);

    /**
     * 搬运工顺序列表
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_SEQUENCE_LIST)
    List<UnloadingWorkerVo> listSequenceWorker(@RequestBody UnloadingWorkerSearchVo searchVo);


    /**
     * 根据id查询搬运工异常日志
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_LOG_GET)
    UnloadingWorkerLogVo findWorkerLogById(@PathVariable Long id);

    /**
     * 搬运工异常日志分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_LOG_PAGE)
    PageResult<UnloadingWorkerLogVo> pageWorkerLog(@RequestBody UnloadingWorkerLogSearchVo searchVo);

    /**
     * 批量添加排期计划
     * @param batchVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_SCHEDULE_BATCH_ADD)
    void batchAddSchedule(@RequestBody UnloadingNightScheduleBatchVo batchVo);

    /**
     * 添加排期计划
     * @param scheduleVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_SCHEDULE_ADD)
    void addSchedule(@RequestBody UnloadingNightScheduleVo scheduleVo);

    /**
     * 更新排期计划
     * @param scheduleVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_SCHEDULE_UPDATE)
    void updateSchedule(@RequestBody UnloadingNightScheduleVo scheduleVo);

    /**
     * 根据id查询排期计划
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_SCHEDULE_GET)
    UnloadingNightScheduleVo findScheduleById(@PathVariable Long id);

    /**
     * 根据日期获取排期计划
     * @param scheduleDate
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_SCHEDULE_FIND_BY_SCHEDULE_DATE)
    List<UnloadingNightScheduleVo> findByScheduleDate(@RequestParam @DateTimeFormat(pattern="yyyy-MM-d") LocalDate scheduleDate);

    /**
     * 根据id删除排期计划
     * @param id
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_SCHEDULE_DELETE)
    void deleteScheduleById(@PathVariable Long id);

    /**
     * 根据日期删除排期计划
     * @param scheduleDate
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_WORKER_SCHEDULE_DELETE_BY_SCHEDULE_DATE)
    void deleteByScheduleDate(@RequestParam @DateTimeFormat(pattern="yyyy-MM-d") LocalDate scheduleDate);

    /**
     * 搬运工排期计划分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_WORKER_SCHEDULE_PAGE)
    PageResult<UnloadingNightScheduleVo> pageSchedule(@RequestBody UnloadingNightScheduleSearchVo searchVo);

    /**
     * 添加搬运订单
     * @param orderVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_ADD)
    void saveOrder(@RequestBody UnloadingOrderVo orderVo);

    /**
     * 删除搬运订单
     * @param id
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_DELETE)
    void deleteOrder(@PathVariable Long id);

    /**
     * 根据id搬运订单及详细
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_ORDER_GET)
    UnloadingOrderVo findDetailById(@PathVariable Long id);

    /**
     * 搬运订单分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_PAGE)
    PageResult<UnloadingOrderVo> pageResult(@RequestBody UnloadOrderSearchVo searchVo);

    /**
     * 搬运订单合计
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_SUM)
    UnloadingOrderVo sumOrder(@RequestBody UnloadOrderSearchVo searchVo);

    /**
     * 搬运订单详细分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_DETAIL_PAGE)
    PageResult<UnloadingOrderVo> pageDetailResult(@RequestBody UnloadOrderSearchVo searchVo);

    /**
     * 指定搬运工人数
     * @param orderId
     * @param workerNum
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_ORDER_APPOINT_WORKER_NUM)
    void appointWorkerNum(@PathVariable Long orderId, @RequestParam Integer workerNum);

    /**
     * 分派搬运工
     * @param orderVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_ASSIGN_WORKERS)
    void assignWorkers(@RequestBody UnloadingOrderVo orderVo);

    /**
     * 完成订单
     * @param orderId
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_ORDER_FINISH_ORDER)
    void finishOrder(@PathVariable Long orderId, @RequestParam BigDecimal amount);

    /**
     * 新增加急费
     * @param orderId
     * @param urgentAmount
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_ORDER_ADD_URGENT_AMOUNT)
    void addUrgentAmount(@PathVariable Long orderId, @RequestParam BigDecimal urgentAmount);

    /**
     * 取消订单
     * @param orderId
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_ORDER_CANCEL_ORDER)
    void cancelOrder(@PathVariable Long orderId);

    /**
     * 补录搬运订单
     * @param orderVo
     * @return 返回的订单号
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_SUPPLEMENT_ORDER)
    String supplementOrder(@RequestBody UnloadingOrderVo orderVo);

    /**
     * 订单顺序分派
     * @param assignVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_SEQUENCE_ASSIGN_WORKERS)
    void sequenceAssignWorkers(@RequestBody UnloadingOrderWorkersAssignVo assignVo);

    /**
     * 运营分析
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_OPERATE_ANALYSIS_STATISTIC)
    UnloadingOperateAnalysisVo operateAnalysisStatistics(@RequestBody UnloadingStatisticSearchVo searchVo);

    /**
     * 排行榜
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_RANKING_STATISTIC)
    UnloadingCountRankingVo rankingStatistics(@RequestBody UnloadingStatisticSearchVo searchVo);

    /**
     * 订单统计
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_ORDER_STATISTIC)
    List<UnloadingOrderCountVo> statistics(@RequestBody UnloadingStatisticSearchVo searchVo);

    /**
     * 添加分佣
     * @param sharesVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_SHARES_SAVE)
    void saveShares(@RequestBody UnloadingSharesVo sharesVo);

    /**
     * 根据id获取分佣
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_SHARES_GET)
    UnloadingSharesVo findSharesById(@PathVariable Long id);

    /**
     * 根据id删除分佣
     * @param id
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_SHARES_DELETE)
    void deleteSharesById(@PathVariable Long id);

    /**
     * 分佣分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_SHARES_PAGE)
    PageResult<UnloadingSharesVo> pageShares(@RequestBody UnloadingSharesSearchVo searchVo);

    /**
     * 生成应收账单
     * @param monthVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_BILL_GENERATE)
    void generateBill(@RequestBody UnloadingMonthVo monthVo);

    /**
     * 批量支付应收账单
     * @param ids
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_BILL_BATCH_PAY_BY_IDS)
    void batchPayByIds(@RequestBody List<Long> ids);

    /**
     * 支付应收账单
     * @param searchVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_BILL_BATCH_PAY)
    void batchPay(@RequestBody UnloadingOrderBillSearchVo searchVo);

    /**
     * 根据id查询应收账单
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_BILL_GET)
    UnloadingOrderBillVo findBillById(@PathVariable Long id);

    /**
     * 根据id删除应收账单
     * @param id
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_BILL_DELETE)
    void deleteBillById(@PathVariable Long id);

    /**
     * 应收账单分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_BILL_PAGE)
    PageResult<UnloadingOrderBillVo> pageBill(@RequestBody UnloadingOrderBillSearchVo searchVo);

    /**
     * 应收账单合计
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_BILL_SUM)
    UnloadingOrderBillVo sumBill(@RequestBody UnloadingOrderBillSearchVo searchVo);

    /**
     * 更新订单应收金额
     * @param orderNo
     * @param amount
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_BILL_UPDATE_ORDER_BILL)
    void updateOrderBill(@PathVariable String orderNo, @RequestParam BigDecimal amount);

    /**
     * 生成应付账单
     * @param monthVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_GENERATE)
    void generateOrderPayoff(@RequestBody UnloadingMonthVo monthVo);

    /**
     * 根据id查询应付记录
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_GET)
    UnloadingOrderPayoffVo findPayoffById(@PathVariable Long id);

    /**
     * 根据订单号删除应付记录
     * @param orderNo
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_DELETE)
    void deletePayoffByOrderNo(@RequestParam String orderNo);

    /**
     * 更新应付金额
     * @param payoffVoList
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_UPDATE_SHARES_AMOUNT)
    void updateSharesAmount(@RequestBody List<UnloadingOrderPayoffVo> payoffVoList);

    /**
     * 应付分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_PAGE)
    PageResult<UnloadingOrderPayoffVo> pagePayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo);

    /**
     * 应付合计
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_SUM)
    UnloadingOrderPayoffVo sumPayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo);

    /**
     * 根据订单编号查询应付记录
     * @param orderNo
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_FIND_BY_ORDER_NO)
    List<UnloadingOrderPayoffVo> listPayoffByOrderNo(@RequestParam String orderNo);

    /**
     * 搬运工应付日信息
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_DAILY_LIST)
    List<UnloadingOrderPayoffVo> payoffDailyList(@RequestBody UnloadingOrderPayoffSearchVo searchVo);

    /**
     * 根据应付月账单id查询应付记录
     * @param monthPayoffId
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_FIND_BY_MONTH_PAYOFF_ID)
    List<UnloadingOrderPayoffVo> findByMonthPayoffId(@PathVariable Long monthPayoffId);

    /**
     * 生成月应付账单
     * @param payoffVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_MONTH_GENERATE)
    void generateMonthPayoff(@RequestBody UnloadingMonthPayoffVo payoffVo);

    /**
     * 更新月应付账单备注
     * @param remarkVo
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_MONTH_UPDATE_REMARK)
    void updateRemark(@RequestBody UnloadingMonthPayoffRemarkVo remarkVo);

    /**
     * 根据id查询月应付账单
     * @param id
     * @return
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_MONTH_GET)
    UnloadingOrderMonthPayoffVo findMonthPayoffById(@PathVariable Long id);

    /**
     * 根据id删除月应付账单
     * @param id
     */
    @GetMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_MONTH_DELETE)
    void deleteMonthPayoff(@PathVariable Long id);

    /**
     * 月应付账单批量支付
     * @param ids
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_MONTH_BATCH)
    void batchMonthPayoffByIds(@RequestBody List<Long> ids);

    /**
     * 月应付账单分页
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_MONTH_PAGE)
    PageResult<UnloadingOrderMonthPayoffVo> pageMonthPayoff(@RequestBody UnloadingOrderMonthPayoffSearchVo searchVo);

    /**
     * 月应付账单合计
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_MONTH_SUM)
    UnloadingOrderMonthPayoffVo sumMonthPayoff(@RequestBody UnloadingOrderMonthPayoffSearchVo searchVo);

    /**
     * 查询搬运工情况
     * @param searchVo
     * @return
     */
    @PostMapping(UnloadingServiceUrl.UNLOADING_PAYOFF_WORKER_STATISTIC)
    UnloadingDayCountWorkerVo workerStatistics(@RequestBody UnloadingStatisticSearchVo searchVo);
}

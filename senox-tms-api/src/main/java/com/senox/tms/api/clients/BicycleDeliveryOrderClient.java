package com.senox.tms.api.clients;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.BicycleDeliveryOrderServiceUrl;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/9/21 13:42
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicycleDeliveryOrderClient {

    /**
     * 添加三轮车配送单
     * @param orderVo
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_ADD)
    Long addBicycleDeliveryOrder(@Validated(Add.class) @RequestBody BicycleDeliveryOrderVo orderVo);

    /**
     * 更新货物明细
     * @param goodsDetailVos
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_UPDATE_GOODS_DETAIL)
    void updateBicycleOrderDetail(@RequestBody List<BicycleOrderGoodsDetailVo> goodsDetailVos);

    /**
     * 更新部分货物明细
     * @param goodsDetailVos
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_UPDATE_PART_GOODS_DETAIL)
    void updatePartBicycleOrderDetail(@RequestBody List<BicycleOrderGoodsDetailVo> goodsDetailVos);

    /**
     * 添加附加费用
     * @param otherChargeVo
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.ORDER_ADD_OTHER_CHARGE)
    void addBicycleOrderOtherCharge(@Validated(Add.class) @RequestBody BicycleOrderOtherChargeVo otherChargeVo);

    /**
     * 添加费用
     * @param chargeVo
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.ORDER_ADD_CHARGE)
    void addBicycleOrderCharge(@Validated(Add.class) @RequestBody BicycleOrderChargeVo chargeVo);

    /**
     * 根据id获取配送单
     * @param id
     * @return
     */
    @GetMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_GET)
    BicycleDeliveryOrderVo findDeliveryOrderById(@PathVariable Long id);

    /**
     * 根据id获取配送详细单
     * @param id
     * @return
     */
    @GetMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_DETAIL_GET)
    BicycleDeliveryOrderDetailVo findDeliveryOrderDetailById(@PathVariable Long id);

    /**
     * 根据订单流水号获取配送详细单
     * @param orderSerialNo
     * @return
     */
    @GetMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_DETAIL_GETBYORDERSERIALNO)
    List<BicycleDeliveryOrderDetailVo> findDeliveryOrderDetailByOrderSerialNo(@PathVariable String orderSerialNo);

    /**
     * 指定骑手配送三轮车配送单
     * @param orderVo
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_APPOINT)
    void appointBicycleDeliveryOrderReider(@Validated(Update.class) @RequestBody BicycleDeliveryOrderVo orderVo);

    /**
     * 取消骑手配送
     * @param id
     */
    @GetMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_CANCEL)
    void cancelRiderDeliveryOrderById(@PathVariable Long id);

    /**
     * 添加三轮车配送单任务
     * @param orderJobVo
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_JOB_ADD)
    Long addBicycleDeliveryOrderJob(@Validated(Add.class) @RequestBody BicycleDeliveryOrderJobVo orderJobVo);

    /**
     * 批量添加三轮车配送单任务
     * @param orderJobVoList
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_JOB_BATCH_ADD)
    void batchAddBicycleDeliveryOrderJob(@Validated(Add.class) @RequestBody List<BicycleDeliveryOrderJobVo> orderJobVoList);

    /**
     * 查询骑手当日当月统计
     * @param riderId
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_COUNT)
    BicycleRiderCountVo countByRiderId(@PathVariable Long riderId);

    /**
     * 查询骑手情况
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_RIDER_COUNT)
    @Deprecated
    BicycleDayCountRiderVo dayCountRider();

    /**
     * 订单统计
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_DAY_COUNT)
    @Deprecated
    List<BicycleOrderCountVo> listOrderCount();

    /**
     * 查询骑手情况
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_RIDER_STATISTICS)
    BicycleDayCountRiderVo riderStatistics(@RequestBody BicycleStatisticsSearchVo searchVo);

    /**
     * 订单统计
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_STATISTICS)
    List<BicycleOrderCountVo> statistics(@RequestBody BicycleStatisticsSearchVo searchVo);

    /**
     * 合并配送单
     * @param orderSerialNoList
     * @param deliveryOrderSerialNo
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_MERGED)
    void merged(@RequestParam("orderSerialNoList") List<String> orderSerialNoList, @RequestParam(required = false) String deliveryOrderSerialNo);

    /**
     * 取消合并
     * @param orderSerialNoList
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_CANCEL_MERGED)
    void cancelMerged(@RequestParam("orderSerialNoList") List<String> orderSerialNoList);

    /**
     * 查询骑手未处理完的配送单
     * @param riderId
     * @return
     */
    @GetMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_UNDO_COUNT)
    Integer undoDeliveryOrderCount(@PathVariable Long riderId);

    /**
     * 查询骑手当日当月统计列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_RIDER_COUNT_LIST)
    PageResult<BicycleRiderCountVo> riderCountList(@RequestBody BicycleRiderCountSearchVo searchVo);

    /**
     * 骑手当日统计合计
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_RIDER_COUNT_SUM)
    BicycleRiderCountVo sumRiderCount(@RequestBody BicycleRiderCountSearchVo searchVo);

    /**
     * 骑手配送历史统计列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_RIDER_HISTORY_COUNT_LIST)
    PageResult<BicycleRiderCountVo> riderHistoryCountList(@RequestBody BicycleRiderCountSearchVo searchVo);

    /**
     * 骑手历史配送统计合计
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_RIDER_HISTORY_COUNT_SUM)
    BicycleRiderCountVo sumRiderHistoryCount(@RequestBody BicycleRiderCountSearchVo searchVo);

    /**
     * 配送订单详细列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_DETAIL_LIST)
    PageResult<BicycleDeliveryOrderDetailVo> listDeliveryDetail(@RequestBody BicycleDeliverySearchVo searchVo);


    /**
     * 配送单提货信息分页
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_INFO_PAGE)
    PageResult<BicycleDeliveryInfoVo> deliveryInfoPage(@RequestBody BicycleDeliveryInfoSearchVo searchVo);

    /**
     * 根据订单编号查询配送订单详细信息
     * @param orderSerialNo
     * @return
     */
    @GetMapping(BicycleDeliveryOrderServiceUrl.DELIVERY_ORDER_ORDER_DELIVERY_DETAIL_INFO)
    List<BicycleDeliveryDetailInfoVo> deliveryDetailInfoByOrderSerialNo(@RequestParam String orderSerialNo);
}

package com.senox.tms.api.clients;

import com.senox.common.validation.groups.Update;
import com.senox.tms.api.BicycleManagerServiceUrl;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.BicycleManagerVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * <AUTHOR>
 * @date 2023/10/27 10:52
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicycleManagerClient {


    /**
     * 更新三轮车管理员
     * @param bicycleManagerVo
     */
    @PostMapping(BicycleManagerServiceUrl.MANAGER_UPDATE)
    void updateBicycleManager(@Validated({Update.class}) @RequestBody BicycleManagerVo bicycleManagerVo);

    /**
     * 根据管理员id查询三轮车管理员
     * @param adminUserId
     * @return
     */
    @GetMapping(BicycleManagerServiceUrl.MANAGER_BY_ADMIN_USERID)
    BicycleManagerVo findByAdminUserId(@PathVariable Long adminUserId);
}

package com.senox.tms.api.clients;

import com.senox.common.vo.PageResult;
import com.senox.tms.api.BicycleBillServiceUrl;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.BicycleBillSearchVo;
import com.senox.tms.vo.BicycleBillVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22 15:32
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicycleBillClient {

    /**
     * 获取账单详情
     * @param id
     * @return
     */
    @GetMapping(BicycleBillServiceUrl.BILL_GET)
    BicycleBillVo getBill(@PathVariable Long id);

    /**
     * 根据id列表获取账单详情
     * @param ids
     * @return
     */
    @PostMapping(BicycleBillServiceUrl.BILL_LIST_BY_IDS)
    List<BicycleBillVo> listBillByIds(@RequestBody List<Long> ids);

    /**
     * 应收账单合计
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleBillServiceUrl.BILL_SUM)
    BicycleBillVo sumBill(@RequestBody BicycleBillSearchVo searchVo);

    /**
     * 应收账单列表页
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleBillServiceUrl.BILL_LIST)
    PageResult<BicycleBillVo> listBillPage(@RequestBody BicycleBillSearchVo searchVo);

}

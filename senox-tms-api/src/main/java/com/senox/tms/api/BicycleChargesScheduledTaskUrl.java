package com.senox.tms.api;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
@Getter
@Setter
public class BicycleChargesScheduledTaskUrl {
    private BicycleChargesScheduledTaskUrl() {
    }

    public static final String BICYCLE_CHARGES_TASK_ADD = "/bicycle/charges/task/add";
    public static final String BICYCLE_CHARGES_TASK_UPDATE = "/bicycle/charges/task/update";
    public static final String BICYCLE_CHARGES_TASK_DELETE = "/bicycle/charges/task/delete/{taskId}";
    public static final String BICYCLE_CHARGES_TASK_LIST = "/bicycle/charges/task/list";
    public static final String BICYCLE_CHARGES_TASK_LIST_PAGE = "/bicycle/charges/task/list/page";
    public static final String BICYCLE_CHARGES_TASK_FIND_BY_ID = "/bicycle/charges/task/find/{id}";
}

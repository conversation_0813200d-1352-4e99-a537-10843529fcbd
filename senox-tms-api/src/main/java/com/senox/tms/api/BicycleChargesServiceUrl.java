package com.senox.tms.api;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023-9-18
 */
@Getter
@Setter
public class BicycleChargesServiceUrl {
    private BicycleChargesServiceUrl() {
    }

    public static final String BICYCLE_CHARGES_ADD = "/bicycle/charges/add";
    public static final String BICYCLE_CHARGES_GET = "/bicycle/charges/get/{id}";
    public static final String BICYCLE_CHARGES_DELETE = "/bicycle/charges/delete/{id}";
    public static final String BICYCLE_CHARGES_UPDATE = "/bicycle/charges/update";
    public static final String BICYCLE_CHARGES_LIST = "/bicycle/charges/list";
    public static final String BICYCLE_CHARGES_DETAIL_ADD = "/bicycle/charges/detail/add";
    public static final String BICYCLE_CHARGES_DETAIL_UPDATE = "/bicycle/charges/detail/update";
    public static final String BICYCLE_CHARGES_DETAIL_DELETE = "/bicycle/charges/detail/delete";
    public static final String BICYCLE_CHARGES_DETAIL_LIST = "/bicycle/charges/detail/list/{chargesId}";
    public static final String BICYCLE_CHARGES_CURRENT_CHARGES = "/bicycle/charges/current/charges";
}

package com.senox.tms.api.clients;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.BicycleTotalPageResult;
import com.senox.tms.vo.LogisticLoaderSettlementFormVo;
import com.senox.tms.vo.LogisticLoaderSettlementSearchVo;
import com.senox.tms.vo.LogisticLoaderSettlementVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.tms.api.LogisticLoaderSettlementServiceUrl.*;

/**
 * <AUTHOR>
 * @date 2023-11-30
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface LogisticLoaderSettlementClient {

    /**
     * 添加
     *
     * @param settlementFormVo 结算表单
     */
    @PostMapping(LOGISTIC_LOADER_SETTLEMENT_ADD)
    void add(@Validated({Add.class}) @RequestBody LogisticLoaderSettlementFormVo settlementFormVo);

    /**
     * 更新
     *
     * @param settlementFormVo 结算表单
     */
    @PostMapping(LOGISTIC_LOADER_SETTLEMENT_UPDATE)
    void update(@Validated({Update.class}) @RequestBody LogisticLoaderSettlementFormVo settlementFormVo);

    /**
     * 根据id获取结算
     * @param id 结算id
     */
    @GetMapping(LOGISTIC_LOADER_SETTLEMENT_FIND_BY_ID)
    LogisticLoaderSettlementVo findById(@PathVariable Long id);

    /**
     * 删除
     *
     * @param id 结算id
     */
    @GetMapping(LOGISTIC_LOADER_SETTLEMENT_DELETE)
    void deleteById(@PathVariable Long id);

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回列表
     */
    @PostMapping(LOGISTIC_LOADER_SETTLEMENT_LIST)
    List<LogisticLoaderSettlementVo> list(@RequestBody LogisticLoaderSettlementSearchVo searchVo);

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    @PostMapping(LOGISTIC_LOADER_SETTLEMENT_LIST_PAGE)
    BicycleTotalPageResult<LogisticLoaderSettlementVo> listPage(@RequestBody LogisticLoaderSettlementSearchVo searchVo);
}

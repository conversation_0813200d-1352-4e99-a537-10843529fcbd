package com.senox.tms.api.clients;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.tms.api.BicyclePointServiceUrl;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.BicyclePointVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 11:41
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicyclePointClient {

    /**
     * 添加三轮车配送地点
     * @param bicyclePointVo
     * @return
     */
    @PostMapping(BicyclePointServiceUrl.POINT_ADD)
    Long addBicyclePoint(@Validated(Add.class) @RequestBody BicyclePointVo bicyclePointVo);

    /**
     * 修改三轮车配送地点
     * @param bicyclePointVo
     */
    @PostMapping(BicyclePointServiceUrl.POINT_UPDATE)
    void updateBicyclePoint(@Validated(Update.class) @RequestBody BicyclePointVo bicyclePointVo);

    /**
     * 删除三轮车配送地点
     * @param id
     */
    @GetMapping(BicyclePointServiceUrl.POINT_DELETE)
    void deleteBicyclePoint(@PathVariable Long id);

    /**
     * 根据id获取三轮车配送地点
     * @param id
     * @return
     */
    @GetMapping(BicyclePointServiceUrl.POINT_GET)
    BicyclePointVo findById(@PathVariable Long id);

    /**
     * 三轮车配送地点列表
     * @return
     */
    @PostMapping(BicyclePointServiceUrl.POINT_LIST)
    List<BicyclePointVo> listBicyclePoint();
}

package com.senox.tms.api.clients;

import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.tms.api.LogisticLoaderIncomeServiceUrl.*;

/**
 * <AUTHOR>
 * @date 2023-11-30
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface LogisticLoaderIncomeClient {

    /**
     * 生成日报表
     *
     * @param dateVo 时间
     */
    @PostMapping(LOGISTIC_LOADER_INCOME_REPORT_GENERATE)
    void generateDayReport(@RequestBody BicycleDateVo dateVo);

    /**
     * 分页报表
     *
     * @param searchVo 查询
     */
    @PostMapping(LOGISTIC_LOADER_INCOME_REPORT_LIST_PAGE)
    BicycleTotalPageResult<LogisticLoaderIncomeVo> listPageStatistics(@RequestBody LogisticLoaderIncomeSearchVo searchVo);

    /**
     * 统计报表
     *
     * @param searchVo 查询
     * @return 返回统计后的报表
     */
    @PostMapping(LOGISTIC_LOADER_INCOME_REPORT_STATISTICS)
    List<LogisticLoaderIncomeReportVo> reportStatistics(@RequestBody LogisticLoaderIncomeSearchVo searchVo);
}

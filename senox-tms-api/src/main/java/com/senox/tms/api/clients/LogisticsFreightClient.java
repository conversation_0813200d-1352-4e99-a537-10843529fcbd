package com.senox.tms.api.clients;

import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.LogisticFreightRemarkVo;
import com.senox.tms.vo.LogisticsFreightSearchVo;
import com.senox.tms.vo.LogisticsFreightStatisticsVo;
import com.senox.tms.vo.LogisticsFreightVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

import static com.senox.tms.api.LogisticsFreightUrl.*;

/**
 * <AUTHOR>
 * @date 2023-12-27
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface LogisticsFreightClient {

    /**
     * 添加
     *
     * @param freightVos 货运列表
     */
    @PostMapping(LOGISTICS_FREIGHT_ADD)
    void add(@RequestBody List<LogisticsFreightVo> freightVos);

    /**
     * 根据id查找
     * @param id id
     * @return 返回查找到的
     */
    @GetMapping(LOGISTICS_FREIGHT_FIND_BY_ID)
    LogisticsFreightVo findById(@PathVariable Long id);

    /**
     * 更新
     *
     * @param freightVo 货运
     */
    @PostMapping(LOGISTICS_FREIGHT_UPDATE)
    void update(@RequestBody LogisticsFreightVo freightVo);

    /**
     * 删除
     *
     * @param id 货运id
     */
    @GetMapping(LOGISTICS_FREIGHT_DELETE_BY_ID)
    void deleteById(@PathVariable Long id);

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回列表
     */
    @PostMapping(LOGISTICS_FREIGHT_LIST)
    List<LogisticsFreightVo> list(@RequestBody LogisticsFreightSearchVo searchVo);


    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页列表
     */
    @PostMapping(LOGISTICS_FREIGHT_LIST_PAGE)
    PageStatisticsResult<LogisticsFreightVo, LogisticsFreightStatisticsVo> listPage(@RequestBody LogisticsFreightSearchVo searchVo);

    /**
     * 根据id集合查询
     * @param ids
     * @return
     */
    @PostMapping(LOGISTICS_FREIGHT_LIST_BY_IDS)
    List<LogisticsFreightVo> listByIds(@RequestBody List<Long> ids);

    /**
     * 更新订单状态
     * @param billPaid
     */
    @PostMapping(LOGISTICS_FREIGHT_PAID_UPDATE)
    void updateBillStatus(@Validated @RequestBody BillPaidVo billPaid);

    /**
     * 更新账单备注
     * @param remarkVo
     */
    @PostMapping(LOGISTICS_FREIGHT_REMARK_UPDATE)
    void updateBillRemark(@Validated @RequestBody LogisticFreightRemarkVo remarkVo);
}

package com.senox.tms.api.clients;

import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

import static com.senox.tms.api.BicyclePayoffServiceUrl.*;

/**
 * <AUTHOR>
 * @date 2023/9/25 14:44
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicyclePayoffClient {

    /**
     * 更新账单状态
     * @param billPaid
     */
    @PostMapping(PAYOFF_PAID_UPDATE)
    void updatePayoffStatus(@Validated @RequestBody BillPaidVo billPaid, @RequestParam("payway") int payway);

    /**
     * 报表应付金额更新
     * @param id 报表id
     * @param amount 金额
     */
    @GetMapping(PAYOFF_REPORT_DAY_UPDATE_AMOUNT)
    void updateAmountFromReport(@PathVariable Long id, @PathVariable BigDecimal amount);

    /**
     * 应付账单合计
     * @param searchVo
     * @return
     */
    @PostMapping(PAYOFF_SUM)
    BicyclePayoffVo sumPayoff(@RequestBody BicyclePayoffSearchVo searchVo);

    /**
     * 应付账单列表页
     * @param searchVo
     * @return
     */
    @PostMapping(PAYOFF_LIST)
    PageResult<BicyclePayoffVo> listPayoff(@RequestBody BicyclePayoffSearchVo searchVo);

    /**
     * 生成日报表
     *
     * @param dateVo 日期
     */
    @PostMapping(PAYOFF_REPORT_DAY_GENERATE)
    void generateReportByDay(@RequestBody BicycleDateVo dateVo);

    /**
     * 生成月报表
     *
     * @param dateVo 日期
     */
    @PostMapping(PAYOFF_REPORT_MONTH_GENERATE)
    void generateReportByMonth(@RequestBody BicycleDateVo dateVo);

    /**
     * 日报表列表
     *
     * @param searchVo 查询参数
     * @return 返回分页列表
     */
    @PostMapping(PAYOFF_REPORT_DAY_LIST)
    BicycleTotalPageResult<BicyclePayoffReportVo> reportDayList(@RequestBody BicyclePayoffReportSearchVo searchVo);

    /**
     * 月报表列表
     *
     * @param searchVo 查询参数
     * @return 返回分页列表
     */
    @PostMapping(PAYOFF_REPORT_MONTH_LIST)
    BicycleTotalPageResult<BicyclePayoffReportVo> reportMonthList(@RequestBody BicyclePayoffReportSearchVo searchVo);
}

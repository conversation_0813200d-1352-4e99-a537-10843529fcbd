package com.senox.tms.api.clients;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.tms.api.BicycleSettingServiceUrl;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.BicycleSettingVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/25 10:06
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicycleSettingClient {

    /**
     * 添加三轮车配置信息
     * @param settingVo
     * @return
     */
    @PostMapping(BicycleSettingServiceUrl.BICYCLE_SETTING_ADD)
    Long addBicycleSetting(@Validated(Add.class) @RequestBody BicycleSettingVo settingVo);

    /**
     * 修改三轮车配置信息
     * @param settingVo
     */
    @PostMapping(BicycleSettingServiceUrl.BICYCLE_SETTING_UPDATE)
    void updateBicycleSetting(@Validated(Update.class) @RequestBody BicycleSettingVo settingVo);

    /**
     * 根据id获取三轮车配置信息
     * @param id
     * @return
     */
    @GetMapping(BicycleSettingServiceUrl.BICYCLE_SETTING_GET)
    BicycleSettingVo findById(@PathVariable Long id);

    /**
     * 根据id删除三轮车配置信息
     * @param id
     */
    @GetMapping(BicycleSettingServiceUrl.BICYCLE_SETTING_DELETE)
    void deleteById(@PathVariable Long id);

    /**
     * 三轮车配置信息列表
     * @return
     */
    @PostMapping(BicycleSettingServiceUrl.BICYCLE_SETTING_LIST)
    List<BicycleSettingVo> listBicycleSetting();

    /**
     * 获取设置是否启用
     * @param name
     * @return
     */
    @GetMapping(BicycleSettingServiceUrl.BICYCLE_SETTING_CHECK_ENABLE)
    boolean checkEnable(@RequestParam("name") String name);
}

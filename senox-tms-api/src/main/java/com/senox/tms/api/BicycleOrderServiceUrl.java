package com.senox.tms.api;

/**
 * <AUTHOR>
 * @date 2023/9/19 10:44
 */
public class BicycleOrderServiceUrl {

    private BicycleOrderServiceUrl() {
    }

    public static final String ORDER_ADD = "/bicycle/order/add";
    public static final String ORDER_BATCH_ADD = "/bicycle/order/batchAdd";
    public static final String ORDER_UPDATE_STATE = "/bicycle/order/updateState/{id}";
    public static final String ORDER_GET = "/bicycle/order/get/{id}";
    public static final String ORDER_BY_ORDER_SERIAL_NO = "/bicycle/order/byOrderSerialNo/{orderSerialNo}";
    public static final String ORDER_DELETE = "/bicycle/order/delete/{id}";
    public static final String ORDER_ADD_OTHER_CHARGE = "/bicycle/order/addOtherCharge";
    public static final String ORDER_LIST = "/bicycle/order/list";
    public static final String ORDER_LIST_VIEW = "/bicycle/order/list/view";
    public static final String ORDER_EXPORT_LIST = "/bicycle/order/exportList";
    public static final String ORDER_SUM= "/bicycle/order/sum";
    public static final String ORDER_PAGE = "/bicycle/order/page";
    public static final String ORDER_DELIVERY_SUM = "/bicycle/order/delivery/page";
    public static final String ORDER_DELIVERY_PAGE = "/bicycle/order/delivery/page";
    public static final String ORDER_CALCULATE = "/bicycle/order/calculate";
    public static final String ORDER_OPERATE_ANALYSIS = "/bicycle/order/operate/analysis";
    public static final String ORDER_POINT_COUNT = "/bicycle/order/point/count";
    public static final String ORDER_RANKING = "/bicycle/order/ranking";
    public static final String ORDER_OPERATE_ANALYSIS_STATISTICS = "/bicycle/order/operate/analysis/statistics";
    public static final String ORDER_RANKING_STATISTICS = "/bicycle/order/ranking/statistics";
    public static final String ORDER_UNDO_COUNT= "/bicycle/order/undoCount";
    public static final String ORDER_ANALYSIS_LIST = "/bicycle/order/analysis/list";
    public static final String ORDER_ANALYSIS_SUM = "/bicycle/order/analysis/sum";
    public static final String ORDER_CUSTOMER_COUNT_LIST = "/bicycle/order/customer/count/list";
    public static final String ORDER_CUSTOMER_COUNT_SUM = "/bicycle/order/customer/count/sum";
    public static final String ORDER_COUNT = "/bicycle/order/count";
    public static final String ORDER_CANCEL = "/bicycle/order/cancel/{id}";
    public static final String ORDER_BATCH_CANCEL = "/bicycle/order/cancel";
    public static final String ORDER_CANCEL_ORDER = "/bicycle/order/cancel/order";
    public static final String ORDER_IMPORT = "/bicycle/order/import";
    public static final String ORDER_DELETE_UNDELIVERED = "/bicycle/order/delete/undelivered/{id}";
    public static final String ORDER_CUSTOMER_INFO_LIST = "/bicycle/order/customer/info/list";


    public static final String ORDER_COUNT_ORDER_V2 = "/bicycle/order/count/order/v2";
    public static final String ORDER_LIST_ORDER_V2 = "/bicycle/order/list/order/v2";
    public static final String ORDER_PAGE_ORDER_VIEW_V2 = "/bicycle/order/page/order/view/v2";
    public static final String ORDER_PAGE_ORDER_V2 = "/bicycle/order/page/order/v2";
    public static final String ORDER_GOODS_DETAIL = "/bicycle/order/goodsDetail/{orderId}";
    public static final String ORDER_COUNT_V2= "/bicycle/order/count/v2";
    public static final String ORDER_SUM_ORDER_V2= "/bicycle/order/sum/order/v2";

}

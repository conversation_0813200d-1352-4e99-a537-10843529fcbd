package com.senox.tms.api.clients;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.BicycleRiderServiceUrl;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 11:51
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicycleRiderClient {

    /**
     * 添加三轮车配送骑手
     * @param bicycleRiderVo
     * @return
     */
    @PostMapping(BicycleRiderServiceUrl.RIDER_ADD)
    Long addBicycleRider(@Validated(Add.class) @RequestBody BicycleRiderVo bicycleRiderVo);

    /**
     * 修改三轮车配送骑手
     * @param bicycleRiderVo
     */
    @PostMapping(BicycleRiderServiceUrl.RIDER_UPDATE)
    void updateBicycleRider(@Validated(Update.class) @RequestBody BicycleRiderVo bicycleRiderVo);

    /**
     * 生成推荐码
     * @param id
     */
    @GetMapping(BicycleRiderServiceUrl.RIDER_GENERATE_REFERRAL_CODE)
    void generateReferralCode(@PathVariable Long id);

    /**
     * 修改三轮车配送骑手密码
     * @param pwdVo
     */
    @PostMapping(BicycleRiderServiceUrl.RIDER_PASSWORD_MODIFY)
    void modifyPassword(@Validated @RequestBody BicycleRiderChangerPwdVo pwdVo);

    /**
     * 根据id获取三轮车骑手
     * @param id
     * @return
     */
    @GetMapping(BicycleRiderServiceUrl.RIDER_GET)
    BicycleRiderVo findById(@PathVariable Long id);

    /**
     * 校验密码
     * @param loginVo
     * @return
     */
    @PostMapping(BicycleRiderServiceUrl.RIDER_PASSWORD_CHECK)
    BicycleRiderVo checkRiderByContactAndPassword(@Validated @RequestBody BicycleRiderLoginVo loginVo);

    /**
     * 删除三轮车配送骑手
     * @param id
     */
    @GetMapping(BicycleRiderServiceUrl.RIDER_DELETE)
    void deleteBicycleRider(@PathVariable Long id);

    /**
     * 三轮车配送骑手列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleRiderServiceUrl.RIDER_LIST)
    PageResult<BicycleRiderVo> list(@RequestBody BicycleRiderSearchVo searchVo);

    /**
     * 骑手上线
     * @param riderId
     * @return
     */
    @GetMapping(BicycleRiderServiceUrl.RIDER_ATTENDANCE_ONLINE)
    Long riderOnline(@PathVariable Long riderId);

    /**
     * 骑手下线
     * @param riderId
     */
    @GetMapping(BicycleRiderServiceUrl.RIDER_ATTENDANCE_OFFLINE)
    void riderOffline(@PathVariable Long riderId);

    /**
     * 根据id获取三轮车骑手考勤信息
     * @param id
     * @return
     */
    @GetMapping(BicycleRiderServiceUrl.RIDER_ATTENDANCE_GET)
    BicycleRiderAttendanceVo findAttendanceById(@PathVariable Long id);

    /**
     * 三轮车骑手考勤信息列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleRiderServiceUrl.RIDER_ATTENDANCE_LIST)
    PageResult<BicycleRiderAttendanceVo> listRiderAttendance(@RequestBody BicycleRiderAttendanceSearchVo searchVo);

    /**
     * 三轮车配送骑手信息列表
     * @return
     */
    @PostMapping(BicycleRiderServiceUrl.RIDER_ATTENDANCE_LIST_INFO)
    List<BicycleRiderInfoVo> listRider();
}

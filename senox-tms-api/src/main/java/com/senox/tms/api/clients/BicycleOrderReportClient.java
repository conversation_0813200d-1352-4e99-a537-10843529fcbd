package com.senox.tms.api.clients;

import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.tms.api.BicycleOrderReportServiceUrl;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/7 17:04
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface BicycleOrderReportClient {

    /**
     * 生成日报表
     * @param dateVo
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_DAY_GENERATE)
    void generateDayReport(@RequestBody BicycleReportDateVo dateVo);


    /**
     * 更新日报表
     * @param dayReportVo
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_DAY_UPDATE)
    void updateDayReport(@Validated(Update.class) @RequestBody BicycleOrderDayReportVo dayReportVo);

    /**
     * 根据id集合查询日报表
     * @param ids
     * @return
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_DAY_LIST_BYIDS)
    List<BicycleOrderDayReportVo> listDayReport(@RequestBody List<Long> ids);

    /**
     * 删除日报表
     * @param ids
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_DAY_DELETE)
    void deleteDayReport(@RequestBody List<Long> ids);

    /**
     * 获取日报表
     * @param id
     * @return
     */
    @GetMapping(BicycleOrderReportServiceUrl.REPORT_DAY_GET)
    BicycleOrderDayReportVo findDayReportById(@PathVariable Long id);

    /**
     * 日报表合计
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_DAY_SUM)
    BicycleOrderDayReportVo sumDayReport(@RequestBody BicycleOrderDayReportSearchVo searchVo);

    /**
     * 日报表列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_DAY_LIST)
    PageResult<BicycleOrderDayReportVo> listDayReport(@RequestBody BicycleOrderDayReportSearchVo searchVo);

    /**
     * 生成月报表
     * @param dateVo
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_MONTH_GENERATE)
    void generateMonthReport(@RequestBody BicycleReportDateVo dateVo);

    /**
     * 更新月报表
     * @param monthReportVo
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_MONTH_UPDATE)
    void updateMonthReport(@Validated(Update.class) @RequestBody BicycleOrderMonthReportVo monthReportVo);

    /**
     * 根据id集合查询月报表
     * @param ids
     * @return
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_MONTH_LIST_BYIDS)
    List<BicycleOrderMonthReportVo> listMonthReport(@RequestBody List<Long> ids);

    /**
     * 删除月报表
     * @param ids
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_MONTH_DELETE)
    void deleteMonthReport(@RequestBody List<Long> ids);

    /**
     * 获取月报表
     * @param id
     * @return
     */
    @GetMapping(BicycleOrderReportServiceUrl.REPORT_MONTH_GET)
    BicycleOrderMonthReportVo findMonthReportById(@PathVariable Long id);

    /**
     * 月报表明细
     * @param id
     * @return
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_MONTH_DAY_LIST)
    List<BicycleOrderDayReportVo> listMonthDayReport(@PathVariable Long id);

    /**
     * 月报表合计
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_MONTH_SUM)
    BicycleOrderMonthReportVo sumMonthReport(@RequestBody BicycleOrderMonthReportSearchVo searchVo);

    /**
     * 月报表列表
     * @param searchVo
     * @return
     */
    @PostMapping(BicycleOrderReportServiceUrl.REPORT_MONTH_LIST)
    PageResult<BicycleOrderMonthReportVo> listMonthReport(@RequestBody BicycleOrderMonthReportSearchVo searchVo);
}

package com.senox.tms.api.clients;

import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.api.TmsServiceUrl;
import com.senox.tms.vo.OutgoingTtlSearchVo;
import com.senox.tms.vo.OutgoingTtlVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8 13:39
 */
@FeignClient(TmsServiceUrl.SERVICE_NAME)
public interface OutgoingTtlClient {

    /**
     * 批量添加太太乐外发
     * @param outgoingTtlVos
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_OUTGOING_TTL_BATCH_ADD)
    void batchAddOutgoingTtl(@RequestBody List<OutgoingTtlVo> outgoingTtlVos);

    /**
     * 添加太太乐外发
     * @param outgoingTtlVo
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_OUTGOING_TTL_ADD)
    Long addOutgoingTtl(@RequestBody OutgoingTtlVo outgoingTtlVo);

    /**
     * 更新太太乐外发
     * @param outgoingTtlVo
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_OUTGOING_TTL_UPDATE)
    void updateOutgoingTtl(@RequestBody OutgoingTtlVo outgoingTtlVo);

    /**
     * 根据Id获取太太乐外发
     * @param id
     * @return
     */
    @GetMapping(TmsServiceUrl.LOGISTIC_OUTGOING_TTL_GET)
    OutgoingTtlVo findById(@PathVariable Long id);

    /**
     * 根据Id删除太太乐外发
     * @param id
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_OUTGOING_TTL_DELETE)
    void deleteById(@PathVariable Long id);

    /**
     * 太太乐外发分页
     * @param searchVo
     * @return
     */
    @PostMapping(TmsServiceUrl.LOGISTIC_OUTGOING_TTL_PAGE)
    PageStatisticsResult<OutgoingTtlVo, OutgoingTtlVo> pageResult(@RequestBody OutgoingTtlSearchVo searchVo);
}

package com.senox.tms.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-4-22
 */
@Getter
@Setter
public class BicycleOrderGoodsDetailImportDto {

    /**
     * 货物名
     */
    private String goodsName;

    /**
     * 货物类型 0其他，1重货，2抛货
     */
    private Integer goodsType;

    /**
     * 件数
     */
    private BigDecimal pieces;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 体积
     */
    private BigDecimal size;
}

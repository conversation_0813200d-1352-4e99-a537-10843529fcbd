package com.senox.tms.dto;

import com.senox.tms.vo.BicycleOrderGoodsDetailVo;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-4-22
 */
@Getter
@Setter
public class BicycleRiderImportDto {

    /**
     * 骑手id
     */
    private Long riderId;

    /**
     * 骑手编号
     */
    private String riderNo;

    /**
     * 完成件数
     */
    private BigDecimal completePieces;

    /**
     * 商品
     */
    private List<BicycleOrderGoodsDetailVo> goodsDetails;

}

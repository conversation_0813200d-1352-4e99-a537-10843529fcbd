package com.senox.tms.dto;

import com.senox.tms.vo.BicycleRiderAttendanceVo;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-12
 */
@Getter
@Setter
public class BicycleOrderImportDto {

    /**
     * 寄件人id
     */
    private Long senderId;

    /**
     * 寄件人编号
     */
    private String senderSerialNo;

    /**
     * 寄件人
     */
    private String sender;

    /**
     * 寄件人联系方式
     */
    private String senderContact;

    /**
     * 骑手集
     */
    private List<BicycleRiderImportDto> riders;

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 时间
     */
    private LocalTime time;

    /**
     * 起点id
     */
    private Long startPointId;

    /**
     * 起点地址
     */
    private String startPointAddress;

    /**
     * 起点详细地址
     */
    private String startPointDetailAddress;

    /**
     * 终点id
     */
    private Long endPointId;

    /**
     * 终点地址
     */
    private String endPointAddress;

    /**
     * 终点详细
     */
    private String endPointDetailAddress;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 品种
     */
    private String variety;

    /**
     * 件数
     */
    private BigDecimal pieces;

    /**
     * 所耗分钟
     */
    private Integer minutesConsumed;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 费用
     */
    private BigDecimal charge;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合并序号
     */
    private Integer mergeNumber;
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.BicycleOrderMonthReport;
import com.senox.tms.mapper.BicycleOrderMonthReportMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/17 9:14
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BicycleOrderMonthReportService extends ServiceImpl<BicycleOrderMonthReportMapper, BicycleOrderMonthReport> {

    private final BicycleOrderDayReportService bicycleOrderDayReportService;

    /**
     * 生成月报表
     * @param dateVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateMonthReport(BicycleReportDateVo dateVo) {
        LocalDate reportDateStart = LocalDate.of(dateVo.getYear(), dateVo.getMonth(), 1);
        LocalDate reportDateEnd = DateUtils.getLastDateInMonth(reportDateStart);
        String lockKey = String.format(TmsConst.Cache.KEY_BICYCLE_MONTH_REPORT_LOCK, dateVo.getYear(), dateVo.getMonth());
        if (!RedisUtils.lock(lockKey, TmsConst.Cache.TTL_1H)) {
            throw new BusinessException(String.format("月报表[%s]-[%s]生成中...", dateVo.getYear(), dateVo.getMonth()));
        }
        try {
            log.info("开始生成{}月报表", dateVo.getMonth());
            BicycleOrderDayReportSearchVo searchVo = new BicycleOrderDayReportSearchVo();
            searchVo.setPage(false);
            searchVo.setReportDateStart(reportDateStart);
            searchVo.setReportDateEnd(reportDateEnd);
            if (WrapperClassUtils.biggerThanLong(dateVo.getMerchantId(), 0L)){
                searchVo.setMerchantId(dateVo.getMerchantId());
            }
            List<BicycleOrderDayReportVo> bicycleOrderDayReportVos = bicycleOrderDayReportService.listDayReport(searchVo).getDataList();
            if (CollectionUtils.isEmpty(bicycleOrderDayReportVos)) {
                log.info("{}月报表为空！----- {}", dateVo.getMonth(), JsonUtils.object2Json(bicycleOrderDayReportVos));
            }

            Map<Long, List<BicycleOrderDayReportVo>> listMap = bicycleOrderDayReportVos.stream().collect(Collectors.groupingBy(BicycleOrderDayReportVo::getMerchantId));
            List<BicycleOrderMonthReport> monthReportList = new ArrayList<>(listMap.keySet().size());
            listMap.forEach((key, value)-> {
                BicycleOrderMonthReport monthBill = buildBicycleMonthReport(value);
                monthReportList.add(monthBill);
            });

            List<BicycleOrderMonthReport> list = listMonthReportByYearAndMonth(dateVo.getYear(), dateVo.getMonth());

            DataSepDto<BicycleOrderMonthReport> sepDto = SeparateUtils.separateData(list, monthReportList,
                    this::checkBicycleMonthReportExist,
                    x -> monthReportList.stream().noneMatch(y -> checkBicycleMonthReportExist(x, y)),
                    this::checkBicycleMonthReportDuplicated
            );
            log.info("操作的月报表集合：{}", JsonUtils.object2Json(sepDto));
            if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
                saveBatch(sepDto.getAddList());
            }
            if (!CollectionUtils.isEmpty(sepDto.getUpdateList())) {
                sepDto.getUpdateList().forEach(update-> {
                    ContextUtils.initEntityModifier(update);
                    update.setModifiedTime(LocalDateTime.now());
                });
                updateBatchById(sepDto.getUpdateList());
            }
        } finally {
            RedisUtils.del(lockKey);
        }
    }


    /**
     * 构建月报表
     * @param reportVoList
     * @return
     */
    private BicycleOrderMonthReport buildBicycleMonthReport(List<BicycleOrderDayReportVo> reportVoList) {
        BicycleOrderDayReportVo dayReportVo = reportVoList.get(0);
        BicycleOrderMonthReport monthReport = new BicycleOrderMonthReport();
        monthReport.setReportYearMonth(DateUtils.formatYearMonth(dayReportVo.getReportDate(), "yyyy-MM"));
        monthReport.setReportYear(dayReportVo.getReportDate().getYear());
        monthReport.setReportMonth(dayReportVo.getReportDate().getMonthValue());
        monthReport.setMerchantId(dayReportVo.getMerchantId());
        monthReport.setDeliveryCharge(reportVoList.stream().map(BicycleOrderDayReportVo::getDeliveryCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
        monthReport.setOtherCharge(reportVoList.stream().map(BicycleOrderDayReportVo::getOtherCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
        monthReport.setHandlingCharge(reportVoList.stream().map(BicycleOrderDayReportVo::getHandlingCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
        monthReport.setUpstairsCharge(reportVoList.stream().map(BicycleOrderDayReportVo::getUpstairsCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
        monthReport.setTotalCharge(DecimalUtils.add(dayReportVo.getDeliveryCharge(), dayReportVo.getOtherCharge(), dayReportVo.getHandlingCharge(), dayReportVo.getUpstairsCharge()));
        //总件数
        monthReport.setTotalPieces(reportVoList.stream().map(BicycleOrderDayReportVo::getTotalPieces).reduce(BigDecimal.ZERO, BigDecimal::add));
        //总单数
        monthReport.setTotalCount(reportVoList.stream().mapToInt(BicycleOrderDayReportVo::getTotalCount).sum());

        ContextUtils.initEntityCreator(monthReport);
        ContextUtils.initEntityModifier(monthReport);
        monthReport.setCreateTime(LocalDateTime.now());
        monthReport.setModifiedTime(LocalDateTime.now());

        log.info("{}的月报表为----{}", monthReport.getMerchantId(), JsonUtils.object2Json(monthReport));
        return monthReport;
    }

    /**
     * 更新月报表
     * @param report
     */
    public void updateMonthReport(BicycleOrderMonthReport report) {
        BicycleOrderMonthReport item = findById(report.getId());
        if (item == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        item.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(item);
        item.setDeliveryCharge(DecimalUtils.nullToZero(report.getDeliveryCharge()));
        item.setOtherCharge(DecimalUtils.nullToZero(report.getOtherCharge()));
        item.setHandlingCharge(DecimalUtils.nullToZero(report.getHandlingCharge()));
        item.setUpstairsCharge(DecimalUtils.nullToZero(report.getUpstairsCharge()));
        item.setTotalCharge(DecimalUtils.add(item.getDeliveryCharge(), item.getOtherCharge(), item.getHandlingCharge(), item.getUpstairsCharge()));
        updateById(item);
    }

    /**
     * 删除月报表
     * @param ids
     */
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        removeByIds(ids);
    }

    /**
     * 根据id查找月报表
     * @param ids
     * @return
     */
    public List<BicycleOrderMonthReport> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return list(new QueryWrapper<BicycleOrderMonthReport>().lambda().in(BicycleOrderMonthReport::getId, ids));
    }

    /**
     * 根据id获取月报表
     * @param id
     * @return
     */
    public BicycleOrderMonthReport findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 月报表合计
     * @param searchVo
     * @return
     */
    public BicycleOrderMonthReport sumMonthReport(BicycleOrderMonthReportSearchVo searchVo) {
        BicycleOrderMonthReport monthReport = getBaseMapper().sumMonthReport(searchVo);
        if (monthReport == null) {
            monthReport = new BicycleOrderMonthReport();
            monthReport.setDeliveryCharge(BigDecimal.ZERO);
            monthReport.setOtherCharge(BigDecimal.ZERO);
            monthReport.setHandlingCharge(BigDecimal.ZERO);
            monthReport.setUpstairsCharge(BigDecimal.ZERO);
            monthReport.setTotalCharge(BigDecimal.ZERO);
        }
        return monthReport;
    }

    /**
     * 月报表列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderMonthReportVo> listMonthReport(BicycleOrderMonthReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countMonthReport(searchVo), () -> getBaseMapper().listMonthReport(searchVo));
    }


    /**
     * 根据年月查询月报表
     * @param reportYear
     * @param reportMonth
     * @return
     */
    private List<BicycleOrderMonthReport> listMonthReportByYearAndMonth(Integer reportYear, Integer reportMonth) {
        return list(new QueryWrapper<BicycleOrderMonthReport>().lambda()
                .eq(BicycleOrderMonthReport::getReportYear, reportYear)
                .eq(BicycleOrderMonthReport::getReportMonth, reportMonth));
    }

    private boolean checkBicycleMonthReportExist(BicycleOrderMonthReport monthReport1, BicycleOrderMonthReport monthReport2) {
        return Objects.equals(monthReport1.getReportYear(), monthReport2.getReportYear())
                && Objects.equals(monthReport1.getReportMonth(), monthReport2.getReportMonth())
                && Objects.equals(monthReport1.getMerchantId(), monthReport2.getMerchantId());
    }

    private boolean checkBicycleMonthReportDuplicated(BicycleOrderMonthReport monthReport1, BicycleOrderMonthReport monthReport2) {
        return checkBicycleMonthReportExist(monthReport1, monthReport2)
                && DecimalUtils.equals(monthReport1.getDeliveryCharge(), monthReport2.getDeliveryCharge())
                && DecimalUtils.equals(monthReport1.getOtherCharge(), monthReport2.getOtherCharge())
                && DecimalUtils.equals(monthReport1.getHandlingCharge(), monthReport2.getHandlingCharge())
                && DecimalUtils.equals(monthReport1.getUpstairsCharge(), monthReport2.getUpstairsCharge())
                && DecimalUtils.equals(monthReport1.getTotalCharge(), monthReport2.getTotalCharge());
    }
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.domain.BicycleChargesDetail;
import com.senox.tms.mapper.BicycleChargesDetailMapper;
import com.senox.tms.vo.BicycleChargesDetailVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 收费标准明细 服务实现类
 * 该服务实现依附于{@link BicycleChargesService}，单独使用会出现校验不符合预期的问题
 *
 * <AUTHOR>
 * @date 2023-9-13
 */
@RequiredArgsConstructor
@Service
public class BicycleChargesDetailService extends ServiceImpl<BicycleChargesDetailMapper, BicycleChargesDetail> {

    /**
     * 批量添加收费标准明细
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<BicycleChargesDetail> chargesDetails) {
        if (CollectionUtils.isEmpty(chargesDetails)) {
            return;
        }
        chargesDetails.forEach(chargesDetail -> {
            if (!WrapperClassUtils.biggerThanLong(chargesDetail.getChargesId(), 0)) {
                throw new BusinessException(ResultConst.INVALID_PARAMETER);
            }
        });
        saveBatch(chargesDetails);
    }

    /**
     * 批量删除收费标准明细
     *
     * @param ids 明细id
     */
    public void deleteBath(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        removeByIds(ids);
    }


    /**
     * 批量更新收费标准明细
     *
     * @param chargesDetails 收费标准明细
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(List<BicycleChargesDetail> chargesDetails) {
        if (CollectionUtils.isEmpty(chargesDetails)) {
            return;
        }
        chargesDetails.forEach(chargesDetail -> {
            if (!WrapperClassUtils.biggerThanLong(chargesDetail.getId(), 0)) {
                return;
            }
            chargesDetail.setChargesId(null);
        });
        updateBatchById(chargesDetails);
    }

    /**
     * 比较明细
     *
     * @param srcDetails    原明细
     * @param targetDetails 目标明细
     */
    public DataSepDto<BicycleChargesDetail> compareAndSepDetail(List<BicycleChargesDetail> srcDetails, List<BicycleChargesDetail> targetDetails) {
        List<BicycleChargesDetail> addDetails;
        List<BicycleChargesDetail> updateDetails = null;
        if (CollectionUtils.isEmpty(srcDetails)) {
            addDetails = targetDetails;
        } else {
            addDetails = new ArrayList<>(targetDetails.size());
            updateDetails = new ArrayList<>(targetDetails.size());
            Map<Long, BicycleChargesDetail> srcDetailMap = srcDetails.stream().collect(Collectors.toMap(BicycleChargesDetail::getId, Function.identity()));
            for (BicycleChargesDetail targetDetail : targetDetails) {
                if (null == srcDetailMap.get(targetDetail.getId())) {
                    addDetails.add(targetDetail);
                    continue;
                }
                updateDetails.add(targetDetail);

            }
        }
        return new DataSepDto<>(addDetails, updateDetails, null);
    }


    /**
     * 批量更新收费标准明细
     *
     * @param chargesDetails 收费标准明细
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchByCharges(List<BicycleChargesDetail> chargesDetails) {
        if (CollectionUtils.isEmpty(chargesDetails)) {
            return;
        }
        for (BicycleChargesDetail chargesDetail : chargesDetails) {
            LambdaUpdateWrapper<BicycleChargesDetail> lambdaUpdateWrapper = new UpdateWrapper<BicycleChargesDetail>().lambda();
            lambdaUpdateWrapper.eq(BicycleChargesDetail::getId, chargesDetail.getId());
            lambdaUpdateWrapper.eq(BicycleChargesDetail::getChargesId, chargesDetail.getChargesId());
            if (WrapperClassUtils.biggerThanInt(chargesDetail.getGoodsType(), -1)) {
                lambdaUpdateWrapper.set(BicycleChargesDetail::getGoodsType, chargesDetail.getGoodsType());
            }
            if (WrapperClassUtils.biggerThanInt(chargesDetail.getMinCount(), 0)) {
                lambdaUpdateWrapper.set(BicycleChargesDetail::getMinCount, chargesDetail.getMinCount());
            }
            if (WrapperClassUtils.biggerThanInt(chargesDetail.getMaxCount(), 0)) {
                lambdaUpdateWrapper.set(BicycleChargesDetail::getMaxCount, chargesDetail.getMaxCount());
            }
            if (chargesDetail.getMinUnit() != null) {
                lambdaUpdateWrapper.set(BicycleChargesDetail::getMinUnit, chargesDetail.getMinUnit());
            }
            if (chargesDetail.getMaxUnit() != null) {
                lambdaUpdateWrapper.set(BicycleChargesDetail::getMaxUnit, chargesDetail.getMaxUnit());
            }
            if (null != chargesDetail.getUnitPrice()) {
                lambdaUpdateWrapper.set(BicycleChargesDetail::getUnitPrice, chargesDetail.getUnitPrice());
            }
            if (null != chargesDetail.getDefaultCharges()) {
                lambdaUpdateWrapper.set(BicycleChargesDetail::getDefaultCharges, chargesDetail.getDefaultCharges());
            }
            if (null != chargesDetail.getDisabled()) {
                lambdaUpdateWrapper.set(BicycleChargesDetail::getDisabled, chargesDetail.getDisabled());

            }
            update(lambdaUpdateWrapper);
        }
    }

    /**
     * 根据收费标准id删除明细
     *
     * @param chargesId 收费标准id
     */
    public void deleteByChargesId(Long chargesId) {
        if (!WrapperClassUtils.biggerThanLong(chargesId, 0)) {
            return;
        }
        LambdaQueryWrapper<BicycleChargesDetail> lambdaQueryWrapper = new QueryWrapper<BicycleChargesDetail>().lambda();
        lambdaQueryWrapper.eq(BicycleChargesDetail::getChargesId, chargesId);
        remove(lambdaQueryWrapper);
    }


    /**
     * 根据收费标准查询明细列表
     *
     * @param chargesId 收费标准
     * @return 查询到的明细列表
     */
    public List<BicycleChargesDetailVo> listVoByCharges(Long chargesId) {
        if (!WrapperClassUtils.biggerThanLong(chargesId, 0)) {
            return Collections.emptyList();
        }
        return baseMapper.listByCharges(chargesId);
    }

    /**
     * 根据收费标准查询明细列表
     * @param chargesId
     * @return
     */
    public List<BicycleChargesDetail> listByChargesId(Long chargesId) {
        if (!WrapperClassUtils.biggerThanLong(chargesId, 0)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BicycleChargesDetail> queryWrapper = new QueryWrapper<BicycleChargesDetail>().lambda()
                .eq(BicycleChargesDetail::getChargesId, chargesId)
                .eq(BicycleChargesDetail::getDisabled, Boolean.FALSE);
        return getBaseMapper().selectList(queryWrapper);
    }
}

package com.senox.tms.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.dto.BicycleOrderImportDto;
import com.senox.tms.event.BicycleOrderBillEvent;
import com.senox.tms.vo.BicycleDeliveryOrderDetailVo;
import com.senox.tms.vo.BicycleDeliveryOrderVo;
import com.senox.tms.vo.BicycleOrderVo;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-3-26
 */
@RequiredArgsConstructor
@Service
public class BicycleOrderUtilityService {
    private final BicycleOrderService orderService;
    private final BicycleDeliveryOrderService orderDeliveryService;
    private final ApplicationEventPublisher publisher;

    /**
     * 订单导入
     *
     * @param orderImports 订单导入集
     */
    @Transactional(rollbackFor = Exception.class)
    public void orderImport(List<BicycleOrderImportDto> orderImports) {
        if (CollectionUtils.isEmpty(orderImports)) {
            return;
        }
        //转换成订单
        List<BicycleOrderVo> orderVos = orderService.orderImportDtoToOrder(orderImports);
        //进行幂等并返回订单集
        List<BicycleOrderVo> orders = orderService.orderIdempotent(orderVos);
        //非合并订单集
        List<BicycleOrderVo> noMergeOrders = orders.stream().filter(o -> !WrapperClassUtils.biggerThanInt(o.getMergeNumber(), 0)).collect(Collectors.toList());
        //合并订单集
        List<BicycleOrderVo> mergeOrders = orders.stream().filter(o -> WrapperClassUtils.biggerThanInt(o.getMergeNumber(), 0)).collect(Collectors.toList());
        //合并订单map
        Map<Integer, List<BicycleOrderVo>> mergerOrderMap = mergeOrders.stream().collect(Collectors.groupingBy(BicycleOrderVo::getMergeNumber));
        //订单流水号map，转换后的格式为: Map<LocalDateTime,List<String>>
        MultiValueMap<LocalDateTime, String> orderSerialNoMap = new LinkedMultiValueMap<>();
        for (BicycleOrderVo orderVo : noMergeOrders) {
            LocalDateTime orderTime = orderVo.getOrderTime();
            orderSerialNoMap.addAll(orderTime, oderBuild(orderTime, Collections.singletonList(orderVo)));
        }
        for (Map.Entry<Integer, List<BicycleOrderVo>> entry : mergerOrderMap.entrySet()) {
            LocalDateTime orderTime = orders.get(0).getOrderTime();
            orderSerialNoMap.addAll(orderTime, oderBuild(orderTime, entry.getValue()));
        }
        publisher.publishEvent(new BicycleOrderBillEvent(this, orderSerialNoMap));
    }

    /**
     * 订单构建
     *
     * @param orderTime 订单时间
     * @param orders    订单集
     * @return 返回订单流水集
     */
    public List<String> oderBuild(LocalDateTime orderTime, List<BicycleOrderVo> orders) {
        //处理订单并返回配送订单
        BicycleDeliveryOrderVo deliveryOrderVo = orderService.orderHandle(orders);
        //插入并返回配送订单
        BicycleDeliveryOrder deliveryOrder = orderDeliveryService.saveBicycleDeliveryOrder(deliveryOrderVo, orderTime);
        //配送单详情处理
        orderDeliveryService.deliverOrderDetailHandle(deliveryOrder.getDeliveryOrderSerialNo(), deliveryOrderVo.getDetailVos(), orderTime);
        //返回订单号
        return deliveryOrderVo.getDetailVos().stream().map(BicycleDeliveryOrderDetailVo::getOrderSerialNo).collect(Collectors.toList());
    }

}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.BicycleOrderGoodsType;
import com.senox.tms.constant.BicycleSettingType;
import com.senox.tms.constant.BicycleStatus;
import com.senox.tms.domain.*;
import com.senox.tms.mapper.BicycleChargesMapper;
import com.senox.tms.vo.BicycleChargesDetailVo;
import com.senox.tms.vo.BicycleChargesSearchVo;
import com.senox.tms.vo.BicycleOrderCalculateEstimateVo;
import groovy.lang.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 收费标准 服务实现类
 *
 * <AUTHOR>
 * @date 2023-9-12
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BicycleChargesService extends ServiceImpl<BicycleChargesMapper, BicycleCharges> {
    private final BicycleChargesDetailService chargesDetailService;
    private final BicycleSettingService settingService;

    /**
     * 添加收费标准
     * 如果有传明细，则会绑定收费标准并添加，这是可选的
     *
     * @param charges        收费标准
     * @param chargesDetails 明细列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(BicycleCharges charges, List<BicycleChargesDetail> chargesDetails) {
        charges.setDefaultEffective(false);
        charges.setCreateTime(LocalDateTime.now());
        charges.setModifiedTime(LocalDateTime.now());
        if (save(charges) && !CollectionUtils.isEmpty(chargesDetails)) {
            chargesDetails.forEach(chargesDetail -> chargesDetail.setChargesId(charges.getId()));
            chargesDetailService.addBatch(chargesDetails);
        }
    }

    /**
     * 删除收费标准
     * 删除收费标准后还会删除相关的明细，保证关联的一致性
     *
     * @param id 要删除标准的id
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        BicycleCharges dbCharges = getById(id);
        if (null == dbCharges) {
            return;
        }
        if (BooleanUtils.isTrue(dbCharges.getDefaultEffective())) {
            throw new BusinessException(String.format("删除失败，[%s]处于默认生效状态", dbCharges.getName()));
        }
        BicycleStatus status = BicycleStatus.DISABLED;
        statusValid(dbCharges, BicycleStatus.DISABLED);
        boolean remove = remove(new QueryWrapper<BicycleCharges>()
                .lambda()
                .eq(BicycleCharges::getId, dbCharges.getId())
                .eq(BicycleCharges::getStatus, status.getNumber())
        );
        if (remove) {
            chargesDetailService.deleteByChargesId(id);
        }
    }

    /**
     * 修改收费标准
     *
     * @param charges 收费标准
     */
    public void update(BicycleCharges charges, List<BicycleChargesDetail> chargesDetails) {
        BicycleCharges dbCharges = baseMapper.selectById(charges.getId());
        if (null == dbCharges) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BooleanUtils.isTrue(charges.getDefaultEffective())) {
            BicycleCharges defaultEffectiveCharges = baseMapper.getDefaultEffective();
            if (null != defaultEffectiveCharges) {
                throw new BusinessException(String.format("修改[%s]失败，默认生效标准已存在", dbCharges.getName()));
            }
        }
        charges.setModifiedTime(LocalDateTime.now());
        boolean update = update(charges, new UpdateWrapper<BicycleCharges>()
                .lambda()
                .eq(BicycleCharges::getId, charges.getId())
        );
        if (update && !CollectionUtils.isEmpty(chargesDetails)) {
            chargesDetails.forEach(chargesDetail -> chargesDetail.setChargesId(charges.getId()));
            //跟DB层数据做比较，选择新增还是修改
            DataSepDto<BicycleChargesDetail> dataSepDto = chargesDetailService.compareAndSepDetail(chargesDetailService.listByChargesId(charges.getId()), chargesDetails);
            if (!CollectionUtils.isEmpty(dataSepDto.getAddList())) {
                chargesDetailService.addBatch(dataSepDto.getAddList());
            }
            if (!CollectionUtils.isEmpty(dataSepDto.getUpdateList())) {
                chargesDetailService.updateBatchByCharges(dataSepDto.getUpdateList());
            }
        }
    }

    /**
     * 根据id查询收费标准
     *
     * @param id 收费标准id
     * @return 查询到的结果
     */
    public BicycleCharges getById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return super.getById(id);
    }

    /**
     * 根据明细查收费标准
     *
     * @param chargesDetails 明细
     * @return 查到的收费标准列表
     */
    public List<BicycleCharges> listByDetail(List<BicycleChargesDetail> chargesDetails) {
        if (CollectionUtils.isEmpty(chargesDetails)) {
            return Collections.emptyList();
        }
        List<Long> chargesIds = chargesDetails.stream()
                .map(BicycleChargesDetail::getChargesId)
                .filter(chargesId -> WrapperClassUtils.biggerThanLong(chargesId, 0))
                .collect(Collectors.toList());
        LambdaQueryWrapper<BicycleCharges> lambdaQueryWrapper = new QueryWrapper<BicycleCharges>().lambda();
        lambdaQueryWrapper.in(BicycleCharges::getId, chargesIds);
        return list(lambdaQueryWrapper);
    }

    /**
     * 收费标准列表
     *
     * @param searchVo 查询参数
     * @return 分页结果
     */
    public PageResult<BicycleCharges> list(BicycleChargesSearchVo searchVo) {
        BicycleStatus status = searchVo.getStatus();
        LambdaQueryWrapper<BicycleCharges> lambdaQueryWrapper = new QueryWrapper<BicycleCharges>().lambda();
        if (!StringUtils.isBlank(searchVo.getName())) {
            lambdaQueryWrapper.like(BicycleCharges::getName, searchVo.getName());
        }
        if (null != searchVo.getStatus()) {
            lambdaQueryWrapper.eq(BicycleCharges::getStatus, status.getNumber());
        }
        if (null != searchVo.getEffectiveStartTime()) {
            lambdaQueryWrapper.ge(BicycleCharges::getEffectiveTime, searchVo.getEffectiveStartTime());
        }
        if (null != searchVo.getEffectiveEndTime()) {
            lambdaQueryWrapper.le(BicycleCharges::getIneffectiveTime, searchVo.getEffectiveEndTime());
        }
        return PageUtils.commonPageResult(searchVo, () -> count(lambdaQueryWrapper), () -> {
            String lastStr = "";
            lastStr = lastStr.concat("order by case status when 2 then 1 when 0 then 2 when 10 then 3 else 2 end");
            if (searchVo.isPage()) {
                lastStr = lastStr.concat(" ").concat(String.format("limit %s , %s", searchVo.getOffset(), searchVo.getPageSize()));
            }
            lambdaQueryWrapper.last(lastStr);
            return list(lambdaQueryWrapper);
        });
    }

    public List<BicycleCharges> listByEffective(LocalDateTime activeTime) {
        if (null == activeTime) {
            activeTime = LocalDateTime.now();
        }
        LambdaQueryWrapper<BicycleCharges> lambdaQueryWrapper = new QueryWrapper<BicycleCharges>().lambda();
        lambdaQueryWrapper.eq(BicycleCharges::getStatus, BicycleStatus.ENABLED.getNumber());
        lambdaQueryWrapper.le(BicycleCharges::getEffectiveTime, activeTime);
        lambdaQueryWrapper.ge(BicycleCharges::getIneffectiveTime, activeTime);
        return list(lambdaQueryWrapper);
    }

    /**
     * 获取当前有效的费用标准
     *
     * @return 返回生效时间跟当前时间最接近的费用标准
     */
    public BicycleCharges getCurrentEffectiveCharges() {
        List<BicycleCharges> chargesList = listByEffective(LocalDateTime.now());
        return chargesList.stream().filter(b -> BooleanUtils.isTrue(b.getDefaultEffective())).findAny().orElseGet(() -> chargesList
                .stream()
                .sorted(Comparator.comparing(i -> DateUtils.getDurationBetween(i.getEffectiveTime(), LocalDateTime.now(), ChronoUnit.SECONDS)))
                .collect(Collectors.toList()).stream().findFirst().orElse(null));
    }

    /**
     * 根据收费标准计算费用
     * @param chargesId
     * @param mattersList
     * @return
     */
    public BigDecimal calculateCharges(Long chargesId, List<? extends BicycleChargeMatters> mattersList) {
        BicycleCharges charges = getById(chargesId);
        if (charges == null) {
            return BigDecimal.ZERO;
        }
        return calculateCharges(charges.getMinAmount(), chargesDetailService.listByChargesId(chargesId), mattersList);
    }

    /**
     * 根据收费标准计算实际费用
     * @param minAmount 低消
     * @param chargeDetails 收费标准明细
     * @param mattersList 件数
     * @return 返回的费用
     */
    public BigDecimal calculateActualCharges(BigDecimal minAmount, List<BicycleChargesDetail> chargeDetails, List<? extends List<? extends BicycleChargeMatters>> mattersList) {
        BigDecimal result = BigDecimal.ZERO;
        BicycleSetting setting = settingService.findByAlias(BicycleSettingType.MULTIPLE_START_PRICE.getAlias());
        log.info("【件数】{},", JsonUtils.object2Json(mattersList));
        if (setting != null && BooleanUtils.isTrue(setting.getEnable())) {
            //多起步价
            for (List<? extends BicycleChargeMatters> matters : mattersList) {
                BigDecimal amount = calculateCharges(minAmount, chargeDetails, matters);
                result = DecimalUtils.add(result, amount);
            }
        } else {
            result = calculateCharges(minAmount, chargeDetails, mattersList.stream().flatMap(List::stream).collect(Collectors.toList()));
        }
        return result;
    }

    /**
     * 根据收费标准计算预估费用
     * @param chargesId
     * @param mattersList
     * @return
     */
    public BicycleOrderCalculateEstimateVo calculateEstimateCharges(Long chargesId, List<? extends BicycleChargeMatters> mattersList) {
        if (CollectionUtils.isEmpty(mattersList)) {
            return BicycleOrderCalculateEstimateVo.builder().carNum(0).totalCharge(BigDecimal.ZERO).build();
        }
        Tuple2<Integer, List<BicycleChargeMatters>> estimateTuple = estimateCharges(mattersList);
        BigDecimal result = calculateCharges(chargesId, mattersList);
        return BicycleOrderCalculateEstimateVo.builder()
                .carNum(estimateTuple.getV1())
                .totalCharge(result)
                .build();
    }

    /**
     * 预估费用
     * @param minAmount
     * @param chargeDetails
     * @param mattersList
     * @return
     */
    public BicycleOrderCalculateEstimateVo calculateEstimateCharges(BigDecimal minAmount, List<BicycleChargesDetail> chargeDetails, List<? extends BicycleChargeMatters> mattersList) {
        if (CollectionUtils.isEmpty(mattersList)) {
            return BicycleOrderCalculateEstimateVo.builder().carNum(0).totalCharge(BigDecimal.ZERO).build();
        }
        Tuple2<Integer, List<BicycleChargeMatters>> estimateTuple = estimateCharges(mattersList);
        BigDecimal result = calculateCharges(minAmount, chargeDetails, mattersList);
        return BicycleOrderCalculateEstimateVo.builder()
                .carNum(estimateTuple.getV1())
                .totalCharge(result)
                .build();
    }


    /**
     * 预估结果
     * @param mattersList
     * @return
     */
    private Tuple2<Integer, List<BicycleChargeMatters>> estimateCharges(List<? extends BicycleChargeMatters> mattersList) {
        List<BicycleChargeMatters> srcMatterList = mattersList.stream()
                .map(obj -> new BicycleOrderGoodsDetail(obj.getGoodsType(), obj.getWeight(), obj.getSize(), obj.getPieces()))
                .collect(Collectors.toList());
        Tuple2<Integer, List<BicycleChargeMatters>> estimateTuple = calculateEstimateCharges(srcMatterList);
        log.info("【预估费用】，结果:{}", JsonUtils.object2Json(estimateTuple));
        return estimateTuple;
    }

    /**
     * 构建拆车车辆数
     * @param mattersList
     * @return
     */
    private Tuple2<Integer, List<BicycleChargeMatters>> calculateEstimateCharges(List<? extends BicycleChargeMatters> mattersList) {
        List<? extends BicycleChargeMatters> sortedMattersList = mattersList.stream().sorted(Comparator.comparingInt(BicycleChargeMatters::getGoodsType)).collect(Collectors.toList());
        List<BicycleChargeMatters> heavyCapacityList = new ArrayList<>();
        List<BicycleChargeMatters> throwCapacityList = new ArrayList<>();
        List<BicycleChargeMatters> otherCapacityList = new ArrayList<>();
        BigDecimal heavySurplusCapacity = new BigDecimal(settingService.findByAlias(BicycleSettingType.HEAVY_GOODS.getAlias()).getIntervalMinute());//重货车剩余容量
        BigDecimal throwSurplusCapacity = new BigDecimal(settingService.findByAlias(BicycleSettingType.THROW_GOODS.getAlias()).getIntervalMinute());//抛货车剩余容量
        BigDecimal heavyGoodsCapacity = heavySurplusCapacity;
        BigDecimal throwGoodsCapacity = throwSurplusCapacity;
        BicycleOrderGoodsDetail heavyCapacity = new BicycleOrderGoodsDetail();
        BicycleOrderGoodsDetail throwCapacity = new BicycleOrderGoodsDetail();

        for (BicycleChargeMatters matters : sortedMattersList) {
            //重货
            if (matters.getGoodsType() == BicycleOrderGoodsType.HEAVY_GOODS.getNumber()) {
                BigDecimal totalWeight = DecimalUtils.multiple(matters.getPieces(), matters.getWeight());
                Tuple2<BicycleOrderGoodsDetail, BigDecimal> heavyCargoTuple = heavyCargoRecursively(matters, heavySurplusCapacity, heavyCapacity, totalWeight, heavyCapacityList, heavyGoodsCapacity);
                heavyCapacity = heavyCargoTuple.getV1();
                heavySurplusCapacity = heavyCargoTuple.getV2();
            } else if (matters.getGoodsType() == BicycleOrderGoodsType.THROW_GOODS.getNumber()) {
                BigDecimal totalSize = DecimalUtils.multiple(matters.getPieces(), matters.getSize());
                Tuple2<BicycleOrderGoodsDetail, BigDecimal> throwCargoTuple = throwCargoRecursively(matters, throwSurplusCapacity, throwCapacity, totalSize, throwCapacityList, throwGoodsCapacity);
                throwCapacity = throwCargoTuple.getV1();
                throwSurplusCapacity = throwCargoTuple.getV2();
            } else {
                otherCapacityList.add(matters);
            }
        }
        List<BicycleChargeMatters> chargeMattersList = new ArrayList<>();
        chargeMattersList.addAll(heavyCapacityList);
        chargeMattersList.addAll(throwCapacityList);
        //所有其他货物类型的件数合并到一起
        if (!CollectionUtils.isEmpty(otherCapacityList)) {
            BicycleOrderGoodsDetail otherGoodsDetail = new BicycleOrderGoodsDetail();
            otherGoodsDetail.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
            otherGoodsDetail.setPieces(otherCapacityList.stream().map(BicycleChargeMatters::getPieces).reduce(BigDecimal.ZERO, BigDecimal::add));
            chargeMattersList.add(otherGoodsDetail);
        }
        int carNum = heavyCapacityList.size() + throwCapacityList.size();
        carNum = CollectionUtils.isEmpty(otherCapacityList) ? carNum : carNum + 1;
        return new Tuple2<>(carNum, chargeMattersList);
    }

    /**
     * 抛货递归
     * @param matters
     * @param throwSurplusCapacity
     * @param throwCapacity
     * @param totalSize
     * @param throwCapacityList
     * @param throwGoodsCapacity
     * @return
     */
    public Tuple2<BicycleOrderGoodsDetail, BigDecimal> throwCargoRecursively(BicycleChargeMatters matters, BigDecimal throwSurplusCapacity, BicycleOrderGoodsDetail throwCapacity, BigDecimal totalSize, List<BicycleChargeMatters> throwCapacityList, BigDecimal throwGoodsCapacity) {
        if (DecimalUtils.isPositive(DecimalUtils.subtract(matters.getSize(), throwGoodsCapacity))) {
            throw new BusinessException("抛货件体积超出车荷载！");
        }
        if (DecimalUtils.isPositive(DecimalUtils.subtract(throwSurplusCapacity, totalSize)) || DecimalUtils.equals(DecimalUtils.subtract(throwSurplusCapacity, totalSize), BigDecimal.ZERO)) {
            //容量足够
            throwCapacity.setGoodsType(matters.getGoodsType());
            throwCapacity.setSize(matters.getSize());
            throwCapacity.setPieces(DecimalUtils.add(throwCapacity.getPieces(), matters.getPieces()));
            //剩余容量
            throwSurplusCapacity = DecimalUtils.subtract(throwSurplusCapacity, totalSize);
            throwCapacityList.add(throwCapacity);
            return new Tuple2<>(throwCapacity, throwSurplusCapacity);
        } else {
            //还可装下的件数
            BigDecimal remainingPiece = throwSurplusCapacity.divide(matters.getSize(), 0, RoundingMode.DOWN);
            throwCapacity.setGoodsType(matters.getGoodsType());
            throwCapacity.setSize(matters.getSize());
            throwCapacity.setPieces(DecimalUtils.add(throwCapacity.getPieces(), remainingPiece));
            throwCapacityList.add(throwCapacity);

            //重置重货对象
            throwCapacity = new BicycleOrderGoodsDetail();
            //还需要装的件数
            BigDecimal surplusPiece = DecimalUtils.subtract(matters.getPieces(), remainingPiece);
            //重置重货剩余容量
            throwSurplusCapacity = new BigDecimal(settingService.findByAlias(BicycleSettingType.THROW_GOODS.getAlias()).getIntervalMinute());
            totalSize = DecimalUtils.multiple(surplusPiece, matters.getSize());
            matters.setPieces(DecimalUtils.subtract(matters.getPieces(), remainingPiece));
            //如果这一车也装不完，继续装一半。递归
            return throwCargoRecursively(matters, throwSurplusCapacity, throwCapacity, totalSize, throwCapacityList, throwGoodsCapacity);
        }
    }

    /**
     * 重货递归
     * @param matters
     * @param heavySurplusCapacity
     * @param heavyCapacity
     * @param totalWeight
     * @param heavyCapacityList
     * @param heavyGoodsCapacity
     * @return
     */
    public Tuple2<BicycleOrderGoodsDetail, BigDecimal> heavyCargoRecursively(BicycleChargeMatters matters, BigDecimal heavySurplusCapacity, BicycleOrderGoodsDetail heavyCapacity, BigDecimal totalWeight, List<BicycleChargeMatters> heavyCapacityList, BigDecimal heavyGoodsCapacity) {
        if (DecimalUtils.isPositive(DecimalUtils.subtract(matters.getWeight(), heavyGoodsCapacity))) {
            throw new BusinessException("重货件重量超出车荷载！");
        }
        if (DecimalUtils.isPositive(DecimalUtils.subtract(heavySurplusCapacity, totalWeight)) || DecimalUtils.equals(DecimalUtils.subtract(heavySurplusCapacity, totalWeight), BigDecimal.ZERO)) {
            //容量足够
            heavyCapacity.setGoodsType(matters.getGoodsType());
            heavyCapacity.setWeight(matters.getWeight());
            heavyCapacity.setPieces(DecimalUtils.add(heavyCapacity.getPieces(), matters.getPieces()));
            //剩余容量
            heavySurplusCapacity = DecimalUtils.subtract(heavySurplusCapacity, totalWeight);
            heavyCapacityList.add(heavyCapacity);
            return new Tuple2<>(heavyCapacity, heavySurplusCapacity);
        } else {
            //还可装下的件数
            BigDecimal remainingPiece = heavySurplusCapacity.divide(matters.getWeight(), 0, RoundingMode.DOWN);
            heavyCapacity.setPieces(DecimalUtils.add(heavyCapacity.getPieces(), remainingPiece));
            heavyCapacity.setGoodsType(matters.getGoodsType());
            heavyCapacity.setWeight(matters.getWeight());
            heavyCapacityList.add(heavyCapacity);

            //重置重货对象
            heavyCapacity = new BicycleOrderGoodsDetail();
            //还需要装的件数
            BigDecimal surplusPiece = DecimalUtils.subtract(matters.getPieces(), remainingPiece);
            //重置重货剩余容量
            heavySurplusCapacity = new BigDecimal(settingService.findByAlias(BicycleSettingType.HEAVY_GOODS.getAlias()).getIntervalMinute());
            totalWeight = DecimalUtils.multiple(surplusPiece, matters.getWeight());
            //如果这一车也装不完，继续装一半。递归
            matters.setPieces(DecimalUtils.subtract(matters.getPieces(), remainingPiece));
            return heavyCargoRecursively(matters, heavySurplusCapacity, heavyCapacity, totalWeight, heavyCapacityList, heavyGoodsCapacity);
        }
    }

    /**
     * 根据收费标准计算费用
     * 起步价10件内（含）5元/单，不足一件按一件计;
     *      10件作为基数，例如：18件=5+（8*0.45）=8.6元
     *      28件=5+（18*0.4）=12.2元
     *
     * @param minAmount 低消
     * @param chargeDetails 收费标准明细列表
     * @param mattersList            件数
     * @return 返回计算出来的费用
     */
    public BigDecimal calculateCharges(BigDecimal minAmount, List<BicycleChargesDetail> chargeDetails, List<? extends BicycleChargeMatters> mattersList) {
        if (CollectionUtils.isEmpty(chargeDetails)) {
            return BigDecimal.ZERO;
        }
        List<Integer> goodsTypeList = chargeDetails.stream().map(BicycleChargesDetail::getGoodsType).distinct().collect(Collectors.toList());
        for (BicycleChargeMatters matters : mattersList) {
            if (!goodsTypeList.contains(matters.getGoodsType())) {
                throw new BusinessException("无效的货物类型！");
            }
        }
        List<BicycleChargesDetail> defaultChargesList = chargeDetails.stream().filter(BicycleChargesDetail::getDefaultCharges).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultChargesList)) {
            throw new BusinessException("未设置默认标准！");
        }
        // 收费标准按最小件数升序排列
        List<BicycleChargesDetail> sortedCharges = chargeDetails.stream()
                .sorted(Comparator.comparing(BicycleChargesDetail::getDefaultCharges)
                        .thenComparingInt(BicycleChargesDetail::getMinCount)
                        .thenComparing(BicycleChargesDetail::getMinUnit))
                .collect(Collectors.toList());

        BigDecimal result = BigDecimal.ZERO;
        for (BicycleChargeMatters matters : mattersList) {
            // 件数不满一件按1件算
            int num = matters.getPieces().setScale(0, RoundingMode.UP).intValue();
            //货物类型，重量，体积
            Integer goodsType = matters.getGoodsType();
            BigDecimal unit = buildUnit(matters, goodsType);
            int numToCal = num;

            for (BicycleChargesDetail charge : sortedCharges) {
                //货物类型不匹配
                if (!BooleanUtils.isTrue(charge.getDefaultCharges())) {
                    if (!Objects.equals(goodsType, charge.getGoodsType())) {
                        continue;
                    }
                }
                BigDecimal unitPrice = DecimalUtils.nullToZero(charge.getUnitPrice());
                Integer minCount = charge.getMinCount();
                Integer maxCount = charge.getMaxCount();
                BigDecimal maxUnit = charge.getMaxUnit();

                // 条件匹配
                if (maxCount > 0 && num > maxCount
                        // 区间不用计算
                        && !BooleanUtils.isTrue(charge.getCalAnyway())
                        // 已经计算过该范围
                        || (maxCount > 0 && (num - numToCal) >= maxCount)
                        //最小单位是闭区间，最大单位是开区间，最大单位包含等于
                        || (unit.compareTo(maxUnit) > 0 && !DecimalUtils.equals(maxUnit, BigDecimal.ZERO))) {
                    continue;
                }

                // 计费数量
                int calNum = maxCount > 0 && num > maxCount ? (maxCount - minCount + 1) : numToCal;
                BigDecimal amount = DecimalUtils.multiple(unitPrice, BigDecimal.valueOf(calNum));
                log.info("calNum: {}, amount:{}", calNum, amount);
                result = DecimalUtils.add(result, amount);
                numToCal -= calNum;

                if (numToCal == 0) {
                    break;
                }
            }
        }
        //比较低消金额，返回最大金额
        return DecimalUtils.max(result, minAmount);
    }

    /**
     * 货物的单位
     * @param matters
     * @param goodsType
     * @return
     */
    private static BigDecimal buildUnit(BicycleChargeMatters matters, Integer goodsType) {
        BigDecimal unit = BigDecimal.ZERO;
        if (goodsType == BicycleOrderGoodsType.HEAVY_GOODS.getNumber()) {
            if (matters.getWeight() == null) {
                throw new BusinessException("重货的重量不能为空！");
            }
            unit = matters.getWeight();
        } else if (goodsType == BicycleOrderGoodsType.THROW_GOODS.getNumber()) {
            if (matters.getSize() == null) {
                throw new BusinessException("抛货的体积不能为空！");
            }
            unit = matters.getSize();
        }
        return unit;
    }

    /**
     * 批量添加明细
     *
     * @param chargesDetails 明细
     */
    public void addDetailBatch(List<BicycleChargesDetail> chargesDetails) {
        if (CollectionUtils.isEmpty(chargesDetails)) {
            return;
        }
        chargesValid(chargesDetails);
        chargesDetailService.addBatch(chargesDetails);

    }

    /**
     * 批量修改明细
     *
     * @param chargesDetails 明细
     */
    public void updateDetailBatch(List<BicycleChargesDetail> chargesDetails) {
        if (CollectionUtils.isEmpty(chargesDetails)) {
            return;
        }
        chargesValid(chargesDetails);
        chargesDetailService.updateBatch(chargesDetails);

    }

    /**
     * 批量删除明细
     *
     * @param chargesDetailIds 明细
     */
    public void deleteDetailBatch(List<Long> chargesDetailIds) {
        if (CollectionUtils.isEmpty(chargesDetailIds)) {
            return;
        }
        List<BicycleChargesDetail> chargesDetails = chargesDetailService.list(new QueryWrapper<BicycleChargesDetail>().lambda().in(BicycleChargesDetail::getId, chargesDetailIds));
        if (CollectionUtils.isEmpty(chargesDetails)) {
            return;
        }
        chargesValid(chargesDetails);

        chargesDetailService.deleteBath(chargesDetailIds);
    }


    /**
     * 根据收费标准查询明细列表
     *
     * @param chargesId 收费标准
     * @return 查询到的明细列表
     */
    public List<BicycleChargesDetailVo> listDetailByCharges(Long chargesId) {
        return chargesDetailService.listVoByCharges(chargesId);
    }

    /**
     * 收费标准检查
     *
     * @param chargesDetails 收费标准明细
     */
    private void chargesValid(List<BicycleChargesDetail> chargesDetails) {
        if (CollectionUtils.isEmpty(chargesDetails)) {
            return;
        }
        List<BicycleCharges> chargesList = listByDetail(chargesDetails);
        chargesList.forEach(charges -> statusValid(charges, BicycleStatus.DISABLED));
    }

    /**
     * 状态检查
     *
     * @param charges 收费标准
     * @param status  状态
     */
    private void statusValid(BicycleCharges charges, BicycleStatus status) {
        if (status.getNumber() != charges.getStatus()) {
            throw new BusinessException(String.format("操作失败，[%s-%s]未处在[%s]状态", charges.getId(), charges.getName(), status.getName()));
        }
    }

}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.domain.BicycleOrderGoodsDetail;
import com.senox.tms.mapper.BicycleOrderGoodsDetailMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/18 10:22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BicycleOrderGoodsDetailService extends ServiceImpl<BicycleOrderGoodsDetailMapper, BicycleOrderGoodsDetail> {


    /**
     * 添加配送订单货物明细
     * @param orderId
     * @param orderGoodsDetails
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBicycleOrderGoodsDetail(Long orderId, List<BicycleOrderGoodsDetail> orderGoodsDetails) {
        log.info("配送订单id ---  {}", orderId);
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            throw new BusinessException("配送订单id有误");
        }
        if (CollectionUtils.isEmpty(orderGoodsDetails)) {
            orderGoodsDetails = new ArrayList<>();
        }
        orderGoodsDetails.forEach(details-> details.setOrderId(orderId));
        DataSepDto<BicycleOrderGoodsDetail> sepData = compareAndSepData(orderGoodsDetailByOrderId(orderId), orderGoodsDetails);
        log.info("转换后的sepData未 ---- {}", JsonUtils.object2Json(sepData));
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateBatchById(sepData.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(BicycleOrderGoodsDetail::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 计算件数
     * @param orderGoodsDetails
     * @return
     */
    public BigDecimal countPieces(List<BicycleOrderGoodsDetail> orderGoodsDetails) {
        if (CollectionUtils.isEmpty(orderGoodsDetails)) {
            return BigDecimal.ZERO;
        }

        return orderGoodsDetails.stream()
                .map(BicycleOrderGoodsDetail::getPieces)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 根据订单id删除订单
     * @param orderId
     */
    public void deleteBicycleOrderGoodsDetailByOrderId(Long orderId) {
        List<BicycleOrderGoodsDetail> goodsDetails = orderGoodsDetailByOrderId(orderId);
        removeByIds(goodsDetails.stream().map(BicycleOrderGoodsDetail::getId).collect(Collectors.toList()));
    }

    /**
     * 配送订单id查询订单货物明细集合
     *
     * @param orderId
     * @return
     */
    public List<BicycleOrderGoodsDetail> orderGoodsDetailByOrderId(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<BicycleOrderGoodsDetail>().eq(BicycleOrderGoodsDetail::getOrderId, orderId));
    }

    /**
     * 根据订单id查询货物
     * @param orderIds
     * @return
     */
    public List<BicycleOrderGoodsDetail> listByOrderIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<BicycleOrderGoodsDetail>().lambda().in(BicycleOrderGoodsDetail::getOrderId, orderIds));
    }

    /**
     * 根据id查询货物
     * @param idList
     * @return
     */
    public List<BicycleOrderGoodsDetail> listByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return listByIds(idList);
    }

    /**
     * 比较替换
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<BicycleOrderGoodsDetail> compareAndSepData(List<BicycleOrderGoodsDetail> srcList, List<BicycleOrderGoodsDetail> targetList) {
        List<BicycleOrderGoodsDetail> addList = new ArrayList<>(targetList.size());
        List<BicycleOrderGoodsDetail> updateList = new ArrayList<>(srcList.size());
        List<BicycleOrderGoodsDetail> delList = new ArrayList<>(srcList.size());

        if (CollectionUtils.isEmpty(srcList)) {
            // 原始记录为空，新增所有目标记录
            addList.addAll(targetList);

        } else if (CollectionUtils.isEmpty(targetList)) {
            // 目标记录为空，删掉所有原始记录
            delList.addAll(srcList);

        } else {
            for (BicycleOrderGoodsDetail item : srcList) {
                BicycleOrderGoodsDetail targetItem = targetList.stream().filter(x -> Objects.equals(x, item)).findFirst().orElse(null);
                if (targetItem != null) {
                    targetItem.setId(item.getId());
                    updateList.add(targetItem);
                } else {
                    delList.add(item);
                }
            }

            addList = targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList());
        }

        return new DataSepDto<>(addList, updateList, delList);
    }
}

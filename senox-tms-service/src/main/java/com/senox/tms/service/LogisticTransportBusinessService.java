package com.senox.tms.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.BillPaidVo;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import com.senox.tms.convert.LogisticTransportBillConvert;
import com.senox.tms.domain.LogisticTransportBill;
import com.senox.tms.domain.LogisticTransportOrder;
import com.senox.tms.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-23
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticTransportBusinessService {
    private final LogisticTransportOrderService orderService;
    private final LogisticTransportBillService billService;
    private final LogisticTransportBillSettlementService billSettlementService;
    private final LogisticTransportBillConvert billConvert;


    /**
     * 修改订单
     *
     * @param orders           订单集
     * @param resetAuditStatus 是否重置审核状态
     */
    public void updateOrder(List<LogisticTransportOrder> orders, boolean resetAuditStatus) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        if (resetAuditStatus) {
            for (LogisticTransportOrder order : orders) {
                order.setAuditStatus(false);
                order.setAuditorId(0L);
                order.setAuditorName(StringUtils.EMPTY);
                order.setAuditTime(null);
            }
        }
        orderService.updateBatch(orders);
    }

    /**
     * 生成账单
     *
     * @param billGenerate 账单生成信息
     * @return 返回账单id
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Long> generateBill(LogisticTransportBillGenerateVo billGenerate) {
        if (billGenerate == null) {
            return Collections.emptyList();
        }
        if (null == billGenerate.getPayer()) {
            throw new BusinessException("收款方缺失");
        }
        LogisticTransportOrderSearchVo search = new LogisticTransportOrderSearchVo();
        search.setPage(false);
        search.setAuditStatus(true);
        search.setSerialNos(billGenerate.getOrderSerialNos());
        search.setStartDate(billGenerate.getStartDate());
        search.setEndDate(billGenerate.getEndDate());
        search.setConsignorCode(billGenerate.getMerchantSerial());
        search.setPayer(billGenerate.getPayer());
        List<LogisticTransportOrderVo> orders = orderService.list(search);
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }
        List<LogisticTransportBill> bills = generateBill(orders);
        //如果是到付的，则分开生成账单
        if (billGenerate.getPayer() == LogisticTransportOrderPayer.CONSIGNEE) {
            List<Long> settlementIds = new ArrayList<>(bills.size());
            bills.forEach(x -> {
                Long settlementId = billSettlementService.generateBillSettlement(x, billGenerate.getSend(), billGenerate.getTollManId(), billGenerate.getPayer());
                settlementIds.add(settlementId);
            });
            return settlementIds;
        }
        return billSettlementService.generateBillSettlement(bills, billGenerate.getSend(), billGenerate.getTollManId(), billGenerate.getPayer());
    }

    /**
     * 生成账单
     *
     * @param orders 订单集
     * @return 返回账单集
     */
    public List<LogisticTransportBill> generateBill(List<LogisticTransportOrderVo> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }
        List<LogisticTransportBill> bills = new ArrayList<>(orders.size());
        List<LogisticTransportBillVo> dbBills = billService.findByOrderSerialNo(orders.stream().map(LogisticTransportOrderVo::getSerialNo).collect(Collectors.toList()));
        Map<String, LogisticTransportBillVo> dbBillMap = dbBills.stream().collect(Collectors.toMap(x -> x.getBillDate().toString().concat(x.getOrderSerialNo()), Function.identity()));
        for (LogisticTransportOrderVo order : orders) {
            if (BooleanUtils.isTrue(order.getPayStatus())) {
                log.info("【Logistic transport】订单已支付，无需生成账单: {}", JsonUtils.object2Json(order));
                continue;
            }
            LogisticTransportBill bill = new LogisticTransportBill();
            LocalDate billDate = DateUtils.parseDate(order.getYearMonthDay(), DateUtils.PATTERN_FULL_DATE);
            LogisticTransportBillVo dbBill = dbBillMap.get(billDate.toString().concat(order.getSerialNo()));
            if (null != dbBill) {
                bill = billConvert.toDo(dbBill);
            } else {
                bill.setOrderSerialNo(order.getSerialNo());
                bill.setBillDate(billDate);
                bill.setBillYear(bill.getBillDate().getYear());
                bill.setBillMonth(bill.getBillDate().getMonthValue());
                bill.setMerchantId(order.getConsignorId());
                bill.setStatus(false);
                bill.setPayWay(PayWay.UNDEFINED.getValue());
            }
            bill.setFreightCharge(order.getFreightCharge());
            bill.setOtherCharge(order.getOtherCharge());
            bill.setReceivableFreightCharge(order.getReceivableFreightCharge());
            bills.add(bill);
        }
        log.info("【Logistic transport】生成账单: {}", JsonUtils.object2Json(bills));
        billService.addOrUpdateBatch(bills);
        return bills;
    }

    /**
     * 账单支付状态
     *
     * @param billPaid 账单支付信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void billSettlementPaidStatus(BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            throw new InvalidParameterException("无效的订单id");
        }
        if (!WrapperClassUtils.biggerThanLong(billPaid.getTollMan(), 0L)) {
            throw new InvalidParameterException("无效的收费员");
        }
        if (!CollectionUtils.isEmpty(billPaid.getBillIds())) {
            log.info("【Logistic transport】根据结算单id更新支付状态: {}", JsonUtils.object2Json(billPaid));
            billSettlementService.updatePaidById(billPaid);
            billService.updatePaidBySettlementId(billPaid);
        } else {
            log.info("【Logistic transport】根据支付订单id更新支付状态: {}", JsonUtils.object2Json(billPaid));
            billSettlementService.updatePaidByOrderId(billPaid);
            billService.updatePaidByOrderId(billPaid);
        }

    }

    /**
     * 根据订单编号删除订单
     *
     * @param orderSerialNo 账单支付信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrderBySerialNo(String orderSerialNo) {
        //删除账单
        billService.deleteByOrderSerialNo(orderSerialNo);
        //删除订单
        orderService.deleteBySerialNos(Collections.singletonList(orderSerialNo));
    }


    /**
     * 订单审核
     *
     * @param orderAudit 订单审核参数
     */
    public void auditOrder(LogisticTransportOrderAuditVo orderAudit) {
        if (null == orderAudit || CollectionUtils.isEmpty(orderAudit.getOrderSerialNos())) {
            return;
        }
        List<LogisticTransportOrderVo> orders = orderService.listBySerialNos(orderAudit.getOrderSerialNos());
        List<LogisticTransportOrder> updateOrders = orders.stream().filter(x -> !x.getAuditStatus().equals(orderAudit.getAuditStatus())).map(x -> {
            LogisticTransportOrder order = new LogisticTransportOrder();
            order.setSerialNo(x.getSerialNo());
            order.setAuditStatus(orderAudit.getAuditStatus());
            order.setAuditorId(AdminContext.getUser().getUserId());
            order.setAuditorName(AdminContext.getUser().getRealName());
            order.setAuditTime(LocalDateTime.now());
            return order;
        }).collect(Collectors.toList());
        orderService.updateBatch(updateOrders);
    }

    /**
     * 结算单更新支付订单
     *
     * @param billPaid 账单支付信息
     */
    public void billSettlementUpdatePaidOrder(BillPaidVo billPaid) {
        billSettlementService.updatePaidOrder(billPaid);
    }
}


package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.domain.LogisticsDailyOrderDelivery;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryTotalAmountVo;
import com.senox.tms.mapper.LogisticsDailyOrderDeliveryMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticsDailyOrderDeliverySearchVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-5
 */
@Service
public class LogisticsDailyOrderDeliveryService extends ServiceImpl<LogisticsDailyOrderDeliveryMapper, LogisticsDailyOrderDelivery> {

    /**
     * 批量添加
     *
     * @param dailyOrderDeliveryList 物流日订单配送列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<LogisticsDailyOrderDelivery> dailyOrderDeliveryList) {
        if (CollectionUtils.isEmpty(dailyOrderDeliveryList)){
            return;
        }
        for (LogisticsDailyOrderDelivery dailyOrderDelivery : dailyOrderDeliveryList) {
            if (null == dailyOrderDelivery.getPieces()) {
                dailyOrderDelivery.setPieces(dailyOrderDelivery.getOrderPieces());
            }
            if (null == dailyOrderDelivery.getKilograms()) {
                dailyOrderDelivery.setKilograms(dailyOrderDelivery.getOrderTotalKilograms());
            }
            if (null==dailyOrderDelivery.getReceivedFreightCharge()){
                dailyOrderDelivery.setReceivedFreightCharge(dailyOrderDelivery.getReceivableFreightCharge());
            }
            dailyOrderDelivery.setFreightUnitPrice(dailyOrderDelivery.getReceivableFreightCharge().divide(dailyOrderDelivery.getKilograms(), 2, RoundingMode.HALF_UP));
            dailyOrderDelivery.setDiscrepancyAmount(DecimalUtils.subtract(dailyOrderDelivery.getReceivedFreightCharge(), dailyOrderDelivery.getReceivableFreightCharge()));
            dailyOrderDelivery.setCreateTime(LocalDateTime.now());
            dailyOrderDelivery.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityCreator(dailyOrderDelivery);
            ContextUtils.initEntityModifier(dailyOrderDelivery);
        }
        saveBatch(dailyOrderDeliveryList);
    }

    /**
     * 修改
     *
     * @param dailyOrderDelivery 物流日订单配送
     */
    public void update(LogisticsDailyOrderDelivery dailyOrderDelivery) {
        if (!WrapperClassUtils.biggerThanLong(dailyOrderDelivery.getId(), 0)) {
            throw new InvalidParameterException();
        }
        dailyOrderDelivery.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(dailyOrderDelivery);
        updateById(dailyOrderDelivery);
    }

    /**
     * 根据id查找
     *
     * @param id id
     * @return 返回查找到的配送
     */
    public LogisticsDailyOrderDelivery findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        return baseMapper.findById(id);
    }

    /**
     * 删除
     *
     * @param dailyOrderDeliveryId 物流日订单配送id
     */
    public void deleteById(Long dailyOrderDeliveryId) {
        if (!WrapperClassUtils.biggerThanLong(dailyOrderDeliveryId, 0)) {
            return;
        }
        removeById(dailyOrderDeliveryId);
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回列表
     */
    public List<LogisticsDailyOrderDelivery> list(LogisticsDailyOrderDeliverySearchVo searchVo) {
        return baseMapper.list(searchVo);
    }

    /**
     * 列表统计
     *
     * @param searchVo 查询
     * @return 返回统计
     */
    public int countList(LogisticsDailyOrderDeliverySearchVo searchVo) {
        return baseMapper.countList(searchVo);
    }

    /**
     * 列表分页查询
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageStatisticsResult<LogisticsDailyOrderDelivery, LogisticsDailyOrderDeliveryTotalAmountVo> listPage(LogisticsDailyOrderDeliverySearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<LogisticsDailyOrderDelivery> pageResult = PageUtils.commonPageResult(searchVo, () -> countList(searchVo), () -> list(searchVo));
        LogisticsDailyOrderDeliveryTotalAmountVo totalPageResult = baseMapper.totalListAmount(searchVo);
        return new PageStatisticsResult<>(pageResult, totalPageResult);
    }

}

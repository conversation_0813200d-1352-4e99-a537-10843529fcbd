package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.convert.LogisticTransportBillConvert;
import com.senox.tms.domain.LogisticTransportBill;
import com.senox.tms.mapper.LogisticTransportBillMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticTransportBillSearchVo;
import com.senox.tms.vo.LogisticTransportBillSettlementDetailVo;
import com.senox.tms.vo.LogisticTransportBillVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-22
 **/
@Service
@RequiredArgsConstructor
public class LogisticTransportBillService extends ServiceImpl<LogisticTransportBillMapper, LogisticTransportBill> {
    private final LogisticTransportBillConvert billConvert;

    /**
     * 批量新增
     * @param bills 账单集
     */
    public void addBatch(List<LogisticTransportBill> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        bills.forEach(x -> {
            ContextUtils.initEntityCreator(x);
            ContextUtils.initEntityModifier(x);
            x.setCreateTime(LocalDateTime.now());
            x.setModifiedTime(LocalDateTime.now());
        });
        baseMapper.addBatch(bills);
    }

    /**
     * 批量更新
     * @param bills 账单集
     */
    public void updateBatch(List<LogisticTransportBill> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        bills.forEach(x -> {
            ContextUtils.initEntityModifier(x);
            x.setModifiedTime(LocalDateTime.now());
        });
        baseMapper.updateBatch(bills);
    }

    /**
     * 批量新增或更新
     * @param bills 账单集
     */
    public void addOrUpdateBatch(List<LogisticTransportBill> bills) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        List<String> orderSerialNos = new ArrayList<>(bills.size());
        final LocalDate[] startDate = {null};
        final LocalDate[] endDate = {null};
        bills.forEach(bill -> {
            LocalDate billDate = bill.getBillDate();
            orderSerialNos.add(bill.getOrderSerialNo());
            if (null == startDate[0] || billDate.isBefore(startDate[0])) {
                startDate[0] = billDate;
            }
            if (null == endDate[0] || billDate.isAfter(endDate[0])) {
                endDate[0] = billDate;
            }
        });
        LogisticTransportBillSearchVo search = new LogisticTransportBillSearchVo();
        search.setPage(false);
        search.setBillDateStart(startDate[0]);
        search.setBillDateEnd(endDate[0]);
        search.setOrderSerialNos(orderSerialNos);
        List<LogisticTransportBill> dbBills = billConvert.toDo(list(search));
        DataSepDto<LogisticTransportBill> sepData = SeparateUtils.separateData(dbBills, bills);
        List<LogisticTransportBill> addList = sepData.getAddList();
        List<LogisticTransportBill> updateList = sepData.getUpdateList();
        if (!CollectionUtils.isEmpty(addList)) {
            addBatch(addList);
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            updateBatch(updateList);
        }
    }

    /**
     * 批量删除
     * @param ids id集
     */
    public void deleteBatch(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<LogisticTransportBillVo> bills = billListById(ids);
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        if (bills.stream().anyMatch(x -> WrapperClassUtils.biggerThanLong(x.getSettlementId(), 0))) {
            throw new BusinessException("结算单已生成，禁止删除");
        }
        if (bills.stream().anyMatch(LogisticTransportBillVo::getStatus)) {
            throw new BusinessException("账单已付款，禁止删除");
        }
        removeByIds(ids);
    }

    /**
     * 根据订单编号批量删除
     * @param orderSerialNo 订单编号
     */
    public void deleteByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return;
        }
        LogisticTransportBillVo bill = findByOrderSerialNo(orderSerialNo);
        if (null == bill) {
            return;
        }
        if (WrapperClassUtils.biggerThanLong(bill.getSettlementId(), 0)) {
            throw new BusinessException("账单已结算，禁止删除");
        }
        remove(new UpdateWrapper<LogisticTransportBill>()
                .lambda()
                .eq(LogisticTransportBill::getOrderSerialNo, orderSerialNo));
    }

    /**
     * 根据结算单id删除账单
     * @param ids 结算单id集
     */
    public void deleteBySettlementIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        remove(new UpdateWrapper<LogisticTransportBill>()
                .lambda()
                .in(LogisticTransportBill::getSettlementId, ids));
    }

    /**
     * 列表统计查询
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(LogisticTransportBillSearchVo search) {
        return baseMapper.countList(search);
    }

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    public List<LogisticTransportBillVo> list(LogisticTransportBillSearchVo search) {
        return baseMapper.list(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<LogisticTransportBillVo> pageList(LogisticTransportBillSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search, () -> countList(search), () -> list(search));
    }

    /**
     * 根据账单id获取账单列表
     * @param billIds 账单id集
     * @return 返回账单列表
     */
    public List<LogisticTransportBillVo> billListById(List<Long> billIds) {
        if (CollectionUtils.isEmpty(billIds)) {
            return new ArrayList<>();
        }
        LogisticTransportBillSearchVo search = new LogisticTransportBillSearchVo();
        search.setPage(false);
        search.setIds(billIds);
        return list(search);
    }

    /**
     * 根据结算单获取账单列表
     * @param settlementId 结算单id
     * @return 返回账单列表
     */
    public List<LogisticTransportBillVo> billListBySettlementId(Long settlementId) {
        if (!WrapperClassUtils.biggerThanLong(settlementId, 0)) {
            return Collections.emptyList();
        }
        LogisticTransportBillSearchVo search = new LogisticTransportBillSearchVo();
        search.setPage(false);
        search.setSettlementId(settlementId);
        return list(search);
    }

    /**
     * 根据订单编号查询账单
     * @param orderSerialNos 订单编号集
     * @return 返回账单集
     */
    public List<LogisticTransportBillVo> findByOrderSerialNo(List<String> orderSerialNos) {
        LogisticTransportBillSearchVo search = new LogisticTransportBillSearchVo();
        search.setPage(false);
        search.setOrderSerialNos(orderSerialNos);
        return list(search);
    }

    /**
     * 根据订单编号查询账单
     * @param orderSerialNo 订单编号
     * @return 返回账单
     */
    public LogisticTransportBillVo findByOrderSerialNo(String orderSerialNo) {
        return findByOrderSerialNo(Collections.singletonList(orderSerialNo)).stream().findFirst().orElse(null);
    }

    /**
     * 根据结算单id更新支付信息
     * @param billPaid 账单支付信息
     */
    public void updatePaidBySettlementId(BillPaidVo billPaid) {
        baseMapper.updatePaidBySettlementId(billPaid);
    }

    /**
     * 根据订单id更新支付信息
     * @param billPaid 账单支付信息
     */
    public void updatePaidByOrderId(BillPaidVo billPaid) {
        baseMapper.updatePaidByOrderId(billPaid);
    }

    /**
     * 根据结算单统计
     * @param settlementId 结算单id
     * @return 返回结算单统计
     */
    public LogisticTransportBillSettlementDetailVo statisticBySettlement(Long settlementId) {
        return baseMapper.statisticBySettlement(settlementId);
    }
}

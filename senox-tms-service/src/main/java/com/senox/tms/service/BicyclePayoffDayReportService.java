package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.domain.BicyclePayoffDayReport;
import com.senox.tms.mapper.BicyclePayoffDayReportMapper;
import com.senox.tms.vo.BicyclePayoffReportSearchVo;
import com.senox.tms.vo.BicycleTotalPageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 应付账单报表 服务实现类
 *
 * <AUTHOR>
 * @date 2023-10-13
 */
@Service
public class BicyclePayoffDayReportService extends ServiceImpl<BicyclePayoffDayReportMapper, BicyclePayoffDayReport> {

    /**
     * 批量添加报表
     *
     * @param payoffReports 报表列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<BicyclePayoffDayReport> payoffReports) {
        if (CollectionUtils.isEmpty(payoffReports)) {
            return;
        }
        saveBatch(payoffReports);
    }

    /**
     * 批量修改报表
     *
     * @param payoffReports 报表列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(List<BicyclePayoffDayReport> payoffReports) {
        if (CollectionUtils.isEmpty(payoffReports)) {
            return;
        }
        updateBatchById(payoffReports);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addBatchOrUpdate(String date, List<BicyclePayoffDayReport> payoffReports,boolean incrementalUpdate) {
        if (CollectionUtils.isEmpty(payoffReports)) {
            return;
        }
        BicyclePayoffReportSearchVo searchVo = new BicyclePayoffReportSearchVo();
        searchVo.setStartTime(date);
        searchVo.setEndTime(date);
        searchVo.setPage(false);
        DataSepDto<BicyclePayoffDayReport> dataSepDto = compareAndSepReport(listPage(searchVo).getDataList(), payoffReports,incrementalUpdate);
        if (!CollectionUtils.isEmpty(dataSepDto.getAddList())) {
            addBatch(dataSepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(dataSepDto.getUpdateList())) {
            updateBatch(dataSepDto.getUpdateList());
        }
    }

    /**
     * 比较报表
     *
     * @param srcReports    原报表
     * @param targetReports 目标报表
     * @param incrementalUpdate 增量更新
     */
    public DataSepDto<BicyclePayoffDayReport> compareAndSepReport(List<BicyclePayoffDayReport> srcReports, List<BicyclePayoffDayReport> targetReports, boolean incrementalUpdate) {
        List<BicyclePayoffDayReport> addReports;
        List<BicyclePayoffDayReport> updateReports = null;
        if (CollectionUtils.isEmpty(srcReports)) {
            addReports = targetReports;
        } else {
            addReports = new ArrayList<>(targetReports.size());
            updateReports = new ArrayList<>(targetReports.size());
            Map<Long, BicyclePayoffDayReport> srcReportMap = srcReports.stream().collect(Collectors.toMap(BicyclePayoffDayReport::getPayeeId, Function.identity()));
            for (BicyclePayoffDayReport targetReport : targetReports) {
                BicyclePayoffDayReport payoffReport = srcReportMap.get(targetReport.getPayeeId());
                if (null == payoffReport) {
                    addReports.add(targetReport);
                    continue;
                }
                BicyclePayoffDayReport updateReport = new BicyclePayoffDayReport();
                updateReport.setId(payoffReport.getId());
                updateReport.setPieces(incrementalUpdate ? payoffReport.getPieces() + targetReport.getPieces() : targetReport.getPieces());
                updateReport.setReferralAmount(incrementalUpdate ? payoffReport.getReferralAmount().add(targetReport.getReferralAmount()) : targetReport.getReferralAmount());
                updateReport.setReferralCount(incrementalUpdate ? payoffReport.getReferralCount() + targetReport.getReferralCount() : targetReport.getReferralCount());
                updateReport.setTotalAmount(incrementalUpdate ? DecimalUtils.add(payoffReport.getTotalAmount(), targetReport.getTotalAmount(), updateReport.getReferralAmount()) : targetReport.getTotalAmount());
                updateReport.setOrderNumber(incrementalUpdate ? payoffReport.getOrderNumber() + targetReport.getOrderNumber() : targetReport.getOrderNumber());
                updateReport.setModifierId(targetReport.getCreatorId());
                updateReport.setModifierName(targetReport.getCreatorName());
                updateReport.setModifiedTime(targetReport.getCreateTime());
                updateReports.add(updateReport);
            }
        }
        return new DataSepDto<>(addReports, updateReports, null);
    }
    public BicycleTotalPageResult<BicyclePayoffDayReport> listPage(BicyclePayoffReportSearchVo searchVo) {
        LambdaQueryWrapper<BicyclePayoffDayReport> lambdaQueryWrapper = new QueryWrapper<BicyclePayoffDayReport>().lambda();
        if (null != searchVo.getStartTime() && !StringUtils.isBlank(searchVo.getStartTime())) {
            lambdaQueryWrapper.ge(BicyclePayoffDayReport::getYearMonthDay, searchVo.getStartTime());
        }
        if (null != searchVo.getEndTime() && !StringUtils.isBlank(searchVo.getEndTime())) {
            lambdaQueryWrapper.le(BicyclePayoffDayReport::getYearMonthDay, searchVo.getEndTime());
        }
        if (null != searchVo.getPayeeName() && !StringUtils.isBlank(searchVo.getPayeeName())) {
            lambdaQueryWrapper.like(BicyclePayoffDayReport::getPayeeName, searchVo.getPayeeName());
        }
        PageResult<BicyclePayoffDayReport> pageResult = PageUtils.commonPageResult(searchVo, () -> count(lambdaQueryWrapper), () -> {
            String lastStr = "";
            if (searchVo.isPage()) {
                lastStr = lastStr.concat(" ").concat(String.format("limit %s , %s", searchVo.getOffset(), searchVo.getPageSize()));
            }
            lambdaQueryWrapper.last(lastStr);
            return list(lambdaQueryWrapper);
        });
        return new BicycleTotalPageResult<>(pageResult,baseMapper.listTotalAmount());
    }
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import com.senox.tms.constant.UnloadingOrderState;
import com.senox.tms.constant.UnloadingOrderWorkerStatus;
import com.senox.tms.convert.UnloadingOrderPayoffConvert;
import com.senox.tms.domain.*;
import com.senox.tms.mapper.UnloadingOrderPayoffMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/1 10:25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UnloadingOrderPayoffService extends ServiceImpl<UnloadingOrderPayoffMapper, UnloadingOrderPayoff> {

    private final UnloadingOrderService orderService;
    private final UnloadingOrderPayoffConvert orderPayoffConvert;

    /**
     * 生成应付
     * @param monthVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateOrderPayoff(UnloadingMonthVo monthVo) {
        List<UnloadingOrderVo> orderVos = listOrders(monthVo);
        List<UnloadingOrderPayoff> payoffs = listPayoffs(orderVos);
        List<UnloadingOrderPayoff> dbPayoffs = listPayoffs(monthVo);

        DataSepDto<UnloadingOrderPayoff> dataSepDto = separatePayoff(dbPayoffs, payoffs);
        log.info("转换后的sepDto值为 ---- {}", JsonUtils.object2Json(dataSepDto));
        if (!CollectionUtils.isEmpty(dataSepDto.getAddList())) {
            saveBatch(dataSepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(dataSepDto.getUpdateList())) {
            updateBatchById(dataSepDto.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(dataSepDto.getRemoveList())) {
            StringBuilder builder = new StringBuilder();
            dataSepDto.getRemoveList().forEach(x -> builder.append(x.getOrderNo()).append("|").append(x.getPayoffDate()).append("|").append(x.getWorkerNo()).append("\r\n"));

            if (builder.length() > 0) {
                builder.append("应付账单已缴费，不做更新。");
            }
            log.info(" --- 【应付账单生成】已缴费应付账单 --- {}", builder);
        }
        //更新订单应付金额
        //updateBatchOrderPayoffAmount(dataSepDto, orderVos);
    }

    private List<UnloadingOrderPayoff> listPayoffs(UnloadingMonthVo monthVo) {
        UnloadingOrderPayoffSearchVo searchVo = new UnloadingOrderPayoffSearchVo();
        if (WrapperClassUtils.biggerThanInt(monthVo.getYear(), 0)
                && WrapperClassUtils.biggerThanInt(monthVo.getMonth(), 0)) {
            searchVo.setPayoffDateStart(DateUtils.parseDate(monthVo.getYear(), monthVo.getMonth(), 1));
            searchVo.setPayoffDateEnd(DateUtils.parseDate(monthVo.getYear(), monthVo.getMonth() + 1, 1, true).minusDays(1));
        }
        searchVo.setMonthPayoffId(0L);
        searchVo.setOrderNoList(monthVo.getOrderNoList());
        searchVo.setOrderNo(monthVo.getOrderNo());
        searchVo.setPage(false);
        return orderPayoffConvert.toDo(listPayoff(searchVo));
    }

    private List<UnloadingOrderPayoff> listPayoffs(List<UnloadingOrderVo> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyList();
        }
        List<UnloadingOrderPayoff> bills = new ArrayList<>(orders.size());
        orders.forEach(order -> bills.addAll(buildOrderPayoff(order)));
        return bills;
    }

    private List<UnloadingOrderVo> listOrders(UnloadingMonthVo monthVo) {
        UnloadOrderSearchVo searchVo = new UnloadOrderSearchVo();
        searchVo.setOrderNo(monthVo.getOrderNo());
        searchVo.setOrderNoList(monthVo.getOrderNoList());
        searchVo.setState(UnloadingOrderState.NORMAL.getNumber());
        searchVo.setWorkerStatusList(Collections.singletonList(UnloadingOrderWorkerStatus.DONE.getNumber()));
        if (WrapperClassUtils.biggerThanInt(monthVo.getYear(), 0)
                && WrapperClassUtils.biggerThanInt(monthVo.getMonth(), 0)) {
            LocalDateTime startTime = DateUtils.parseDate(monthVo.getYear(), monthVo.getMonth(), 1).atStartOfDay();
            LocalDateTime endTime = DateUtils.parseDate(monthVo.getYear(), monthVo.getMonth() + 1, 1, true).minusDays(1).atStartOfDay();
            searchVo.setOrderTimeEnd(endTime);
            searchVo.setOrderTimeStart(startTime);
        }
        searchVo.setPage(false);
        return orderService.pageDetailResult(searchVo).getDataList();
    }

    private void updateBatchOrderPayoffAmount(DataSepDto<UnloadingOrderPayoff> dataSepDto, List<UnloadingOrderVo> orderVos) {
        if (CollectionUtils.isEmpty(dataSepDto.getAddList())) {
            dataSepDto.setAddList(Collections.emptyList());
        }
        if (CollectionUtils.isEmpty(dataSepDto.getUpdateList())) {
            dataSepDto.setUpdateList(Collections.emptyList());
        }
        List<UnloadingOrderPayoff> payoffList = new ArrayList<>(dataSepDto.getAddList().size() + dataSepDto.getUpdateList().size());
        payoffList.addAll(dataSepDto.getAddList());
        payoffList.addAll(dataSepDto.getUpdateList());
        if (CollectionUtils.isEmpty(orderVos) || CollectionUtils.isEmpty(payoffList)) {
            log.info("【更新订单分佣金额】---- 订单或应付记录为空。。。");
            return;
        }
        Map<String, UnloadingOrderVo> orderVoMap = orderVos.stream().collect(Collectors.toMap(UnloadingOrderVo::getOrderNo, Function.identity()));
        Map<String, List<UnloadingOrderPayoff>> payoffMap = payoffList.stream().collect(Collectors.groupingBy(UnloadingOrderPayoff::getOrderNo));
        List<UnloadingOrderVo> updateOrderList = new ArrayList<>(payoffMap.size());
        payoffMap.forEach((orderNo, payoffs) -> {
            UnloadingOrderVo orderVo = orderVoMap.get(orderNo);
            orderVo.setPayoffAmount(payoffs.stream().map(UnloadingOrderPayoff::getPayoffAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            updateOrderList.add(orderVo);
        });
        orderService.updateBatchOrderPayoffAmount(updateOrderList);
    }

    /**
     * 根据id查询应付记录
     * @param id
     * @return
     */
    public UnloadingOrderPayoff findPayoffById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据应付月账单id查询应付记录
     * @param monthPayoffId
     * @return
     */
    public List<UnloadingOrderPayoff> findByMonthPayoffId(Long monthPayoffId) {
        if (!WrapperClassUtils.biggerThanLong(monthPayoffId, 0L)) {
            throw new InvalidParameterException();
        }
        return findByMonthPayoffIds(Collections.singletonList(monthPayoffId));
    }

    /**
     * 根据应付月账单id集合查询应付记录
     * @param monthPayoffIds
     * @return
     */
    public List<UnloadingOrderPayoff> findByMonthPayoffIds(List<Long> monthPayoffIds) {
        if (CollectionUtils.isEmpty(monthPayoffIds)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<UnloadingOrderPayoff>().lambda().in(UnloadingOrderPayoff::getMonthPayoffId, monthPayoffIds));
    }

    /**
     * 重置账单
     * @param monthPayoffId
     */
    public void resetPayoffMonth(Long monthPayoffId) {
        List<UnloadingOrderPayoff> payoffs = findByMonthPayoffIds(Collections.singletonList(monthPayoffId));
        if (!CollectionUtils.isEmpty(payoffs)) {
            payoffs.forEach(payoff -> {
                payoff.setMonthPayoffId(0L);
                ContextUtils.initEntityModifier(payoff);
                payoff.setModifiedTime(LocalDateTime.now());
            });
            updateBatchById(payoffs);
        }
    }

    /**
     * 批量设置月应付账单id
     * @param payoffs
     * @param monthPayoffId
     */
    public void batchSetMonthPayoffId(List<UnloadingOrderPayoff> payoffs, Long monthPayoffId) {
        if (!WrapperClassUtils.biggerThanLong(monthPayoffId, 0L) || CollectionUtils.isEmpty(payoffs)) {
            throw new InvalidParameterException();
        }
        payoffs.forEach(payoff -> {
            payoff.setMonthPayoffId(monthPayoffId);
            ContextUtils.initEntityModifier(payoff);
            payoff.setModifiedTime(LocalDateTime.now());
        });
        updateBatchById(payoffs);
    }

    /**
     * 批量支付应付记录
     * @param monthPayoffIds
     */
    public void batchByMonthPayoffIds(List<Long> monthPayoffIds) {
        List<UnloadingOrderPayoff> payoffs = findByMonthPayoffIds(monthPayoffIds);
        payoffs.forEach(payoff -> {
            ContextUtils.initEntityModifier(payoff);
            payoff.setModifiedTime(LocalDateTime.now());
            payoff.setPayoffTime(LocalDateTime.now());
            payoff.setStatus(UnloadingOrderBillPayoffStatus.PAID.getNumber());
        });
        updateBatchById(payoffs);
    }

    /**
     * 根据订单号删除应付记录
     * @param orderNo
     */
    public void deletePayoffByOrderNo(String orderNo) {
        remove(new LambdaQueryWrapper<UnloadingOrderPayoff>().eq(UnloadingOrderPayoff::getOrderNo, orderNo));
    }

    /**
     * 更新应付金额
     * @param payoffList
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateSharesAmount(List<UnloadingOrderPayoff> payoffList) {
        if (CollectionUtils.isEmpty(payoffList)) {
            return;
        }
        List<String> orderNos = payoffList.stream().map(UnloadingOrderPayoff::getOrderNo).distinct().collect(Collectors.toList());
        if (orderNos.size() > 1) {
            throw new BusinessException("只允许更新同一个订单的金额!");
        }
        UnloadingOrder order = orderService.findByOrderNo(orderNos.get(0));
        List<UnloadingOrderPayoff> payoffs = listPayoffByOrderNo(order.getOrderNo());
        if (!CollectionUtils.isEmpty(payoffs) && payoffs.stream().anyMatch(x -> WrapperClassUtils.biggerThanLong(x.getMonthPayoffId(), 0L))) {
            throw new BusinessException("该订单已经生成了月应付账单，不允许分配金额...");
        }
        BigDecimal reduceAmount = payoffList.stream().map(UnloadingOrderPayoff::getPayoffAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (reduceAmount.compareTo(order.getAmount()) > 0) {
            throw new BusinessException("分配的金额超出订单应付总金额！");
        }
        payoffList.forEach(payoff -> {
            ContextUtils.initEntityModifier(payoff);
            payoff.setModifiedTime(LocalDateTime.now());
        });
        updateBatchById(payoffList);
    }

    /**
     * 根据订单编号查询应付记录
     * @param orderNo
     * @return
     */
    public List<UnloadingOrderPayoff> listPayoffByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<UnloadingOrderPayoff>().lambda().eq(UnloadingOrderPayoff::getOrderNo, orderNo));
    }

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    public int countPayoff(UnloadingOrderPayoffSearchVo searchVo) {
        return getBaseMapper().countPayoff(searchVo);
    }

    /**
     * 列表
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderPayoffVo> listPayoff(UnloadingOrderPayoffSearchVo searchVo) {
        return getBaseMapper().listPayoff(searchVo);
    }

    /**
     * 搬运工应付日信息
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderPayoffVo> payoffDailyList(UnloadingOrderPayoffSearchVo searchVo) {
        return getBaseMapper().payoffDailyList(searchVo);
    }

    /**
     * 分佣分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderPayoffVo> pagePayoff(UnloadingOrderPayoffSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> countPayoff(searchVo), () -> listPayoff(searchVo));
    }

    /**
     * 应付合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderPayoff sumPayoff(UnloadingOrderPayoffSearchVo searchVo) {
        return getBaseMapper().sumPayoff(searchVo);
    }

    /**
     * 判断字符串是单数还是双数
     * @param value 输入的字符串
     * @return
     */
    public int checkParity(String value) {
        // 检查是否为纯数字
        if (value.matches("\\d+")) {
            // 将字符串转换为整数
            int number = Integer.parseInt(value);
            // 判断单双数
            return (number % 2 == 0) ? 1 : 0;
        } else {
            log.info("搬运工编号非纯数字，置0");
            return 0;
        }
    }

    /**
     * 构建应付
     * @param orderVo
     * @return
     */
    private List<UnloadingOrderPayoff> buildOrderPayoff(UnloadingOrderVo orderVo) {
        List<UnloadingOrderWorkersVo> workersVoList = orderVo.getWorkersVoList();
        LocalDateTime nowTime = LocalDateTime.now();
        List<UnloadingOrderPayoff> payoffList = new ArrayList<>(workersVoList.size());
        //每人分得的金额（保留一位小数，向下取整）
        BigDecimal amountPerPerson = orderVo.getTotalAmount()
                .divide(new BigDecimal(workersVoList.size()), 1, RoundingMode.DOWN);
        //剩下的金额
        BigDecimal remainingAmount = DecimalUtils.subtract(orderVo.getTotalAmount()
                , DecimalUtils.multiple(amountPerPerson, BigDecimal.valueOf(orderVo.getWorkersVoList().size())));

        BigDecimal[] amounts = new BigDecimal[orderVo.getWorkersVoList().size()];
        // 分配金额给每个人
        Arrays.fill(amounts, amountPerPerson);

        // 将剩余的金额给第一个人
        amounts[0] = DecimalUtils.add(amounts[0], remainingAmount);

        for (int i = 0; i < workersVoList.size(); i++) {
            UnloadingOrderWorkersVo workers = workersVoList.get(i);
            UnloadingOrderPayoff payoff = new UnloadingOrderPayoff();
            LocalDateTime orderTime = orderVo.getOrderTime();
            payoff.setPayoffDate(orderVo.getOrderTime().toLocalDate());
            payoff.setPayoffYear(orderTime.getYear());
            payoff.setPayoffMonth(orderTime.getMonthValue());
            payoff.setOrderId(orderVo.getId());
            payoff.setOrderNo(orderVo.getOrderNo());
            payoff.setWorkerId(workers.getWorkerId());
            payoff.setWorkerNo(workers.getWorkerNo());
            payoff.setWorkerName(workers.getWorkerName());
            payoff.setPayoffAmount(amounts[i]);
            payoff.setOrderNum(checkParity(workers.getWorkerNo()));
            payoff.setCreateTime(nowTime);
            payoff.setModifiedTime(nowTime);
            payoff.setStatus(UnloadingOrderBillPayoffStatus.INIT.getNumber());
            ContextUtils.initEntityCreator(payoff);
            ContextUtils.initEntityModifier(payoff);
            payoffList.add(payoff);
        }
        return payoffList;
    }


    /**
     * 账单比较
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<UnloadingOrderPayoff> separatePayoff(List<UnloadingOrderPayoff> srcList,
                                                        List<UnloadingOrderPayoff> targetList) {
        List<UnloadingOrderPayoff> addList = new ArrayList<>(targetList.size());
        List<UnloadingOrderPayoff> updateList = new ArrayList<>(targetList.size());
        List<UnloadingOrderPayoff> removeList = new ArrayList<>(targetList.size());

        if (CollectionUtils.isEmpty(srcList)) {
            addList = targetList;

        } else {
            Map<String, List<UnloadingOrderPayoff>> srcMap = srcList.stream()
                    .collect(Collectors.groupingBy(UnloadingOrderPayoff::getOrderNo));

            Map<String, List<UnloadingOrderPayoff>> targetMap = targetList.stream()
                    .collect(Collectors.groupingBy(UnloadingOrderPayoff::getOrderNo));

            for (Map.Entry<String, List<UnloadingOrderPayoff>> targetEntry : targetMap.entrySet()) {
                List<UnloadingOrderPayoff> srcPayoffs = srcMap.get(targetEntry.getKey());
                if (CollectionUtils.isEmpty(srcPayoffs)) {
                    addList.addAll(targetEntry.getValue());
                } else {
                    Map<String, UnloadingOrderPayoff> srcWorkerMap = srcPayoffs.stream()
                            .collect(Collectors.toMap(UnloadingOrderPayoff::getWorkerNo, Function.identity()));
                    if (srcPayoffs.stream().allMatch(x -> Objects.equals(x.getStatus(), UnloadingOrderBillPayoffStatus.INIT.getNumber()))) {
                        //未缴费，可以更新
                        for (UnloadingOrderPayoff item : targetEntry.getValue()) {
                            UnloadingOrderPayoff srcItem = srcWorkerMap.get(item.getWorkerNo());
                            item.setId(srcItem.getId());
                            item.setCreatorId(null);
                            item.setCreatorName(null);
                            item.setCreateTime(null);
                            updateList.add(item);
                        }
                    } else {
                        // 提示不更新
                        removeList.addAll(targetEntry.getValue());
                    }
                }
            }
        }

        return new DataSepDto<>(addList, updateList, removeList);
    }

    /**
     * 当天最佳搬运工
     * @param startTime
     * @param endTime
     * @return
     */
    public List<UnloadingDayBestWorkerVo> listBestWorker(LocalDateTime startTime, LocalDateTime endTime) {
        return getBaseMapper().listBestWorker(startTime, endTime);
    }
}

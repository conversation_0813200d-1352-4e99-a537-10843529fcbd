package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.BicyclePoint;
import com.senox.tms.mapper.BicyclePointMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/12 14:26
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BicyclePointService extends ServiceImpl<BicyclePointMapper, BicyclePoint> {


    /**
     * 添加三轮车配送地点
     * @param bicyclePoint
     * @return
     */
    public Long addBicyclePoint(BicyclePoint bicyclePoint) {
        if (isBicyclePointExists(bicyclePoint.getName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "名称已存在");
        }
        if (bicyclePoint.getCreateTime() == null) {
            bicyclePoint.setCreateTime(LocalDateTime.now());
        }
        if (bicyclePoint.getModifiedTime() == null) {
            bicyclePoint.setModifiedTime(LocalDateTime.now());
        }
        long resultL = save(bicyclePoint) ? bicyclePoint.getId() : 0L;
        if (resultL > 0) {
            RedisUtils.del(TmsConst.Cache.KEY_BICYCLE_POINT);
        }
        return resultL;
    }

    /**
     * 更新三轮车配送地点
     *
     * @param bicyclePoint
     */
    public void updateBicyclePoint(BicyclePoint bicyclePoint) {
        if (!WrapperClassUtils.biggerThanLong(bicyclePoint.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        BicyclePoint point = findById(bicyclePoint.getId());
        if (point == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (!Objects.equals(point.getName(), bicyclePoint.getName())
                && isBicyclePointExists(bicyclePoint.getName())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "名称已存在");
        }
        bicyclePoint.setModifiedTime(LocalDateTime.now());
        boolean result = updateById(bicyclePoint);
        if (result) {
            RedisUtils.del(TmsConst.Cache.KEY_BICYCLE_POINT);
        }
    }

    /**
     * 删除三轮车配送地点
     * @param id
     */
    public void deleteBicyclePoint(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        boolean result = removeById(id);
        if (result) {
            RedisUtils.del(TmsConst.Cache.KEY_BICYCLE_POINT);
        }
    }

    /**
     * 根据id查询三轮车配送地点
     * @param id
     * @return
     */
    public BicyclePoint findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 三轮车配送地点列表
     * @return
     */
    public List<BicyclePoint> listBicyclePoint() {
        List<BicyclePoint> resultList = null;

        // load from cache
        String cacheValue = RedisUtils.get(TmsConst.Cache.KEY_BICYCLE_POINT);
        if (!StringUtils.isBlank(cacheValue)) {
            resultList = JsonUtils.json2GenericObject(cacheValue, new TypeReference<List<BicyclePoint>>() {});
        }

        // load from db
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = list(new QueryWrapper<BicyclePoint>().lambda().eq(BicyclePoint::getDisabled, Boolean.FALSE)
                    .select(BicyclePoint::getId, BicyclePoint::getName));

            // set cache
            if (!CollectionUtils.isEmpty(resultList)) {
                RedisUtils.set(TmsConst.Cache.KEY_BICYCLE_POINT, JsonUtils.object2Json(resultList), TmsConst.Cache.TTL_7D);
            }
        }
        return resultList;
    }

    /**
     * 判断名称是否存在
     * @param name
     * @return
     */
    private boolean isBicyclePointExists(String name) {
        if (StringUtils.isBlank(name)) {
            return false;
        }
        BicyclePoint deliveryPoint = getOne(new QueryWrapper<BicyclePoint>().lambda().eq(BicyclePoint::getName, name));
        return deliveryPoint != null;
    }

}

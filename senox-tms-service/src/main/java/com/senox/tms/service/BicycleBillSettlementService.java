package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.OrderStatus;
import com.senox.pm.constant.OrderType;
import com.senox.pm.constant.PayWay;
import com.senox.pm.vo.OrderItemDetailVo;
import com.senox.pm.vo.OrderItemVo;
import com.senox.pm.vo.OrderVo;
import com.senox.tms.component.OrderComponent;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.convert.BicycleBillSettlementConvert;
import com.senox.tms.domain.BicycleBill;
import com.senox.tms.domain.BicycleBillDetail;
import com.senox.tms.domain.BicycleBillSettlement;
import com.senox.tms.dto.BicycleBillSettlementContentAdditional;
import com.senox.tms.event.BicycleBillSettlementNotifyEvent;
import com.senox.tms.mapper.BicycleBillSettlementMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-11-8
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BicycleBillSettlementService extends ServiceImpl<BicycleBillSettlementMapper, BicycleBillSettlement> {
    private final BicycleBillDetailService billDetailService;
    private final BicycleBillService billService;
    private final OrderComponent orderComponent;
    private final BicycleBillSettlementConvert settlementConvert;
    private final ApplicationEventPublisher publisher;

    /**
     * 批量添加结算
     *
     * @param billSettlements 账单结算集
     */
    public void addBatch(List<BicycleBillSettlement> billSettlements) {
        if (CollectionUtils.isEmpty(billSettlements)) {
            return;
        }
        billSettlements.forEach(b -> {
            ContextUtils.initEntityCreator(b);
            ContextUtils.initEntityModifier(b);
        });
        int count = baseMapper.addBatch(billSettlements);
        if (count == 0) {
            throw new BusinessException(ResultConst.ERROR);
        }
    }

    /**
     * 添加结算
     *
     * @param billSettlement 账单结算
     */
    public void add(BicycleBillSettlement billSettlement) {
        addBatch(Collections.singletonList(billSettlement));
    }

    /**
     * 批量更新结算
     *
     * @param billSettlements 账单结算集
     */
    public void updateBatch(List<BicycleBillSettlement> billSettlements) {
        if (CollectionUtils.isEmpty(billSettlements)) {
            return;
        }
        billSettlements.forEach(ContextUtils::initEntityModifier);
        baseMapper.updateBatch(billSettlements);
    }

    /**
     * 更新结算
     *
     * @param billSettlement 账单结算
     */
    public void update(BicycleBillSettlement billSettlement) {
        updateBatch(Collections.singletonList(billSettlement));
    }


    /**
     * 更新结算远程订单
     *
     * @param billSettlements 账单结算集
     * @param orderResult     订单返回
     * @param billOrder       订单
     */
    public void updateRemoteOrder(List<BicycleBillSettlementVo> billSettlements, OrderResultVo orderResult, BicycleBillOrderVo billOrder) {
        if (CollectionUtils.isEmpty(billSettlements)) {
            return;
        }
        billSettlements.forEach(b -> {
            BicycleBillSettlement billSettlement = new BicycleBillSettlement();
            billSettlement.setId(b.getId());
            billSettlement.setRemoteOrderId(orderResult.getOrderId());
            if (OrderStatus.fromStatus(orderResult.getStatus()) == OrderStatus.PAID) {
                billSettlement.setStatus(true);
                billSettlement.setTollManId(billOrder.getTollManId());
                billSettlement.setPaidAmount(b.getAmount());
                billSettlement.setPaidTime(LocalDateTime.now());
                billSettlement.setPayWay(PayWay.fromValue(billOrder.getPayWay()).getValue());
            }
            update(billSettlement);
        });
    }

    /**
     * 根据id查询结算单
     *
     * @param id id
     * @return 返回结算单
     */
    public BicycleBillSettlementVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        return baseMapper.findById(id);
    }

    /**
     * 账单支付
     *
     * @param billOrder 订单
     */
    public OrderResultVo payBillOrder(BicycleBillOrderVo billOrder) {
        if (CollectionUtils.isEmpty(billOrder.getSettlementIds())) {
            return null;
        }
        List<BicycleBillSettlementVo> billSettlements = listByIds(billOrder.getSettlementIds());
        if (CollectionUtils.isEmpty(billSettlements)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        billSettlements.forEach(b -> {
            if (!RedisUtils.lock(String.format(TmsConst.Cache.KEY_BICYCLE_BILL_SETTLEMENT_LOCK, b.getId()), TmsConst.Cache.TTL_1H)) {
                throw new BusinessException("操作太频繁，请稍后尝试");
            }
        });
        OrderResultVo orderResult;
        try {
            billSettlements.forEach(this::checkBillSettlementPayTerms);
            OrderVo orderVo = new OrderVo();
            orderVo.setTitle(String.format(TmsConst.TITLE_BICYCLE_BILL_TITLE, DateUtils.formatYearMonth(LocalDate.now())));
            orderVo.setOrderType(OrderType.BICYCLE);
            orderVo.setWxOpenid(billOrder.getWxOpenid());
            orderVo.setPayWay(PayWay.fromValue(billOrder.getPayWay()));
            orderVo.setLongitude(billOrder.getLongitude());
            orderVo.setLatitude(billOrder.getLatitude());
            orderVo.setCreateIp(billOrder.getRequestIp());
            orderVo.setItems(billSettlements.stream().map(this::builderOrderItem).collect(Collectors.toList()));
            orderResult = JsonUtils.json2Object(JsonUtils.object2Json(orderComponent.addOrder(orderVo)), OrderResultVo.class);
            if (null == orderResult || !WrapperClassUtils.biggerThanLong(orderResult.getOrderId(), 0)) {
                throw new BusinessException("下单失败");
            }
            //更新远程订单
            updateRemoteOrder(billSettlements, orderResult, billOrder);

        } finally {
            billSettlements.forEach(b -> RedisUtils.del(String.format(TmsConst.Cache.KEY_BICYCLE_BILL_SETTLEMENT_LOCK, b.getId())));
        }
        return orderResult;

    }


    /**
     * 根据id集查询结算列表
     *
     * @param ids id集
     * @return 返回查询到的结算列表
     */
    public List<BicycleBillSettlementVo> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return baseMapper.listByIds(ids);
    }

    /**
     * 根据账单保存结算
     *
     * @param bills             账单集
     * @param contentAdditional 附加内容
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveByBill(List<BicycleBillVo> bills, BicycleBillSettlementContentAdditional contentAdditional) {
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        List<BicycleBillVo> passBills = bills.stream().filter(b -> !WrapperClassUtils.biggerThanLong(b.getSettlementId(), 0)).collect(Collectors.toList());
        Map<Long, List<BicycleBillVo>> billMap = passBills.stream().collect(Collectors.groupingBy(BicycleBillVo::getMerchantId));
        if (CollectionUtils.isEmpty(billMap)) {
            return;
        }
        List<BicycleBillSettlement> newSettlements = new ArrayList<>(bills.size());
        LinkedHashMap<Integer, List<BicycleBillVo>> updateBillMap = new LinkedHashMap<>();
        LocalDate billDate = passBills.stream().map(BicycleBillVo::getBillDate).max(Comparator.naturalOrder()).orElse(LocalDate.now());
        billMap.forEach((k, v) -> {
            BicycleBillSettlement settlement = new BicycleBillSettlement();
            settlement.setBillDate(billDate);
            settlement.setBillYear(billDate.getYear());
            settlement.setBillMonth(billDate.getMonthValue());
            settlement.setBillYearMonth(StringUtils.buildYearMonthStr(settlement.getBillYear(), settlement.getBillMonth()));
            settlement.setMerchantId(v.get(0).getMerchantId());
            settlement.setMerchantName(v.get(0).getMerchantName());
            settlement.setAmount(v.stream().map(BicycleBillVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            settlement.setSettlePeriod(v.get(0).getSettlePeriod());
            newSettlements.add(settlement);
            updateBillMap.put(newSettlements.size() - 1, v);
        });
        log.info("【Bicycle】Bill settlement wait add: {}", JsonUtils.object2Json(newSettlements));
        addBatch(newSettlements);
        List<Long> deleteSettlementIds = new ArrayList<>(newSettlements.size());
        newSettlements.forEach(settlement -> {
            if (contentAdditional.isSend()) {
                settlement.setSend(Boolean.TRUE);
                settlement.setSendTime(LocalDateTime.now());
            }
            Map.Entry<Integer, List<BicycleBillVo>> next = updateBillMap.entrySet().iterator().next();
            updateBillMap.remove(next.getKey());
            List<BicycleBillVo> updateSettlementBills = next.getValue();
            updateSettlementBills.forEach(bill -> {
                BicycleBill updateBill = new BicycleBill();
                updateBill.setId(bill.getId());
                int updateResult = billService.updateSettlement(updateBill, settlement.getId());
                if (updateResult < 1) {
                    log.info("【Bicycle】账单[{}]的结算单[{}]更新失败", bill.getId(), settlement.getId());
                    //待删结算单
                    deleteSettlementIds.add(settlement.getId());
                }
            });
        });
        if (contentAdditional.isSend()) {
            List<BicycleBillSettlement> sendSettlements = newSettlements.stream().filter(s -> !deleteSettlementIds.contains(s.getId())).collect(Collectors.toList());
            log.info("【Bicycle】Bill settlement update: {}", JsonUtils.object2Json(sendSettlements));
            updateBatch(sendSettlements);
            publisher.publishEvent(new BicycleBillSettlementNotifyEvent(this, sendSettlements));
        }
        if (!CollectionUtils.isEmpty(deleteSettlementIds)) {
            deleteByIds(deleteSettlementIds);
        }
    }

    /**
     * 列表查询
     *
     * @param searchVo 查询参数
     * @return 返回查询到的列表
     */
    public List<BicycleBillSettlementVo> list(BicycleBillSettlementSearchVo searchVo) {
        return baseMapper.list(searchVo);
    }

    /**
     * 列表统计查询
     *
     * @param searchVo 查询参数
     * @return 返回统计数
     */
    public int countList(BicycleBillSettlementSearchVo searchVo) {
        return baseMapper.countList(searchVo);
    }


    /**
     * 账单状态更新
     *
     * @param billPaidVo 账单支付信息
     */
    public void updateStatus(BillPaidVo billPaidVo) {
        if (!WrapperClassUtils.biggerThanLong(billPaidVo.getOrderId(), 0L)) {
            throw new InvalidParameterException();
        }
        if (BooleanUtils.isTrue(billPaidVo.getPaid())) {
            updatePaid(billPaidVo);
        }

    }

    /**
     * 桀算单支付更新
     *
     * @param billPaidVo 账单支付信息
     */
    private int updatePaid(BillPaidVo billPaidVo) {
        return baseMapper.updatePaid(billPaidVo);
    }

    /**
     * 根据结算单id查询详情
     *
     * @param settlementId 结算单id
     * @return 返回查询到的详情
     */
    public BicycleBillSettlementDetailVo detailBySettlement(Long settlementId) {
        if (!WrapperClassUtils.biggerThanLong(settlementId, 0)) {
            return null;
        }
        BicycleBillSettlementDetailVo billSettlementCount = baseMapper.countDetailByDetailBySettlement(settlementId);
        billSettlementCount.setBillList(baseMapper.detailBySettlement(settlementId));
        return billSettlementCount;
    }

    /**
     * 结算单下发
     *
     * @param sendVo 下发参数
     */
    public void send(BicycleBillSettlementSendVo sendVo) {
        if ((null == sendVo.getStartDate() || null == sendVo.getEndDate())
                && CollectionUtils.isEmpty(sendVo.getSettlementIds())
        ) {
            throw new InvalidParameterException();
        }
        baseMapper.send(sendVo);
    }

    /**
     * 结算单下发通知
     *
     * @param sendVo 下发参数
     */
    public void notifySend(BicycleBillSettlementSendVo sendVo) {
        if (null == sendVo.getStartDate() || null == sendVo.getEndDate()) {
            throw new InvalidParameterException();
        }
        BicycleBillSettlementSearchVo searchVo = new BicycleBillSettlementSearchVo();
        searchVo.setPage(false);
        searchVo.setStartDate(sendVo.getStartDate());
        searchVo.setEndDate(sendVo.getEndDate());
        searchVo.setStatus(false);
        searchVo.setSend(true);
        List<BicycleBillSettlementVo> list = list(searchVo);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        publisher.publishEvent(new BicycleBillSettlementNotifyEvent(this, settlementConvert.toDo(list)));
    }


    /**
     * 校验结算单是否满足支付条件
     */
    private void checkBillSettlementPayTerms(BicycleBillSettlementVo billSettlement) {
        if (BooleanUtils.isTrue(billSettlement.getStatus())) {
            throw new BusinessException(String.format("账单[%s]-[%s]已支付", billSettlement.getMerchantName(), billSettlement.getBillMonth()));
        }
        checkBillSettlementOrder(billSettlement, b -> {
            BicycleBillSettlement newBillSettlement = new BicycleBillSettlement();
            newBillSettlement.setId(b.getId());
            newBillSettlement.setStatus(true);
            update(newBillSettlement);
        });

    }

    /**
     * 检查结算订单
     *
     * @param billSettlement 结算单
     * @param consumer       回调
     */
    private void checkBillSettlementOrder(BicycleBillSettlementVo billSettlement, Consumer<BicycleBillSettlementVo> consumer) {
        if (null == billSettlement || !WrapperClassUtils.biggerThanLong(billSettlement.getRemoteOrderId(), 0L)) {
            return;
        }
        Long orderId = billSettlement.getRemoteOrderId();
        OrderVo remoteOrder = orderComponent.findOrderById(orderId);
        if (remoteOrder == null) {
            return;
        }

        if (remoteOrder.getStatus() == OrderStatus.PAID) {
            consumer.accept(billSettlement);
            throw new BusinessException("部分账单已支付");
        }

        if (remoteOrder.getRemoteOrderTime() != null
                && (remoteOrder.getRemoteOrderTime().plusSeconds(5L).isAfter(LocalDateTime.now()))) {
            throw new BusinessException("下单太频繁，请稍后尝试");
        }

    }


    /**
     * 列表分页查询
     *
     * @param searchVo 查询参数
     * @return 返回分页后的列表
     */
    public BicycleBillSettlementPageResult<BicycleBillSettlementVo> listPage(BicycleBillSettlementSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return BicycleBillSettlementPageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<BicycleBillSettlementVo> pageResult = PageUtils.commonPageResult(searchVo, () -> countList(searchVo), () -> list(searchVo));
        BicycleBillSettlementPageResult<BicycleBillSettlementVo> billSettlementPageResult = new BicycleBillSettlementPageResult<>(pageResult);
        BicycleBillSettlementDetailVo detailVo = baseMapper.listCount(searchVo);
        billSettlementPageResult.setTotalAmount(detailVo.getTotalAmount());
        billSettlementPageResult.setTotalPaidAmount(detailVo.getTotalPaidAmount());
        return billSettlementPageResult;
    }

    /**
     * 构建订单项
     *
     * @param billSettlement 结算单
     * @return 返回构建好的订单项
     */
    private OrderItemVo builderOrderItem(BicycleBillSettlementVo billSettlement) {
        OrderItemVo orderItem = new OrderItemVo();
        orderItem.setProductId(billSettlement.getId());
        orderItem.setProductName(String.format(TmsConst.TITLE_BICYCLE_BILL, billSettlement.getBillMonth(), billSettlement.getMerchantName()));
        orderItem.setQuantity(1);
        orderItem.setPrice(billSettlement.getAmount());
        orderItem.setTotalAmount(billSettlement.getAmount());
        orderItem.setFree(false);
        orderItem.setDetails(builderOrderItemDetail(billSettlement));
        return orderItem;

    }

    /**
     * 构建订单项明细
     *
     * @param billSettlement 结算单
     * @return 返回构建好的订单项明细
     */
    private List<OrderItemDetailVo> builderOrderItemDetail(BicycleBillSettlementVo billSettlement) {
        List<BicycleBill> bills = billService.billListBySettlementId(billSettlement.getId());
        List<BicycleBillDetail> billDetails = billDetailService.listByBillIds(bills.stream().map(BicycleBill::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(billDetails)) {
            return Collections.emptyList();
        }
        List<OrderItemDetailVo> orderItemDetails = new ArrayList<>(billDetails.size());
        Collection<BicycleBillDetail> billDetailMerges = billDetails.stream().collect(Collectors.toMap(BicycleBillDetail::getFeeId, Function.identity(), (d1, d2) -> {
            d1.setAmount(d1.getAmount().add(d2.getAmount()));
            return d1;
        })).values();
        orderItemDetails.addAll(billDetailMerges.stream().map(this::billDetailToOrderItemDetail).collect(Collectors.toList()));
        return orderItemDetails;
    }

    private OrderItemDetailVo billDetailToOrderItemDetail(BicycleBillDetail billDetail) {
        OrderItemDetailVo orderItemDetail = new OrderItemDetailVo();
        orderItemDetail.setFeeId(billDetail.getFeeId());
        orderItemDetail.setFeeName(billDetail.getFeeName());
        orderItemDetail.setPrice(billDetail.getAmount());
        orderItemDetail.setQuantity(1);
        orderItemDetail.setTotalAmount(billDetail.getAmount());
        return orderItemDetail;
    }

    /**
     * 结算单生成
     *
     * @param sendVo 参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void generate(BicycleBillSettlementSendVo sendVo) {
        BicycleBillSearchVo searchVo = new BicycleBillSearchVo();
        if (CollectionUtils.isEmpty(sendVo.getBillIds())) {
            if (null == sendVo.getStartDate() || null == sendVo.getEndDate()) {
                throw new InvalidParameterException();
            }
            String startDate = sendVo.getStartDate();
            String endDate = sendVo.getEndDate();
            searchVo.setStartDate(DateUtils.parseDate(startDate));
            searchVo.setEndDate(DateUtils.parseDate(endDate));
        } else {
            searchVo.setBillIds(sendVo.getBillIds());
        }
        searchVo.setContainSettlement(false);
        Set<Integer> statusSet = new HashSet<>();
        statusSet.add(BicycleDeliveryOrderStatus.SEND_COMPLETED.getNumber());
        statusSet.add(BicycleDeliveryOrderStatus.CONFIRM_COMPLETION.getNumber());
        searchVo.setStatusSet(statusSet);
        List<BicycleBillVo> billList = billService.listBill(searchVo);
        saveByBill(billList, BicycleBillSettlementContentAdditional.builder().build());
    }


    /**
     * 根据id删除
     * @param ids id集
     */
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        removeByIds(ids);
    }
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.domain.OutgoingTtl;
import com.senox.tms.mapper.OutgoingTtlMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticStatisticsDayReportPageResult;
import com.senox.tms.vo.OutgoingTtlSearchVo;
import com.senox.tms.vo.OutgoingTtlVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/8 11:27
 */
@Service
@Slf4j
public class OutgoingTtlService extends ServiceImpl<OutgoingTtlMapper, OutgoingTtl> {

    /**
     * 批量添加太太乐外发
     * @param outgoingTtlList
     */
    public void batchAdd(List<OutgoingTtl> outgoingTtlList) {
        List<OutgoingTtl> dbOutgoingList = listOutgoingByLogisticsNoList(outgoingTtlList);
        DataSepDto<OutgoingTtl> sepDto = SeparateUtils.separateData(dbOutgoingList, outgoingTtlList);
        if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
            sepDto.getAddList().forEach(item -> {
                calculateProfitAmount(item);
                ContextUtils.initEntityCreator(item);
                ContextUtils.initEntityModifier(item);
                item.setCreateTime(LocalDateTime.now());
                item.setModifiedTime(LocalDateTime.now());
            });
            saveBatch(sepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getUpdateList())) {
            sepDto.getUpdateList().forEach(item -> {
                calculateProfitAmount(item);
                ContextUtils.initEntityModifier(item);
                item.setModifiedTime(LocalDateTime.now());
            });
            updateBatchById(sepDto.getUpdateList());
        }
    }

    /**
     * 新增太太乐外发
     * @param outgoingTtl
     * @return
     */
    public Long addOutgoingTtl(OutgoingTtl outgoingTtl) {
        OutgoingTtl dbOutgoing = findOutgoing(outgoingTtl);
        if (dbOutgoing != null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "太太乐外发单号已存在，单号为：" + outgoingTtl.getLogisticsNo());
        }
        calculateProfitAmount(outgoingTtl);
        ContextUtils.initEntityCreator(outgoingTtl);
        ContextUtils.initEntityModifier(outgoingTtl);
        outgoingTtl.setCreateTime(LocalDateTime.now());
        outgoingTtl.setModifiedTime(LocalDateTime.now());
        boolean save = save(outgoingTtl);
        return save ? outgoingTtl.getId() : 0L;
    }

    /**
     * 更新太太乐外发
     * @param outgoingTtl
     */
    public void updateOutgoingTtl(OutgoingTtl outgoingTtl) {
        if (!WrapperClassUtils.biggerThanLong(outgoingTtl.getId(), 0)) {
            throw new InvalidParameterException();
        }
        calculateProfitAmount(outgoingTtl);
        ContextUtils.initEntityModifier(outgoingTtl);
        outgoingTtl.setModifiedTime(LocalDateTime.now());
        updateById(outgoingTtl);
    }

    /**
     * 计算利润
     * @param outgoingTtl
     */
    private void calculateProfitAmount(OutgoingTtl outgoingTtl) {
        if (outgoingTtl.getActualFreightAmount() != null
                && outgoingTtl.getActualShippingAmount() != null) {
            outgoingTtl.setProfitAmount(DecimalUtils.subtract(outgoingTtl.getActualFreightAmount(), outgoingTtl.getActualShippingAmount()));
        }
        log.info("太太乐单号{}的利润是：{}", outgoingTtl.getLogisticsNo(), outgoingTtl.getProfitAmount());
    }

    /**
     * 根据Id获取太太乐外发
     * @param id
     * @return
     */
    public OutgoingTtl findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据Id删除太太乐外发
     * @param id
     */
    public void deleteById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        removeById(id);
    }

    /**
     * 根据物流单号查询太太乐外发
     * @param outgoingTtl
     * @return
     */
    private OutgoingTtl findOutgoing(OutgoingTtl outgoingTtl) {
        List<OutgoingTtl> resultList = listOutgoingByLogisticsNoList(Collections.singletonList(outgoingTtl));
        return CollectionUtils.isEmpty(resultList) ? null : resultList.get(0);
    }

    /**
     * 根据物流单号查询太太乐外发
     * @param outgoingTtlList
     * @return
     */
    private List<OutgoingTtl> listOutgoingByLogisticsNoList(List<OutgoingTtl> outgoingTtlList) {
        if (CollectionUtils.isEmpty(outgoingTtlList)) {
            return Collections.emptyList();
        }
        List<OutgoingTtl> resultList = new ArrayList<>(outgoingTtlList.size());
        List<String> logisticsList = outgoingTtlList.stream().map(OutgoingTtl::getLogisticsNo).collect(Collectors.toList());
        resultList.addAll(getBaseMapper().listOutgoingByLogisticsNoList(logisticsList));
        return resultList;
    }

    /**
     * 太太乐外发列表
     * @param searchVo
     * @return
     */
    public PageStatisticsResult<OutgoingTtlVo, OutgoingTtlVo> pageOutgoing(OutgoingTtlSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new  PageStatisticsResult<>();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<OutgoingTtlVo> result = PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countOutgoing(searchVo), () -> getBaseMapper().listOutgoing(searchVo));
        OutgoingTtlVo sumOutgoing = getBaseMapper().sumOutgoing(searchVo);
        return new PageStatisticsResult<>(result, sumOutgoing);
    }
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.LogisticOrderBill;
import com.senox.tms.mapper.LogisticOrderBillMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/29 14:30
 */
@Service
public class LogisticOrderBillService extends ServiceImpl<LogisticOrderBillMapper, LogisticOrderBill> {

    /**
     * 新增物流订单账单
     * @param bill
     * @return
     */
    public Long addOrderBill(LogisticOrderBill bill) {
        // 初始化配送单信息
        prepareOrderBill(bill);

        // 创建人信息初始化
        bill.setCreateTime(LocalDateTime.now());
        bill.setModifierId(bill.getCreatorId());
        bill.setModifierName(bill.getCreatorName());
        bill.setModifiedTime(bill.getCreateTime());

        save(bill);

        // 更新账单金额
        updateOrderBillAmount(Collections.singletonList(bill.getOrderProductId()));
        return bill.getId();
    }

    /**
     * 批量保存物流订单账单
     * @param list
     * @param overwrite
     */
    public void saveOrderBillBatch(List<LogisticOrderBill> list, Boolean overwrite) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<LogisticOrderBill> rawList = listOrderBillByProductAndShip(list);
        DataSepDto<LogisticOrderBill> sepData = SeparateUtils.separateData(rawList, list);

        // 新增数据
        addOrderBillBatch(sepData.getAddList());
        // 更新数据
        if (BooleanUtils.isTrue(overwrite)) {
            updateOrderBillBatch(sepData.getUpdateList());
        }

        // 更细账单金额
        updateOrderBillAmount(list.stream().map(LogisticOrderBill::getOrderProductId).distinct().collect(Collectors.toList()));
    }

    /**
     * 批量添加物流订单账单
     * @param list
     */
    private void addOrderBillBatch(List<LogisticOrderBill> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (LogisticOrderBill item : list) {
            // 初始化配送单信息
            prepareOrderBill(item);

            // 创建人信息初始化
            item.setCreateTime(LocalDateTime.now());
            item.setModifierId(item.getCreatorId());
            item.setModifierName(item.getCreatorName());
            item.setModifiedTime(item.getCreateTime());
        }
        saveBatch(list);
    }

    /**
     * 更新物流账单
     * @param bill
     */
    public void updateOrderBill(LogisticOrderBill bill) {
        if (!WrapperClassUtils.biggerThanLong(bill.getId(), 0L)) {
            return;
        }

        bill.setCreatorId(null);
        bill.setCreatorName(null);
        bill.setCreateTime(null);
        bill.setModifiedTime(LocalDateTime.now());

        updateById(bill);
        // 更新账单金额
        updateOrderBillAmount(Collections.singletonList(bill.getOrderProductId()));
    }

    /**
     * 批量更新物流订单账单
     * @param list
     */
    private void updateOrderBillBatch(List<LogisticOrderBill> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (LogisticOrderBill item : list) {
            // 创建人信息初始化
            item.setCreatorId(null);
            item.setCreatorName(null);
            item.setCreateTime(null);
            item.setModifiedTime(LocalDateTime.now());
        }

        updateBatchById(list);
    }

    /**
     * 更新物流账单金额
     * @param list
     */
    public void updateOrderBillAmount(List<Long> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        getBaseMapper().updateOrderBillAmount(list);
        getBaseMapper().updateBillAmount(list);
    }

    /**
     * 根据日期更新物流账单金额
     * @param shipDates
     */
    public void updateOrderBillAmountByShipDates(List<LocalDate> shipDates) {
        if (CollectionUtils.isEmpty(shipDates)) {
            return;
        }
        getBaseMapper().updateOrderBillAmountByShipDates(shipDates);
        getBaseMapper().updateBillAmountByShipDates(shipDates);
    }

    /**
     * 删除订单id
     * @param orderIds
     */
    public void deleteByOrderIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }

        LambdaQueryWrapper<LogisticOrderBill> queryWrapper = new QueryWrapper<LogisticOrderBill>().lambda().in(LogisticOrderBill::getOrderProductId, orderIds);
        remove(queryWrapper);
    }

    /**
     * 根据商品订单及配送订单查找账单
     * @param bill
     * @return
     */
    public LogisticOrderBill findOrderBillByProductAndShip(LogisticOrderBill bill) {
        List<LogisticOrderBill> list = listOrderBillByProductAndShip(Collections.singletonList(bill));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 根据商品订单及配送订单查找账单
     * @param list
     * @return
     */
    private List<LogisticOrderBill> listOrderBillByProductAndShip(List<LogisticOrderBill> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<LogisticOrderBill> resultList = new ArrayList<>(list.size());
        for (int index = 0; index < list.size(); index += TmsConst.BATCH_SIZE_1000) {
            List<LogisticOrderBill> batchList = list.stream().skip(index).limit(TmsConst.BATCH_SIZE_1000).collect(Collectors.toList());
            resultList.addAll(getBaseMapper().listOrderBillByProductAndShip(batchList));
        }
        return resultList;
    }

    /**
     * 订单账单初始化
     * @param bill
     */
    private void prepareOrderBill(LogisticOrderBill bill) {
        bill.setOrderAmount(DecimalUtils.nullToZero(bill.getOrderAmount()));
        bill.setProductPaid(DecimalUtils.nullToZero(bill.getProductPaid()));
        bill.setProductPaidManual(BooleanUtils.isTrue(bill.getProductPaidManual()));
        bill.setProductDiversity(DecimalUtils.nullToZero(bill.getProductDiversity()));
        bill.setProductOwe(DecimalUtils.nullToZero(bill.getProductOwe()));
        bill.setShipAmount(DecimalUtils.nullToZero(bill.getShipAmount()));
        bill.setTotalAmount(DecimalUtils.nullToZero(bill.getTotalAmount()));
    }
}

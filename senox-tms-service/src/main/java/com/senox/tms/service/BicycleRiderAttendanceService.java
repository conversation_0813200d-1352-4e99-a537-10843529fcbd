package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.BicycleRiderStatus;
import com.senox.tms.domain.BicycleRiderAttendance;
import com.senox.tms.mapper.BicycleRiderAttendanceMapper;
import com.senox.tms.vo.BicycleRiderAttendanceSearchVo;
import com.senox.tms.vo.BicycleRiderAttendanceVo;
import com.senox.tms.vo.BicycleRiderCountVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/14 16:56
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BicycleRiderAttendanceService extends ServiceImpl<BicycleRiderAttendanceMapper, BicycleRiderAttendance> {

    private final BicycleRiderService bicycleRiderService;
    private final BicycleDeliveryOrderDetailService bicycleDeliveryOrderDetailService;

    /**
     * 骑手上线
     * @param riderId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long riderOnline(Long riderId) {
        if (!WrapperClassUtils.biggerThanLong(riderId, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleRiderAttendance rider = findLastUnOfflineAttendanceByRiderId(riderId);
        if (rider != null) {
            log.info("骑手已上线:{}, 无需重复上线", JsonUtils.object2Json(rider));
            throw new BusinessException("骑手已上线，无需重复上线");
        }
        BicycleRiderAttendance attendance = new BicycleRiderAttendance();
        //骑手上线
        attendance.setOnlineTime(LocalDateTime.now());
        attendance.setRiderId(riderId);
        boolean result = bicycleRiderService.updateBicycleRiderStatus(riderId, BicycleRiderStatus.ONLINE.getNumber());
        boolean save = save(attendance);
        return save && result ? attendance.getId() : 0L;
    }

    /**
     * 骑手下线
     * @param riderId
     */
    @Transactional(rollbackFor = Exception.class)
    public void riderOffline(Long riderId) {
        if (!WrapperClassUtils.biggerThanLong(riderId, 0L)) {
            throw new InvalidParameterException();
        }
        boolean result = bicycleRiderService.updateBicycleRiderStatus(riderId, BicycleRiderStatus.OFFLINE.getNumber());
        BicycleRiderAttendance rider = findLastUnOfflineAttendanceByRiderId(riderId);
        if (!result || rider == null) {
            log.info("找不到最近一次 {} 骑手的考勤", riderId);
            throw new BusinessException("骑手未上线，不可下线");
        }
        //计算上线时长
        rider.setOfflineTime(LocalDateTime.now());
        int duration = (int) Duration.between(rider.getOnlineTime(), rider.getOfflineTime()).getSeconds();
        rider.setDuration(duration);

        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = rider.getOnlineTime();
        //骑手上线到下线所完成的单
        BicycleRiderCountVo riderCountVo = bicycleDeliveryOrderDetailService.riderCountByRiderId(riderId, startTime, endTime);
        if (riderCountVo != null) {
            rider.setCompleteCount(riderCountVo.getTodayCount());
            rider.setCompletePieces(riderCountVo.getTodayPieces());
            rider.setShareAmount(riderCountVo.getTodayIncome());
        }
        log.info("rider----:{}", JsonUtils.object2Json(rider));
        updateById(rider);
    }

    /**
     * 根据id查询三轮车骑手考勤信息
     * @param id
     * @return
     */
    public BicycleRiderAttendance findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 三轮车骑手考勤列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRiderAttendanceVo> listRiderAttendance(BicycleRiderAttendanceSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().count(searchVo), () -> getBaseMapper().listRiderAttendance(searchVo));
    }

    /**
     * 查询骑手最后一次未下线考勤
     * @param riderId
     * @return
     */
    public BicycleRiderAttendance findLastUnOfflineAttendanceByRiderId(Long riderId) {
        return getOne(new QueryWrapper<BicycleRiderAttendance>().lambda()
                .eq(BicycleRiderAttendance::getRiderId, riderId)
                .isNull(BicycleRiderAttendance::getOfflineTime));
    }
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.tms.domain.BicycleBillDetail;
import com.senox.tms.mapper.BicycleBillDetailMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/25 9:05
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BicycleBillDetailService extends ServiceImpl<BicycleBillDetailMapper, BicycleBillDetail> {

    /**
     * 保存账单明细
     * @param billDetails
     * @param billId
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBillDetail(List<BicycleBillDetail> billDetails, Long billId) {
        log.info("删除应收账单详细{}的原数据，重新插入", JsonUtils.object2Json(billId));
        deleteBicycleBillDetailByBillId(billId);

        billDetails.forEach(bicycleBillDetail -> {
            bicycleBillDetail.setBillId(billId);
            bicycleBillDetail.setModifiedTime(LocalDateTime.now());
        });
        log.info("生成的应收账单明细为----:{}", JsonUtils.object2Json(billDetails));
        saveOrUpdateBatch(billDetails);
    }

    /**
     * 根据账单id集查询费项
     *
     * @param billIds 账单id集
     * @return 返回查询到的费项
     */
    public List<BicycleBillDetail> listByBillIds(Collection<Long> billIds) {
        if (CollectionUtils.isEmpty(billIds)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<BicycleBillDetail>().lambda().in(BicycleBillDetail::getBillId, billIds));
    }

    /**
     * 根据账单id查询费项
     *
     * @param billId 账单id
     * @return 返回查询到的费项
     */
    public List<BicycleBillDetail> listByBillId(Long billId) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            return Collections.emptyList();
        }
        return listByBillIds(Collections.singleton(billId));
    }

    /**
     * 根据账单id删除费项
     * @param billId
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBicycleBillDetailByBillId(Long billId) {
        if (!WrapperClassUtils.biggerThanLong(billId, 0L)) {
            throw new InvalidParameterException();
        }
        remove(new QueryWrapper<BicycleBillDetail>().lambda().eq(BicycleBillDetail::getBillId, billId));
    }
}

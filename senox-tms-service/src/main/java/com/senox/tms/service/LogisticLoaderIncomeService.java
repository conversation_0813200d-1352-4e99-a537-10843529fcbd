package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.RedisUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.DictLogisticCategory;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.LogisticLoaderIncome;
import com.senox.tms.mapper.LogisticLoaderIncomeMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Service
public class LogisticLoaderIncomeService extends ServiceImpl<LogisticLoaderIncomeMapper, LogisticLoaderIncome> {

    /**
     * 添加
     *
     * @param income 收益
     */
    public void add(LogisticLoaderIncome income) {
        addBatch(Collections.singleton(income));
    }

    /**
     * 批量添加
     *
     * @param incomes 收益集
     * @return 影响条
     */
    public int addBatch(Collection<LogisticLoaderIncome> incomes) {
        if (CollectionUtils.isEmpty(incomes)) {
            return 0;
        }
        incomes.forEach(c -> {
            ContextUtils.initEntityCreator(c);
            ContextUtils.initEntityModifier(c);
        });
        return baseMapper.addBatch(incomes);
    }

    /**
     * 更新
     *
     * @param income 收益
     */
    public void update(LogisticLoaderIncome income) {
        updateBatch(Collections.singleton(income));
    }

    /**
     * 批量更新
     *
     * @param incomes 收益集
     * @return 影响条
     */
    public int updateBatch(Collection<LogisticLoaderIncome> incomes) {
        if (CollectionUtils.isEmpty(incomes)) {
            return 0;
        }
        incomes.forEach(ContextUtils::initEntityModifier);
        return baseMapper.updateBatch(incomes);
    }

    /**
     * 根据id删除
     *
     * @param id id
     */
    public void deleteById(Long id) {
        deleteByIds(Collections.singletonList(id));
    }

    /**
     * 根据结算id删除
     *
     * @param settlementId 结算id
     */
    public void deleteBySettlementId(Long settlementId) {
        remove(new QueryWrapper<LogisticLoaderIncome>().lambda().eq(LogisticLoaderIncome::getSettlementId, settlementId));
    }

    /**
     * 根据id集删除
     *
     * @param ids id集
     */
    public void deleteByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        removeByIds(ids);
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public List<LogisticLoaderIncomeVo> list(LogisticLoaderIncomeSearchVo searchVo) {
        return baseMapper.list(searchVo);
    }

    /**
     * 列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    public int countList(LogisticLoaderIncomeSearchVo searchVo) {
        return baseMapper.countList(searchVo);
    }

    /**
     * 列表分页
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<LogisticLoaderIncomeVo> listPage(LogisticLoaderIncomeSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> countList(searchVo), () -> list(searchVo));
    }

    /**
     * 生成日报表
     *
     * @param dateVo 时间
     */
    @Transactional
    public void generateDayReport(BicycleDateVo dateVo) {
        String lockKey = String.format(TmsConst.Cache.KEY_LOADER_INCOME_MONTH_REPORT_LOCK, dateVo.getYear(), dateVo.getMonth(), dateVo.getDay());
        if (!RedisUtils.lock(lockKey, TmsConst.Cache.TTL_1H)) {
            throw new BusinessException(String.format("收益报表[%s]-[%s]-[%s]生成中...", dateVo.getYear(), dateVo.getMonth(), dateVo.getDay()));
        }
        try {
            LogisticLoaderIncomeSearchVo searchVo = new LogisticLoaderIncomeSearchVo();
            searchVo.setPage(false);
            searchVo.setStartDate(LocalDate.of(dateVo.getYear(), dateVo.getMonth(), dateVo.getDay()));
            searchVo.setEndDate(searchVo.getStartDate());
            List<LogisticLoaderIncomeVo> incomes = baseMapper.statisticsOfDay(DictLogisticCategory.LOADER, searchVo);
            DataSepDto<LogisticLoaderIncomeVo> dataSepDto = statisticsCompareAndSepData(baseMapper.listStatistics(searchVo), incomes);
            if (!CollectionUtils.isEmpty(dataSepDto.getRemoveList())) {
                baseMapper.incomeStatisticsRemoveBatch(dataSepDto.getRemoveList());
            }
            if (!CollectionUtils.isEmpty(dataSepDto.getAddList())) {
                baseMapper.incomeStatisticsAddBatch(dataSepDto.getAddList());
            }
            if (!CollectionUtils.isEmpty(dataSepDto.getUpdateList())) {
                baseMapper.incomeStatisticsUpdateBatch(dataSepDto.getUpdateList());
            }

        } finally {
            RedisUtils.del(lockKey);
        }
    }

    /**
     * 收益统计分页列表
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public BicycleTotalPageResult<LogisticLoaderIncomeVo> listPageStatistics(LogisticLoaderIncomeSearchVo searchVo) {
        PageResult<LogisticLoaderIncomeVo> pageResult = PageUtils.commonPageResult(searchVo, () -> baseMapper.listCountStatistics(searchVo), () -> baseMapper.listStatistics(searchVo));
        return new BicycleTotalPageResult<>(pageResult, baseMapper.listTotalAmount());

    }

    /**
     * 收益统计报表
     *
     * @param searchVo 查询
     * @return 返回统计后的报表
     */
    public List<LogisticLoaderIncomeReportVo> reportStatistics(LogisticLoaderIncomeSearchVo searchVo) {
        searchVo.setPage(false);
        List<LogisticLoaderIncomeVo> logisticLoaderIncomeVos = baseMapper.listStatistics(searchVo);
        Map<LocalDate, List<LogisticLoaderIncomeVo>> collect = logisticLoaderIncomeVos.stream().collect(Collectors.groupingBy(LogisticLoaderIncomeVo::getDate));
        List<LogisticLoaderIncomeReportVo> incomeReports = new ArrayList<>();
        collect.forEach((date, incomes) -> {
            LogisticLoaderIncomeReportVo incomeReportVo = new LogisticLoaderIncomeReportVo();
            incomeReportVo.setDate(date);
            incomeReportVo.setItems(incomes);
            incomeReports.add(incomeReportVo);
        });
        return incomeReports.stream().sorted(Comparator.comparing(LogisticLoaderIncomeReportVo::getDate)).collect(Collectors.toList());
    }


    private DataSepDto<LogisticLoaderIncomeVo> statisticsCompareAndSepData(List<LogisticLoaderIncomeVo> srcData, List<LogisticLoaderIncomeVo> targetData) {
        List<LogisticLoaderIncomeVo> addData;
        List<LogisticLoaderIncomeVo> updateData = new ArrayList<>();
        List<LogisticLoaderIncomeVo> removeData = new ArrayList<>();

        if (CollectionUtils.isEmpty(srcData)) {
            addData = targetData;
        } else {
            addData = new ArrayList<>(targetData.size());
            removeData = new ArrayList<>();
            Map<String, LogisticLoaderIncomeVo> srcMap = srcData.stream().collect(Collectors.toMap(s -> s.getDate().toString().concat(s.getLoaderNumber()), Function.identity()));
            Map<String, LogisticLoaderIncomeVo> targetMap = targetData.stream().collect(Collectors.toMap(s -> s.getDate().toString().concat(s.getLoaderNumber()), Function.identity()));
            for (LogisticLoaderIncomeVo data : targetData) {
                String dataKey = data.getDate().toString().concat(data.getLoaderNumber());
                LogisticLoaderIncomeVo dbData = srcMap.get(dataKey);
                if (null == dbData) {
                    addData.add(data);
                    continue;
                }
                if (!dbData.getAmount().equals(data.getAmount())) {
                    updateData.add(data);
                }
            }
            for (LogisticLoaderIncomeVo data : srcData) {
                String dataKey = data.getDate().toString().concat(data.getLoaderNumber());
                if (null == targetMap.get(dataKey)) {
                    removeData.add(data);
                }
            }
        }
        return new DataSepDto<>(addData, updateData, removeData);
    }


}

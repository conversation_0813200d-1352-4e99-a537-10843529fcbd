package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.LogisticOrderShip;
import com.senox.tms.mapper.LogisticOrderShipMapper;
import com.senox.tms.vo.ShipOrderDiscountSearchVo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/28 14:09
 */
@Service
public class LogisticOrderShipService extends ServiceImpl<LogisticOrderShipMapper, LogisticOrderShip> {

    private static final String ORDER_622 = "62";
    private static final BigDecimal WEIGHT_500 = new BigDecimal(500);
    private static final BigDecimal WEIGHT_1000 = new BigDecimal(1000);
    private static final BigDecimal DISCOUNT_95= new BigDecimal("0.95");
    private static final BigDecimal DISCOUNT_90= new BigDecimal("0.90");

    /**
     * 添加配送订单
     * @param shipOrder
     * @return
     */
    public Long addShipOrder(LogisticOrderShip shipOrder) {
        // 初始化配送单信息
        prepareShipOrder(shipOrder);

        // 创建人信息初始化
        shipOrder.setCreateTime(LocalDateTime.now());
        shipOrder.setModifierId(shipOrder.getCreatorId());
        shipOrder.setModifierName(shipOrder.getCreatorName());
        shipOrder.setModifiedTime(shipOrder.getCreateTime());

        save(shipOrder);
        // 根据商品订单更新物流订单
        updateShipOrderByProductOrder(Collections.singletonList(shipOrder.getOrderProductId()));
        // 计算折扣率 和 费用
        updateShipOrderDiscountByShipDate(Collections.singletonList(shipOrder.getShipDate()));
        return shipOrder.getId();
    }

    /**
     * 批量保存配送订单
     * @param list
     * @param overwrite
     */
    public void saveShipOrderBatch(List<LogisticOrderShip> list, Boolean overwrite) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Long> productOrderIds = list.stream().map(LogisticOrderShip::getOrderProductId).distinct().collect(Collectors.toList());
        List<LogisticOrderShip> rawList = listShipOrderByProductOrderId(productOrderIds);
        DataSepDto<LogisticOrderShip> sepData = SeparateUtils.separateData(rawList, list);

        // 新增数据
        addShipOrderBatch(sepData.getAddList());
        // 更新数据
        if (BooleanUtils.isTrue(overwrite)) {
            updateShipOrderBatch(sepData.getUpdateList());
        }

        // 根据商品订单更新物流订单
        updateShipOrderByProductOrder(list.stream().map(LogisticOrderShip::getOrderProductId).distinct().collect(Collectors.toList()));
        // 计算折扣率 和 费用
        updateShipOrderDiscountByShipDate(list.stream().map(LogisticOrderShip::getShipDate).distinct().collect(Collectors.toList()));
    }

    /**
     * 批量添加配送订单
     * @param list
     */
    private void addShipOrderBatch(List<LogisticOrderShip> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (LogisticOrderShip item : list) {
            // 初始化配送单信息
            prepareShipOrder(item);

            // 创建人信息初始化
            item.setCreateTime(LocalDateTime.now());
            item.setModifierId(item.getCreatorId());
            item.setModifierName(item.getCreatorName());
            item.setModifiedTime(item.getCreateTime());
        }
        saveBatch(list);
    }

    /**
     * 更新配送订单
     * @param shipOrder
     */
    public void updateShipOrder(LogisticOrderShip shipOrder) {
        if (!WrapperClassUtils.biggerThanLong(shipOrder.getId(), 0L)) {
            return;
        }

        // 创建人信息初始化
        shipOrder.setCreatorId(null);
        shipOrder.setCreatorName(null);
        shipOrder.setCreateTime(null);
        shipOrder.setModifiedTime(LocalDateTime.now());

        updateById(shipOrder);
        // 根据商品订单更新物流订单
        updateShipOrderByProductOrder(Collections.singletonList(shipOrder.getOrderProductId()));
        // 计算折扣率 和 费用
        updateShipOrderDiscountByShipDate(Collections.singletonList(shipOrder.getShipDate()));
    }

    /**
     * 批量更新配送订单
     * @param list
     */
    private void updateShipOrderBatch(List<LogisticOrderShip> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (LogisticOrderShip item : list) {
            // 创建人信息初始化
            item.setCreatorId(null);
            item.setCreatorName(null);
            item.setCreateTime(null);
            item.setModifiedTime(LocalDateTime.now());
        }

        updateBatchById(list);
    }

    /**
     * 根据商品订单更新物流订单
     * @param productOrderIds
     */
    private void updateShipOrderByProductOrder(List<Long> productOrderIds) {
        if (CollectionUtils.isEmpty(productOrderIds)) {
            return;
        }

        getBaseMapper().updateShipSizeByProductOrder(productOrderIds);
        // 物流费=IF(商品名称="采购商运费",应收金额,IF(备注="不代收货款（运费由采购商承担）",0,ROUND(IF(运费单价>=60,总体积*运费单价,IF(运费单价>0.3,件数*运费单价,总重量*运费单价)),2)))
        // 分拣费=IF(运费单价=0.02,ROUND(件数*运费单价,2),IF(OR(商户="满家兴",商户="满家兴自下单"),ROUND(总质量/1000*10,2),0))
        // 实际运费=物流费*折扣率+分拣费
        getBaseMapper().updateShipAmountByProductOrder(productOrderIds);

        getBaseMapper().updateShipTotalAmountByProductOrder(productOrderIds);
    }

    /**
     * 根据订单时间更新物流订单折扣率
     * @param shipDates
     */
    public void updateShipOrderDiscountByShipDate(List<LocalDate> shipDates) {
        if (CollectionUtils.isEmpty(shipDates)) {
            return;
        }
        // 623开头的订单，总重量500kg以下的不打折，总重量500kg（含）以上，1000kg （不含）以下打95折，1000kg（含）以上打9折
        getBaseMapper().updateShipDiscountByShipDate(shipDates);
        getBaseMapper().updateShipTotalByShipDate(shipDates);
    }

    /**
     * 根据产品订单id删除发货订单
     * @param orderIds
     */
    public void deleteByOrderIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        LambdaQueryWrapper<LogisticOrderShip> queryWrapper = new QueryWrapper<LogisticOrderShip>().lambda().in(LogisticOrderShip::getOrderProductId, orderIds);
        remove(queryWrapper);
    }

    /**
     * 根据商品订单id查找配送单信息
     * @param id
     * @return
     */
    public LogisticOrderShip findShipOrderByProductOrderId(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }

        List<LogisticOrderShip> list = listShipOrderByProductOrderId(Collections.singletonList(id));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 发送订单折扣率
     * @param search
     * @return
     */
    public BigDecimal findShipOrderDiscount(ShipOrderDiscountSearchVo search) {
        // 623开头的订单，总重量500kg以下的不打折，总重量500kg（含）以上，1000kg （不含）以下打95折，1000kg（含）以上打9折
        if (search.getShipDate() == null || !search.getOrderNo().startsWith(ORDER_622)) {
            return BigDecimal.ONE;
        }

        BigDecimal weight = getBaseMapper().findShipOrderWeight(search);
        weight = DecimalUtils.add(weight, search.getShipWeight());
        if (weight.compareTo(WEIGHT_1000) >= 0) {
            return DISCOUNT_90;
        } else if (weight.compareTo(WEIGHT_500) >= 0) {
            return DISCOUNT_95;
        } else {
            return BigDecimal.ONE;
        }
    }

    /**
     * 根据商品订单id查找配送单信息
     * @param ids
     * @return
     */
    private List<LogisticOrderShip> listShipOrderByProductOrderId(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<LogisticOrderShip> resultList = new ArrayList<>(ids.size());
        for (int index = 0; index <= ids.size(); index += TmsConst.BATCH_SIZE_1000) {
            List<Long> list = ids.stream().skip(index).limit(TmsConst.BATCH_SIZE_1000).collect(Collectors.toList());

            LambdaQueryWrapper<LogisticOrderShip> queryWrapper = new QueryWrapper<LogisticOrderShip>().lambda()
                    .in(LogisticOrderShip::getOrderProductId, list);
            resultList.addAll(list(queryWrapper));
        }
        return resultList;
    }

    /**
     * 配送订单信息初始化
     * @param order
     */
    private void prepareShipOrder(LogisticOrderShip order) {
        order.setMarket(StringUtils.trimToEmpty(order.getMarket()));
        order.setArea(StringUtils.trimToEmpty(order.getArea()));
        order.setVehicleNo(StringUtils.trimToEmpty(order.getVehicleNo()));

        order.setPieceWeight(DecimalUtils.nullToZero(order.getPieceWeight()));
        order.setShipWeight(DecimalUtils.nullToZero(order.getShipWeight()));
        order.setPieceSize(DecimalUtils.nullToZero(order.getPieceSize()));
        order.setShipSize(DecimalUtils.nullToZero(order.getShipSize()));

        order.setShipPrice(DecimalUtils.nullToZero(order.getShipPrice()));
        order.setShipMultiplying(order.getShipMultiplying() == null ? BigDecimal.ONE : order.getShipMultiplying());
        order.setShipAmount(DecimalUtils.nullToZero(order.getShipAmount()));
        order.setShipDiscount(order.getShipDiscount() == null ? BigDecimal.ONE : order.getShipDiscount());
        order.setSortCharge(BooleanUtils.isTrue(order.getSortCharge()));
        order.setSortAmount(DecimalUtils.nullToZero(order.getSortAmount()));
        order.setTotalAmount(DecimalUtils.nullToZero(order.getTotalAmount()));
    }
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.BicycleStatus;
import com.senox.tms.domain.BicycleShares;
import com.senox.tms.mapper.BicycleSharesMapper;
import com.senox.tms.vo.BicycleSharesSearchVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class BicycleSharesService extends ServiceImpl<BicycleSharesMapper, BicycleShares> {

    /**
     * 添加分佣
     *
     * @param shares 分佣
     */
    public void add(BicycleShares shares) {
        shares.setCreateTime(LocalDateTime.now());
        shares.setModifiedTime(LocalDateTime.now());
        save(shares);
    }

    /**
     * 删除分佣
     */
    public void delete(Long id) {
        BicycleShares shares = getById(id);
        if (null == shares) {
            return;
        }
        BicycleStatus status = BicycleStatus.DISABLED;
        statusValid(shares, status);
        remove(new QueryWrapper<BicycleShares>()
                .lambda()
                .eq(BicycleShares::getId, shares.getId())
                .eq(BicycleShares::getStatus, status.getNumber())
        );
    }

    /**
     * 修改分佣
     *
     * @param shares 分佣
     */
    public void update(BicycleShares shares) {
        if (null == shares || !WrapperClassUtils.biggerThanLong(shares.getId(), 0)) {
            return;
        }
        BicycleShares dbShares = getById(shares.getId());
        if (null == dbShares) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }

        shares.setModifiedTime(LocalDateTime.now());
        updateById(shares);
    }

    /**
     * 根据id查询分佣
     *
     * @param id 分佣id
     * @return 查询到的分佣
     */
    public BicycleShares getById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return super.getById(id);
    }

    /**
     * 佣金列表
     *
     * @param searchVo 查询参数
     * @return 分页结果
     */
    public PageResult<BicycleShares> list(BicycleSharesSearchVo searchVo) {
        BicycleStatus status = searchVo.getStatus();
        LambdaQueryWrapper<BicycleShares> lambdaQueryWrapper = new QueryWrapper<BicycleShares>().lambda();
        if (!StringUtils.isBlank(searchVo.getName())) {
            lambdaQueryWrapper.like(BicycleShares::getName, searchVo.getName());
        }
        if (null != searchVo.getStatus()) {
            lambdaQueryWrapper.eq(BicycleShares::getStatus, status.getNumber());
        }
        if (null != searchVo.getEffectiveStartTime()) {
            lambdaQueryWrapper.ge(BicycleShares::getEffectiveTime, searchVo.getEffectiveStartTime());
        }
        if (null != searchVo.getEffectiveEndTime()) {
            lambdaQueryWrapper.le(BicycleShares::getIneffectiveTime, searchVo.getEffectiveEndTime());
        }
        return PageUtils.commonPageResult(searchVo, () -> count(lambdaQueryWrapper), () -> {
            if (searchVo.isPage()) {
                lambdaQueryWrapper.last(String.format("limit %s , %s", searchVo.getOffset(), searchVo.getPageSize()));
            }
            return list(lambdaQueryWrapper);
        });
    }

    public List<BicycleShares> listByEffective(LocalDateTime activeTime){
        if (null == activeTime){
            activeTime = LocalDateTime.now();
        }
        LambdaQueryWrapper<BicycleShares> lambdaQueryWrapper = new QueryWrapper<BicycleShares>().lambda();
        lambdaQueryWrapper.eq(BicycleShares::getStatus, BicycleStatus.ENABLED.getNumber());
        lambdaQueryWrapper.le(BicycleShares::getEffectiveTime,activeTime);
        lambdaQueryWrapper.ge(BicycleShares::getIneffectiveTime,activeTime);
        return list(lambdaQueryWrapper);
    }

    /**
     * 获取当前有效的分佣
     * @return 返回生效时间跟当前时间最接近的分佣
     */
    public BicycleShares getCurrentEffectiveShares(){
        List<BicycleShares> sharesList = listByEffective(LocalDateTime.now());
        List<BicycleShares> collect = sharesList
                .stream()
                .sorted(Comparator.comparing(i -> DateUtils.getDurationBetween(i.getEffectiveTime(), LocalDateTime.now(), ChronoUnit.SECONDS)))
                .collect(Collectors.toList());
        return collect.get(0);
    }


    /**
     * 状态检查
     *
     * @param shares 佣金
     * @param status 状态
     */
    private void statusValid(BicycleShares shares, BicycleStatus status) {
        if (status.getNumber() != shares.getStatus()) {
            throw new BusinessException(String.format("操作失败，[%s-%s]未处在[%s]状态", shares.getId(), shares.getName(), status.getName()));
        }
    }
}

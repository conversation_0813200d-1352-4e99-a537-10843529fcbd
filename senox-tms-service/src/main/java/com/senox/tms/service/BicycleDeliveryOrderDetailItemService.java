package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.domain.BicycleDeliveryOrderDetailItem;
import com.senox.tms.mapper.BicycleDeliveryOrderDetailItemMapper;
import com.senox.tms.utils.ContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/3 14:19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BicycleDeliveryOrderDetailItemService extends ServiceImpl<BicycleDeliveryOrderDetailItemMapper, BicycleDeliveryOrderDetailItem> {

    /**
     * 批量添加配送子详细
     * @param detailItems
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<BicycleDeliveryOrderDetailItem> detailItems) {
        detailItems.forEach(detail -> {
            ContextUtils.initEntityCreator(detail);
            detail.setCreateTime(LocalDateTime.now());
            ContextUtils.initEntityModifier(detail);
            detail.setModifiedTime(LocalDateTime.now());
        });
        saveBatch(detailItems);
    }

    /**
     * 批量保存配送子详细
     * @param orderSerialNo
     * @param orderDetailItems
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(String orderSerialNo, List<BicycleDeliveryOrderDetailItem> orderDetailItems) {
        log.info("订单号 ---  {}", orderSerialNo);
        if (CollectionUtils.isEmpty(orderDetailItems)) {
            orderDetailItems = new ArrayList<>();
        }
        DataSepDto<BicycleDeliveryOrderDetailItem> sepData = compareAndSepData(listByOrderSerialNo(orderSerialNo), orderDetailItems);
        log.info("转换后的sepData数据是 ---- {}", JsonUtils.object2Json(sepData));
        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getUpdateList())) {
            updateBatchById(sepData.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(BicycleDeliveryOrderDetailItem::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 根据配送单流水号删除配送子详细
     * @param deliveryOrderSerialNo
     */
    public void deleteByDeliveryOrderSerialNo(String deliveryOrderSerialNo) {
        if (StringUtils.isBlank(deliveryOrderSerialNo)) {
            return;
        }
        remove(new QueryWrapper<BicycleDeliveryOrderDetailItem>().lambda().eq(BicycleDeliveryOrderDetailItem::getDeliveryOrderSerialNo, deliveryOrderSerialNo));
    }

    /**
     * 根据订单流水号删除配送子详细
     * @param orderSerialNo
     */
    public void deleteByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return;
        }
        remove(new QueryWrapper<BicycleDeliveryOrderDetailItem>().lambda().eq(BicycleDeliveryOrderDetailItem::getOrderSerialNo, orderSerialNo));
    }

    /**
     * 根据订单号查询配送子详细
     * @param goodsIds
     * @return
     */
    public List<BicycleDeliveryOrderDetailItem> listByGoodsIds(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<BicycleDeliveryOrderDetailItem>().lambda().in(BicycleDeliveryOrderDetailItem::getGoodsId, goodsIds));
    }

    /**
     * 批量更新配送子详细
     * @param orderDetailItems
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(List<BicycleDeliveryOrderDetailItem> orderDetailItems) {
        orderDetailItems.forEach(detail -> {
            ContextUtils.initEntityModifier(detail);
            detail.setModifiedTime(LocalDateTime.now());
        });
        updateBatchById(orderDetailItems);
    }

    /**
     * 根据订单号查询配送子详细
     * @param orderSerialNo
     * @return
     */
    public List<BicycleDeliveryOrderDetailItem> listByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<BicycleDeliveryOrderDetailItem>().lambda().eq(BicycleDeliveryOrderDetailItem::getOrderSerialNo, orderSerialNo));
    }

    /**
     * 比较替换
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<BicycleDeliveryOrderDetailItem> compareAndSepData(List<BicycleDeliveryOrderDetailItem> srcList, List<BicycleDeliveryOrderDetailItem> targetList) {
        List<BicycleDeliveryOrderDetailItem> addList = new ArrayList<>(targetList.size());
        List<BicycleDeliveryOrderDetailItem> updateList = new ArrayList<>(srcList.size());
        List<BicycleDeliveryOrderDetailItem> delList = new ArrayList<>(srcList.size());

        if (CollectionUtils.isEmpty(srcList)) {
            // 原始记录为空，新增所有目标记录
            addList.addAll(targetList);

        } else if (CollectionUtils.isEmpty(targetList)) {
            // 目标记录为空，删掉所有原始记录
            delList.addAll(srcList);

        } else {
            for (BicycleDeliveryOrderDetailItem item : srcList) {
                BicycleDeliveryOrderDetailItem targetItem = targetList.stream().filter(x -> Objects.equals(x, item)).findFirst().orElse(null);
                if (targetItem != null) {
                    targetItem.setId(item.getId());
                    updateList.add(targetItem);
                } else {
                    delList.add(item);
                }
            }

            addList = targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList());
        }

        return new DataSepDto<>(addList, updateList, delList);
    }
}

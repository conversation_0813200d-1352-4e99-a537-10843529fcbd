package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import com.senox.tms.convert.UnloadingOrderPayoffConvert;
import com.senox.tms.domain.UnloadingOrderMonthPayoff;
import com.senox.tms.domain.UnloadingOrderPayoff;
import com.senox.tms.domain.UnloadingShares;
import com.senox.tms.mapper.UnloadingOrderMonthPayoffMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.UnloadingMonthPayoffRemarkVo;
import com.senox.tms.vo.UnloadingMonthPayoffVo;
import com.senox.tms.vo.UnloadingOrderMonthPayoffSearchVo;
import com.senox.tms.vo.UnloadingOrderPayoffSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/8 10:30
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UnloadingOrderMonthPayoffService extends ServiceImpl<UnloadingOrderMonthPayoffMapper, UnloadingOrderMonthPayoff> {

    private final UnloadingSharesService sharesService;
    private final UnloadingOrderPayoffService payoffService;
    private final UnloadingOrderPayoffConvert orderPayoffConvert;

    /**
     * 生成月应付账单
     * @param payoffVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateMonthPayoff(UnloadingMonthPayoffVo payoffVo) {
        List<UnloadingOrderPayoff> payoffs = listPayoffs(payoffVo);
        Map<Long, List<UnloadingOrderPayoff>> workerMap = payoffs.stream().collect(Collectors.groupingBy(UnloadingOrderPayoff::getWorkerId));
        List<UnloadingOrderMonthPayoff> monthPayoffList = buildMonthPayoff(workerMap, payoffVo);
        List<UnloadingOrderMonthPayoff> dbMonthPayoffList = findMonthPayoff(payoffVo);
        List<UnloadingOrderMonthPayoff> dataSepDto = separateMonthPayoff(dbMonthPayoffList, monthPayoffList);
        for (UnloadingOrderMonthPayoff monthPayoff : dataSepDto) {
            saveOrUpdate(monthPayoff);
            payoffService.batchSetMonthPayoffId(payoffs.stream().filter(x -> Objects.equals(x.getWorkerId(), monthPayoff.getWorkerId())).collect(Collectors.toList()), monthPayoff.getId());
        }
    }

    /**
     * 更改备注
     * @param remarkVo
     */
    public void updateRemark(UnloadingMonthPayoffRemarkVo remarkVo) {
        UnloadingOrderMonthPayoff monthPayoff = new UnloadingOrderMonthPayoff();
        monthPayoff.setId(remarkVo.getId());
        monthPayoff.setRemark(remarkVo.getRemark());
        ContextUtils.initEntityModifier(monthPayoff);
        monthPayoff.setModifiedTime(LocalDateTime.now());
        updateById(monthPayoff);
    }

    /**
     * 根据id查询月应付
     * @param id
     * @return
     */
    public UnloadingOrderMonthPayoff findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 删除月应付
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMonthPayoff(Long id) {
        UnloadingOrderMonthPayoff monthPayoff = findById(id);
        if (monthPayoff == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (UnloadingOrderBillPayoffStatus.fromStatus(monthPayoff.getStatus()) == UnloadingOrderBillPayoffStatus.PAID) {
            throw new BusinessException("该月应付已支付，无法删除");
        }
        removeById(id);
        payoffService.resetPayoffMonth(id);
    }

    /**
     * 批量支付
     * @param ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchMonthPayoffByIds(List<Long> ids) {
        List<UnloadingOrderMonthPayoff> payoffs = findByIds(ids);
        payoffs.forEach(payoff -> {
            payoff.setPayoffTime(LocalDateTime.now());
            payoff.setStatus(UnloadingOrderBillPayoffStatus.PAID.getNumber());
            ContextUtils.initEntityModifier(payoff);
            payoff.setModifiedTime(LocalDateTime.now());
        });
        updateBatchById(payoffs);
        payoffService.batchByMonthPayoffIds(ids);
    }

    /**
     * 月应付统计数量
     * @param searchVo
     * @return
     */
    public int countMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo) {
        return getBaseMapper().countMonthPayoff(searchVo);
    }

    /**
     * 月应付列表
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderMonthPayoff> listMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo) {
        return getBaseMapper().listMonthPayoff(searchVo);
    }

    /**
     * 月应付分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderMonthPayoff> pageMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> countMonthPayoff(searchVo), () -> listMonthPayoff(searchVo));
    }

    /**
     * 月应付合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderMonthPayoff sumMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo) {
        return getBaseMapper().sumMonthPayoff(searchVo);
    }


    /**
     * 根据id集合查询月应付账单
     * @param ids
     * @return
     */
    public List<UnloadingOrderMonthPayoff> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<UnloadingOrderMonthPayoff>().lambda().in(UnloadingOrderMonthPayoff::getId, ids));
    }

    private List<UnloadingOrderMonthPayoff> findMonthPayoff(UnloadingMonthPayoffVo payoffVo) {
        LambdaQueryWrapper<UnloadingOrderMonthPayoff> wrapper = new QueryWrapper<UnloadingOrderMonthPayoff>().lambda()
                .eq(UnloadingOrderMonthPayoff::getPayoffYear, payoffVo.getYear())
                .eq(UnloadingOrderMonthPayoff::getPayoffMonth, payoffVo.getMonth())
                .eq(UnloadingOrderMonthPayoff::getStatus, UnloadingOrderBillPayoffStatus.INIT.getNumber());
        if (WrapperClassUtils.biggerThanLong(payoffVo.getWorkerId(), 0L)) {
            wrapper.eq(UnloadingOrderMonthPayoff::getWorkerId, payoffVo.getWorkerId());
        }
        return list(wrapper);
    }

    /**
     * 构建月应付
     * @param workerMap
     * @param payoffVo
     * @return
     */
    private List<UnloadingOrderMonthPayoff> buildMonthPayoff(Map<Long, List<UnloadingOrderPayoff>> workerMap, UnloadingMonthPayoffVo payoffVo) {
        if (CollectionUtils.isEmpty(workerMap)) {
            throw new BusinessException("未查询到该搬运工的应付记录");
        }
        UnloadingShares shares = sharesService.getCurrentEffectiveShares();
        if (shares == null) {
            throw new BusinessException("暂未设置分佣！");
        }
        List<UnloadingOrderMonthPayoff> monthPayoffs = new ArrayList<>(workerMap.keySet().size());
        if (WrapperClassUtils.biggerThanLong(payoffVo.getWorkerId(), 0L)) {
            monthPayoffs.add(buildMonthPayoff(payoffVo, workerMap.values().stream().flatMap(List::stream).collect(Collectors.toList()), shares));
        } else {
            for (Map.Entry<Long, List<UnloadingOrderPayoff>> entry : workerMap.entrySet()) {
                monthPayoffs.add(buildMonthPayoff(payoffVo, entry.getValue(), shares));
            }
        }
        return monthPayoffs;
    }

    private static UnloadingOrderMonthPayoff buildMonthPayoff(UnloadingMonthPayoffVo payoffVo, List<UnloadingOrderPayoff> payoffs, UnloadingShares shares) {
        UnloadingOrderPayoff payoff = payoffs.get(0);
        BigDecimal totalAmount = payoffs.stream().map(UnloadingOrderPayoff::getPayoffAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        UnloadingOrderMonthPayoff monthPayoff = new UnloadingOrderMonthPayoff();
        monthPayoff.setPayoffMonth(payoffVo.getMonth());
        monthPayoff.setPayoffYear(payoffVo.getYear());
        monthPayoff.setRemark(payoffVo.getRemark());
        monthPayoff.setWorkerId(payoff.getWorkerId());
        monthPayoff.setWorkerNo(payoff.getWorkerNo());
        monthPayoff.setWorkerName(payoff.getWorkerName());
        monthPayoff.setShareAmount(DecimalUtils.multiple(totalAmount, shares.getSharesRate().movePointLeft(2)));
        monthPayoff.setPayoffAmount(DecimalUtils.subtract(totalAmount, monthPayoff.getShareAmount()));
        return monthPayoff;
    }

    /**
     * 应付记录
     * @param payoffVo
     * @return
     */
    private List<UnloadingOrderPayoff> listPayoffs(UnloadingMonthPayoffVo payoffVo) {
        UnloadingOrderPayoffSearchVo searchVo = new UnloadingOrderPayoffSearchVo();
        searchVo.setPayoffDateStart(DateUtils.parseDate(payoffVo.getYear(), payoffVo.getMonth(), 1));
        searchVo.setPayoffDateEnd(DateUtils.parseDate(payoffVo.getYear(), payoffVo.getMonth() + 1, 1, true).minusDays(1));
        searchVo.setStatus(UnloadingOrderBillPayoffStatus.INIT.getNumber());
        searchVo.setWorkerId(payoffVo.getWorkerId());
        searchVo.setPage(false);
        return orderPayoffConvert.toDo(payoffService.listPayoff(searchVo));
    }


    /**
     * 账单比较
     * @param srcList
     * @param targetList
     * @return
     */
    private List<UnloadingOrderMonthPayoff> separateMonthPayoff(List<UnloadingOrderMonthPayoff> srcList,
                                                        List<UnloadingOrderMonthPayoff> targetList) {
        List<UnloadingOrderMonthPayoff> addList = new ArrayList<>(targetList.size());
        List<UnloadingOrderMonthPayoff> updateList = new ArrayList<>(targetList.size());

        if (CollectionUtils.isEmpty(srcList)) {
            addList = targetList;
            addList.forEach(item ->{
                ContextUtils.initEntityCreator(item);
                ContextUtils.initEntityModifier(item);
                item.setCreateTime(LocalDateTime.now());
                item.setModifiedTime(LocalDateTime.now());
            });

        } else {
            Map<Long, UnloadingOrderMonthPayoff> srcMap = srcList.stream()
                    .collect(Collectors.toMap(UnloadingOrderMonthPayoff::getWorkerId, Function.identity()));

            for (UnloadingOrderMonthPayoff item : targetList) {

                UnloadingOrderMonthPayoff srcItem = srcMap.get(item.getWorkerId());
                if (srcItem == null) {
                    ContextUtils.initEntityCreator(item);
                    ContextUtils.initEntityModifier(item);
                    item.setCreateTime(LocalDateTime.now());
                    item.setModifiedTime(LocalDateTime.now());
                    // 新增
                    addList.add(item);

                } else {
                    if (UnloadingOrderBillPayoffStatus.fromStatus(srcItem.getStatus()) != UnloadingOrderBillPayoffStatus.PAID) {
                        // 未缴费，可以更新
                        item.setId(srcItem.getId());
                        item.setCreatorId(null);
                        item.setCreatorName(null);
                        item.setCreateTime(null);
                        ContextUtils.initEntityModifier(item);
                        item.setModifiedTime(LocalDateTime.now());
                        updateList.add(item);
                    }
                }
            }
        }
        List<UnloadingOrderMonthPayoff> resultList = new ArrayList<>(srcList.size() + targetList.size());
        resultList.addAll(addList);
        resultList.addAll(updateList);
        return resultList;
    }

}

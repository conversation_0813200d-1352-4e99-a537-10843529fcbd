package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.LogisticOrderProduct;
import com.senox.tms.domain.LogisticPayoff;
import com.senox.tms.mapper.LogisticOrderProductMapper;
import com.senox.tms.vo.LogisticOrderEditBatchVo;
import com.senox.tms.vo.LogisticOrderSearchVo;
import com.senox.tms.vo.LogisticOrderVo;
import com.senox.tms.vo.LogisticPayoffGenerateVo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/27 16:20
 */
@Service
public class LogisticOrderProductService extends ServiceImpl<LogisticOrderProductMapper, LogisticOrderProduct> {

    /**
     * 添加商品订单
     * @param productOrder
     * @return
     */
    public Long addProductOrder(LogisticOrderProduct productOrder) {
        LogisticOrderProduct dbProduct = findProductOrderByProduct(productOrder);
        if (dbProduct != null) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "同一发货日期重复的客户订单产品条目");
        }

        // 初始化订单信息
        prepareProductOrder(productOrder);

        // 创建人信息初始化
        productOrder.setCreateTime(LocalDateTime.now());
        productOrder.setModifierId(productOrder.getCreatorId());
        productOrder.setModifierName(productOrder.getCreatorName());
        productOrder.setModifiedTime(productOrder.getCreateTime());
        save(productOrder);

        // 更新产品订单金额
        updateProductOrderAmount(Collections.singletonList(productOrder.getId()));
        return productOrder.getId();
    }

    /**
     * 批量保存商品订单
     * @param list
     * @param overwrite
     */
    public void saveProductOrderBatch(List<LogisticOrderProduct> list, Boolean overwrite) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<LogisticOrderProduct> rawList = listProductOrderByProduct(list);
        if (!CollectionUtils.isEmpty(rawList) && overwrite == null) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "数据记录已存在，是否覆盖");
        }
        DataSepDto<LogisticOrderProduct> sepData = SeparateUtils.separateData(rawList, list);

        // 新增数据
        addProductOrderBatch(sepData.getAddList());
        // 更细数据
        if (BooleanUtils.isTrue(overwrite)) {
            updateProductOrderBatch(sepData.getUpdateList());
        }

        // 更新商品订单金额
        List<Long> ids = list.stream()
                .map(LogisticOrderProduct::getId)
                .filter(x -> WrapperClassUtils.biggerThanLong(x, 0L))
                .distinct()
                .collect(Collectors.toList());
        updateProductOrderAmount(ids);
    }

    /**
     * 批量添加商品订单
     * @param list
     */
    private void addProductOrderBatch(List<LogisticOrderProduct> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (LogisticOrderProduct item : list) {
            // 初始化订单信息
            prepareProductOrder(item);

            // 创建人信息初始化
            item.setCreateTime(LocalDateTime.now());
            item.setModifierId(item.getCreatorId());
            item.setModifierName(item.getCreatorName());
            item.setModifiedTime(item.getCreateTime());
        }
        saveBatch(list);
    }

    /**
     * 更新产品订单
     * @param productOrder
     */
    public void updateProductOrder(LogisticOrderProduct productOrder) {
        if (!WrapperClassUtils.biggerThanLong(productOrder.getId(), 0L)) {
            return;
        }

        productOrder.setCreatorId(null);
        productOrder.setCreatorName(null);
        productOrder.setCreateTime(null);
        productOrder.setModifiedTime(LocalDateTime.now());

        updateById(productOrder);
        updateProductOrderAmount(Collections.singletonList(productOrder.getId()));
    }

    /**
     * 批量更新产品订单
     * @param list
     */
    private void updateProductOrderBatch(List<LogisticOrderProduct> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (LogisticOrderProduct item : list) {
            item.setRemark(StringUtils.trimToEmpty(item.getRemark()));
            // 创建人信息初始化
            item.setCreatorId(null);
            item.setCreatorName(null);
            item.setCreateTime(null);
            item.setModifiedTime(LocalDateTime.now());
        }

        updateBatchById(list);
    }

    /**
     * 更新商品订单金额
     * 商品订单金额 = 商品总价 - 优惠金额 - 满减金额
     * @param ids
     */
    public void updateProductOrderAmount(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        for (int index = 0; index < ids.size(); index += TmsConst.BATCH_SIZE_1000) {
            List<Long> list = ids.stream().skip(index).limit(TmsConst.BATCH_SIZE_1000).collect(Collectors.toList());
            getBaseMapper().updateProductOrderAmount(list);
        }
    }

    /**
     * 更新商品订单信息
     * @param editInfo
     */
    public void updateProductOrderInfoBatch(LogisticOrderEditBatchVo editInfo) {
        if (CollectionUtils.isEmpty(editInfo.getProductOrderIds())) {
            return;
        }

        getBaseMapper().updateOrderBatch(editInfo);
    }

    /**
     * 根据id删除订单
     * @param orderIds
     */
    public void deleteByOrderIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        removeByIds(orderIds);
    }

    /**
     * 根据商品订单id查找订单明细
     * @param id
     * @return
     */
    public LogisticOrderVo findOrderByProductOrderId(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getBaseMapper().findOrderByProductOrderId(id) : null;
    }

    /**
     * 根据id查找商品订单
     * @param orderIds
     * @return
     */
    public List<LogisticOrderProduct> listOrderByProductOrderIds(List<Long> orderIds) {
        return CollectionUtils.isEmpty(orderIds) ? Collections.emptyList() : listByIds(orderIds);
    }

    /**
     * 订单数合计
     * @param search
     * @return
     */
    public int countOrder(LogisticOrderSearchVo search) {
        return getBaseMapper().countOrder(search);
    }

    /**
     * 订单统计
     * @param search
     * @return
     */
    public LogisticOrderVo sumOrder(LogisticOrderSearchVo search) {
        return getBaseMapper().sumOrder(search);
    }

    /**
     * 订单列表
     * @param search
     * @return
     */
    public List<LogisticOrderVo> listOrder(LogisticOrderSearchVo search) {
        return getBaseMapper().listOrder(search);
    }

    /**
     * 物流客户应付
     * @param generate
     * @return
     */
    public List<LogisticPayoff> listMerchantPayoff(LogisticPayoffGenerateVo generate) {
        return getBaseMapper().listMerchantPayoff(generate);
    }

    /**
     * 根据发货日期、订单编号、商品名查找商品订单
     * @param product
     * @return
     */
    private LogisticOrderProduct findProductOrderByProduct(LogisticOrderProduct product) {
        List<LogisticOrderProduct> list = listProductOrderByProduct(Collections.singletonList(product));
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }

    /**
     * 根据发货日期、订单编号、商品名查找商品订单
     * @param list
     * @return
     */
    private List<LogisticOrderProduct> listProductOrderByProduct(List<LogisticOrderProduct> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<LogisticOrderProduct> resultList = new ArrayList<>(list.size());
        for (int index = 0; index < list.size(); index += TmsConst.BATCH_SIZE_1000) {
            List<LogisticOrderProduct> batchList = list.stream().skip(index).limit(TmsConst.BATCH_SIZE_1000).collect(Collectors.toList());
            resultList.addAll(getBaseMapper().listProductOrderByProduct(batchList));
        }

        return resultList;
    }

    /**
     * 商品订单信息初始化
     * @param order
     */
    private void prepareProductOrder(LogisticOrderProduct order) {
        order.setOrderNo(StringUtils.trimToEmpty(order.getOrderNo()));
        order.setMerchant(StringUtils.trimToEmpty(order.getMerchant()));
        order.setProduct(StringUtils.trimToEmpty(order.getProduct()));

        order.setProductPrice(DecimalUtils.nullToZero(order.getProductPrice()));
        order.setProductAmount(DecimalUtils.nullToZero(order.getProductAmount()));
        order.setProductWeight(DecimalUtils.nullToZero(order.getProductWeight()));
        order.setProductSize(DecimalUtils.nullToZero(order.getProductSize()));
        order.setProductCount(DecimalUtils.nullToZero(order.getProductCount()));
        order.setProductDeduction(DecimalUtils.nullToZero(order.getProductDeduction()));
        order.setProductFullReduction(DecimalUtils.nullToZero(order.getProductFullReduction()));

        // 商品应收金额
        prepareProductOrderAmount(order);
    }

    /**
     * 商品订单应收金额
     * @param item
     */
    private void prepareProductOrderAmount(LogisticOrderProduct item) {
        // 商品总价 - 优惠金额 - 满减金额
        item.setTotalAmount(DecimalUtils.subtract(item.getProductAmount(), item.getProductDeduction(), item.getProductFullReduction()));
    }
}

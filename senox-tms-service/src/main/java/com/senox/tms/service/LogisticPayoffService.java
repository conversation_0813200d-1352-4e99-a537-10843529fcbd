package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.LogisticPayoff;
import com.senox.tms.mapper.LogisticPayoffMapper;
import com.senox.tms.vo.LogisticPayoffSearchVo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/15 11:38
 */
@Service
public class LogisticPayoffService extends ServiceImpl<LogisticPayoffMapper, LogisticPayoff> {

    /**
     * 新增应付
     * @param payoff
     * @return
     */
    public Long add(LogisticPayoff payoff) {
        LogisticPayoff rawPayoff = findByBillDateAndMerchant(payoff);
        if (rawPayoff != null) {
            throw new BusinessException("已存在该客户的物流应付账单");
        }

        // 创建人信息初始化
        payoff.setCreateTime(LocalDateTime.now());
        payoff.setModifierId(payoff.getCreatorId());
        payoff.setModifierName(payoff.getCreatorName());
        payoff.setModifiedTime(payoff.getCreateTime());
        save(payoff);

        // 处理应付合计
        updatePayoffTotal(Collections.singletonList(payoff.getId()));
        return payoff.getId();
    }

    /**
     * 更新应付
     * @param payoff
     */
    public void update(LogisticPayoff payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            return;
        }

        payoff.setCreatorId(null);
        payoff.setCreatorName(null);
        payoff.setCreateTime(null);
        payoff.setModifiedTime(LocalDateTime.now());

        updateById(payoff);
        updatePayoffTotal(Collections.singletonList(payoff.getId()));
    }

    /**
     * 删除应付
     * @param ids
     */
    public void delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        removeByIds(ids);
    }

    /**
     * 批量保存应付
     * @param list
     */
    public void saveBatch(List<LogisticPayoff> list, Boolean overwrite) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // separate to add or update
        List<LogisticPayoff> rawList = listByBillDateAndMerchant(list);
        DataSepDto<LogisticPayoff> sepData = SeparateUtils.separateData(rawList, list);
        if (overwrite == null && !CollectionUtils.isEmpty(sepData.getUpdateList())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "检测到部分账单已生成，是否覆盖账单？");
        }

        // batch add
        addBatch(sepData.getAddList());
        // batch update
        if (BooleanUtils.isTrue(overwrite)) {
            updateBatch(sepData.getUpdateList());
        }

        // 处理应付合计
        List<Long> ids = list.stream().map(LogisticPayoff::getId).collect(Collectors.toList());
        updatePayoffTotal(ids);
    }

    /**
     * 更新应付合计
     * @param ids
     */
    public void updatePayoffTotal(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        for (int index = 0; index < ids.size(); index += TmsConst.BATCH_SIZE_1000) {
            List<Long> tmpIds = ids.stream().skip(index).limit(TmsConst.BATCH_SIZE_1000).collect(Collectors.toList());
            getBaseMapper().updatePayoffTotalAmount(tmpIds);
        }
    }

    /**
     * 根据id查找应付
     * @param id
     * @return
     */
    public LogisticPayoff findById(Long id) {
        return WrapperClassUtils.biggerThanLong(id, 0L) ? getById(id) : null;
    }

    /**
     * 应付记录数合计
     * @param search
     * @return
     */
    public int countPayoff(LogisticPayoffSearchVo search) {
        return getBaseMapper().countPayoff(search);
    }

    /**
     * 应付统计
     * @param search
     * @return
     */
    public LogisticPayoff sumPayoff(LogisticPayoffSearchVo search) {
        return getBaseMapper().sumPayoff(search);
    }

    /**
     * 应付列表
     * @param search
     * @return
     */
    public List<LogisticPayoff> listPayoff(LogisticPayoffSearchVo search) {
        return getBaseMapper().listPayoff(search);
    }

    /**
     * 批量添加
     * @param list
     */
    private void addBatch(List<LogisticPayoff> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (LogisticPayoff item : list) {
            // 初始化订单信息
            prepareLogisticPayoff(item);

            // 创建人信息初始化
            item.setCreateTime(LocalDateTime.now());
            item.setModifierId(item.getCreatorId());
            item.setModifierName(item.getCreatorName());
            item.setModifiedTime(item.getCreateTime());
        }
        saveBatch(list);
    }

    /**
     * 批量更新
     * @param list
     */
    private void updateBatch(List<LogisticPayoff> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (LogisticPayoff item : list) {
            // 创建人信息初始化
            item.setCreatorId(null);
            item.setCreatorName(null);
            item.setCreateTime(null);
            item.setModifiedTime(LocalDateTime.now());
        }

        updateBatchById(list);
    }

    /**
     * 根据账单日和商户列表查询应付
     * @param payoff
     * @return
     */
    private LogisticPayoff findByBillDateAndMerchant(LogisticPayoff payoff) {
        List<LogisticPayoff> list = listByBillDateAndMerchant(Collections.singletonList(payoff));
        List<LogisticPayoff> resultList = listByBillDateAndMerchant(list);
        return CollectionUtils.isEmpty(resultList) ? null : resultList.get(0);
    }

    /**
     * 根据账单日和商户列表查询应付
     * @param list
     * @return
     */
    private List<LogisticPayoff> listByBillDateAndMerchant(List<LogisticPayoff> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<LogisticPayoff> resultList = new ArrayList<>(list.size());
        for (int index = 0; index < list.size(); index += TmsConst.BATCH_SIZE_1000) {
            List<LogisticPayoff> tmpList = list.stream().skip(index).limit(TmsConst.BATCH_SIZE_1000).collect(Collectors.toList());
            resultList.addAll(getBaseMapper().listByBillDateAndMerchant(tmpList));
        }
        return resultList;
    }

    private void prepareLogisticPayoff(LogisticPayoff payoff) {
        payoff.setProductCount(payoff.getProductCount() == null ? BigDecimal.ZERO : payoff.getProductCount());
        payoff.setProductAmount(DecimalUtils.nullToZero(payoff.getProductAmount()));
        payoff.setProductFullReduction(DecimalUtils.nullToZero(payoff.getProductFullReduction()));
        payoff.setProductToPaid(DecimalUtils.nullToZero(payoff.getProductToPaid()));
        payoff.setProductPaid(DecimalUtils.nullToZero(payoff.getProductPaid()));
        payoff.setProductOwe(DecimalUtils.nullToZero(payoff.getProductOwe()));
        payoff.setProductDeduction(DecimalUtils.nullToZero(payoff.getProductDeduction()));
        payoff.setProductDiversity(DecimalUtils.nullToZero(payoff.getProductDiversity()));
        payoff.setShipAmount(DecimalUtils.nullToZero(payoff.getShipAmount()));
        payoff.setTotalAmount(DecimalUtils.nullToZero(payoff.getTotalAmount()));
    }
}

package com.senox.tms.service;

import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.domain.BicycleChargesScheduledTask;
import com.senox.tms.mapper.BicycleChargesScheduledTaskMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.BicycleChargesScheduledTaskSearchVo;
import com.senox.tms.vo.BicycleChargesScheduledTaskVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
@RequiredArgsConstructor
@Service
public class BicycleChargesScheduledTaskService {
    private final BicycleChargesScheduledTaskMapper taskMapper;

    /**
     * 批量添加
     *
     * @param tasks 任务集
     */
    public void addBatch(Collection<BicycleChargesScheduledTask> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }
        tasks.forEach(t -> {
            ContextUtils.initEntityCreator(t);
            ContextUtils.initEntityModifier(t);
        });
        taskMapper.addBatch(tasks);
    }

    /**
     * 添加商户
     *
     * @param taskId      任务id
     * @param merchantIds 商户集
     */
    public void addMerchant(Long taskId, Collection<Long> merchantIds) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0) || CollectionUtils.isEmpty(merchantIds)) {
            return;
        }
        taskMapper.addMerchant(taskId, merchantIds);
    }

    /**
     * 根据任务id删除商户
     *
     * @param taskId 任务id
     */
    public void deleteMerchantByTaskId(Long taskId) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0)) {
            return;
        }
        taskMapper.deleteMerchantByTaskId(taskId);
    }

    /**
     * 批量更新
     *
     * @param updateTasks  任务集
     */
    public void updateBatch(Collection<BicycleChargesScheduledTask> updateTasks) {
        if (CollectionUtils.isEmpty(updateTasks)) {
            return;
        }
        updateTasks.forEach(ContextUtils::initEntityModifier);
        taskMapper.updateBatch(updateTasks);
    }

    /**
     * 更新
     *
     * @param updateTask  任务
     * @param merchantIds 商户集
     */
    @Transactional(rollbackFor = Exception.class)
    public void update(BicycleChargesScheduledTask updateTask, Collection<Long> merchantIds) {
        if (null == updateTask) {
            return;
        }
        updateBatch(Collections.singleton(updateTask));
        deleteMerchantByTaskId(updateTask.getId());
        addMerchant(updateTask.getId(), merchantIds);
    }

    /**
     * 删除
     *
     * @param taskId 任务id
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long taskId) {
        if (!WrapperClassUtils.biggerThanLong(taskId, 0)) {
            return;
        }
        taskMapper.delete(taskId);
        taskMapper.deleteMerchantByTaskId(taskId);
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public List<BicycleChargesScheduledTaskVo> list(BicycleChargesScheduledTaskSearchVo searchVo) {
        searchVo.setPage(false);
        return taskMapper.list(searchVo);
    }

    /**
     * 列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    public int listCount(BicycleChargesScheduledTaskSearchVo searchVo) {
        return taskMapper.listCount(searchVo);
    }

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<BicycleChargesScheduledTaskVo> listPage(BicycleChargesScheduledTaskSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> listCount(searchVo), () -> taskMapper.list(searchVo));
    }

    /**
     * 根据id查找计划任务
     *
     * @param id id
     * @return 返回查找到的计划任务
     */
    public BicycleChargesScheduledTaskVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return taskMapper.findById(id);
    }
}

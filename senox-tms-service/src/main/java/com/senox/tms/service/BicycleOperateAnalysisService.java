package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.convert.BicycleOperateAnalysisConvert;
import com.senox.tms.domain.BicycleOperateAnalysis;
import com.senox.tms.mapper.BicycleOperateAnalysisMapper;
import com.senox.tms.vo.BicycleOperateAnalysisSearchVo;
import com.senox.tms.vo.BicycleOperateAnalysisVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/12 14:48
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BicycleOperateAnalysisService extends ServiceImpl<BicycleOperateAnalysisMapper, BicycleOperateAnalysis> {

    private final BicycleOrderService bicycleOrderService;
    private final BicycleOperateAnalysisConvert convert;

    /**
     * 生成运营分析记录
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addBicycleOperateAnalysis(LocalDateTime startTime, LocalDateTime endTime) {
        BicycleOperateAnalysis analysis = initOperateAnalysis(startTime, endTime);
        log.info("初始化运营分析记录数据位：{}", JsonUtils.object2Json(analysis));
        BicycleOperateAnalysis operateAnalysis = findByOperateAnalysisDate(analysis.getOperateAnalysisDate());
        log.info("当天是否有存在已有的运营分析记录数据：{}", JsonUtils.object2Json(operateAnalysis));
        if (operateAnalysis != null) {
            //如果有当天已生成运营分析记录，则重新生成
            analysis.setId(operateAnalysis.getId());
        }
        return saveOrUpdate(analysis) ? analysis.getId() : 0L;
    }

    /**
     * 根据id获取运营分析记录
     * @param id
     * @return
     */
    public BicycleOperateAnalysis findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 运营分析记录列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOperateAnalysisVo> listOperateAnalysis(BicycleOperateAnalysisSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().count(searchVo), () -> getBaseMapper().listOperateAnalysis(searchVo));
    }

    /**
     * 运营分析合计
     * @param searchVo
     * @return
     */
    public BicycleOperateAnalysis sumOperateAnalysis(BicycleOperateAnalysisSearchVo searchVo) {
        BicycleOperateAnalysis analysis = getBaseMapper().sumOperateAnalysis(searchVo);
        if (analysis == null) {
            analysis = new BicycleOperateAnalysis();
            analysis.setTotalAmount(BigDecimal.ZERO);
            analysis.setTotalPieces(BigDecimal.ZERO);
        }
        return analysis;
    }

    /**
     * 根据日期查询运营分析记录
     * @param date
     * @return
     */
    public BicycleOperateAnalysis findByOperateAnalysisDate(LocalDate date) {
        return getOne(new QueryWrapper<BicycleOperateAnalysis>().lambda()
                .eq(BicycleOperateAnalysis::getOperateAnalysisDate, date));
    }

    /**
     * 构建运营分析记录
     * @param startTime
     * @param endTime
     * @return
     */
    private BicycleOperateAnalysis initOperateAnalysis(LocalDateTime startTime, LocalDateTime endTime) {
        BicycleOperateAnalysisVo analysisVo = bicycleOrderService.findOperateAnalysis(startTime, endTime);
        BicycleOperateAnalysis analysis = convert.toDo(analysisVo);
        BicycleOperateAnalysisVo bestRider = bicycleOrderService.findBestRider(startTime, endTime);
        if (bestRider != null) {
            analysis.setBestRiderId(bestRider.getBestRiderId());
            analysis.setBestRiderName(bestRider.getBestRiderName());
        }
        BicycleOperateAnalysisVo bestCustomer = bicycleOrderService.findBestCustomer(startTime, endTime);
        if (bestCustomer != null) {
            analysis.setBestSenderId(bestCustomer.getBestSenderId());
            analysis.setBestCustomerName(bestCustomer.getBestCustomerName());
        }
        analysis.setOperateAnalysisDate(startTime.toLocalDate());
        analysis.setModifiedTime(LocalDateTime.now());
        return analysis;
    }
}

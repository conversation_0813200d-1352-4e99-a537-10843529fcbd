package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.domain.BicyclePayoffMonthReport;
import com.senox.tms.mapper.BicyclePayoffYearMonthMapper;
import com.senox.tms.vo.BicyclePayoffReportSearchVo;
import com.senox.tms.vo.BicycleTotalPageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-10-16
 */
@Service
public class BicyclePayoffMonthReportService extends ServiceImpl<BicyclePayoffYearMonthMapper, BicyclePayoffMonthReport> {

    /**
     * 批量添加报表
     *
     * @param payoffReports 报表列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<BicyclePayoffMonthReport> payoffReports) {
        if (CollectionUtils.isEmpty(payoffReports)) {
            return;
        }
        saveBatch(payoffReports);
    }

    /**
     * 批量修改报表
     *
     * @param payoffReports 报表列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(List<BicyclePayoffMonthReport> payoffReports) {
        if (CollectionUtils.isEmpty(payoffReports)) {
            return;
        }
        updateBatchById(payoffReports);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addBatchOrUpdate(String date, List<BicyclePayoffMonthReport> payoffReports) {
        BicyclePayoffReportSearchVo searchVo = new BicyclePayoffReportSearchVo();
        searchVo.setStartTime(date);
        searchVo.setEndTime(date);
        searchVo.setPage(false);
        DataSepDto<BicyclePayoffMonthReport> dataSepDto = compareAndSepReport(listPage(searchVo).getDataList(), payoffReports);
        if (!CollectionUtils.isEmpty(dataSepDto.getAddList())) {
            addBatch(dataSepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(dataSepDto.getUpdateList())) {
            updateBatch(dataSepDto.getUpdateList());
        }
    }

    /**
     * 比较报表
     *
     * @param srcReports    原报表
     * @param targetReports 目标报表
     */
    public DataSepDto<BicyclePayoffMonthReport> compareAndSepReport(List<BicyclePayoffMonthReport> srcReports, List<BicyclePayoffMonthReport> targetReports) {
        List<BicyclePayoffMonthReport> addReports;
        List<BicyclePayoffMonthReport> updateReports = null;
        if (CollectionUtils.isEmpty(srcReports)) {
            addReports = targetReports;
        } else {
            addReports = new ArrayList<>(targetReports.size());
            updateReports = new ArrayList<>(targetReports.size());
            Map<Long, BicyclePayoffMonthReport> srcReportMap = srcReports.stream().collect(Collectors.toMap(BicyclePayoffMonthReport::getPayeeId, Function.identity()));
            for (BicyclePayoffMonthReport targetReport : targetReports) {
                BicyclePayoffMonthReport payoffReport = srcReportMap.get(targetReport.getPayeeId());
                if (null == payoffReport) {
                    addReports.add(targetReport);
                    continue;
                }
                BicyclePayoffMonthReport updateReport = new BicyclePayoffMonthReport();
                updateReport.setId(payoffReport.getId());
                updateReport.setPieces(targetReport.getPieces());
                updateReport.setTotalAmount(targetReport.getTotalAmount());
                updateReport.setCreatorId(targetReport.getCreatorId());
                updateReport.setCreatorName(targetReport.getCreatorName());
                updateReport.setCreateTime(targetReport.getCreateTime());
                updateReports.add(updateReport);
            }
        }
        return new DataSepDto<>(addReports, updateReports, null);
    }

    public BicycleTotalPageResult<BicyclePayoffMonthReport> listPage(BicyclePayoffReportSearchVo searchVo) {
        LambdaQueryWrapper<BicyclePayoffMonthReport> lambdaQueryWrapper = new QueryWrapper<BicyclePayoffMonthReport>().lambda();
        if (null != searchVo.getStartTime() && !StringUtils.isBlank(searchVo.getStartTime())) {
            lambdaQueryWrapper.ge(BicyclePayoffMonthReport::getYearMonth, searchVo.getStartTime());
        }
        if (null != searchVo.getEndTime() && !StringUtils.isBlank(searchVo.getEndTime())) {
            lambdaQueryWrapper.le(BicyclePayoffMonthReport::getYearMonth, searchVo.getEndTime());
        }
        if (null != searchVo.getPayeeName() && !StringUtils.isBlank(searchVo.getPayeeName())) {
            lambdaQueryWrapper.like(BicyclePayoffMonthReport::getPayeeName, searchVo.getPayeeName());
        }
        PageResult<BicyclePayoffMonthReport> pageResult = PageUtils.commonPageResult(searchVo, () -> count(lambdaQueryWrapper), () -> {
            String lastStr = "";
            if (searchVo.isPage()) {
                lastStr = lastStr.concat(" ").concat(String.format("limit %s , %s", searchVo.getOffset(), searchVo.getPageSize()));
            }
            lambdaQueryWrapper.last(lastStr);
            return list(lambdaQueryWrapper);
        });
        return new BicycleTotalPageResult<>(pageResult,baseMapper.listTotalAmount());
    }
}

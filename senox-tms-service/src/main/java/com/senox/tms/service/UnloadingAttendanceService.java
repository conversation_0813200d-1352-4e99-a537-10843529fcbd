package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.domain.UnloadingAttendance;
import com.senox.tms.mapper.UnloadingAttendanceMapper;
import com.senox.tms.vo.UnloadingAttendanceSearchVo;
import com.senox.tms.vo.UnloadingAttendanceVo;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/14 9:20
 */
@Service
public class UnloadingAttendanceService extends ServiceImpl<UnloadingAttendanceMapper, UnloadingAttendance> {

    /**
     * 新增考勤记录
     * @param attendance
     */
    public void addAttendance(UnloadingAttendance attendance) {
        attendance.setOperateTime(LocalDateTime.now());
        attendance.setModifiedTime(LocalDateTime.now());
        save(attendance);
    }

    /**
     * 根据id查询考勤记录
     * @param id
     * @return
     */
    public UnloadingAttendance findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 考勤记录分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingAttendanceVo> pageAttendance(UnloadingAttendanceSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countAttendance(searchVo), () -> getBaseMapper().listAttendance(searchVo));
    }

    /**
     * 更新考勤记录备注
     * @param id
     * @param remark
     */
    public void updateRemark(Long id, String remark) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        UnloadingAttendance attendance = new UnloadingAttendance();
        attendance.setId(id);
        attendance.setRemark(remark);
        attendance.setModifiedTime(LocalDateTime.now());
        updateById(attendance);
    }
}

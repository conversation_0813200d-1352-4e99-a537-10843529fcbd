package com.senox.tms.service;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.tms.domain.BicyclePayoffCharges;
import com.senox.tms.mapper.BicyclePayoffChargesMapper;
import com.senox.tms.utils.ContextUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024-4-1
 */
@RequiredArgsConstructor
@Service
public class BicyclePayoffChargesService {
    private final BicyclePayoffChargesMapper chargesMapper;

    /**
     * 更新
     *
     * @param payoffCharges 应付费用
     */
    public void update(BicyclePayoffCharges payoffCharges) {
        ContextUtils.initEntityModifier(payoffCharges);
        chargesMapper.update(payoffCharges);
    }

    /**
     * 查找一个应付费用
     *
     * @return 返回应付费用
     */
    public BicyclePayoffCharges findOne() {
        BicyclePayoffCharges payoffCharges = chargesMapper.findById(null);
        if (null == payoffCharges) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        return payoffCharges;
    }


    /**
     * 根据id查找应付费用
     *
     * @param id id
     * @return 返回应付费用
     */
    public BicyclePayoffCharges findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        return chargesMapper.findById(id);
    }
}

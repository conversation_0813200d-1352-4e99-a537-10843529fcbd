package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.config.AppConfig;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.convert.LogisticTransportOrderConvert;
import com.senox.tms.domain.LogisticTransportOrder;
import com.senox.tms.mapper.LogisticTransportOrderMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticTransportOrderSearchVo;
import com.senox.tms.vo.LogisticTransportOrderStatisticsVo;
import com.senox.tms.vo.LogisticTransportOrderVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-05-20
 **/
@Service
@RequiredArgsConstructor
public class LogisticTransportOrderService extends ServiceImpl<LogisticTransportOrderMapper, LogisticTransportOrder> {
    private final LogisticTransportOrderConvert orderConvert;
    private final AppConfig appConfig;

    /**
     * 批量添加订单
     *
     * @param orders 订单集
     */
    public void addBatch(List<LogisticTransportOrder> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        orders.forEach(x -> {
            prepareOrder(x);
            ContextUtils.initEntityCreator(x);
            ContextUtils.initEntityModifier(x);
            x.setCreateTime(LocalDateTime.now());
            x.setModifiedTime(LocalDateTime.now());
        });
        baseMapper.addBatch(orders);
    }

    /**
     * 批量更新订单
     *
     * @param orders 订单集
     */
    public void updateBatch(List<LogisticTransportOrder> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        List<LogisticTransportOrderVo> dbOrders = listBySerialNos(orders.stream().map(LogisticTransportOrder::getSerialNo).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(dbOrders)) {
            return;
        }
        if (dbOrders.stream().anyMatch(LogisticTransportOrderVo::getHasBill)) {
            throw new BusinessException("订单已生成结算单，禁止操作");
        }
        orders.forEach(x -> {
            if (null != x.getFreightCharge() && null != x.getOtherCharge()) {
                x.setReceivableFreightCharge(x.getFreightCharge().subtract(x.getOtherCharge()));
            }
            ContextUtils.initEntityModifier(x);
            x.setModifiedTime(LocalDateTime.now());
        });
        baseMapper.updateBatch(orders);
    }

    /**
     * 批量添加或更新订单
     *
     * @param orders 订单集
     */
    public void addOrUpdateBatch(List<LogisticTransportOrder> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return;
        }
        List<LogisticTransportOrderVo> dbOrders = listBySerialNos(orders.stream().map(LogisticTransportOrder::getSerialNo).collect(Collectors.toList()));
        DataSepDto<LogisticTransportOrder> sepData = SeparateUtils.separateData(orderConvert.toDo(dbOrders), orders);
        List<LogisticTransportOrder> addList = sepData.getAddList();
        List<LogisticTransportOrder> updateList = sepData.getUpdateList();
        if (CollectionUtils.isEmpty(addList)) {
            addBatch(addList);
        }
        if (CollectionUtils.isEmpty(updateList)) {
            updateBatch(updateList);
        }
    }

    /**
     * 根据编号删除订单
     *
     * @param serialNos 编号集合
     * @return 是否删除成功
     */
    public boolean deleteBySerialNos(List<String> serialNos) {
        if (CollectionUtils.isEmpty(serialNos)) {
            return false;
        }
        List<LogisticTransportOrderVo> orders = listBySerialNos(serialNos);
        if (CollectionUtils.isEmpty(orders)) {
            return false;
        }
        if (orders.stream().anyMatch(LogisticTransportOrderVo::getPayStatus)) {
            throw new BusinessException("订单已支付，禁止删除");
        }
        return remove(new QueryWrapper<LogisticTransportOrder>().lambda().in(LogisticTransportOrder::getSerialNo, serialNos));
    }

    /**
     * 根据id查找订单
     *
     * @param id id
     * @return 返回查找到的订单
     */
    public LogisticTransportOrderVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            return null;
        }
        List<LogisticTransportOrderVo> orders = listByIds(Collections.singletonList(id));
        return orders.stream().findFirst().orElse(null);
    }

    /**
     * 根据编号查找订单
     *
     * @param serialNo 编号
     * @return 返回查找到的订单
     */
    public LogisticTransportOrderVo findBySerialNo(String serialNo) {
        if (StringUtils.isBlank(serialNo)) {
            return null;
        }
        List<LogisticTransportOrderVo> orders = listBySerialNos(Collections.singletonList(serialNo));
        return orders.stream().findFirst().orElse(null);
    }


    /**
     * 根据id查找订单
     *
     * @param ids id集
     * @return 返回查找到的订单集
     */
    public List<LogisticTransportOrderVo> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LogisticTransportOrderSearchVo search = new LogisticTransportOrderSearchVo();
        search.setPage(false);
        search.setIds(ids);
        return list(search);
    }

    /**
     * 根据编号集查找订单
     *
     * @param serialNos 编号集
     * @return 返回查找到的订单集
     */
    public List<LogisticTransportOrderVo> listBySerialNos(List<String> serialNos) {
        if (CollectionUtils.isEmpty(serialNos)) {
            return Collections.emptyList();
        }
        LogisticTransportOrderSearchVo search = new LogisticTransportOrderSearchVo();
        search.setPage(false);
        search.setSerialNos(serialNos);
        return list(search);
    }

    /**
     * 根据日期查找订单
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 返回查找到的订单集
     */
    public List<LogisticTransportOrderVo> listByDate(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            return Collections.emptyList();
        }
        LogisticTransportOrderSearchVo search = new LogisticTransportOrderSearchVo();
        search.setPage(false);
        search.setStartDate(startDate);
        search.setEndDate(endDate);
        return list(search);
    }

    /**
     * 列表统计查询
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(LogisticTransportOrderSearchVo search) {
        return baseMapper.countList(search);
    }

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    public List<LogisticTransportOrderVo> list(LogisticTransportOrderSearchVo search) {
        return baseMapper.list(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<LogisticTransportOrderVo> pageList(LogisticTransportOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search, () -> countList(search), () -> list(search));
    }

    /**
     * 订单统计
     *
     * @param search 查询参数
     * @return 返回订单统计
     */
    public LogisticTransportOrderStatisticsVo orderStatistics(LogisticTransportOrderSearchVo search) {
        return baseMapper.orderStatistics(search);
    }

    /**
     * 订单编号初始化
     */
    private String prepareOrderNo() {
        String prefix = DateUtils.formatYearMonth(LocalDate.now(), "yyMM");
        String cacheKey = String.format(TmsConst.Cache.KEY_LOGISTIC_TRANSPORT_ORDER_SERIAL_NO, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, TmsConst.Cache.TTL_30D);
        return prefix.concat(StringUtils.fixLength(String.valueOf(result), appConfig.getLogisticTransportOrderLength(), appConfig.getFillChar()));
    }

    /**
     * 订单初始化
     *
     * @param order 订单
     */
    public void prepareOrder(LogisticTransportOrder order) {
        order.setSerialNo(prepareOrderNo());
        if (StringUtils.isBlank(order.getYearMonthDay())) {
            order.setYearMonthDay(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_FULL_DATE));
        }
        if (StringUtils.isBlank(order.getConsigneeName())) {
            order.setConsigneeName(StringUtils.EMPTY);
        }
        if (StringUtils.isBlank(order.getConsigneePhone())) {
            order.setConsigneePhone(StringUtils.EMPTY);
        }
        if (null == order.getFreightCharge()) {
            order.setFreightCharge(BigDecimal.ZERO);
        }
        if (null == order.getOtherCharge()) {
            order.setOtherCharge(BigDecimal.ZERO);
        }
        order.setReceivableFreightCharge(order.getFreightCharge().subtract(order.getOtherCharge()));
        if (null == order.getReceivableFreightCharge()) {
            order.setReceivableFreightCharge(BigDecimal.ZERO);
        }
    }
}

package com.senox.tms.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.tms.domain.*;
import com.senox.tms.vo.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/27 16:20
 */
@Service
@RequiredArgsConstructor
public class LogisticOrderService {

    private final LogisticOrderProductService productService;
    private final LogisticOrderShipService shipService;
    private final LogisticOrderBillService billService;
    private final LogisticPayoffService payoffService;

    /**
     * 添加物流订单
     * @param order
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addLogisticOrder(LogisticOrder order) {
        // 添加商品订单信息
        productService.addProductOrder(order.getProduct());

        // 添加配送订单信息
        order.getShip().setOrderProductId(order.getProduct().getId());
        shipService.addShipOrder(order.getShip());

        // 添加账单信息
        order.getBill().setOrderProductId(order.getProduct().getId());
        order.getBill().setShipId(order.getShip().getId());
        billService.addOrderBill(order.getBill());

        return order.getProduct().getId();
    }

    /**
     * 更新物流订单
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateLogisticOrder(LogisticOrder order) {
        if (!WrapperClassUtils.biggerThanLong(order.getProduct().getId(), 0L)) {
            return;
        }

        // 更新商品订单信息
        productService.updateProductOrder(order.getProduct());

        // 更新配送订单信息
        LogisticOrderShip srcShip = shipService.findShipOrderByProductOrderId(order.getProduct().getId());
        order.getShip().setId(srcShip.getId());
        order.getShip().setOrderProductId(order.getProduct().getId());
        shipService.updateShipOrder(order.getShip());

        // 更新账单信息
        order.getBill().setOrderProductId(order.getProduct().getId());
        order.getBill().setShipId(order.getShip().getId());
        LogisticOrderBill srcBill = billService.findOrderBillByProductAndShip(order.getBill());
        order.getBill().setId(srcBill.getId());
        billService.updateOrderBill(order.getBill());
    }

    /**
     * 批量保存物流订单
     * @param list
     * @param overwrite
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveLogisticOrderBatch(List<LogisticOrder> list, Boolean overwrite) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 商品订单信息保存
        productService.saveProductOrderBatch(list.stream().map(LogisticOrder::getProduct).collect(Collectors.toList()), overwrite);

        // 配送订单信息保存
        list.forEach(x -> x.getShip().setOrderProductId(x.getProduct().getId()));
        shipService.saveShipOrderBatch(list.stream().map(LogisticOrder::getShip).collect(Collectors.toList()), overwrite);

        // 账单信息保存
        list.forEach(x -> {
            x.getBill().setOrderProductId(x.getProduct().getId());
            x.getBill().setOrderAmount(x.getProduct().getTotalAmount());
            x.getBill().setShipId(x.getShip().getId());
            x.getBill().setShipAmount(x.getShip().getShipAmount());
        });
        billService.saveOrderBillBatch(list.stream().map(LogisticOrder::getBill).collect(Collectors.toList()), overwrite);
    }

    /**
     * 更新物流订单信息
     * @param editInfo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateLogisticOrderInfo(LogisticOrderEditBatchVo editInfo) {
        productService.updateProductOrderInfoBatch(editInfo);

        if (!StringUtils.isEmpty(editInfo.getRemark())) {
            billService.updateOrderBillAmount(editInfo.getProductOrderIds());
        }
    }

    /**
     * 删除物流订单
     * @param orderIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteLogisticOrders(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }

        List<LogisticOrderProduct> products = productService.listOrderByProductOrderIds(orderIds);
        List<LocalDate> shipDates = products.stream().map(LogisticOrderProduct::getShipDate).distinct().collect(Collectors.toList());

        productService.deleteByOrderIds(orderIds);
        shipService.deleteByOrderIds(orderIds);
        billService.deleteByOrderIds(orderIds);

        shipService.updateShipOrderDiscountByShipDate(shipDates);
        billService.updateOrderBillAmountByShipDates(shipDates);
    }

    /**
     * 发送订单折扣率
     * @param search
     * @return
     */
    public BigDecimal calShipOrderDiscount(ShipOrderDiscountSearchVo search) {
        return shipService.findShipOrderDiscount(search);
    }

    /**
     * 根据商品订单id查找订单明细
     * @param productOrderId
     * @return
     */
    public LogisticOrderVo findByProductOrderId(Long productOrderId) {
        return productService.findOrderByProductOrderId(productOrderId);
    }

    /**
     * 生成客户应付账单
     * @param generate
     */
    public void generatePayoff(LogisticPayoffGenerateVo generate) {
        if (generate.getBillDate() == null) {
            throw new InvalidParameterException("请指定发货日期");
        }

        List<LogisticPayoff> list = productService.listMerchantPayoff(generate);
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("暂无订单");
        }

        list.forEach(x -> {
            x.setCreatorId(generate.getOperatorId());
            x.setCreatorName(generate.getOperateName());
            x.setModifierId(generate.getOperatorId());
            x.setModifierName(generate.getOperateName());
        });

        payoffService.saveBatch(list, generate.getOverwrite());
    }

    /**
     * 订单数合计
     * @param search
     * @return
     */
    public int countOrder(LogisticOrderSearchVo search) {
        return productService.countOrder(search);
    }

    /**
     * 订单统计
     * @param search
     * @return
     */
    public LogisticOrderVo sumOrder(LogisticOrderSearchVo search) {
        return productService.sumOrder(search);
    }

    /**
     * 订单列表
     * @param search
     * @return
     */
    public List<LogisticOrderVo> listOrder(LogisticOrderSearchVo search) {
        return productService.listOrder(search);
    }
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.domain.BicycleOrderMedia;
import com.senox.tms.mapper.BicycleOrderMediaMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/13 9:26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BicycleOrderMediaService extends ServiceImpl<BicycleOrderMediaMapper, BicycleOrderMedia> {

    /**
     * 添加订单媒体信息
     * @param orderId
     * @param mediaUrls
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBicycleOrderMedia(Long orderId, List<String> mediaUrls) {
        List<BicycleOrderMedia> mediaList = prepareListMedia(orderId, mediaUrls);
        DataSepDto<BicycleOrderMedia> sepData = compareAndSeparateMedia(listByOrderIds(Collections.singletonList(orderId)), mediaList);

        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(BicycleOrderMedia::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 订单媒体信息初始化
     * @param orderId
     * @param mediaUrls
     * @return
     */
    private List<BicycleOrderMedia> prepareListMedia(Long orderId, List<String> mediaUrls) {
        if (CollectionUtils.isEmpty(mediaUrls)) {
            return Collections.emptyList();
        }
        List<BicycleOrderMedia> resultList = new ArrayList<>(mediaUrls.size());
        for (String item : mediaUrls) {
            resultList.add(new BicycleOrderMedia(orderId, item));
        }
        return resultList;
    }

    /**
     * 根据订单id删除媒体
     * @param orderId
     */
    public void deleteBicycleOrderMedia(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            throw new InvalidParameterException();
        }
        remove(new QueryWrapper<BicycleOrderMedia>().
                lambda().eq(BicycleOrderMedia::getOrderId, orderId));
    }

    /**
     * 比较区分多媒体信息
     *
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<BicycleOrderMedia> compareAndSeparateMedia(List<BicycleOrderMedia> srcList, List<BicycleOrderMedia> targetList) {
        List<BicycleOrderMedia> addList = null;
        List<BicycleOrderMedia> removeList = null;
        if (CollectionUtils.isEmpty(srcList)) {
            addList = targetList;

        } else if (CollectionUtils.isEmpty(targetList)) {
            removeList = srcList;

        } else {
            addList = targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList());
            removeList = srcList.stream().filter(x -> !targetList.contains(x)).collect(Collectors.toList());
        }

        DataSepDto<BicycleOrderMedia> result = new DataSepDto<>();
        result.setAddList(addList);
        result.setRemoveList(removeList);
        return result;
    }

    /**
     * 根据订单id查询媒体
     * @param orderIds
     * @return
     */
    public List<BicycleOrderMedia> listByOrderIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<BicycleOrderMedia>().lambda().in(BicycleOrderMedia::getOrderId, orderIds));
    }
}

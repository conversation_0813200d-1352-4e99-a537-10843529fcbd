package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.StringUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.DictLogisticCategory;
import com.senox.tms.domain.DictLogistic;
import com.senox.tms.mapper.DictLogisticMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.DictLogisticSearchVo;
import com.senox.tms.vo.DictLogisticVo;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Service
public class DictLogisticService extends ServiceImpl<DictLogisticMapper, DictLogistic> {

    /**
     * 添加
     *
     * @param dictLogistic 物流字典
     */
    public void add(DictLogistic dictLogistic) {
        addBatch(Collections.singleton(dictLogistic));
    }

    /**
     * 批量添加
     *
     * @param dictLogistics 物流字典集
     * @return 影响条
     */
    public int addBatch(Collection<DictLogistic> dictLogistics) {
        if (CollectionUtils.isEmpty(dictLogistics)) {
            return 0;
        }
        dictLogistics.forEach(c -> {
            ContextUtils.initEntityCreator(c);
            ContextUtils.initEntityModifier(c);
            if (StringUtils.isBlank(c.getKey())) {
                c.setKey(StringUtils.EMPTY);
            }
            if (StringUtils.isBlank(c.getName())) {
                c.setName(StringUtils.EMPTY);
            }
            if (StringUtils.isBlank(c.getAttr1())) {
                c.setAttr1(StringUtils.EMPTY);
            }
            if (StringUtils.isBlank(c.getAttr2())) {
                c.setAttr2(StringUtils.EMPTY);
            }
        });
        int count = baseMapper.addBatch(dictLogistics);
        if (count > 0) {
            //更新排序
            List<DictLogistic> dictLogisticSorts = dictLogistics.stream().map(x -> {
                DictLogistic dictLogisticSort = new DictLogistic();
                dictLogisticSort.setId(x.getId());
                dictLogisticSort.setSort(x.getId());
                return dictLogisticSort;
            }).collect(Collectors.toList());
            updateBatch(dictLogisticSorts);
        }
        return count;
    }

    /**
     * 更新
     *
     * @param dictLogistic 物流字典
     */
    public void update(DictLogistic dictLogistic) {
        updateBatch(Collections.singleton(dictLogistic));
    }

    /**
     * 批量更新
     *
     * @param dictLogistics 物流字典集
     * @return 影响条
     */
    public int updateBatch(Collection<DictLogistic> dictLogistics) {
        if (CollectionUtils.isEmpty(dictLogistics)) {
            return 0;
        }
        dictLogistics.forEach(ContextUtils::initEntityModifier);
        return baseMapper.updateBatch(dictLogistics);
    }

    /**
     * 根据id删除
     *
     * @param id id
     */
    public void deleteById(Long id) {
        deleteByIds(Collections.singletonList(id));
    }

    /**
     * 根据id集删除
     *
     * @param ids id集
     */
    public void deleteByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        removeByIds(ids);
    }

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    public List<DictLogisticVo> list(DictLogisticSearchVo searchVo) {
        return baseMapper.list(searchVo);
    }

    /**
     * 根据key查询列表
     *
     * @param keys key集
     * @return 返回查询到的列表
     */
    public List<DictLogisticVo> listByKeys(Collection<String> keys) {
        if (CollectionUtils.isEmpty(keys)) {
            return Collections.emptyList();
        }
        DictLogisticSearchVo searchVo = new DictLogisticSearchVo();
        searchVo.setPage(false);
        searchVo.setKeys(keys);
        return baseMapper.list(searchVo);
    }

    /**
     * 根据id查询列表
     *
     * @param ids id集
     * @return 返回查询到的列表
     */
    public List<DictLogisticVo> listByIds(List<Long> ids) {
        return listByIds(ids, null);
    }

    /**
     * 根据id查询列表
     *
     * @param ids id集
     * @param category 类型
     * @return 返回查询到的列表
     */
    public List<DictLogisticVo> listByIds(List<Long> ids, DictLogisticCategory category) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        DictLogisticSearchVo searchVo = new DictLogisticSearchVo();
        searchVo.setPage(false);
        searchVo.setIds(ids);
        searchVo.setCategory(category);
        return baseMapper.list(searchVo);
    }

    /**
     * 列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    public int countList(DictLogisticSearchVo searchVo) {
        return baseMapper.countList(searchVo);
    }

    /**
     * 分页列表
     *
     * @param searchVo 查询
     * @return 返回分页后的列表
     */
    public PageResult<DictLogisticVo> listPage(DictLogisticSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> countList(searchVo), () -> list(searchVo));
    }
}

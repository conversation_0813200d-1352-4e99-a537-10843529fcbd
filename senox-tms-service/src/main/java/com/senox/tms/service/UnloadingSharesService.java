package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DateUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.UnloadingSharesStatus;
import com.senox.tms.domain.UnloadingShares;
import com.senox.tms.mapper.UnloadingSharesMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.UnloadingSharesSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/30 14:55
 */
@Service
@Slf4j
public class UnloadingSharesService extends ServiceImpl<UnloadingSharesMapper, UnloadingShares> {

    /**
     * 添加分佣
     * @param shares
     */
    public void addShares(UnloadingShares shares) {
        shares.setCreateTime(LocalDateTime.now());
        shares.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityCreator(shares);
        ContextUtils.initEntityModifier(shares);
        save(shares);
    }

    /**
     * 修改分佣
     * @param shares
     */
    public void updateShares(UnloadingShares shares) {
        shares.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(shares);
        updateById(shares);
    }

    /**
     * 保存分佣
     * @param shares
     */
    public void saveShares(UnloadingShares shares) {
        if (!WrapperClassUtils.biggerThanLong(shares.getId(), 0L)) {
            addShares(shares);
        } else {
            updateShares(shares);
        }
    }

    /**
     * 根据id查询分佣
     * @param id
     * @return
     */
    public UnloadingShares findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 删除分佣
     * @param id
     */
    public void deleteById(Long id) {
        UnloadingShares shares = findById(id);
        if (shares == null || shares.getStatus() == UnloadingSharesStatus.ENABLED.getNumber()) {
            throw new BusinessException("该分佣记录不存在或生效中");
        }
        removeById(id);
    }

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    public int countShares(UnloadingSharesSearchVo searchVo) {
        return getBaseMapper().countShares(searchVo);
    }

    /**
     * 列表
     * @param searchVo
     * @return
     */
    public List<UnloadingShares> listShares(UnloadingSharesSearchVo searchVo) {
        return getBaseMapper().listShares(searchVo);
    }

    /**
     * 分佣分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingShares> pageShares(UnloadingSharesSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> countShares(searchVo), () -> listShares(searchVo));
    }

    /**
     * 获取当前有效的分佣
     * @return 返回生效时间跟当前时间最接近的分佣
     */
    public UnloadingShares getCurrentEffectiveShares(){
        UnloadingSharesSearchVo searchVo = new UnloadingSharesSearchVo();
        searchVo.setPage(false);
        searchVo.setEffectiveTimeEnd(LocalDateTime.now());
        searchVo.setStatus(UnloadingSharesStatus.ENABLED.getNumber());
        List<UnloadingShares> sharesList = listShares(searchVo);
        List<UnloadingShares> collect = sharesList
                .stream()
                .sorted(Comparator.comparing(i -> DateUtils.getDurationBetween(i.getEffectiveTime(), LocalDateTime.now(), ChronoUnit.SECONDS)))
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(collect) ? null : collect.get(0);
    }
}

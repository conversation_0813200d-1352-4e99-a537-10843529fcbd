package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.tms.constant.BicycleOrderState;
import com.senox.tms.constant.BicycleOrderStatus;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.BicycleOrderDayReport;
import com.senox.tms.domain.BicycleOrderMonthReport;
import com.senox.tms.mapper.BicycleOrderDayReportMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/16 9:37
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BicycleOrderDayReportService extends ServiceImpl<BicycleOrderDayReportMapper, BicycleOrderDayReport> {

    private final BicycleOrderService bicycleOrderService;

    /**
     * 生成日报表
     * @param dateVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void generateDayReport(BicycleReportDateVo dateVo) {
        LocalDateTime orderTimeStart = LocalDateTime.of(dateVo.getYear(), dateVo.getMonth(), dateVo.getDay(), 0, 0, 0);
        LocalDateTime orderTimeEnd = LocalDateTime.of(orderTimeStart.toLocalDate(), LocalTime.MAX);
        LocalDate reportDate = LocalDate.of(dateVo.getYear(), dateVo.getMonth(), dateVo.getDay());
        String lockKey = String.format(TmsConst.Cache.KEY_BICYCLE_DAY_REPORT_LOCK, orderTimeStart.getYear(), orderTimeStart.getMonth(), orderTimeStart.getDayOfMonth());
        if (!RedisUtils.lock(lockKey, TmsConst.Cache.TTL_1H)) {
            throw new BusinessException(String.format("日报表[%s]-[%s]-[%s]生成中...", orderTimeStart.getYear(), orderTimeStart.getMonth(), orderTimeStart.getDayOfMonth()));
        }
        try {
            log.info("开始生成{}-{}-{}日报表", dateVo.getYear(), dateVo.getMonth(), dateVo.getDay());
            //正常的订单
            BicycleOrderSearchVo searchVo = getBicycleOrderSearchVo(dateVo, orderTimeStart, orderTimeEnd);
            List<BicycleOrderVo> dataList = bicycleOrderService.listOrder(searchVo).getDataList();
            List<BicycleOrderDayReport> reportList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(dataList)) {
                Map<Long, List<BicycleOrderVo>> listMap = dataList.stream().collect(Collectors.groupingBy(BicycleOrderVo::getSenderId));
                listMap.forEach((key, value)-> {
                    BicycleOrderDayReport dayReport = buildBicycleDayReport(value, reportDate);
                    reportList.add(dayReport);
                });
            } else {
                log.info("{}-{}-{}三轮车订单为空！----- {}", dateVo.getYear(), dateVo.getMonth(), dateVo.getDay(), JsonUtils.object2Json(dataList));
            }

            List<BicycleOrderDayReport> list = listDayReportByReportDate(reportDate);

            DataSepDto<BicycleOrderDayReport> sepDto = SeparateUtils.separateData(list, reportList,
                    this::checkBicycleDayReportExist,
                    x -> reportList.stream().noneMatch(y -> checkBicycleDayReportExist(x, y)),
                    this::checkBicycleDayReportDuplicated
            );
            log.info("操作的日报表集合：{}", JsonUtils.object2Json(sepDto));
            if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
                saveBatch(sepDto.getAddList());
            }
            if (!CollectionUtils.isEmpty(sepDto.getUpdateList())) {
                sepDto.getUpdateList().forEach(update-> {
                    ContextUtils.initEntityModifier(update);
                    update.setModifiedTime(LocalDateTime.now());
                });
                updateBatchById(sepDto.getUpdateList());
            }
            if (!CollectionUtils.isEmpty(sepDto.getRemoveList())) {
                removeByIds(sepDto.getRemoveList().stream().map(BicycleOrderDayReport::getId).collect(Collectors.toList()));
            }
        } finally {
            RedisUtils.del(lockKey);
        }
    }

    @NotNull
    private static BicycleOrderSearchVo getBicycleOrderSearchVo(BicycleReportDateVo dateVo, LocalDateTime orderTimeStart, LocalDateTime orderTimeEnd) {
        BicycleOrderSearchVo searchVo = new BicycleOrderSearchVo();
        searchVo.setPage(false);
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);
        searchVo.setState(BicycleOrderState.FINALIZE.getNumber());
        searchVo.setOrderStatus(BicycleOrderStatus.FINALIZE.getNumber());
        searchVo.setOrderTimeStart(orderTimeStart);
        searchVo.setOrderTimeEnd(orderTimeEnd);
        if (WrapperClassUtils.biggerThanLong(dateVo.getMerchantId(), 0L)){
            searchVo.setSenderId(dateVo.getMerchantId());
        }
        return searchVo;
    }

    /**
     * 更新日报表
     * @param bill
     */
    public void updateDayReport(BicycleOrderDayReport bill) {
        BicycleOrderDayReport item = findById(bill.getId());
        if (item == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        item.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(item);
        item.setDeliveryCharge(DecimalUtils.nullToZero(bill.getDeliveryCharge()));
        item.setOtherCharge(DecimalUtils.nullToZero(bill.getOtherCharge()));
        item.setHandlingCharge(DecimalUtils.nullToZero(bill.getHandlingCharge()));
        item.setUpstairsCharge(DecimalUtils.nullToZero(bill.getUpstairsCharge()));
        item.setTotalCharge(DecimalUtils.add(item.getDeliveryCharge(), item.getOtherCharge(), item.getHandlingCharge(), item.getUpstairsCharge()));
        updateById(item);
    }

    /**
     * 删除日报表
     * @param ids
     */
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        removeByIds(ids);
    }

    /**
     * 根据id查找日报表
     * @param ids
     * @return
     */
    public List<BicycleOrderDayReport> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        return list(new QueryWrapper<BicycleOrderDayReport>().lambda().in(BicycleOrderDayReport::getId, ids));
    }

    /**
     * 根据id获取日报表
     * @param id
     * @return
     */
    public BicycleOrderDayReport findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据月报表查询日报表
     * @param monthReport
     * @return
     */
    public List<BicycleOrderDayReport> listByMonthReport(BicycleOrderMonthReport monthReport) {
        if (!WrapperClassUtils.biggerThanLong(monthReport.getMerchantId(), 0)
                || !WrapperClassUtils.biggerThanInt(monthReport.getReportYear(), 0)
                || !WrapperClassUtils.biggerThanInt(monthReport.getReportMonth(), 0)) {
            return Collections.emptyList();
        }
        LocalDate reportDateStart = LocalDate.of(monthReport.getReportYear(), monthReport.getReportMonth(), 1);
        LocalDate reportDateEnd = DateUtils.getLastDateInMonth(reportDateStart);
        return list(new QueryWrapper<BicycleOrderDayReport>().lambda()
                .eq(BicycleOrderDayReport::getMerchantId, monthReport.getMerchantId())
                .ge(BicycleOrderDayReport::getReportDate, reportDateStart)
                .le(BicycleOrderDayReport::getReportDate, reportDateEnd)
                .orderByAsc(BicycleOrderDayReport::getReportDate));
    }

    /**
     * 日报表合计
     * @param searchVo
     * @return
     */
    public BicycleOrderDayReport sumDayReport(BicycleOrderDayReportSearchVo searchVo) {
        BicycleOrderDayReport dayReport = getBaseMapper().sumDayReport(searchVo);
        if (dayReport == null) {
            dayReport = new BicycleOrderDayReport();
            dayReport.setDeliveryCharge(BigDecimal.ZERO);
            dayReport.setOtherCharge(BigDecimal.ZERO);
            dayReport.setHandlingCharge(BigDecimal.ZERO);
            dayReport.setUpstairsCharge(BigDecimal.ZERO);
            dayReport.setTotalCharge(BigDecimal.ZERO);
        }
        return dayReport;
    }

    /**
     * 日报表列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderDayReportVo> listDayReport(BicycleOrderDayReportSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countDayReport(searchVo), () -> getBaseMapper().listDayReport(searchVo));
    }


    /**
     * 构建日报表
     * @param orderVoList
     * @param reportDate
     * @return
     */
    private BicycleOrderDayReport buildBicycleDayReport(List<BicycleOrderVo> orderVoList, LocalDate reportDate) {
        orderVoList = orderVoList.stream().filter(orderVo -> orderVo.getOrderStatus() == BicycleOrderStatus.FINALIZE.getNumber()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderVoList)) {
            return null;
        }
        BicycleOrderVo orderVo = orderVoList.get(0);
        BicycleOrderDayReport dayReport = new BicycleOrderDayReport();
        dayReport.setReportDate(reportDate);
        dayReport.setMerchantId(orderVo.getSenderId());
        dayReport.setDeliveryCharge(orderVoList.stream().map(BicycleOrderVo::getDeliveryCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
        dayReport.setOtherCharge(orderVoList.stream().map(BicycleOrderVo::getOtherCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
        dayReport.setHandlingCharge(orderVoList.stream().map(BicycleOrderVo::getHandlingCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
        dayReport.setUpstairsCharge(orderVoList.stream().map(BicycleOrderVo::getUpstairsCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
        dayReport.setTotalCharge(DecimalUtils.add(dayReport.getDeliveryCharge(), dayReport.getOtherCharge(), dayReport.getHandlingCharge(), dayReport.getUpstairsCharge()));
        //总件数
        dayReport.setTotalPieces(orderVoList.stream().map(BicycleOrderVo::getPieces).reduce(BigDecimal.ZERO, BigDecimal::add));
        //总单数
        dayReport.setTotalCount(orderVoList.size());

        ContextUtils.initEntityCreator(dayReport);
        ContextUtils.initEntityModifier(dayReport);
        dayReport.setCreateTime(LocalDateTime.now());
        dayReport.setModifiedTime(LocalDateTime.now());
        log.info("{}的日报表为----{}", reportDate, JsonUtils.object2Json(dayReport));
        return dayReport;
    }

    /**
     * 根据报表日期查询报表
     * @param reportDate
     * @return
     */
    private List<BicycleOrderDayReport> listDayReportByReportDate(LocalDate reportDate) {
        return list(new QueryWrapper<BicycleOrderDayReport>().lambda()
                .eq(BicycleOrderDayReport::getReportDate, reportDate));
    }

    private boolean checkBicycleDayReportExist(BicycleOrderDayReport dayReport1, BicycleOrderDayReport dayReport2) {
        return Objects.equals(dayReport1.getReportDate(), dayReport2.getReportDate())
                && Objects.equals(dayReport1.getMerchantId(), dayReport2.getMerchantId());
    }

    private boolean checkBicycleDayReportDuplicated(BicycleOrderDayReport dayReport1, BicycleOrderDayReport dayReport2) {
        return checkBicycleDayReportExist(dayReport1, dayReport2)
                && DecimalUtils.equals(dayReport1.getDeliveryCharge(), dayReport2.getDeliveryCharge())
                && DecimalUtils.equals(dayReport1.getOtherCharge(), dayReport2.getOtherCharge())
                && DecimalUtils.equals(dayReport1.getHandlingCharge(), dayReport2.getHandlingCharge())
                && DecimalUtils.equals(dayReport1.getUpstairsCharge(), dayReport2.getUpstairsCharge())
                && DecimalUtils.equals(dayReport1.getTotalCharge(), dayReport2.getTotalCharge());
    }
}

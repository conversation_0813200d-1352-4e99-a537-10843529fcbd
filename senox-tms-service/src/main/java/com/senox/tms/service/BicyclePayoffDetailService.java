package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.tms.domain.BicyclePayoffDetail;
import com.senox.tms.mapper.BicyclePayoffDetailMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/8 14:21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BicyclePayoffDetailService extends ServiceImpl<BicyclePayoffDetailMapper, BicyclePayoffDetail> {

    /**
     * 保存应付账单详细
     * @param payoffDetailList
     * @param removeIds
     */
    public void savePayoffDetail(List<BicyclePayoffDetail> payoffDetailList, List<Long> removeIds) {
        log.info("删除应付账单详细{}的原数据，重新插入", JsonUtils.object2Json(payoffDetailList));
        List<Long> payoffIds = payoffDetailList.stream().map(BicyclePayoffDetail::getPayoffId).distinct().collect(Collectors.toList());
        payoffIds.addAll(removeIds);
        deleteBicyclePayoffDetailByBillIdList(payoffIds);

        saveOrUpdateBatch(payoffDetailList);
    }

    /**
     * 根据账单id删除费项
     * @param payoffIdList
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBicyclePayoffDetailByBillIdList(List<Long> payoffIdList) {
        if (CollectionUtils.isEmpty(payoffIdList)) {
            throw new InvalidParameterException();
        }
        remove(new QueryWrapper<BicyclePayoffDetail>().lambda().in(BicyclePayoffDetail::getPayoffId, payoffIdList));
    }
}

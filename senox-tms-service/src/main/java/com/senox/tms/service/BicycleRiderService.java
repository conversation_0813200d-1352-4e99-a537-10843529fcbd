package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.tms.config.AppConfig;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.BicycleRider;
import com.senox.tms.mapper.BicycleRiderMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.BicycleDayCountRiderVo;
import com.senox.tms.vo.BicycleRiderChangerPwdVo;
import com.senox.tms.vo.BicycleRiderInfoVo;
import com.senox.tms.vo.BicycleRiderSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/13 15:47
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BicycleRiderService extends ServiceImpl<BicycleRiderMapper, BicycleRider> {

    private final AppConfig appConfig;

    /**
     * 添加三轮车配送骑手
     * @param bicycleRider
     * @return
     */
    public Long addBicycleRider(BicycleRider bicycleRider) {
        if (isBicycleRiderExists(bicycleRider)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "手机号已被注册");
        }
        //初始化密码
        initSaltAndPassword(bicycleRider);
        if (bicycleRider.getCreateTime() == null) {
            bicycleRider.setCreateTime(LocalDateTime.now());
        }
        if (bicycleRider.getModifiedTime() == null) {
            bicycleRider.setModifiedTime(LocalDateTime.now());
        }
        //初始化骑手编号
        prepareRiderNo(bicycleRider);
        boolean save = save(bicycleRider);
        if (save) {
            generateReferralCode(bicycleRider.getId());
        }
        return save ? bicycleRider.getId() : 0L;
    }

    /**
     * 更新骑手信息
     * @param bicycleRider
     */
    public void updateBicycleRider(BicycleRider bicycleRider) {
        if (!WrapperClassUtils.biggerThanLong(bicycleRider.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        if (isBicycleRiderExists(bicycleRider)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "手机号已被注册");
        }
        if (!StringUtils.isBlank(bicycleRider.getPassword())) {
            //重置密码
            initSaltAndPassword(bicycleRider);
        }
        bicycleRider.setRiderNo(null);
        bicycleRider.setReferralCode(null);
        bicycleRider.setModifiedTime(LocalDateTime.now());
        updateById(bicycleRider);
    }

    /**
     * 生成推荐码
     * @param id
     */
    public void generateReferralCode(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleRider rider = findById(id);
        if (!StringUtils.isBlank(rider.getReferralCode())) {
            throw new BusinessException("已生成推荐码，请勿重复生成！");
        }
        ContextUtils.initEntityModifier(rider);
        rider.setModifiedTime(LocalDateTime.now());
        rider.setRiderNo(null);
        rider.setReferralCode(String.valueOf(ThreadLocalRandom.current().nextInt(100000, 999999)));
        updateById(rider);
    }

    /**
     * 修改密码
     * @param changePwd
     */
    public void changePassword(BicycleRiderChangerPwdVo changePwd) {
        BicycleRider rider = findByContactAndPassword(changePwd.getContact(), changePwd.getOldPassword());
        if (rider == null) {
            throw new BusinessException("原密码错误");
        }
        //修改密码，修改盐
        rider.setSalt(StringUtils.randStr(appConfig.getSaltLength()));
        rider.setPassword(Md5Utils.encode(changePwd.getNewPassword(), rider.getSalt(), appConfig.getHashIterations()));
        rider.setLastModifyPasswordTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(rider);
        rider.setModifiedTime(LocalDateTime.now());
        updateById(rider);
    }

    /**
     * 通过id查询骑手
     * @param id
     * @param isPwdRequired 是否返回密码
     * @return
     */
    public BicycleRider findById(Long id, boolean isPwdRequired) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        LambdaQueryWrapper<BicycleRider> wrapper = new QueryWrapper<BicycleRider>().lambda()
                .eq(BicycleRider::getId, id).eq(BicycleRider::getDisabled, Boolean.FALSE);
        if (!isPwdRequired) {
            excludePasswordAndSalt(wrapper);
        }
        return getOne(wrapper);
    }

    /**
     * 根据推荐码查询骑手
     * @param referralCode
     * @return
     */
    public BicycleRider findByReferralCode(String referralCode) {
        if (StringUtils.isBlank(referralCode)) {
            return null;
        }
        return getOne(new QueryWrapper<BicycleRider>().lambda()
                .eq(BicycleRider::getReferralCode, referralCode).eq(BicycleRider::getDisabled, Boolean.FALSE));
    }

    /**
     * 根据id删除骑手信息
     * @param id
     */
    public void deleteBicycleRider(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleRider rider = findById(id, false);
        if (rider == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "骑手信息未找到");
        }

        ContextUtils.initEntityModifier(rider);
        rider.setModifiedTime(LocalDateTime.now());
        rider.setDisabled(Boolean.TRUE);
        rider.setContact(rider.getContact().concat("_").concat(rider.getId().toString()));
        updateById(rider);
    }

    /**
     * 骑手列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleRider> list(BicycleRiderSearchVo searchVo) {

        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countRider(searchVo), () -> getBaseMapper().listRider(searchVo));
    }

    /**
     * 通过id查询骑手
     * @param id
     * @return
     */
    public BicycleRider findById(Long id) {
        return findById(id, true);
    }

    /**
     * 更新骑手状态
     * @param id
     * @param status
     */
    public boolean updateBicycleRiderStatus(Long id, Integer status) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleRider rider = new BicycleRider();
        rider.setId(id);
        rider.setStatus(status);
        ContextUtils.initEntityModifier(rider);
        rider.setModifiedTime(LocalDateTime.now());
        return updateById(rider);
    }

    /**
     * 初始化话盐和密码
     * @param rider
     */
    private void initSaltAndPassword(BicycleRider rider) {
        if (StringUtils.isEmpty(rider.getPassword())) {
            return;
        }

        rider.setSalt(StringUtils.randStr(appConfig.getSaltLength()));
        rider.setPassword(Md5Utils.encode(rider.getPassword(), rider.getSalt(), appConfig.getHashIterations()));
    }

    /**
     * 通过手机号查询骑手
     * @param contact
     * @param isPwdRequired 是否返回密码和盐
     * @return
     */
    public BicycleRider findRiderByContact(String contact, boolean isPwdRequired) {
        if (StringUtils.isBlank(contact)) {
            return null;
        }
        LambdaQueryWrapper<BicycleRider> wrapper = new QueryWrapper<BicycleRider>().lambda()
                .eq(BicycleRider::getContact, contact);
        if (!isPwdRequired) {
            excludePasswordAndSalt(wrapper);
        }
        return getOne(wrapper);
    }

    /**
     * 骑手情况
     * @return
     */
    public BicycleDayCountRiderVo dayCountRider() {
        return getBaseMapper().dayCountRider();
    }

    /**
     * 排除密码和盐
     * @param wrapper
     */
    public void excludePasswordAndSalt(LambdaQueryWrapper<BicycleRider> wrapper) {
        wrapper.select(BicycleRider.class,
                rider -> !rider.getColumn().equals("password") && !rider.getColumn().equals("salt"));
    }

    /**
     * 根据手机号和密码查找骑手
     * @param contact
     * @param password
     * @return
     */
    public BicycleRider findByContactAndPassword(String contact, String password) {
        if (StringUtils.isEmpty(contact) || StringUtils.isEmpty(password)) {
            return null;
        }

        BicycleRider rider = findRiderByContact(contact, true);
        if (rider == null) {
            return null;
        }
        String passwordHashed = Md5Utils.encode(password, rider.getSalt(), appConfig.getHashIterations());
        if (!Objects.equals(rider.getPassword(), passwordHashed)) {
            return null;
        }
        rider.setPassword(null);
        rider.setSalt(null);
        return rider;
    }

    /**
     * 骑手信息列表
     * @return
     */
    public List<BicycleRiderInfoVo> listRider() {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = LocalDateTime.of(endTime.toLocalDate(), LocalTime.MIN);
        List<BicycleRiderInfoVo> riderInfoVos = getBaseMapper().listRiderInfo(startTime, endTime);
        //排序 根据骑手忙碌状态 和 骑手单量
        riderInfoVos = riderInfoVos.stream()
                .sorted(Comparator.comparingInt(BicycleRiderInfoVo::getRiderStatus))
                .collect(Collectors.toList());
        return sortRiderByRiderName(riderInfoVos);
    }

    /**
     * 骑手编号排序
     * @param riderInfoVos
     * @return
     */
    private List<BicycleRiderInfoVo> sortRiderByRiderName(List<BicycleRiderInfoVo> riderInfoVos) {
        Pattern pattern = Pattern.compile("(\\d+)[\\u4e00-\\u9fa5]");
        riderInfoVos.forEach(rider -> {
            Matcher matcher = pattern.matcher(rider.getRiderName());
            if (matcher.find()) {
                rider.setRiderNoPrefix(Integer.parseInt(matcher.group(1)));
            } else {
                rider.setRiderNoPrefix(0);
            }
        });
        return riderInfoVos.stream().sorted(Comparator.comparingInt(BicycleRiderInfoVo::getRiderNoPrefix)).collect(Collectors.toList());
    }

    /**
     * 骑手编号初始化
     * @param rider
     */
    private void prepareRiderNo(BicycleRider rider) {
        // get serial from cache
        String cacheKey = String.format(TmsConst.Cache.KEY_BICYCLE_RIDER_NO, appConfig.getRiderNoPrefix());
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, TmsConst.Cache.TTL_2D);

        // compare serial from db and recorrect serial from cache
        Long dbSerial = findMaxRiderNo(appConfig.getRiderNoPrefix());
        if (result <= dbSerial) {
            result = ++dbSerial;
            RedisUtils.set(cacheKey, result, TmsConst.Cache.TTL_2D);
        }

        String riderNo = appConfig.getRiderNoPrefix().concat(StringUtils.fixLength(String.valueOf(result), appConfig.getRiderNoPostfixLength(), appConfig.getFillChar()));
        log.info("骑手编号初始化完成...{}", riderNo);
        rider.setRiderNo(riderNo);
    }

    /**
     * 获取最大骑手编号
     *
     * @param prefix
     * @return
     */
    private Long findMaxRiderNo(String prefix) {
        String maxRiderNo = getBaseMapper().findMaxRiderNo(prefix);
        return (StringUtils.isBlank(maxRiderNo) ? 0L : NumberUtils.parseLong(maxRiderNo.substring(prefix.length()), 0L));
    }

    /**
     * 通过手机号判断骑手是否存在
     * @param rider
     * @return
     */
    private boolean isBicycleRiderExists(BicycleRider rider) {
        BicycleRider dbRider = findRiderByContact(rider.getContact(), false);
        return dbRider != null && !Objects.equals(rider.getId(), dbRider.getId());
    }
}

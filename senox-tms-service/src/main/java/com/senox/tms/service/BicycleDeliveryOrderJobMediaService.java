package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.domain.BicycleDeliveryOrderJobMedia;
import com.senox.tms.mapper.BicycleDeliveryOrderJobMediaMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/20 10:02
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BicycleDeliveryOrderJobMediaService extends ServiceImpl<BicycleDeliveryOrderJobMediaMapper, BicycleDeliveryOrderJobMedia> {

    /**
     * 添加配送单任务多媒体信息
     * @param jobId
     * @param mediaUrls
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBicycleDeliveryOrderJobMedia(Long jobId, List<String> mediaUrls) {
        log.info("【配送单任务媒体资源比较替换。。。】");
        List<BicycleDeliveryOrderJobMedia> mediaList = prepareListMedia(jobId, mediaUrls);

        DataSepDto<BicycleDeliveryOrderJobMedia> sepData = compareAndSeparateMedia(listByJobId(jobId), mediaList);

        if (!CollectionUtils.isEmpty(sepData.getAddList())) {
            saveBatch(sepData.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepData.getRemoveList())) {
            removeByIds(sepData.getRemoveList().stream().map(BicycleDeliveryOrderJobMedia::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 配送单任务多媒体信息初始化
     * @param jobId
     * @param mediaUrls
     * @return
     */
    private List<BicycleDeliveryOrderJobMedia> prepareListMedia(Long jobId, List<String> mediaUrls) {
        if (CollectionUtils.isEmpty(mediaUrls)) {
            return Collections.emptyList();
        }
        List<BicycleDeliveryOrderJobMedia> resultList = new ArrayList<>(mediaUrls.size());
        for (String item : mediaUrls) {
            resultList.add(new BicycleDeliveryOrderJobMedia(jobId, item));
        }
        return resultList;
    }

    /**
     * 根据派工id获取媒体信息
     * @param jobId
     * @return
     */
    public List<BicycleDeliveryOrderJobMedia> listByJobId(Long jobId) {
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<BicycleDeliveryOrderJobMedia> queryWrapper = new QueryWrapper<BicycleDeliveryOrderJobMedia>().lambda()
                .eq(BicycleDeliveryOrderJobMedia::getJobId, jobId);
        return list(queryWrapper);
    }

    /**
     * 比较区分多媒体信息
     *
     * @param srcList
     * @param targetList
     * @return
     */
    private DataSepDto<BicycleDeliveryOrderJobMedia> compareAndSeparateMedia(List<BicycleDeliveryOrderJobMedia> srcList, List<BicycleDeliveryOrderJobMedia> targetList) {
        List<BicycleDeliveryOrderJobMedia> addList = null;
        List<BicycleDeliveryOrderJobMedia> removeList = null;
        if (CollectionUtils.isEmpty(srcList)) {
            addList = targetList;

        } else if (CollectionUtils.isEmpty(targetList)) {
            removeList = srcList;

        } else {
            addList = targetList.stream().filter(x -> !srcList.contains(x)).collect(Collectors.toList());
            removeList = srcList.stream().filter(x -> !targetList.contains(x)).collect(Collectors.toList());
        }

        DataSepDto<BicycleDeliveryOrderJobMedia> result = new DataSepDto<>();
        result.setAddList(addList);
        result.setRemoveList(removeList);
        return result;
    }

    /**
     * 根据派工id删除任务媒体
     * @param jobId
     */
    public void deleteBicycleDeliveryOrderJobMedia(Long jobId) {
        if (!WrapperClassUtils.biggerThanLong(jobId, 0L)) {
            throw new InvalidParameterException();
        }
        remove(new QueryWrapper<BicycleDeliveryOrderJobMedia>().
                lambda().eq(BicycleDeliveryOrderJobMedia::getJobId, jobId));
    }
}

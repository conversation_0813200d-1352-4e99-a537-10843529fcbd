package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.StringUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.tms.domain.BicycleSetting;
import com.senox.tms.mapper.BicycleSettingMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/25 9:38
 */
@Service
@Slf4j
public class BicycleSettingService extends ServiceImpl<BicycleSettingMapper, BicycleSetting> {

    /**
     * 添加三轮车配置信息
     * @param bicycleSetting
     * @return
     */
    public Long addBicycleSetting(BicycleSetting bicycleSetting) {
        bicycleSetting.setCreateTime(LocalDateTime.now());
        bicycleSetting.setModifiedTime(LocalDateTime.now());
        boolean save = save(bicycleSetting);
        return save ? bicycleSetting.getId() : 0L;
    }

    /**
     * 更新三轮车配置信息
     * @param bicycleSetting
     */
    public void updateBicycleSetting(BicycleSetting bicycleSetting) {
        if (!WrapperClassUtils.biggerThanLong(bicycleSetting.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        bicycleSetting.setModifiedTime(LocalDateTime.now());
        updateById(bicycleSetting);
    }

    /**
     * 根据id获取三轮车配置信息
     * @param id
     * @return
     */
    public BicycleSetting findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据id删除三轮车配置信息
     * @param id
     */
    public void deleteById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        removeById(id);
    }

    /**
     * 三轮车配置信息列表
     * @return
     */
    public List<BicycleSetting> listBicycleSetting() {
        return list(new QueryWrapper<BicycleSetting>().lambda()
                .eq(BicycleSetting::getDisabled, Boolean.FALSE));
    }

    /**
     * 根据别名获取三轮车配置信息
     * @param alias
     * @return
     */
    public BicycleSetting findByAlias(String alias) {
        if (StringUtils.isBlank(alias)) {
            return null;
        }
        return getOne(new QueryWrapper<BicycleSetting>().lambda()
                .eq(BicycleSetting::getAlias, alias));
    }

    /**
     * 检查是否有效
     * @param name
     * @return
     */
    public boolean checkEnable(String name) {
        BicycleSetting setting = findByAlias(name);
        if (setting == null) {
            return false;
        }
        return setting.getEnable();
    }
}

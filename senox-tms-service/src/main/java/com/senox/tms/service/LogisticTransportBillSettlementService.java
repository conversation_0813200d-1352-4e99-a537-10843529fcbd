package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.pm.constant.PayWay;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import com.senox.tms.domain.LogisticTransportBill;
import com.senox.tms.domain.LogisticTransportBillSettlement;
import com.senox.tms.event.LogisticTransportBillNotifySendEvent;
import com.senox.tms.mapper.LogisticTransportBillSettlementMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025-06-09
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticTransportBillSettlementService extends ServiceImpl<LogisticTransportBillSettlementMapper, LogisticTransportBillSettlement> {
    private final LogisticTransportBillService billService;
    private final ApplicationEventPublisher publisher;

    /**
     * 批量添加
     *
     * @param billSettlements 结算单集
     */
    public void addBatch(List<LogisticTransportBillSettlement> billSettlements) {
        if (CollectionUtils.isEmpty(billSettlements)) {
            return;
        }
        billSettlements.forEach(x -> {
            if (null == x.getSend()) {
                x.setSend(false);
            }
            x.setStatus(false);
            ContextUtils.initEntityCreator(x);
            ContextUtils.initEntityModifier(x);
            x.setCreateTime(LocalDateTime.now());
            x.setModifiedTime(LocalDateTime.now());
        });
        baseMapper.addBatch(billSettlements);
    }

    /**
     * 批量更新
     *
     * @param billSettlements 结算单集
     */
    public void updateBatch(List<LogisticTransportBillSettlement> billSettlements) {
        if (CollectionUtils.isEmpty(billSettlements)) {
            return;
        }
        billSettlements.forEach(x -> {
            ContextUtils.initEntityModifier(x);
            x.setModifiedTime(LocalDateTime.now());
        });
        baseMapper.updateBatch(billSettlements);
    }

    /**
     * 批量删除
     *
     * @param ids id集
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<LogisticTransportBillSettlementVo> lists = listByIds(ids);
        if (!CollectionUtils.isEmpty(lists) && lists.stream().anyMatch(LogisticTransportBillSettlementVo::getStatus)) {
            throw new BusinessException("结算单已支付，禁止删除");
        }
        removeByIds(ids);
        //删除账单
        billService.deleteBySettlementIds(ids);
    }


    /**
     * 列表统计查询
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    public int countList(LogisticTransportBillSettlementSearchVo search) {
        return baseMapper.countList(search);
    }

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    public List<LogisticTransportBillSettlementVo> list(LogisticTransportBillSettlementSearchVo search) {
        return baseMapper.list(search);
    }

    /**
     * 获取下发结算单集
     *
     * @param send 查询参数
     * @return 结算单集
     */
    public List<LogisticTransportBillSettlementVo> getSendList(LogisticTransportBillSettlementSendVo send) {
        LogisticTransportBillSettlementSearchVo search = new LogisticTransportBillSettlementSearchVo();
        search.setPage(false);
        search.setSend(!send.getSend());
        search.setStartDate(send.getStartDate());
        search.setEndDate(send.getEndDate());
        search.setIds(send.getSettlementIds());
        search.setMerchantIds(send.getMerchantIds());
        return list(search);
    }

    /**
     * 列表分页查询
     *
     * @param search 查询参数
     * @return 返回分页后的列表
     */
    public PageResult<LogisticTransportBillSettlementVo> pageList(LogisticTransportBillSettlementSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return PageUtils.commonPageResult(search, () -> countList(search), () -> list(search));
    }

    /**
     * 根据id获取结算单
     *
     * @param ids id集
     * @return 返回结算单集
     */
    public List<LogisticTransportBillSettlementVo> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LogisticTransportBillSettlementSearchVo search = new LogisticTransportBillSettlementSearchVo();
        search.setPage(false);
        search.setIds(ids);
        return list(search);

    }

    /**
     * 结算单统计
     *
     * @param search 查询参数
     * @return 返回结算单统计
     */
    public LogisticTransportBillSettlementStatisticsVo statistics(LogisticTransportBillSettlementSearchVo search) {
        return baseMapper.statistics(search);
    }

    /**
     * 生成账单结算
     *
     * @param bill      账单
     * @param send      是否下发
     * @param tollManId 收费员id
     * @param payer     付款人
     * @return 结算单id
     */
    public Long generateBillSettlement(LogisticTransportBill bill, Boolean send, Long tollManId, LogisticTransportOrderPayer payer) {
        return generateBillSettlement(Collections.singletonList(bill), send, tollManId, payer).stream().findFirst().orElse(null);
    }

    /**
     * 生成账单结算
     *
     * @param bills     账单集
     * @param send      是否下发
     * @param tollManId 收费员id
     * @param payer     付款人
     * @return 结算单id
     */
    public List<Long> generateBillSettlement(List<LogisticTransportBill> bills, Boolean send, Long tollManId, LogisticTransportOrderPayer payer) {
        if (CollectionUtils.isEmpty(bills)) {
            return Collections.emptyList();
        }
        Map<Long, List<LogisticTransportBill>> billMap = bills.stream().collect(Collectors.groupingBy(LogisticTransportBill::getMerchantId));
        List<LogisticTransportBillSettlement> addBillSettlements = new ArrayList<>(billMap.size());
        List<LogisticTransportBillSettlement> updateBillSettlements = new ArrayList<>(billMap.size());
        List<LogisticTransportBill> updateBills = new ArrayList<>(bills.size());
        billMap.forEach((merchantId, billList) -> {
            Map<Long, List<LogisticTransportBill>> billSettlementMap = billList.stream().filter(x -> WrapperClassUtils.biggerThanLong(x.getSettlementId(), 0)).collect(Collectors.groupingBy(LogisticTransportBill::getSettlementId));
            if (!CollectionUtils.isEmpty(billSettlementMap)) {
                billSettlementMap.forEach((settlementId, settlementBills) -> {
                    LogisticTransportBillSettlement updateBillSettlement = new LogisticTransportBillSettlement();
                    updateBillSettlement.setId(settlementId);
                    updateBillSettlement.setAmount(settlementBills.stream().map(LogisticTransportBill::getReceivableFreightCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
                    updateBillSettlement.setPaidAmount(BigDecimal.ZERO);
                    updateBillSettlement.setPaidStillAmount(updateBillSettlement.getAmount());
                    updateBillSettlements.add(updateBillSettlement);
                });
            }
            billList = billList.stream().filter(x -> !WrapperClassUtils.biggerThanLong(x.getSettlementId(), 0)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(billList)) {
                return;
            }
            LogisticTransportBillSettlement billSettlement = new LogisticTransportBillSettlement();
            billSettlement.setBillDate(billList.stream().map(LogisticTransportBill::getBillDate).max(Comparator.naturalOrder()).orElse(LocalDate.now()));
            billSettlement.setBillYear(billSettlement.getBillDate().getYear());
            billSettlement.setBillMonth(billSettlement.getBillDate().getMonthValue());
            billSettlement.setBillYearMonth(StringUtils.buildYearMonthStr(billSettlement.getBillYear(), billSettlement.getBillMonth()));
            billSettlement.setMerchantId(merchantId);
            billSettlement.setAmount(billList.stream().map(LogisticTransportBill::getReceivableFreightCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
            billSettlement.setPaidAmount(BigDecimal.ZERO);
            billSettlement.setPaidStillAmount(billSettlement.getAmount());
            billSettlement.setPayWay(PayWay.UNDEFINED.getValue());
            billSettlement.setPayer(payer.getNumber());
            if (BooleanUtils.isTrue(send)) {
                billSettlement.setSend(true);
                billSettlement.setSendTime(LocalDateTime.now());
            }
            billSettlement.setTollManId(tollManId);
            addBatch(Collections.singletonList(billSettlement));
            addBillSettlements.add(billSettlement);
            billList.forEach(x -> {
                LogisticTransportBill updateBill = new LogisticTransportBill();
                updateBill.setId(x.getId());
                updateBill.setSettlementId(billSettlement.getId());
                updateBills.add(updateBill);
            });
        });
        //回更账单结算id
        billService.updateBatch(updateBills);
        log.info("【Logistic transport】生成结算单 {}", JsonUtils.object2Json(addBillSettlements));
        updateBatch(updateBillSettlements);
        return Stream.concat(addBillSettlements.stream().map(LogisticTransportBillSettlement::getId), updateBillSettlements.stream().map(LogisticTransportBillSettlement::getId)).collect(Collectors.toList());
    }

    /**
     * 更新账单支付状态
     *
     * @param billPaid 账单支付信息
     */
    public void updatePaidById(BillPaidVo billPaid) {
        baseMapper.updateBillPaidById(billPaid);
    }

    /**
     * 更新账单支付状态
     *
     * @param billPaid 账单支付信息
     */
    public void updatePaidByOrderId(BillPaidVo billPaid) {
        baseMapper.updateBillPaidByOrderId(billPaid);
    }

    /**
     * 下发
     *
     * @param send 下发参数
     */
    public void send(LogisticTransportBillSettlementSendVo send) {
        if ((null == send.getStartDate() || null == send.getEndDate())
                && CollectionUtils.isEmpty(send.getSettlementIds())
                && CollectionUtils.isEmpty(send.getMerchantIds())
        ) {
            throw new InvalidParameterException();
        }
        List<LogisticTransportBillSettlementVo> list = getSendList(send);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        send.setSettlementIds(list.stream().map(LogisticTransportBillSettlementVo::getId).collect(Collectors.toList()));
        baseMapper.send(send);
        if (BooleanUtils.isTrue(send.getSend())) {
            sendNotify(list);
        }

    }

    /**
     * 结算单详情
     *
     * @param settlementId 结算单id
     * @return 返回结算单详情
     */
    public LogisticTransportBillSettlementDetailVo billSettlementDetail(Long settlementId) {
        if (!WrapperClassUtils.biggerThanLong(settlementId, 0)) {
            return null;
        }
        LogisticTransportBillSettlementDetailVo settlementDetail = billService.statisticBySettlement(settlementId);
        settlementDetail.setBills(billService.billListBySettlementId(settlementId));
        return settlementDetail;
    }

    /**
     * 根据id查找结算单
     *
     * @param settlementId 结算单id
     * @return 返回查找到的结算单
     */
    public LogisticTransportBillSettlementVo billSettlementFindById(Long settlementId) {
        LogisticTransportBillSettlementSearchVo search = new LogisticTransportBillSettlementSearchVo();
        search.setPage(false);
        search.setIds(Collections.singletonList(settlementId));
        List<LogisticTransportBillSettlementVo> list = list(search);
        return CollectionUtils.isEmpty(list) ? null : list(search).get(0);
    }

    /**
     * 结算单更新支付订单
     *
     * @param billPaid 账单支付信息
     */
    public void updatePaidOrder(BillPaidVo billPaid) {
        baseMapper.updatePaidOrder(billPaid);
    }

    /**
     * 下发通知
     *
     * @param list 数据集
     */
    private void sendNotify(List<LogisticTransportBillSettlementVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        publisher.publishEvent(new LogisticTransportBillNotifySendEvent(this, list));
    }
}

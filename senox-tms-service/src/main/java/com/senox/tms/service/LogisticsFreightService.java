package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.BillStatus;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.tms.domain.LogisticsFreight;
import com.senox.tms.mapper.LogisticsFreightMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticFreightRemarkVo;
import com.senox.tms.vo.LogisticsFreightSearchVo;
import com.senox.tms.vo.LogisticsFreightStatisticsVo;
import com.senox.tms.vo.LogisticsFreightVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-6
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class LogisticsFreightService extends ServiceImpl<LogisticsFreightMapper, LogisticsFreight> {

    /**
     * 添加
     *
     * @param freight 货运
     */
    public void add(LogisticsFreight freight) {
       addBatch(Collections.singletonList(freight));
    }

    /**
     * 批量添加
     *
     * @param freights 货运列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatch(List<LogisticsFreight> freights) {
        if (CollectionUtils.isEmpty(freights)){
            return;
        }
        freights.forEach(f->{
            ContextUtils.initEntityCreator(f);
            ContextUtils.initEntityModifier(f);
            f.setCreateTime(LocalDateTime.now());
            f.setModifiedTime(LocalDateTime.now());
        });
        saveBatch(freights);
    }

    /**
     * 更新
     *
     * @param freight 货运
     */
    public void update(LogisticsFreight freight) {
        if (!WrapperClassUtils.biggerThanLong(freight.getId(), 0)) {
            throw new InvalidParameterException();
        }
        LogisticsFreightVo freightVo = findById(freight.getId());
        if (freightVo.getStatus() == BillStatus.PAID.getValue()) {
            throw new BusinessException("已缴费的账单不能更新！");
        }
        ContextUtils.initEntityModifier(freight);
        freight.setModifiedTime(LocalDateTime.now());
        updateById(freight);
    }

    /**
     * 根据id删除
     *
     * @param id id
     */
    public void deleteById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new InvalidParameterException();
        }
        LogisticsFreightVo freightVo = findById(id);
        if (freightVo.getStatus() == BillStatus.PAID.getValue()) {
            throw new BusinessException("已缴费的账单不能删除！");
        }
        removeById(id);
    }

    /**
     * 列表
     *
     * @param searchVo 货运查询
     * @return 返回列表
     */
    public List<LogisticsFreightVo> list(LogisticsFreightSearchVo searchVo) {
        return baseMapper.list(searchVo);
    }

    /**
     * 根据id集合查询
     * @param ids
     * @return
     */
    public List<LogisticsFreight> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<LogisticsFreight>().lambda().in(LogisticsFreight::getId, ids));
    }

    /**
     * 列表统计
     *
     * @param searchVo 货运查询
     * @return 返回统计
     */
    public int countList(LogisticsFreightSearchVo searchVo) {
        return baseMapper.countList(searchVo);
    }

    /**
     * 列表分页查询
     *
     * @param searchVo 货运查询
     * @return 返回分页后的列表
     */
    public PageStatisticsResult<LogisticsFreightVo, LogisticsFreightStatisticsVo> listPage(LogisticsFreightSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return new PageStatisticsResult<>();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        PageResult<LogisticsFreightVo> pageResult = PageUtils.commonPageResult(searchVo, () -> countList(searchVo), () -> list(searchVo));
        LogisticsFreightStatisticsVo statistics = baseMapper.listTotalAmount(searchVo);
        return new PageStatisticsResult<>(pageResult,statistics);
    }

    /**
     * 根据id查找
     * @param id id
     * @return 返回查找到的
     */
    public LogisticsFreightVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id,0)) {
            throw new InvalidParameterException();
        }
        return baseMapper.findById(id);
    }

    /**
     * 更新账单状态
     * @param billPaid
     */
    public void updateBillStatus(BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            return;
        }

        // 更新订单支付结果
        if (!CollectionUtils.isEmpty(billPaid.getBillIds())) {
            log.info("根据账单id更新账单状态 {}", JsonUtils.object2Json(billPaid));
            updateBillPaidById(billPaid);

        } else {
            log.info("根据支付订单id更新账单状态 {}", JsonUtils.object2Json(billPaid));
            updateBillPaidByRemoteOrder(billPaid);
        }
    }

    /**
     * 根据账单id更新账单状态
     * @param billPaid
     * @return
     */
    private int updateBillPaidById(BillPaidVo billPaid) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds())) {
            return 0;
        }

        return getBaseMapper().updateBillPaidById(billPaid);
    }

    /**
     * 根据支付订单id更新账单状态
     * @param billPaid
     * @return
     */
    private int updateBillPaidByRemoteOrder(BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            return 0;
        }

        return getBaseMapper().updateBillPaidByRemoteOrder(billPaid);
    }

    /**
     * 更新账单备注
     * @param remarkVo
     */
    public void updateBillRemark(LogisticFreightRemarkVo remarkVo) {
        getBaseMapper().updateBillRemark(remarkVo);
    }
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.SeparateUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.DataSepDto;
import com.senox.tms.domain.UnloadingOrderGoods;
import com.senox.tms.mapper.UnloadingOrderGoodsMapper;
import com.senox.tms.utils.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/14 16:17
 */
@Service
@Slf4j
public class UnloadingOrderGoodsService extends ServiceImpl<UnloadingOrderGoodsMapper, UnloadingOrderGoods> {


    /**
     * 保存订单货物明细
     * @param orderId
     * @param goods
     */
    public void saveGoods(Long orderId, List<UnloadingOrderGoods> goods) {
        log.info("【装卸订单id】----- {}， 货物明细:{}", orderId, JsonUtils.object2Json(goods));
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            throw new BusinessException("装卸订单id有误");
        }
        goods.forEach(good -> {
            good.setOrderId(orderId);
            good.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityModifier(good);
        });
        DataSepDto<UnloadingOrderGoods> sepDto = SeparateUtils.separateData(listByOrderId(orderId), goods);
        log.info("转换后的sepDto值为 ---- {}", JsonUtils.object2Json(sepDto));
        if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
            sepDto.getAddList().forEach(good -> {
                good.setCreateTime(LocalDateTime.now());
                ContextUtils.initEntityCreator(good);
            });
            saveBatch(sepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getUpdateList())) {
            updateBatchById(sepDto.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getRemoveList())) {
            removeByIds(sepDto.getRemoveList().stream().map(UnloadingOrderGoods::getId).collect(Collectors.toList()));
        }
    }


    /**
     * 根据订单id查询明细
     * @param orderId
     * @return
     */
    public List<UnloadingOrderGoods> listByOrderId(Long orderId) {
        if (!WrapperClassUtils.biggerThanLong(orderId, 0L)) {
            return Collections.emptyList();
        }
        return listByOrderIds(Collections.singletonList(orderId));
    }

    /**
     * 根据订单id集合查询明细
     * @param orderIds
     * @return
     */
    public List<UnloadingOrderGoods> listByOrderIds(List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<UnloadingOrderGoods>().in(UnloadingOrderGoods::getOrderId, orderIds));
    }
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.tms.component.MerchantComponent;
import com.senox.tms.config.AppConfig;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.constant.BicycleOrderGoodsType;
import com.senox.tms.constant.BicycleOrderState;
import com.senox.tms.constant.BicycleOrderStatus;
import com.senox.tms.constant.BicycleSettingType;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.convert.BicycleDeliveryDetailItemConvert;
import com.senox.tms.convert.BicycleDeliveryOrderConvert;
import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.domain.BicycleDeliveryOrderDetail;
import com.senox.tms.domain.BicycleDeliveryOrderDetailItem;
import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.domain.BicycleOrderGoodsDetail;
import com.senox.tms.domain.BicyclePayoff;
import com.senox.tms.domain.BicycleRider;
import com.senox.tms.domain.BicycleSetting;
import com.senox.tms.dto.BicycleDeliveryOrderBuild;
import com.senox.tms.event.BicycleCancelBillPayoffEvent;
import com.senox.tms.event.BicycleDeliveryMessageEvent;
import com.senox.tms.event.BicycleGenerateBillPayoffEvent;
import com.senox.tms.event.BicycleOperateAnalysisEvent;
import com.senox.tms.event.BicycleOrderDayReportGenerateEvent;
import com.senox.tms.event.BicycleOrderWebSocketEvent;
import com.senox.tms.event.CancelMergedEvent;
import com.senox.tms.mapper.BicycleDeliveryOrderMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.utils.RabbitMqSenderUtil;
import com.senox.tms.vo.*;
import com.senox.user.vo.MerchantVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/20 8:52
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BicycleDeliveryOrderService extends ServiceImpl<BicycleDeliveryOrderMapper, BicycleDeliveryOrder> {

    private final AppConfig appConfig;
    private final BicycleDeliveryOrderDetailService bicycleDeliveryOrderDetailService;
    private final BicycleBillService bicycleBillService;
    private final BicyclePayoffService bicyclePayoffService;
    private final RabbitTemplate rabbitTemplate;
    private final BicycleRiderService bicycleRiderService;
    private final ApplicationEventPublisher publisher;
    private final BicycleSettingService bicycleSettingService;
    private final BicycleDeliveryOrderConvert deliveryOrderConvert;
    private final BicycleOrderService bicycleOrderService;
    private final BicycleOrderGoodsDetailService goodsDetailService;
    private final BicycleDeliveryOrderDetailItemService deliveryOrderDetailItemService;
    private final MerchantComponent merchantComponent;
    private final BicycleSharesService sharesService;
    private final BicycleDeliveryDetailItemConvert detailItemConvert;


    /**
     * 添加配送单
     * @param bicycleDeliveryOrder
     * @param detailVos
     * @param buildBuilder
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addBicycleDeliveryOrder(BicycleDeliveryOrder bicycleDeliveryOrder, List<BicycleDeliveryOrderDetailVo> detailVos, BicycleDeliveryOrderBuild buildBuilder) {
        List<String> orderSerialList = detailVos.stream().map(BicycleDeliveryOrderDetailVo::getOrderSerialNo).collect(Collectors.toList());
        if (!WrapperClassUtils.biggerThanLong(bicycleDeliveryOrder.getId(), 0L)) {
            //如果没有传id，判断是否重复点击
            List<BicycleDeliveryOrderDetail> deliveryOrderDetails = bicycleDeliveryOrderDetailService.findByOrderSerialNoList(orderSerialList);
            if (!CollectionUtils.isEmpty(deliveryOrderDetails)) {
                throw new BusinessException("订单已经配送，不要重复点击！");
            }
        }

        List<BicycleOrder> orderList = bicycleOrderService.findBySerialNoList(orderSerialList);
        orderList.stream().filter(order -> order.getState() == BicycleOrderState.DRAFT.getNumber()).findFirst()
                .ifPresent(order -> {
                    throw new BusinessException("订单是草稿状态不能分配，请刷新查看最新订单情况，单号为：" + order.getOrderSerialNo());
                });

        //构建配送单信息
        prepareBicycleDeliveryOrder(bicycleDeliveryOrder);

        //构建配送单信息详细
        prepareBicycleDeliveryOrderDetail(orderList, detailVos);

        //是否推荐分配
        bicycleDeliveryOrder.setReferralDelivery(buildBuilder.isReferralDelivery());
        boolean save = saveOrUpdate(bicycleDeliveryOrder);
        //添加配送单详细
        List<String> serialList = bicycleDeliveryOrderDetailService.addBicycleDeliveryOrderDetail(bicycleDeliveryOrder.getDeliveryOrderSerialNo(), detailVos, BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber());

        //更新订单信息
        prepareBicycleOrder(orderList, buildBuilder.getGoodsDetailMap(), bicycleDeliveryOrder);

        //生成应收和应付账单 收益明细
        publisher.publishEvent(new BicycleGenerateBillPayoffEvent(this, serialList));
        //发送消息
        publisher.publishEvent(new BicycleDeliveryMessageEvent(this, bicycleDeliveryOrder, detailVos.stream().map(BicycleDeliveryOrderDetailVo::getRiderId).collect(Collectors.toList())));
        //发送websocket
        publisher.publishEvent(new BicycleOrderWebSocketEvent(publisher));
        return save ? bicycleDeliveryOrder.getId() : 0L;
    }

    /**
     * 构建货物
     * @param detailVos
     * @return
     */
    public Map<String, List<List<BicycleOrderGoodsDetail>>> buildOrderGoodsDetail(List<BicycleDeliveryOrderDetailVo> detailVos) {
        Map<String, List<List<BicycleOrderGoodsDetail>>> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(detailVos)) {
            Map<String, List<List<BicycleDeliveryOrderDetailItemVo>>> collect = detailVos.stream()
                    .collect(Collectors.groupingBy(BicycleDeliveryOrderDetailVo::getOrderSerialNo,
                            Collectors.mapping(BicycleDeliveryOrderDetailVo::getDetailItemVos, Collectors.toList())));

            for (Map.Entry<String, List<List<BicycleDeliveryOrderDetailItemVo>>> entry : collect.entrySet()) {
                List<List<BicycleOrderGoodsDetail>> goodsDetailList = new ArrayList<>();
                for (List<BicycleDeliveryOrderDetailItemVo> itemVos : entry.getValue()) {
                    List<BicycleOrderGoodsDetail> goodsList = detailItemConvert.toGoodsList(itemVos);
                    goodsDetailList.add(goodsList);
                }
                map.put(entry.getKey(), goodsDetailList);
            }
        }
        return map;
    }

    /**
     * 保存配送费单
     * @param orderVo
     * @param rider
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveDeliveryOrder(BicycleOrderVo orderVo, BicycleRider rider) {
        BicycleDeliveryOrder deliveryOrder = new BicycleDeliveryOrder();
        deliveryOrder.setStatus(BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber());
        ContextUtils.initEntityCreator(deliveryOrder);
        ContextUtils.initEntityModifier(deliveryOrder);

        List<BicycleDeliveryOrderDetailVo> detailVos = buildDetailVo(orderVo, rider);
        Map<String, List<List<BicycleOrderGoodsDetail>>> goodsDetailMap = buildOrderGoodsDetail(detailVos);
        BicycleDeliveryOrderBuild buildBuilder = BicycleDeliveryOrderBuild.builder().goodsDetailMap(goodsDetailMap).referralDelivery(true).build();
        addBicycleDeliveryOrder(deliveryOrder, detailVos, buildBuilder);
    }

    private List<BicycleDeliveryOrderDetailVo> buildDetailVo(BicycleOrderVo orderVo, BicycleRider rider) {
        BicycleDeliveryOrderDetailVo detailVo = new BicycleDeliveryOrderDetailVo();
        detailVo.setRiderId(rider.getId());
        detailVo.setChargesId(orderVo.getChargesId());
        detailVo.setStatus(BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber());
        detailVo.setOrderSerialNo(orderVo.getOrderSerialNo());
        detailVo.setPickedPieces(orderVo.getPieces());
        List<BicycleDeliveryOrderDetailItemVo> orderDetailItemVos = buildDetailItem(orderVo);
        detailVo.setDetailItemVos(orderDetailItemVos);
        return Collections.singletonList(detailVo);
    }

    private List<BicycleDeliveryOrderDetailItemVo> buildDetailItem(BicycleOrderVo orderVo) {
        List<BicycleDeliveryOrderDetailItemVo> orderDetailItemVos = new ArrayList<>();
        for (BicycleOrderGoodsDetailVo goodsDetailVo : orderVo.getOrderGoodsDetailVos()) {
            BicycleDeliveryOrderDetailItemVo itemVo = new BicycleDeliveryOrderDetailItemVo();
            itemVo.setGoodsId(goodsDetailVo.getId());
            itemVo.setGoodsName(goodsDetailVo.getGoodsName());
            itemVo.setGoodsType(goodsDetailVo.getGoodsType());
            itemVo.setPieces(goodsDetailVo.getPieces());
            itemVo.setSize(goodsDetailVo.getSize());
            itemVo.setWeight(goodsDetailVo.getWeight());
            itemVo.setOrderSerialNo(orderVo.getOrderSerialNo());
            orderDetailItemVos.add(itemVo);
        }
        return orderDetailItemVos;
    }

    /**
     * 更新部分配送商品
     * @param goodsDetails
     * @param deliveryOrderSerialNoItem
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePartBicycleOrderDetail(List<BicycleOrderGoodsDetail> goodsDetails, String deliveryOrderSerialNoItem) {
        Long orderId = goodsDetails.stream().map(BicycleOrderGoodsDetail::getOrderId).distinct().findFirst().orElse(0L);
        BicycleOrder order = bicycleOrderService.findById(orderId);
        log.info("【更新部分配送商品】修改订单信息为：{}", JsonUtils.object2Json(order));
        List<BicycleDeliveryOrderDetailItem> orderDetailItems = deliveryOrderDetailItemService.listByOrderSerialNo(order.getOrderSerialNo());
        //获取改单其他骑手配送的商品
        List<BicycleDeliveryOrderDetailItem> filterOrderDetailItems = orderDetailItems.stream().filter(x -> !Objects.equals(deliveryOrderSerialNoItem, x.getDeliveryOrderSerialNoItem())).collect(Collectors.toList());
        //该骑手配送的货物和其他骑手配送的货物
        Map<Long, List<BicycleDeliveryOrderDetailItem>> deliveryGoodsDetailMap = filterOrderDetailItems.stream().collect(Collectors.groupingBy(BicycleDeliveryOrderDetailItem::getGoodsId));
        Map<Long, List<BicycleOrderGoodsDetail>> goodsDetailMap = goodsDetails.stream().collect(Collectors.groupingBy(BicycleOrderGoodsDetail::getId));
        //更新的商品
        List<BicycleOrderGoodsDetail> updateGoodDetailList = buildOrderGoodsDetail(orderId, deliveryGoodsDetailMap, goodsDetailMap);
        List<BicycleOrderGoodsDetail> orderGoodsDetails =  updateGoodDetailList.stream()
                //根据订单id，豁免名字，货物类型，体积，重量一致的
                .collect(Collectors.groupingBy(g -> Arrays.asList(g.getOrderId(), g.getGoodsName(), g.getGoodsType(), g.getSize(), g.getWeight())))
                .values().stream()
                //件数相加
                .map(group -> group.stream().reduce((g1, g2) ->
                        new BicycleOrderGoodsDetail(g1.getOrderId(), g1.getGoodsName(), g1.getGoodsType(), DecimalUtils.add(g1.getPieces(), g2.getPieces()), g1.getWeight(), g1.getSize())))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        log.info("构建完毕需要更新的订单商品：{}", JsonUtils.object2Json(orderGoodsDetails));
        //更新商品详细,返回新增的商品id
        goodsDetailService.addBicycleOrderGoodsDetail(orderId, orderGoodsDetails);
        //更新配送子详细商品
        List<BicycleOrderGoodsDetail> addGoodsDetailList = goodsDetailService.orderGoodsDetailByOrderId(orderId);
        //需要更新的配送商品
        List<BicycleDeliveryOrderDetailItem> updateDeliveryOrderDetailItemList = buildDeliveryOrderDetailItem(deliveryOrderSerialNoItem, orderDetailItems.get(0), goodsDetailMap, addGoodsDetailList);
        log.info("构建完毕的骑手商品：{}", JsonUtils.object2Json(updateDeliveryOrderDetailItemList));

        List<BicycleDeliveryOrderDetailItem> detailItemList = deliveryOrderDetailItemService.listByOrderSerialNo(order.getOrderSerialNo());

        //其他骑手配送的商品合并
        List<BicycleDeliveryOrderDetailItem> deliveryGoodsDetailList = detailItemList.stream().filter(x -> !Objects.equals(x.getDeliveryOrderSerialNoItem(), deliveryOrderSerialNoItem)).collect(Collectors.toList());
        updateDeliveryOrderDetailItemList.addAll(deliveryGoodsDetailList);
        log.info("构建完毕需要更新的配送商品：{}", JsonUtils.object2Json(updateDeliveryOrderDetailItemList));

        //保存替换配送子详细
        deliveryOrderDetailItemService.batchSave(order.getOrderSerialNo(), updateDeliveryOrderDetailItemList);
        //重新计算金额
        List<BicycleDeliveryOrderDetailItem> orderDetailItemList = deliveryOrderDetailItemService.listByOrderSerialNo(order.getOrderSerialNo());
        List<BicycleDeliveryOrderDetailItem> ownDeliveryDetailList = orderDetailItemList.stream().filter(x -> Objects.equals(x.getDeliveryOrderSerialNoItem(), deliveryOrderSerialNoItem)).collect(Collectors.toList());
        //更新配送单配送件数
        BicycleDeliveryOrderDetail orderDetail = bicycleDeliveryOrderDetailService.orderDetailByItemVo(deliveryOrderSerialNoItem);
        orderDetail.setPickedPieces(ownDeliveryDetailList.stream().map(BicycleDeliveryOrderDetailItem::getPieces).reduce(BigDecimal.ZERO, BigDecimal::add));
        bicycleDeliveryOrderDetailService.updateById(orderDetail);
        List<List<BicycleDeliveryOrderDetailItem>> calculateList = new ArrayList<>(orderDetailItemList.stream().collect(Collectors.groupingBy(BicycleDeliveryOrderDetailItem::getDeliveryOrderSerialNoItem)).values());
        bicycleOrderService.calculateActualCharge(order, calculateList);
        ContextUtils.initEntityModifier(order);
        order.setModifiedTime(LocalDateTime.now());
        order.setPieces(orderDetailItemList.stream().map(BicycleDeliveryOrderDetailItem::getPieces).reduce(BigDecimal.ZERO, BigDecimal::add));
        //更新订单费用
        bicycleOrderService.updateById(order);
        //重新生成应收应付
        generateBillAndPayoff(Collections.singletonList(order.getOrderSerialNo()), LocalDateTime.now());
    }

    /**
     * 构建新增的配送子商品
     * @param deliveryOrderSerialNoItem
     * @param deliveryOrderDetailItem
     * @param goodsDetailMap 骑手本人配送的商品
     * @param addGoodsDetailList 新增的商品
     * @return
     */
    private List<BicycleDeliveryOrderDetailItem> buildDeliveryOrderDetailItem(String deliveryOrderSerialNoItem, BicycleDeliveryOrderDetailItem deliveryOrderDetailItem, Map<Long, List<BicycleOrderGoodsDetail>> goodsDetailMap, List<BicycleOrderGoodsDetail> addGoodsDetailList) {
        List<BicycleDeliveryOrderDetailItem> detailItemList = new ArrayList<>();
        //新配送的单
        if (!CollectionUtils.isEmpty(goodsDetailMap.get(0L))) {
            //新增的商品构建
            detailItemList.addAll(buildDeliveryOrderDetailItem(deliveryOrderSerialNoItem, deliveryOrderDetailItem, goodsDetailMap.get(0L), addGoodsDetailList));
        }
        detailItemList.forEach(detailItem -> {
            detailItem.setCreateTime(LocalDateTime.now());
            detailItem.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityCreator(detailItem);
            ContextUtils.initEntityModifier(detailItem);
        });
        return detailItemList;
    }

    /**
     * 构建新增的配送子商品
     * @param deliveryOrderSerialNoItem
     * @param deliveryOrderDetailItem
     * @param goodsDetailList
     * @param addGoodsDetailList 新增的商品
     * @return
     */
    private List<BicycleDeliveryOrderDetailItem> buildDeliveryOrderDetailItem(String deliveryOrderSerialNoItem, BicycleDeliveryOrderDetailItem deliveryOrderDetailItem, List<BicycleOrderGoodsDetail> goodsDetailList, List<BicycleOrderGoodsDetail> addGoodsDetailList) {
        List<BicycleDeliveryOrderDetailItem> detailItemList = new ArrayList<>();
        for (BicycleOrderGoodsDetail detail : goodsDetailList) {
            BicycleOrderGoodsDetail goodsDetail = addGoodsDetailList.stream().filter(x -> checkExists(x, detail)).findFirst().orElse(null);
            if (goodsDetail == null) {
                continue;
            }
            BicycleDeliveryOrderDetailItem detailItem = new BicycleDeliveryOrderDetailItem();
            detailItem.setOrderSerialNo(deliveryOrderDetailItem.getOrderSerialNo());
            detailItem.setDeliveryOrderSerialNo(deliveryOrderDetailItem.getDeliveryOrderSerialNo());
            detailItem.setDeliveryOrderSerialNoItem(deliveryOrderSerialNoItem);
            detailItem.setGoodsId(goodsDetail.getId());
            detailItem.setGoodsName(goodsDetail.getGoodsName());
            detailItem.setGoodsType(goodsDetail.getGoodsType());
            detailItem.setPieces(detail.getPieces());
            detailItem.setWeight(goodsDetail.getWeight());
            detailItem.setSize(goodsDetail.getSize());
            detailItemList.add(detailItem);
        }
        return detailItemList;
    }

    private boolean checkExists(BicycleOrderGoodsDetail detail1, BicycleOrderGoodsDetail detail2) {
        return Objects.equals(detail1.getGoodsName(), detail2.getGoodsName()) && Objects.equals(detail1.getGoodsType(), detail2.getGoodsType())
                && Objects.equals(detail1.getWeight(), detail2.getWeight()) && Objects.equals(detail1.getSize(), detail2.getSize());
    }

    /**
     * 构建订单商品
     * @param deliveryGoodsDetailMap 其他骑手配送的货物
     * @param goodsDetailMap 当前骑手配送的货物
     * @return
     */
    private List<BicycleOrderGoodsDetail> buildOrderGoodsDetail(Long orderId, Map<Long, List<BicycleDeliveryOrderDetailItem>> deliveryGoodsDetailMap, Map<Long, List<BicycleOrderGoodsDetail>> goodsDetailMap) {
        List<BicycleOrderGoodsDetail> goodsDetailList = new ArrayList<>();
        //其他骑手有配送一样的单
        for (Map.Entry<Long, List<BicycleDeliveryOrderDetailItem>> entry : deliveryGoodsDetailMap.entrySet()) {
            BicycleDeliveryOrderDetailItem detailItem = entry.getValue().get(0);
            BigDecimal pieces = entry.getValue().stream().map(BicycleDeliveryOrderDetailItem::getPieces).reduce(BigDecimal.ZERO, BigDecimal::add);
            BicycleOrderGoodsDetail goodsDetail = new BicycleOrderGoodsDetail(orderId, detailItem.getGoodsName(), detailItem.getGoodsType(), pieces, detailItem.getWeight(), detailItem.getSize());
            goodsDetailList.add(goodsDetail);
        }
        //新加的单
        if (!CollectionUtils.isEmpty(goodsDetailMap.get(0L))) {
            goodsDetailList.addAll(goodsDetailMap.get(0L));
        }
        //不是其他骑手的单，骑手本人自己的单
        List<Long> goodIds = goodsDetailList.stream().map(BicycleOrderGoodsDetail::getId).collect(Collectors.toList());
        Map<Long, List<BicycleOrderGoodsDetail>> ownMap = goodsDetailMap.entrySet().stream().filter(key -> !goodIds.contains(key.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (!CollectionUtils.isEmpty(ownMap)) {
            goodsDetailList.addAll(ownMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));
        }
        return goodsDetailList;
    }

    /**
     * 更新货物明细
     * @param goodsDetailsMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBicycleOrderDetail(Map<Long, List<BicycleOrderGoodsDetail>> goodsDetailsMap) {
        for (Map.Entry<Long, List<BicycleOrderGoodsDetail>> entry : goodsDetailsMap.entrySet()) {
            BicycleOrder order = bicycleOrderService.findById(entry.getKey());
            if (order == null) {
                throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "订单不存在");
            }
            //更新货物信息
            goodsDetailService.addBicycleOrderGoodsDetail(entry.getKey(), entry.getValue());
            List<BicycleDeliveryOrderDetail> orderDetailList = bicycleDeliveryOrderDetailService.findByOrderSerialNo(order.getOrderSerialNo());
            if (CollectionUtils.isEmpty(orderDetailList)) {
                log.info("未分配订单，直接预估费用");
                bicycleOrderService.calculateCharge(order, entry.getValue());
            } else {
                throw new BusinessException("已分配的订单不允许改商品信息，请取消骑手配送再改商品信息！");
                //更新实际费用
                //calculateActualCharge(entry, orderDetailList, order);
            }
            ContextUtils.initEntityModifier(order);
            order.setPieces(entry.getValue().stream().map(BicycleOrderGoodsDetail::getPieces).reduce(BigDecimal.ZERO, BigDecimal::add));
            order.setModifiedTime(LocalDateTime.now());
            //更新订单费用
            bicycleOrderService.updateById(order);
            //重新生成应收应付
            generateBillAndPayoff(Collections.singletonList(order.getOrderSerialNo()), LocalDateTime.now());
        }
    }

    /**
     * 更新实际费用
     * @param entry
     * @param orderDetailList
     * @param order
     */
    private void calculateActualCharge(Map.Entry<Long, List<BicycleOrderGoodsDetail>> entry, List<BicycleDeliveryOrderDetail> orderDetailList, BicycleOrder order) {
        log.info("已分配订单，分配了 {} 个骑手", orderDetailList.size());
        List<BicycleOrderGoodsDetail> details = goodsDetailService.orderGoodsDetailByOrderId(entry.getKey());
        List<BicycleDeliveryOrderDetailItem> orderDetailItems = deliveryOrderDetailItemService.listByGoodsIds(details.stream().map(BicycleOrderGoodsDetail::getId).distinct().collect(Collectors.toList()));
        for (BicycleDeliveryOrderDetailItem detailItem : orderDetailItems) {
            detailItem.setGoodsType(details.stream().filter(k -> Objects.equals(k.getId(), detailItem.getGoodsId())).findFirst().orElseThrow(() -> new BusinessException("货物类型错误")).getGoodsType());
            detailItem.setSize(details.stream().filter(k -> Objects.equals(k.getId(), detailItem.getGoodsId())).findFirst().orElseThrow(() -> new BusinessException("体积错误")).getSize());
            detailItem.setWeight(details.stream().filter(k -> Objects.equals(k.getId(), detailItem.getGoodsId())).findFirst().orElseThrow(() -> new BusinessException("重量错误")).getWeight());
        }
        log.info("【更新配送子明细】，参数为:{}", JsonUtils.object2Json(orderDetailItems));
        deliveryOrderDetailItemService.updateBatch(orderDetailItems);
        List<List<BicycleDeliveryOrderDetailItem>> detailItemList = new ArrayList<>(orderDetailItems.stream().collect(Collectors.groupingBy(BicycleDeliveryOrderDetailItem::getDeliveryOrderSerialNoItem)).values());
        log.info("【件数相加前】， 结果为:{} ", JsonUtils.object2Json(detailItemList));
        //要把类型、重量、体积相匹配的件数相加
        List<List<BicycleDeliveryOrderDetailItem>> orderDetailItemList = new ArrayList<>(detailItemList.size());
        //其他货物件数合计
        BicycleDeliveryOrderDetailItem otherOrderDetail = new BicycleDeliveryOrderDetailItem();
        otherOrderDetail.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        for (List<BicycleDeliveryOrderDetailItem> detail : detailItemList) {
            //其他类型的件数合计
            otherOrderDetail.setPieces(DecimalUtils.add(otherOrderDetail.getPieces()
                    , detail.stream().filter(g -> Objects.equals(g.getGoodsType(), BicycleOrderGoodsType.OTHER.getNumber()))
                            .map(BicycleDeliveryOrderDetailItem::getPieces).reduce(BigDecimal.ZERO, BigDecimal::add)));
            //所有非其他货物类型
            List<BicycleDeliveryOrderDetailItem> orderGoodsDetails =  detail.stream()
                    //排除其他货物类型
                    .filter(g -> !Objects.equals(g.getGoodsType(), BicycleOrderGoodsType.OTHER.getNumber()))
                    //根据货物类型，体积，重量一致的
                    .collect(Collectors.groupingBy(g -> Arrays.asList(g.getGoodsType(), g.getSize(), g.getWeight())))
                    .values().stream()
                    //件数相加
                    .map(group -> group.stream().reduce((g1, g2) ->
                            new BicycleDeliveryOrderDetailItem(g1.getGoodsType(), g1.getWeight(), g1.getSize(), DecimalUtils.add(g1.getPieces(), g2.getPieces()))))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            orderDetailItemList.add(orderGoodsDetails);
        }
        if (DecimalUtils.isPositive(otherOrderDetail.getPieces())) {
            //把其他类型的件数加上去
            orderDetailItemList.add(Collections.singletonList(otherOrderDetail));
        }
        //过滤空列表
        orderDetailItemList = orderDetailItemList.stream().filter(list -> !list.isEmpty()).collect(Collectors.toList());
        log.info("【件数相加后】， 结果为:{} ", JsonUtils.object2Json(orderDetailItemList));
        //计算配送后的金额
        bicycleOrderService.calculateActualCharge(order, orderDetailItemList);
    }

    /**
     * 构建配送单信息详细
     * @param orderList
     * @param detailVos
     * @return
     */
    private void prepareBicycleDeliveryOrderDetail(List<BicycleOrder> orderList, List<BicycleDeliveryOrderDetailVo> detailVos) {
        Map<String, BicycleOrder> orderMap = orderList.stream().collect(Collectors.toMap(BicycleOrder::getOrderSerialNo, o -> o, (exist, replace) -> exist));
        Map<String, List<BicycleDeliveryOrderDetailVo>> detailMap = detailVos.stream().collect(Collectors.groupingBy(BicycleDeliveryOrderDetailVo::getOrderSerialNo));
        detailMap.forEach((key, value) -> {
            BicycleOrder order = orderMap.get(key);
            if (order == null) {
                log.info("订单不存在，订单号为:{}", key);
                throw new BusinessException("订单号不存在，订单号为：" + key);
            }
            log.info("【校验件数】 order pieces :{}, picked pieces {}", order.getPieces(), JsonUtils.object2Json(value));
            if (!DecimalUtils.equals(order.getPieces(), value.stream().map(BicycleDeliveryOrderDetailVo::getPickedPieces).reduce(BigDecimal.ZERO, BigDecimal::add))) {
                throw new BusinessException("件数与订单的不一致");
            }
        });
    }

    /**
     * 更新订单信息
     * @param orderList
     * @param goodsDetailMap
     * @param bicycleDeliveryOrder
     */
    private void prepareBicycleOrder(List<BicycleOrder> orderList, Map<String, List<List<BicycleOrderGoodsDetail>>> goodsDetailMap, BicycleDeliveryOrder bicycleDeliveryOrder) {
        orderList.forEach(order -> {
            calculateActualCharge(goodsDetailMap, order);
            if (StringUtils.isBlank(order.getDeliveryOrderSerialNo())) {
                order.setDeliveryOrderSerialNo(bicycleDeliveryOrder.getDeliveryOrderSerialNo());
            }
        });//计算实际费用
        bicycleOrderService.updateBatchOrder(orderList);
        log.info("订单费用更新完成");
    }

    /**
     * 计算实际费用
     * @param goodsDetailMap
     * @param order
     */
    private void calculateActualCharge(Map<String, List<List<BicycleOrderGoodsDetail>>> goodsDetailMap, BicycleOrder order) {
        List<List<BicycleOrderGoodsDetail>> goodsDetails = goodsDetailMap.get(order.getOrderSerialNo());
        log.info("【件数相加前】， 结果为:{} ", JsonUtils.object2Json(goodsDetails));
        //要把类型、重量、体积相匹配的件数相加
        List<List<BicycleOrderGoodsDetail>> goodsDetailList = bicycleOrderService.mergeGoodsDetail(goodsDetails);
        //过滤空列表
        goodsDetailList = goodsDetailList.stream().filter(list -> !list.isEmpty()).collect(Collectors.toList());
        log.info("【件数相加后】， 结果为:{} ", JsonUtils.object2Json(goodsDetailList));
        //计算金额
        bicycleOrderService.calculateActualCharge(order, goodsDetailList);
    }

    /**
     * 构建配送单信息
     * @param bicycleDeliveryOrder
     */
    private void prepareBicycleDeliveryOrder(BicycleDeliveryOrder bicycleDeliveryOrder) {
        BicycleDeliveryOrder order = null;
        if (WrapperClassUtils.biggerThanLong(bicycleDeliveryOrder.getId(), 0L)) {
            //更新操作，不用再次生成流水号
            order = findById(bicycleDeliveryOrder.getId());
        }
        bicycleDeliveryOrder.setModifiedTime(LocalDateTime.now());
        if (order != null) {
            bicycleDeliveryOrder.setDeliveryOrderSerialNo(order.getDeliveryOrderSerialNo());
        } else {
            log.info("配送流水号为空，生成配送流水号");
            bicycleDeliveryOrder.setCreateTime(LocalDateTime.now());
            //如果配送单不存在则生成配送单流水号
            prepareDeliveryOrderSerialNo(bicycleDeliveryOrder);
        }
    }

    /**
     * 根据id获取配送单
     * @param id
     * @return
     */
    public BicycleDeliveryOrder findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 更新配送单及应收应付
     * @param order 配送订单
     */
    private void updateOrder(BicycleOrder order) {
        ContextUtils.initEntityModifier(order);
        order.setModifiedTime(LocalDateTime.now());
        bicycleOrderService.updateById(order);
        List<BicycleDeliveryOrderDetail> orderDetailList = bicycleDeliveryOrderDetailService.findByOrderSerialNo(order.getOrderSerialNo());
        if (!CollectionUtils.isEmpty(orderDetailList)
                && orderDetailList.stream().allMatch(x -> x.getStatus() >= BicycleDeliveryOrderStatus.SEND_COMPLETED.getNumber())) {
            throw new BusinessException("该订单已经配送完毕，不支持更改费用！");
        }
        //查询是否有应付需要更新
        BicycleBillVo billVo = bicycleBillService.findByOrderSerialNo(order.getOrderSerialNo());
        //更新应收应付
        if (billVo != null) {
            generateBillAndPayoff(Collections.singletonList(order.getOrderSerialNo()), LocalDateTime.now());
        }
    }

    /**
     * 取消骑手配送
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelRiderDeliveryOrderById(BicycleDeliveryOrder order) {
        BicycleDeliveryOrder deliveryOrder = findById(order.getId());
        if (deliveryOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "配送订单未找到！");
        }
        List<BicycleDeliveryOrderDetail> orderDetails = bicycleDeliveryOrderDetailService.listOrderDetailByDeliveryOrderSerialNo(deliveryOrder.getDeliveryOrderSerialNo());
        if (!CollectionUtils.isEmpty(orderDetails)
                && orderDetails.stream().anyMatch(x -> x.getStatus() > BicycleDeliveryOrderStatus.ARRIVING_PICKUP_POINT.getNumber())) {
            throw new BusinessException("该配送单配送，不能取消骑手!");
        }
        order.setDeliveryOrderSerialNo(deliveryOrder.getDeliveryOrderSerialNo());
        //删除配送单
        removeById(order.getId());
        List<BicycleOrder> orderList = bicycleOrderService.findByDeliveryOrderSerialNo(deliveryOrder.getDeliveryOrderSerialNo());
        //订单取消配送单号
        bicycleOrderService.cancelDeliveryOrderSerialNo(orderList);
        //取消应收和应付账单 收益明细
        publisher.publishEvent(new BicycleCancelBillPayoffEvent(this, orderDetails, Collections.emptyList(), order));
    }

    /**
     * 添加费用
     * @param chargeVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBicycleOrderCharge(BicycleOrderChargeVo chargeVo) {
        BicycleOrder order = bicycleOrderService.findById(chargeVo.getId());
        if (order == null || order.getState() == BicycleOrderState.DRAFT.getNumber()) {
            throw new BusinessException( "配送订单不存在或者是草稿状态");
        }
        prepareCharge(order, chargeVo);
        //更新配送单及应收应付
        updateOrder(order);
    }

    private void prepareCharge(BicycleOrder order, BicycleOrderChargeVo chargeVo) {
        if (!StringUtils.isBlank(chargeVo.getOtherRemark())) {
            order.setOtherRemark(chargeVo.getOtherRemark());
        }
        order.setHandlingCharge(chargeVo.getHandlingCharge());
        order.setUpstairsCharge(chargeVo.getUpstairsCharge());
        order.setOtherCharge(chargeVo.getOtherCharge());
        order.setTotalCharge(DecimalUtils.add(order.getDeliveryCharge(), order.getHandlingCharge(), order.getUpstairsCharge(), order.getOtherCharge()));
    }

    /**
     * 根据id获取配送单及详细
     * @param id
     * @return
     */
    public BicycleDeliveryOrderVo findDeliveryOrderById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getBaseMapper().findDeliveryOrderById(id);
    }

    /**
     * 根据配送单流水号查询配送单
     * @param deliveryOrderSerialNo
     * @return
     */
    public BicycleDeliveryOrder findBySerialNo(String deliveryOrderSerialNo) {
        if (StringUtils.isBlank(deliveryOrderSerialNo)) {
            return null;
        }
        LambdaQueryWrapper<BicycleDeliveryOrder> wrapper = new QueryWrapper<BicycleDeliveryOrder>().lambda();
        wrapper.eq(BicycleDeliveryOrder::getDeliveryOrderSerialNo, deliveryOrderSerialNo);
        return getOne(wrapper);
    }

    /**
     * 更新配送单状态
     * @param order
     */
    public void updateDeliveryOrderStatus(BicycleDeliveryOrder order) {
        if (BicycleDeliveryOrderStatus.CONFIRM_COMPLETION == BicycleDeliveryOrderStatus.fromStatus(order.getStatus())) {
            //设置完成时间
            order.setFinishTime(LocalDateTime.now());
        }
        order.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(order);
        updateById(order);
    }

    /**
     * 指定骑手配送（弃用）
     * @param id
     * @param riderId
     * @param chargesId
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public void appointBicycleDeliveryOrderReider(Long id, Long riderId, Long chargesId) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)
                || !WrapperClassUtils.biggerThanLong(riderId, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleDeliveryOrder order = findById(id);
        if (order == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }

        order.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(order);
        order.setStatus(BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber());
        updateById(order);

        //配送详细单改为未揽货
        List<BicycleDeliveryOrderDetail> orderDetails = bicycleDeliveryOrderDetailService.listOrderDetailByDeliveryOrderSerialNo(order.getDeliveryOrderSerialNo());
        orderDetails.forEach(detail -> {
            //没有分配骑手的指定骑手
            if (detail.getRiderId() == 0L) {
                detail.setRiderId(riderId);
            }
        });
        bicycleDeliveryOrderDetailService.updateStatus(orderDetails, BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber());
        List<BicycleOrder> orderList = bicycleOrderService.findBySerialNoList(orderDetails.stream().map(BicycleDeliveryOrderDetail::getOrderSerialNo).collect(Collectors.toList()));
        //更新订单信息
        //prepareBicycleOrder(chargesId, orderList);
        //发送消息
        sendMessage(order, Collections.singletonList(riderId));
        publisher.publishEvent(new BicycleOrderWebSocketEvent(publisher));
    }

    /**
     * 取消骑手配送
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelRiderDeliveryOrderById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleDeliveryOrder order = findById(id);
        if (order == null) {
            log.info("配送单不存在");
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        List<BicycleDeliveryOrderDetail> detailList = bicycleDeliveryOrderDetailService.listOrderDetailByDeliveryOrderSerialNo(order.getDeliveryOrderSerialNo());
        detailList.forEach(detail -> {
            if (detail.getStatus() != BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber()) {
                log.info("配送订单已取货，订单信息为：{}", JsonUtils.object2Json(detail));
                throw new BusinessException("配送订单已取货，订单号为：" + detail.getOrderSerialNo());
            }
        });
        List<Long> riderIdList = detailList.stream().map(BicycleDeliveryOrderDetail::getRiderId).filter(riderId -> riderId > 0).collect(Collectors.toList());
        order.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(order);
        order.setStatus(BicycleDeliveryOrderStatus.UN_ASSIGN.getNumber());
        updateById(order);

        detailList.forEach(orderDetail -> {
            orderDetail.setRiderId(0L);
            //删除应收账单 已支付的就不能取消合并
            bicycleBillService.deleteBicycleBillByOrderSerialNo(orderDetail.getOrderSerialNo());
            //删除应付账单
            bicyclePayoffService.deleteBicyclePayoffByOrderSerialNo(orderDetail.getOrderSerialNo());
        });
        //配送详细单改为未揽货
        bicycleDeliveryOrderDetailService.updateStatus(detailList, BicycleDeliveryOrderStatus.UN_ASSIGN.getNumber());

        //如果取消的不为合并单或者都为拆分单，则删除配送单，可重新分派
        if (!CollectionUtils.isEmpty(detailList)
                && (detailList.size() == 1 || detailList.stream().map(BicycleDeliveryOrderDetail::getOrderSerialNo).distinct().count() == 1)) {
            bicycleDeliveryOrderDetailService.deleteDeliveryOrderDetailByOrderSerialNo(detailList.get(0).getOrderSerialNo());
            deleteDeliveryOrderByDeliveryOrderSerialNo(detailList.get(0).getDeliveryOrderSerialNo());
        }

        //发送消息
        sendMessage(order.getDeliveryOrderSerialNo(), riderIdList);
        publisher.publishEvent(new BicycleOrderWebSocketEvent(this));
    }

    /**
     * 配送单流水号初始化
     * @param bicycleDeliveryOrder
     */
    private void prepareDeliveryOrderSerialNo(BicycleDeliveryOrder bicycleDeliveryOrder) {
        String prefix = appConfig.getDeliveryOrderSerialNoPrefix().concat(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        // get serial from cache
        String cacheKey = String.format(TmsConst.Cache.KEY_BICYCLE_DELIVERY_ORDER_SERIAL, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, TmsConst.Cache.TTL_2D);

        // compare serial from db and recorrect serial from cache
        Long dbSerial = findMaxDeliveryOrderSerial(prefix);
        if (result <= dbSerial) {
            result = ++dbSerial;
            RedisUtils.set(cacheKey, result, TmsConst.Cache.TTL_2D);
        }
        String orderSerial = prefix.concat(StringUtils.fixLength(String.valueOf(result), appConfig.getOrderNoPostfixLength(), appConfig.getFillChar()));
        log.info("配送单流水号号初始化完成...{}", orderSerial);
        bicycleDeliveryOrder.setDeliveryOrderSerialNo(orderSerial);
    }

    /**
     * 获取最大配送单序号
     *
     * @param prefix
     * @return
     */
    private Long findMaxDeliveryOrderSerial(String prefix) {
        String maxOrderNo = getBaseMapper().findMaxDeliveryOrderNo(prefix);
        return (StringUtils.isBlank(maxOrderNo) ? 0L : NumberUtils.parseLong(maxOrderNo.substring(prefix.length()), 0L));
    }

    /**
     * 生成应收应付
     * @param orderSerialList
     */
    public void generateBillAndPayoff(List<String> orderSerialList, LocalDateTime orderTime) {
        log.info("【重新生成应收应付】，订单号为：{}", JsonUtils.object2Json(orderSerialList));
        for (String orderSerial : orderSerialList) {
            List<BicycleDeliveryOrderDetailVo> detailVos = bicycleDeliveryOrderDetailService.findDeliveryOrderDetailByOrderSerialNo(orderSerial);
            log.info("【配送单详细内容】 {}", JsonUtils.object2Json(detailVos));
            if (!CollectionUtils.isEmpty(detailVos)) {
                BicycleDeliveryOrderDetailVo detailVo = detailVos.get(0);
                bicycleBillService.addBicycleDeliveryBill(detailVo, orderTime);
                bicyclePayoffService.addBicyclePayoff(detailVos, orderTime);
                int countOrder = bicycleOrderService.countOrder(detailVo.getSenderId());
                BicycleSetting setting = bicycleSettingService.findByAlias(BicycleSettingType.ORDER_COMPLETE_REFERRAL_FEE.getAlias());
                if (setting != null && BooleanUtils.isFalse(setting.getEnable()) && countOrder == 1) {
                    //商户第一次下单，骑手推荐分佣
                    MerchantVo merchantVo = merchantComponent.findById(detailVo.getSenderId());
                    BicyclePayoff payoff = bicyclePayoffService.buildPayoff(orderTime, detailVo.getOrderSerialNo(), detailVo.getDeliveryOrderSerialNo(), merchantVo.getReferralCode(), BigDecimal.ZERO, sharesService.getCurrentEffectiveShares().getReferralAmount(), BigDecimal.ZERO, true);
                    if (payoff != null) {
                        bicyclePayoffService.save(payoff);
                    }
                }
            }
        }
    }

    /**
     * 合并配送单
     * @param orderSerialNoList
     * @param deliveryOrderSerialNo
     */
    @Transactional
    public void merged(List<String> orderSerialNoList, String deliveryOrderSerialNo) {
        List<BicycleDeliveryOrderDetail> detailList = bicycleDeliveryOrderDetailService.orderDetailByOrderSerialNo(orderSerialNoList);
        if (!CollectionUtils.isEmpty(detailList)) {
            List<String> list = detailList.stream().map(BicycleDeliveryOrderDetail::getOrderSerialNo).collect(Collectors.toList());
            //如果有配送单已经开始送了就不能操作合并
            if (detailList.stream().anyMatch(x -> x.getRiderId() > 0)) {
                throw new BusinessException("订单已分配骑手，不能合并订单！");
            }
            // 对集合进行排序
            Collections.sort(orderSerialNoList);
            Collections.sort(list);
            //如果合并的订单编号相同，则无需重复合并
            if (list.equals(orderSerialNoList)) {
                throw new BusinessException("订单已经合并，无需重复合并！");
            }
        }
        BicycleDeliveryOrder deliveryOrder = buildDeliveryOrder(deliveryOrderSerialNo);
        deliveryOrder.setStatus(BicycleDeliveryOrderStatus.UN_ASSIGN.getNumber());
        boolean save = saveOrUpdate(deliveryOrder);
        if (save) {
            List<BicycleDeliveryOrderDetailVo> orderDetails = bicycleDeliveryOrderDetailService.buildDeliveryOrderDetailList(orderSerialNoList);
            bicycleDeliveryOrderDetailService.addBicycleDeliveryOrderDetail(deliveryOrder.getDeliveryOrderSerialNo(), orderDetails, BicycleDeliveryOrderStatus.UN_ASSIGN.getNumber());
            List<BicycleOrder> orderList = bicycleOrderService.findBySerialNoList(orderSerialNoList);
            orderList.forEach(order -> order.setDeliveryOrderSerialNo(deliveryOrder.getDeliveryOrderSerialNo()));
            bicycleOrderService.updateBatchOrder(orderList);
            publisher.publishEvent(new BicycleOrderWebSocketEvent(publisher));
        }
    }

    /**
     * 构建配送单
     * @param deliveryOrderSerialNo
     * @return
     */
    private BicycleDeliveryOrder buildDeliveryOrder(String deliveryOrderSerialNo) {
        BicycleDeliveryOrder order = new BicycleDeliveryOrder();
        ContextUtils.initEntityModifier(order);
        ContextUtils.initEntityCreator(order);
        order.setMerged(true);
        order.setCreateTime(LocalDateTime.now());
        order.setModifiedTime(LocalDateTime.now());
        if (!StringUtils.isBlank(deliveryOrderSerialNo)) {
            BicycleDeliveryOrder deliveryOrder = findBySerialNo(deliveryOrderSerialNo);
            if (deliveryOrder == null) {
                throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "合并的配送单不存在");
            }
            order.setDeliveryOrderSerialNo(deliveryOrderSerialNo);
            order.setId(deliveryOrder.getId());
        } else {
            prepareDeliveryOrderSerialNo(order);
        }
        return order;
    }

    /**
     * 取消合并
     * @param orderSerialNoList
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelMerged(List<String> orderSerialNoList) {
        List<BicycleDeliveryOrderDetail> orderDetails = bicycleDeliveryOrderDetailService.orderDetailByOrderSerialNo(orderSerialNoList);
        if (CollectionUtils.isEmpty(orderDetails)) {
            return;
        }
        //只能取消未分派和未揽货的
        orderDetails = orderDetails.stream()
                .filter(detail -> detail.getStatus() == BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber()
                        || detail.getStatus() == BicycleDeliveryOrderStatus.UN_ASSIGN.getNumber())
                .collect(Collectors.toList());
        log.info("取消合并的订单为: {}", JsonUtils.object2Json(orderDetails));
        orderDetails.forEach(this::cancelMerged);
        publisher.publishEvent(new BicycleOrderWebSocketEvent(publisher));
    }

    /**
     * 取消合并 (删除配送单、收益明细、应收应付)
     * @param orderDetail
     */
    private void cancelMerged(BicycleDeliveryOrderDetail orderDetail) {
        List<BicycleDeliveryOrderDetail> orderDetailList = bicycleDeliveryOrderDetailService.findByOrderSerialNo(orderDetail.getOrderSerialNo());
        if (orderDetailList.stream().anyMatch(detail -> detail.getStatus() > BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber())) {
            log.info("订单已揽货，不能取消合并！订单号为：{}", JsonUtils.object2Json(orderDetail.getOrderSerialNo()));
            throw new BusinessException("订单已揽货，不能取消合并！订单号为：" + orderDetail.getOrderSerialNo());
        }
        //删除应收账单 已支付的就不能取消合并
        bicycleBillService.deleteBicycleBillByOrderSerialNo(orderDetail.getOrderSerialNo());
        //删除应付账单
        bicyclePayoffService.deleteBicyclePayoffByOrderSerialNo(orderDetail.getOrderSerialNo());
        //删除配送单
        bicycleDeliveryOrderDetailService.deleteDeliveryOrderDetailByOrderSerialNo(orderDetail.getOrderSerialNo());
        BicycleOrder order = bicycleOrderService.findBySerialNo(orderDetail.getOrderSerialNo());
        order.setDeliveryOrderSerialNo(StringUtils.EMPTY);
        bicycleOrderService.updateBatchOrder(Collections.singletonList(order));
        List<BicycleDeliveryOrderDetail> orderDetails = bicycleDeliveryOrderDetailService.listOrderDetailByDeliveryOrderSerialNo(orderDetail.getDeliveryOrderSerialNo());
        //如果撤销之后只有一个单就把配送单改为未分派状态
        if (!CollectionUtils.isEmpty(orderDetails) && orderDetails.size() == 1) {
            cancelMerged(orderDetails.get(0));
            return;
        }
        //如果删除后配送单详细为空则后续删除配送单
        if (CollectionUtils.isEmpty(orderDetails)) {
            deleteDeliveryOrderByDeliveryOrderSerialNo(orderDetail.getDeliveryOrderSerialNo());
        }
    }

    /**
     * 根据配送单流水号删除配送单
     * @param deliveryOrderSerialNo
     */
    public void deleteDeliveryOrderByDeliveryOrderSerialNo(String deliveryOrderSerialNo) {
        if (StringUtils.isBlank(deliveryOrderSerialNo)) {
            throw new InvalidParameterException();
        }
        LambdaQueryWrapper<BicycleDeliveryOrder> wrapper = new QueryWrapper<BicycleDeliveryOrder>().lambda();
        wrapper.eq(BicycleDeliveryOrder::getDeliveryOrderSerialNo, deliveryOrderSerialNo);
        remove(wrapper);
    }

    /**
     * 发送消息
     * @param deliveryOrderSerialNo
     * @param riderIdList
     */
    private void sendMessage(String deliveryOrderSerialNo, List<Long> riderIdList) {
        if (CollectionUtils.isEmpty(riderIdList)) {
            return;
        }
        riderIdList.forEach(riderId -> RabbitMqSenderUtil.sendCancelDeliveryOrderMessage(deliveryOrderSerialNo, riderId, rabbitTemplate));
    }

    /**
     * 发送消息
     * @param deliveryOrder
     * @param riderIdList
     */
    public void sendMessage(BicycleDeliveryOrder deliveryOrder, List<Long> riderIdList) {
        for (Long riderId : riderIdList) {
            BicycleRider rider = bicycleRiderService.findById(riderId);
            if (rider == null) {
                throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "骑手不存在");
            }
            //发送分配骑手消息
            RabbitMqSenderUtil.sendDeliveryRiderMessage(deliveryOrder, rider, rabbitTemplate);

            //获取超时未揽货的设置
            BicycleSetting bicycleSetting = bicycleSettingService.findByAlias(BicycleSettingType.UNPICKED.getAlias());
            if (bicycleSetting != null && bicycleSetting.getEnable()) {
                //发送骑手未揽货延时消息
                BicycleRiderUnpickedMessageVo messageVo = new BicycleRiderUnpickedMessageVo();
                messageVo.setRiderId(rider.getId());
                messageVo.setStartTime(LocalDateTime.now());
                messageVo.setDeliveryOrderSerialNo(deliveryOrder.getDeliveryOrderSerialNo());
                messageVo.setRiderName(rider.getName());
                messageVo.setStatusDescription(BicycleDeliveryOrderStatus.UNPICKED_GOODS.getName());
                int unpickedSecond = bicycleSetting.getIntervalMinute() * 60;
                log.info("发送骑手未揽货延时消息------，延时时间:{}, 参数为:{}", unpickedSecond, JsonUtils.object2Json(messageVo));
                RabbitMqSenderUtil.sendUnpickedMessage(unpickedSecond, messageVo, rabbitTemplate);
            }
        }
    }

    /**
     * 批量删除未生成结算单的订单
     * @param ids
     * @param status
     * @param statusRemark
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelBicycleOrderByIds(List<Long> ids, Integer status, String statusRemark) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("id集合不能为空");
        }
        ids.forEach(id -> cancelBicycleOrderById(id, status, statusRemark));
    }

    /**
     * 删除未生成结算单的订单
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelBicycleOrderById(Long id, Integer status, String statusRemark) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleOrder order = bicycleOrderService.findById(id);
        if (order == null) {
            log.info("{} 三轮车订单未找到！", id);
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "三轮车订单未找到！");
        }
        BicycleBillVo billVo = bicycleBillService.findByOrderSerialNo(order.getOrderSerialNo());
        if (billVo != null && billVo.getSettlementId() > 0) {
            log.info("{} 账单已生成了结算单！", JsonUtils.object2Json(billVo));
            throw new BusinessException("账单已生成了结算单，无法取消！");
        }

        //取消应收应付
        log.info("订单已配送，取消对应的应收应付账单: {}", JsonUtils.object2Json(order));
        cancelBillAndPayoff(order);

        if (BicycleOrderStatus.FINALIZE.getNumber() == status) {
            //删除配送订单及其以下数据
            bicycleOrderService.deleteBicycleOrder(id, false);
        }
        if (BicycleOrderStatus.CANCEL.getNumber() == status) {
            log.info("订单取消。更新订单状态，新增一个已完成的配送单");
            //新增一个已完成的配送单order order_detail
            saveBicycleDeliveryOrder(order);
            List<BicycleDeliveryOrderDetail> orderDetailList = bicycleDeliveryOrderDetailService.findByOrderSerialNo(order.getOrderSerialNo());
            //更新三轮车订单状态
            bicycleOrderService.updateOrderStatus(id, status, statusRemark
                    , CollectionUtils.isEmpty(orderDetailList) ? StringUtils.EMPTY : orderDetailList.get(0).getDeliveryOrderSerialNo());
            //发送三轮车订单取消通知
            RabbitMqSenderUtil.sendCancelOrderMessage(order, statusRemark, rabbitTemplate);
        }

        //如果删除的不是今天的订单，则重新生成对应的日报表
        if (! Objects.equals(order.getOrderTime().toLocalDate(), LocalDate.now())) {
            publisher.publishEvent(new BicycleOrderDayReportGenerateEvent(this, Collections.singletonList(order.getOrderTime().toLocalDate())));
            publisher.publishEvent(new BicycleOperateAnalysisEvent(this, Collections.singletonList(order.getOrderTime().toLocalDate())));
        }
    }

    /**
     * 根据订单删除应收应付
     * @param orderSerialNoList
     */
    public void cancelBillAndPayoff(List<String> orderSerialNoList) {
        if (CollectionUtils.isEmpty(orderSerialNoList)) {
            return;
        }
        orderSerialNoList.forEach(orderSerialNo -> {
            //删除应收账单及其以下数据
            bicycleBillService.deleteBicycleBillByOrderSerialNo(orderSerialNo);
            //删除应付账单及其以下数据
            bicyclePayoffService.deleteBicyclePayoffByOrderSerialNo(orderSerialNo);
        });
    }

    /**
     * 取消应收应付
     * @param order
     */
    private void cancelBillAndPayoff(BicycleOrder order) {
        List<BicycleDeliveryOrderDetail> orderDetailList = bicycleDeliveryOrderDetailService.findByOrderSerialNo(order.getOrderSerialNo());
        if (CollectionUtils.isEmpty(orderDetailList)) {
            log.info("订单未生成配送单，无需删除配送信息");
            return;
        }
        //获取配送单详细
        List<BicycleDeliveryOrderDetail> orderDetails = bicycleDeliveryOrderDetailService.listOrderDetailByDeliveryOrderSerialNo(orderDetailList.get(0).getDeliveryOrderSerialNo());
        //删除应收账单及其以下数据
        bicycleBillService.deleteBicycleBillByOrderSerialNo(order.getOrderSerialNo());
        //删除应付账单及其以下数据
        bicyclePayoffService.deleteBicyclePayoffByOrderSerialNo(order.getOrderSerialNo());

        //删除配送单详细
        bicycleDeliveryOrderDetailService.deleteDeliveryOrderDetailByOrderSerialNo(order.getOrderSerialNo());
        List<BicycleDeliveryOrderDetail> otherDetails = orderDetails.stream().filter(x -> !Objects.equals(x.getOrderSerialNo(), order.getOrderSerialNo())).collect(Collectors.toList());
        //删除配送子详细
        deliveryOrderDetailItemService.deleteByOrderSerialNo(order.getOrderSerialNo());
        if (CollectionUtils.isEmpty(otherDetails)) {
            //如果配送单不是合并的单或都为拆分单，则删除配送单
            deleteDeliveryOrderByDeliveryOrderSerialNo(orderDetails.get(0).getDeliveryOrderSerialNo());
        } else {
            BicycleDeliveryOrder deliveryOrder = findBySerialNo(orderDetailList.get(0).getDeliveryOrderSerialNo());
            deliveryOrder.setStatus(otherDetails.stream().min(Comparator.comparing(BicycleDeliveryOrderDetail::getStatus)).get().getStatus());
            deliveryOrder.setMerged(false);
            //是否合并单
            if (otherDetails.stream().collect(Collectors.groupingBy(BicycleDeliveryOrderDetail::getOrderSerialNo)).keySet().size() > 1) {
                deliveryOrder.setMerged(true);
            }
            updateDeliveryOrderStatus(deliveryOrder);
        }
    }

    /**
     * 新增已完成的配送单
     * @param order
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBicycleDeliveryOrder(BicycleOrder order) {
        log.info("【配送单】新增一个已完成的配送单 :{}", JsonUtils.object2Json(order));
        BicycleDeliveryOrder deliveryOrder = buildBicycleDeliveryOrder();
        if (save(deliveryOrder)) {
            BicycleDeliveryOrderDetailVo orderDetail = buildBicycleDeliveryOrderDetail(order);
            bicycleDeliveryOrderDetailService.addBicycleDeliveryOrderDetail(deliveryOrder.getDeliveryOrderSerialNo(), Collections.singletonList(orderDetail), BicycleDeliveryOrderStatus.CONFIRM_COMPLETION.getNumber());
        }
    }

    /**
     * 构建配送单
     * @return
     */
    public BicycleDeliveryOrder buildBicycleDeliveryOrder() {
        BicycleDeliveryOrder deliveryOrder = new BicycleDeliveryOrder();
        prepareDeliveryOrderSerialNo(deliveryOrder);
        deliveryOrder.setStatus(BicycleDeliveryOrderStatus.CONFIRM_COMPLETION.getNumber());
        ContextUtils.initEntityCreator(deliveryOrder);
        ContextUtils.initEntityModifier(deliveryOrder);
        deliveryOrder.setCreateTime(LocalDateTime.now());
        deliveryOrder.setModifiedTime(LocalDateTime.now());
        return deliveryOrder;
    }

    /**
     * 构建配送单详细
     * @param order
     * @return
     */
    public BicycleDeliveryOrderDetailVo buildBicycleDeliveryOrderDetail(BicycleOrder order) {
        BicycleDeliveryOrderDetailVo deliveryOrderDetail = new BicycleDeliveryOrderDetailVo();
        deliveryOrderDetail.setOrderSerialNo(order.getOrderSerialNo());
        deliveryOrderDetail.setStatus(BicycleDeliveryOrderStatus.CONFIRM_COMPLETION.getNumber());
        return deliveryOrderDetail;
    }

    /**
     * 添加配送单
     * @param deliveryOrderVo
     */
    @Transactional(rollbackFor = Exception.class)
    public BicycleDeliveryOrder saveBicycleDeliveryOrder(BicycleDeliveryOrderVo deliveryOrderVo, LocalDateTime orderTime) {
        BicycleDeliveryOrder deliveryOrder = deliveryOrderConvert.toDo(deliveryOrderVo);
        ContextUtils.initEntityCreator(deliveryOrder);
        ContextUtils.initEntityModifier(deliveryOrder);
        deliveryOrder.setCreateTime(orderTime);
        deliveryOrder.setModifiedTime(orderTime);
        prepareDeliveryOrderSerialNo(deliveryOrder);
        if (!save(deliveryOrder)) {
            throw new BusinessException(ResultConst.ERROR);
        }
        return deliveryOrder;
    }



    /**
     * 配送单详情处理
     *
     * @param deliveryOrderSerialNo 配送单流水号
     * @param details               配送单集
     * @param orderTime             订单日期
     * @return 返回构建的配送单
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> deliverOrderDetailHandle(String deliveryOrderSerialNo, List<BicycleDeliveryOrderDetailVo> details, LocalDateTime orderTime) {
        if (StringUtils.isBlank(deliveryOrderSerialNo) || CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        details.forEach(detail -> {
            detail.setStatus(BicycleDeliveryOrderStatus.CONFIRM_COMPLETION.getNumber());
            detail.setPickingTime(orderTime);
            detail.setSendTime(orderTime);
            detail.setReceivingTime(orderTime);
            detail.setFinishTime(orderTime);
        });
        return bicycleDeliveryOrderDetailService.addBicycleDeliveryOrderDetail(deliveryOrderSerialNo, details, BicycleDeliveryOrderStatus.CONFIRM_COMPLETION.getNumber());
    }


    /**
     * 根据id删除未配送的订单
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteUndeliveredBicycleOrder(Long id) {
        BicycleOrder order = bicycleOrderService.findById(id);
        if (order == null) {
            log.info("{} 三轮车订单未找到！", id);
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "三轮车订单未找到！");
        }
        List<BicycleDeliveryOrderDetail> orderDetailList = bicycleDeliveryOrderDetailService.findByOrderSerialNo(order.getOrderSerialNo());
        if (!CollectionUtils.isEmpty(orderDetailList)) {
            log.info("三轮车订单已配送！不能取消, 订单Id为:{}",id);
            throw new BusinessException("已生成配送单，不能取消！");
        }
        //删除配送订单及其以下数据
        bicycleOrderService.deleteBicycleOrder(id, false);
    }

    /**
     * 更新订单状态
     * @param id
     * @param state
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBicycleOrderState(Long id, Integer state) {
        BicycleOrder order = bicycleOrderService.findById(id);
        if (order == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "配送订单不存在");
        }
        List<BicycleDeliveryOrderDetail> orderDetailList = bicycleDeliveryOrderDetailService.findByOrderSerialNo(order.getOrderSerialNo());
        if (!CollectionUtils.isEmpty(orderDetailList) && state == BicycleOrderState.DRAFT.getNumber()) {
            throw new BusinessException("订单已经配送，不能取消！");
        }
        AdminUserDto context = ContextUtils.getUserInContext(false);
        LambdaUpdateWrapper<BicycleOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(BicycleOrder::getState, state);
        wrapper.set(BicycleOrder::getModifiedTime, LocalDateTime.now());
        wrapper.set(BicycleOrder::getModifierId, context.getUserId());
        wrapper.set(BicycleOrder::getModifierName, context.getUsername());
        wrapper.eq(BicycleOrder::getId, id);
        if (state == BicycleOrderState.FINALIZE.getNumber()) {
            wrapper.set(BicycleOrder::getOrderTime, LocalDateTime.now());
            bicycleOrderService.autoMerged(order);
        } else {
            wrapper.set(BicycleOrder::getOrderTime, null);
            BicycleOrderVo bicycleOrderVo = bicycleOrderService.findOrderVoById(id, true);
            publisher.publishEvent(new CancelMergedEvent(this, bicycleOrderVo));
        }
        bicycleOrderService.update(wrapper);
        publisher.publishEvent(new BicycleOrderWebSocketEvent(publisher));
    }


    /**
     * 配送单提货信息分页
     * @param searchVo
     * @return
     */
    public PageResult<BicycleDeliveryInfoVo> deliveryInfoPage(BicycleDeliveryInfoSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().deliveryInfoCount(searchVo), () -> getBaseMapper().deliveryInfoList(searchVo));
    }
}

package com.senox.tms.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.domain.UnloadingWorkerLog;
import com.senox.tms.mapper.UnloadingWorkerLogMapper;
import com.senox.tms.vo.UnloadingWorkerLogSearchVo;
import com.senox.tms.vo.UnloadingWorkerLogVo;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24 8:47
 */
@Service
public class UnloadingWorkerLogService extends ServiceImpl<UnloadingWorkerLogMapper, UnloadingWorkerLog> {

    /**
     * 添加搬运工异常日志
     * @param workerLogs
     */
    public void batchAddWorkerLog(List<UnloadingWorkerLog> workerLogs) {
        workerLogs.forEach(x -> x.setModifiedTime(LocalDateTime.now()));
        saveBatch(workerLogs);
    }

    /**
     * 根据id查询搬运工异常日志
     * @param id
     * @return
     */
    public UnloadingWorkerLogVo findWorkerLogById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getBaseMapper().findWorkerLogById(id);
    }

    /**
     * 搬运工异常日志统计
     * @param searchVo
     * @return
     */
    public int countWorkerLog(UnloadingWorkerLogSearchVo searchVo) {
        return getBaseMapper().countWorkerLog(searchVo);
    }

    /**
     * 搬运工异常日志列表
     * @param searchVo
     * @return
     */
    public List<UnloadingWorkerLogVo> listWorkerLog(UnloadingWorkerLogSearchVo searchVo) {
        return getBaseMapper().listWorkerLog(searchVo);
    }

    /**
     * 搬运工异常日志分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingWorkerLogVo> pageWorkerLog(UnloadingWorkerLogSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> countWorkerLog(searchVo), () -> listWorkerLog(searchVo));
    }
}

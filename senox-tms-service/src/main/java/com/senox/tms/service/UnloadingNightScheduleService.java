package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.domain.UnloadingNightSchedule;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.mapper.UnloadingNightScheduleMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.UnloadingNightScheduleBatchVo;
import com.senox.tms.vo.UnloadingNightScheduleSearchVo;
import com.senox.tms.vo.UnloadingNightScheduleVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/10 11:11
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UnloadingNightScheduleService extends ServiceImpl<UnloadingNightScheduleMapper, UnloadingNightSchedule> {

    private final UnloadingWorkerService workerService;

    /**
     * 批量添加排期计划
     * @param schedules
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchAddSchedule(List<UnloadingNightSchedule> schedules) {
        if (CollectionUtils.isEmpty(schedules)) {
            log.info("【schedule batchAdd】 schedules is empty, return");
            return;
        }
        schedules.forEach(schedule ->{
            schedule.setScheduleYear(schedule.getScheduleDate().getYear());
            schedule.setScheduleMonth(schedule.getScheduleDate().getMonthValue());
            ContextUtils.initEntityCreator(schedule);
            ContextUtils.initEntityModifier(schedule);
            schedule.setCreateTime(LocalDateTime.now());
            schedule.setModifiedTime(LocalDateTime.now());
        });
        List<UnloadingNightSchedule> addScheduleList = new ArrayList<>(schedules.size());
        Map<LocalDate, List<UnloadingNightSchedule>> scheduleDateMap = schedules.stream().collect(Collectors.groupingBy(UnloadingNightSchedule::getScheduleDate));
        for (Map.Entry<LocalDate, List<UnloadingNightSchedule>> entry : scheduleDateMap.entrySet()) {
            List<String> workerNos = entry.getValue().stream().map(UnloadingNightSchedule::getWorkerNo).collect(Collectors.toList());
            List<UnloadingNightSchedule> existsScheduleList = listScheduleByDateAndWorkerNo(workerNos, entry.getKey());
            List<UnloadingNightSchedule> collect = entry.getValue().stream().filter(schedule -> !existsScheduleList.contains(schedule)).collect(Collectors.toList());
            addScheduleList.addAll(collect);
        }
        saveBatch(addScheduleList);
    }

    /**
     * 批量添加排期计划
     * @param batchVo
     */
    public void batchAddSchedule(UnloadingNightScheduleBatchVo batchVo) {
        List<UnloadingNightSchedule> schedules = buildScheduleList(batchVo);
        batchAddSchedule(schedules);
    }

    /**
     * 构建排期计划集合
     * @param batchVo
     * @return
     */
    private List<UnloadingNightSchedule> buildScheduleList(UnloadingNightScheduleBatchVo batchVo) {
        if (batchVo.getScheduleDateStart() == null || batchVo.getScheduleDateEnd() == null) {
            throw new BusinessException("排期起止日期不能为空！");
        }
        List<UnloadingWorker> workerList = workerService.listByIds(batchVo.getWorkerIdList());
        if (CollectionUtils.isEmpty(workerList)) {
            throw new BusinessException("未查询到搬运工！");
        }
        int daysBetween = (int)ChronoUnit.DAYS.between(batchVo.getScheduleDateStart(), batchVo.getScheduleDateEnd());
        if (daysBetween >= 15) {
            throw new BusinessException("一次性排期不能超过15天！");
        }

        List<UnloadingNightSchedule> schedules = new ArrayList<>(daysBetween * workerList.size());
        for (long i = 0; i <= daysBetween; i++) {
            LocalDate date = batchVo.getScheduleDateStart().plusDays(i);
            workerList.forEach(worker -> schedules.add(buildSchedule(date, worker)));
        }
        return schedules;
    }

    /**
     * 构建排期计划
     * @param date
     * @param worker
     * @return
     */
    private UnloadingNightSchedule buildSchedule(LocalDate date, UnloadingWorker worker) {
        UnloadingNightSchedule schedule = new UnloadingNightSchedule();
        schedule.setScheduleDate(date);
        schedule.setWorkerId(worker.getId());
        schedule.setWorkerNo(worker.getWorkerNo());
        schedule.setWorkerName(worker.getName());
        return schedule;
    }

    /**
     * 添加排期计划
     * @param schedule
     */
    public void addSchedule(UnloadingNightSchedule schedule) {
        batchAddSchedule(Collections.singletonList(schedule));
    }

    /**
     * 更新排期计划
     * @param schedule
     */
    public void updateSchedule(UnloadingNightSchedule schedule) {
        if (!WrapperClassUtils.biggerThanLong(schedule.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        List<UnloadingNightSchedule> nightSchedules = listScheduleByDateAndWorkerNo(Collections.singletonList(schedule.getWorkerNo()), schedule.getScheduleDate());
        if (!CollectionUtils.isEmpty(nightSchedules)) {
            throw new BusinessException("【排期计划已存在】，时间：" + schedule.getScheduleDate());
        }
        ContextUtils.initEntityModifier(schedule);
        schedule.setModifiedTime(LocalDateTime.now());
        updateById(schedule);
    }

    /**
     * 根据id查询排期计划
     * @param id
     * @return
     */
    public UnloadingNightSchedule findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据日期获取排期计划
     * @param scheduleDate
     * @return
     */
    public List<UnloadingNightSchedule> findByScheduleDate(LocalDate scheduleDate) {
        return list(new LambdaQueryWrapper<UnloadingNightSchedule>().eq(UnloadingNightSchedule::getScheduleDate, scheduleDate));
    }

    /**
     * 根据id删除排期计划
     * @param id
     */
    public void deleteById(Long id) {
        removeById(id);
    }

    /**
     * 根据日期删除排期计划
     * @param scheduleDate
     */
    public void deleteByScheduleDate(LocalDate scheduleDate) {
        remove(new LambdaQueryWrapper<UnloadingNightSchedule>().eq(UnloadingNightSchedule::getScheduleDate, scheduleDate));
    }

    /**
     * 搬运工排期计划列表
     * @param searchVo
     * @return
     */
    public List<UnloadingNightScheduleVo> listSchedule(UnloadingNightScheduleSearchVo searchVo) {
        return getBaseMapper().listSchedule(searchVo);
    }

    /**
     * 搬运工排期计划分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingNightScheduleVo> pageSchedule(UnloadingNightScheduleSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countSchedule(searchVo), () -> getBaseMapper().listSchedule(searchVo));
    }

    /**
     * 根据搬运工编号和日期获取记录
     * @param workerNos
     * @param scheduleDate
     * @return
     */
    public List<UnloadingNightSchedule> listScheduleByDateAndWorkerNo(List<String> workerNos, LocalDate scheduleDate) {
        return list(new QueryWrapper<UnloadingNightSchedule>().lambda()
                .eq(UnloadingNightSchedule::getScheduleDate, scheduleDate)
                .in(UnloadingNightSchedule::getWorkerNo, workerNos));
    }
}

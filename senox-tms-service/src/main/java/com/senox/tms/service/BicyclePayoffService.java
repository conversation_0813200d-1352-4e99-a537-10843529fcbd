package com.senox.tms.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.BillStatus;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.DataSepDto;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.pm.constant.PayWay;
import com.senox.tms.component.MerchantComponent;
import com.senox.tms.constant.BicycleBillStatus;
import com.senox.tms.constant.BicycleFee;
import com.senox.tms.constant.BicyclePayeeType;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.*;
import com.senox.tms.mapper.BicyclePayoffMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import com.senox.user.vo.MerchantVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@RequiredArgsConstructor
@Service
public class BicyclePayoffService extends ServiceImpl<BicyclePayoffMapper, BicyclePayoff> {

    private final BicyclePayoffDetailService payoffDetailService;
    private final BicycleRiderService bicycleRiderService;
    private final BicyclePayoffDayReportService payoffDayReportService;
    private final BicyclePayoffMonthReportService payoffMonthReportService;
    private final BicyclePayoffChargesService chargesService;
    private final BicycleSharesService sharesService;
    private final BicycleOrderService bicycleOrderService;
    private final MerchantComponent merchantComponent;


    /**
     * 添加应付账单
     * @param payoff 应付账单
     * @return 返回应付账单id
     */
    public Long add(BicyclePayoff payoff) {
        if (null == payoff) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        if (null != findPayoff(payoff)) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR);
        }
        boolean save = save(payoff);
        return save ? payoff.getId() : 0;
    }

    /**
     * 添加应付账单明细
     * @param payoffDetail 应付账单明细
     */
    public void addDetail(BicyclePayoffDetail payoffDetail) {
        if (null == payoffDetail
                || !WrapperClassUtils.biggerThanLong(payoffDetail.getPayoffId(), 0)
                || !WrapperClassUtils.biggerThanLong(payoffDetail.getFeeId(), 0)
        ) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        payoffDetailService.save(payoffDetail);
    }

    /**
     * 添加应付账单和明细
     * @param payoff 应付账单
     * @param payoffDetail 应付账单明细
     * @return 返回应付账单id
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addAndDetail(BicyclePayoff payoff, BicyclePayoffDetail payoffDetail) {
        if (add(payoff) > 0) {
            payoffDetail.setPayoffId(payoff.getId());
            payoffDetail.setModifiedTime(LocalDateTime.now());
            addDetail(payoffDetail);
        }
        return payoff.getId();
    }

    /**
     * 查询应付账单
     * @param payoff 应付账单
     * @return 返回查询到的应付账单
     */
    public BicyclePayoff findPayoff(BicyclePayoff payoff) {
        LambdaQueryWrapper<BicyclePayoff> lambdaQueryWrapper = new QueryWrapper<BicyclePayoff>().lambda();
        //根据订单流水号跟配送单流水号查询
        lambdaQueryWrapper.eq(BicyclePayoff::getOrderSerialNo, payoff.getOrderSerialNo())
                .eq(BicyclePayoff::getDeliveryOrderSerialNo, payoff.getDeliveryOrderSerialNo());
        return getOne(lambdaQueryWrapper);
    }

    /**
     * 根据id查询应付账单
     * @param id id
     * @return 返回查询到的应付账单
     */
    public BicyclePayoff findById(Long id) {
        return getById(id);
    }

    /**
     * 付款
     * 该方法没有进行实际付款
     * @param id 账单id
     */
    public void pay(Long id) {
        BicyclePayoff payoff = getById(id);
        if (null == payoff) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (BicycleBillStatus.PAID.getNumber() == payoff.getStatus()) {
            log.info("【Bicycle payoff】账单[%s]已支付");
            return;
        }
        BicyclePayoff updatePayoff = new BicyclePayoff();
        updatePayoff.setId(payoff.getId());
        updatePayoff.setPaidTime(LocalDateTime.now());
        updatePayoff.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityCreator(updatePayoff);
        update(updatePayoff);
    }

    /**
     * 更新应付账单
     * @param updatePayoff 要更新的应付账单
     */
    public void update(BicyclePayoff updatePayoff) {
        LambdaUpdateWrapper<BicyclePayoff> lambdaUpdateWrapper = new UpdateWrapper<BicyclePayoff>().lambda();
        if (!StringUtils.isBlank(updatePayoff.getOrderSerialNo())) {
            lambdaUpdateWrapper.set(BicyclePayoff::getOrderSerialNo, updatePayoff.getOrderSerialNo());
        }
        if (!StringUtils.isBlank(updatePayoff.getDeliveryOrderSerialNo())) {
            lambdaUpdateWrapper.set(BicyclePayoff::getDeliveryOrderSerialNo, updatePayoff.getDeliveryOrderSerialNo());
        }
        if (!StringUtils.isBlank(updatePayoff.getPayeeName())) {
            lambdaUpdateWrapper.set(BicyclePayoff::getPayeeName, updatePayoff.getPayeeName());
        }
        if (null != updatePayoff.getPayeeType()) {
            lambdaUpdateWrapper.set(BicyclePayoff::getPayeeType, updatePayoff.getPayeeType());
        }
        if (!StringUtils.isBlank(updatePayoff.getPayeeContact())) {
            lambdaUpdateWrapper.set(BicyclePayoff::getPayeeContact, updatePayoff.getPayeeContact());
        }
        if (null != updatePayoff.getShareAmount()) {
            lambdaUpdateWrapper.set(BicyclePayoff::getShareAmount, updatePayoff.getShareAmount());
        }
        if (null != updatePayoff.getPaidTime()) {
            lambdaUpdateWrapper.set(BicyclePayoff::getPaidTime, updatePayoff.getPaidTime());
        }
        lambdaUpdateWrapper.set(BicyclePayoff::getModifierId, updatePayoff.getModifierId());
        lambdaUpdateWrapper.set(BicyclePayoff::getModifierName, updatePayoff.getModifierName());
        lambdaUpdateWrapper.set(BicyclePayoff::getModifiedTime, updatePayoff.getModifiedTime());
        lambdaUpdateWrapper.eq(BicyclePayoff::getId, updatePayoff.getId());
        update(lambdaUpdateWrapper);
    }

    /**
     * 更新应付账单状态
     * @param billPaid
     */
    public void updatePayoffStatus(BillPaidVo billPaid, PayWay payway) {
        BicyclePayoff payoff = new BicyclePayoff();
        payoff.setPayway(payway.getValue());
        payoff.setStatus(BillStatus.PAID.getValue());
        payoff.setPaidTime(billPaid.getPaidTime());
        ContextUtils.initEntityModifier(payoff);
        payoff.setModifiedTime(LocalDateTime.now());

        LambdaQueryWrapper<BicyclePayoff> queryWrapper =
                new QueryWrapper<BicyclePayoff>().lambda()
                        .in(BicyclePayoff::getId, billPaid.getBillIds())
                        .eq(BicyclePayoff::getStatus, BillStatus.INIT.getValue());
        update(payoff, queryWrapper);
    }

    /**
     * 应收账单合计
     * @param searchVo
     * @return
     */
    public BicyclePayoffVo sumPayoff(BicyclePayoffSearchVo searchVo) {
        searchVo.setExcludeZero(true);
        return getBaseMapper().sumPayoff(searchVo);
    }

    /**
     * 应收账单列表
     * @param searchVo
     * @return
     */
    public PageResult<BicyclePayoffVo> listPayoff(BicyclePayoffSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        searchVo.setExcludeZero(true);
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countPayoff(searchVo), () -> getBaseMapper().listPayoff(searchVo));
    }

    /**
     * 添加应付账单及明细
     * @param orderDetailVoList
     * @param orderTime
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBicyclePayoff(List<BicycleDeliveryOrderDetailVo> orderDetailVoList, LocalDateTime orderTime) {
        if (CollectionUtils.isEmpty(orderDetailVoList)) {
            return;
        }
        BicycleDeliveryOrderDetailVo orderDetailVo = orderDetailVoList.get(0);
        if (orderDetailVo == null
                || StringUtils.isBlank(orderDetailVo.getOrderSerialNo())
                || StringUtils.isBlank(orderDetailVo.getDeliveryOrderSerialNo())) {
            return;
        }
        BicycleOrder order = bicycleOrderService.findBySerialNo(orderDetailVo.getOrderSerialNo());
        MerchantVo merchantVo = merchantComponent.findById(order.getSenderId());
        List<BicyclePayoff> payoffList = new ArrayList<>(orderDetailVoList.size());
        orderDetailVoList.forEach(detailVo -> {
            BicyclePayoff payoff = buildPayoff(orderTime, detailVo, Objects.isNull(merchantVo.getReferralCode()) && Objects.equals(merchantVo.getReferralCode(), detailVo.getReferralCode()) ? sharesService.getCurrentEffectiveShares().getRiderShares() : BigDecimal.ZERO);
            if (payoff != null) {
                payoffList.add(payoff);
            }
        });
        //如果分配的骑手没有包含推荐的骑手，则分佣推荐奖励
        BicycleDeliveryOrderDetailVo detailVo = orderDetailVoList.stream().filter(x -> Objects.equals(merchantVo.getReferralCode(), x.getReferralCode())).findFirst().orElse(null);
        if(detailVo == null) {
            BicyclePayoff payoff = buildPayoff(orderTime, orderDetailVo.getOrderSerialNo(), orderDetailVo.getDeliveryOrderSerialNo(), merchantVo.getReferralCode(), sharesService.getCurrentEffectiveShares().getRiderDefaultShares(), BigDecimal.ZERO, order.getTotalCharge(), false);
            if (payoff != null) {
                payoffList.add(payoff);
            }
        }
        List<BicyclePayoff> bicyclePayoffList = findByOrderSerialNo(orderDetailVo.getOrderSerialNo());
        log.info("【构建应付】结果为：{}", JsonUtils.object2Json(payoffList));
        log.info("【查询的应付】结果为：{}", JsonUtils.object2Json(bicyclePayoffList));
        DataSepDto<BicyclePayoff> sepDto = SeparateUtils.separateData(bicyclePayoffList, payoffList);
        log.info("【变动的应付】结果为：{}", JsonUtils.object2Json(sepDto));
        List<Long> removeIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(sepDto.getAddList())) {
            saveBatch(sepDto.getAddList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getUpdateList())) {
            updateBatchById(sepDto.getUpdateList());
        }
        if (!CollectionUtils.isEmpty(sepDto.getRemoveList())) {
            removeIds = sepDto.getRemoveList().stream().map(BicyclePayoff::getId).collect(Collectors.toList());
            removeByIds(removeIds);
        }
        List<BicyclePayoff> payoffs = findByOrderSerialNo(orderDetailVo.getOrderSerialNo());
        if (!CollectionUtils.isEmpty(payoffs)) {
            List<Long> payoffIdList = payoffs.stream().map(BicyclePayoff::getId).distinct().collect(Collectors.toList());
            List<BicyclePayoffDetail> payoffDetailList = new ArrayList<>(payoffIdList.size());
            payoffIdList.forEach(payoffId -> {
                BicyclePayoffDetail payoffDetail = buildPayoffDetail();
                payoffDetail.setPayoffId(payoffId);
                payoffDetail.setModifiedTime(LocalDateTime.now());
                payoffDetailList.add(payoffDetail);
            });
            payoffDetailService.savePayoffDetail(payoffDetailList, removeIds);
        }
    }

    /**
     * 构建应付账单
     * @param orderTime
     * @param detailVo
     * @param sharesProportion 比例
     * @return
     */
    private BicyclePayoff buildPayoff(LocalDateTime orderTime, BicycleDeliveryOrderDetailVo detailVo, BigDecimal sharesProportion) {
        BicycleRider rider = bicycleRiderService.findById(detailVo.getRiderId(), false);
        if (rider == null) {
            log.info("骑手不存在----");
            return null;
        }
        BicyclePayoff payoff = new BicyclePayoff();
        payoff.setBillYear(orderTime.getYear());
        payoff.setBillMonth(orderTime.getMonthValue());
        payoff.setOrderSerialNo(detailVo.getOrderSerialNo());
        payoff.setDeliveryOrderSerialNo(detailVo.getDeliveryOrderSerialNo());
        payoff.setPayeeName(rider.getName());
        payoff.setRiderId(rider.getId());
        payoff.setPayeeType(BicyclePayeeType.RIDER.getNumber());
        payoff.setPayeeContact(rider.getContact());
        payoff.setPieces(detailVo.getPickedPieces());
        payoff.setShareAmount(DecimalUtils.multiple(detailVo.getTotalCharge(), sharesProportion.movePointLeft(2)));
        if (null != detailVo.getSendTime() && null != detailVo.getReceivingTime()) {
            long sendTime = detailVo.getSendTime().toEpochSecond(ZoneOffset.UTC);
            long receivingTime = detailVo.getReceivingTime().toEpochSecond(ZoneOffset.UTC);
            payoff.setDeliveryTime((int) (receivingTime - sendTime));
        }
        ContextUtils.initEntityModifier(payoff);
        ContextUtils.initEntityModifier(payoff);
        payoff.setCreateTime(orderTime);
        payoff.setModifiedTime(orderTime);
        return payoff;
    }

    /**
     * 构建应付
     * @param orderTime
     * @param orderSerialNo
     * @param deliveryOrderSerialNo
     * @param referralCode
     * @param sharesProportion
     * @param referralAmount
     * @param totalCharge
     * @return
     */
    public BicyclePayoff buildPayoff(LocalDateTime orderTime, String orderSerialNo, String deliveryOrderSerialNo, String referralCode, BigDecimal sharesProportion, BigDecimal referralAmount, BigDecimal totalCharge, boolean referral) {
        BicycleRider bicycleRider = bicycleRiderService.findByReferralCode(referralCode);
        if (bicycleRider == null) {
            return null;
        }
        BicyclePayoff payoff = new BicyclePayoff();
        payoff.setBillYear(orderTime.getYear());
        payoff.setBillMonth(orderTime.getMonthValue());
        payoff.setOrderSerialNo(orderSerialNo);
        payoff.setDeliveryOrderSerialNo(deliveryOrderSerialNo);
        payoff.setPayeeName(bicycleRider.getName());
        payoff.setRiderId(bicycleRider.getId());
        payoff.setPayeeType(BicyclePayeeType.RIDER.getNumber());
        payoff.setPayeeContact(bicycleRider.getContact());
        payoff.setShareAmount(DecimalUtils.multiple(totalCharge, sharesProportion.movePointLeft(2)));
        payoff.setReferralAmount(referralAmount);
        payoff.setPieces(BigDecimal.ZERO);
        payoff.setReferral(referral);
        ContextUtils.initEntityModifier(payoff);
        ContextUtils.initEntityModifier(payoff);
        payoff.setCreateTime(orderTime);
        payoff.setModifiedTime(orderTime);
        return payoff;
    }

    /**
     * 计算应付报表金额
     * @param payoffCharges 应付收费标准
     * @param payoffReport 应付报表
     * @return 应付金额
     */
    public BigDecimal calculatePayoffReportAmount(BicyclePayoffCharges payoffCharges,BicyclePayoffVo payoffReport) {
        BigDecimal basicAmount = payoffCharges.getBasicAmount();
        BigDecimal basicPieces = payoffCharges.getBasicPieces();
        BigDecimal excessPrice = payoffCharges.getExcessPrice();
        BigDecimal excessPieces = new BigDecimal(payoffReport.getPieces()).subtract(basicPieces);
        return excessPieces.compareTo(BigDecimal.ZERO) > 0 ? basicAmount.add(excessPrice.multiply(excessPieces)) : basicAmount;
    }

    /**
     * 生成应付账单详细
     * @return
     */
    private BicyclePayoffDetail buildPayoffDetail() {
        BicyclePayoffDetail payoffDetail = new BicyclePayoffDetail();
        payoffDetail.setFeeId(BicycleFee.DELIVERY.getFeeId());
        payoffDetail.setFeeName(BicycleFee.DELIVERY.getName());
        return payoffDetail;
    }

    /**
     * 根据订单流水号查询应付账单
     * @param orderSerialNo
     * @return
     */
    public List<BicyclePayoff> findByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            throw new InvalidParameterException("订单流水号为空");
        }
        return list(new QueryWrapper<BicyclePayoff>().lambda()
                .eq(BicyclePayoff::getOrderSerialNo, orderSerialNo));
    }

    /**
     * 根据订单流水号删除应付账单
     * @param orderSerialNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBicyclePayoffByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            throw new InvalidParameterException("订单流水号为空");
        }
        List<BicyclePayoff> bicyclePayoffList = findByOrderSerialNo(orderSerialNo);
        if (CollectionUtils.isEmpty(bicyclePayoffList)) {
            log.info("【应付账单】记录不存在，{} 对应的应付账单不存在", orderSerialNo);
            return;
        }
        List<Long> ids = bicyclePayoffList.stream().map(BicyclePayoff::getId).collect(Collectors.toList());
        boolean remove = removeByIds(ids);
        if (remove) {
            payoffDetailService.deleteBicyclePayoffDetailByBillIdList(ids);
        }
    }

    /**
     * 生成账单日报表
     *
     * @param dateVo 日期
     */
    public void generateReportByDay(BicycleDateVo dateVo) {
        String lockKey = String.format(TmsConst.Cache.KEY_BICYCLE_DYA_PAYOFF_BILL_REPORT_LOCK, dateVo.getYear(), dateVo.getMonth(), dateVo.getDay());
        if (!RedisUtils.lock(lockKey, TmsConst.Cache.TTL_1H)) {
            throw new BusinessException(String.format("日付款账单报表[%s]-[%s]-[%s]生成中...", dateVo.getYear(), dateVo.getMonth(), dateVo.getDay()));
        }
        try {
            log.info("【Bicycle payoff】开始生成[{}]-[{}]-[{}]付款账单报表", dateVo.getYear(), dateVo.getMonth(), dateVo.getDay());
            BicyclePayoffSearchVo searchVo = new BicyclePayoffSearchVo();
            LocalDate date = LocalDate.of(dateVo.getYear(), dateVo.getMonth(), dateVo.getDay());
            searchVo.setStartTime(LocalDateTime.of(date, LocalTime.MIN));
            searchVo.setEndTime(LocalDateTime.of(date, LocalTime.MAX));
            searchVo.setStatus(0);
            searchVo.setPage(false);
            if (!StringUtils.isBlank(dateVo.getPayeeName())){
                searchVo.setPayeeName(dateVo.getPayeeName());
            }
            List<BicyclePayoffVo> list = getBaseMapper().listPayoff(searchVo);
            if (CollectionUtils.isEmpty(list)) {
                log.warn("【Bicycle payoff】[{}]-[{}]-[{}]没有付款账单", dateVo.getYear(), dateVo.getMonth(), dateVo.getDay());
                return;
            }

            //根据骑手id统计
            Map<Long, List<BicyclePayoffVo>> payoffRiderMaps = list.stream().collect(Collectors.groupingBy(BicyclePayoffVo::getRiderId));
            Map<Long, BicyclePayoffVo> payoffRiderMap = payoffRiderMaps.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> {
                List<BicyclePayoffVo> values = e.getValue();
                BicyclePayoffVo payoffVo = new BicyclePayoffVo();
                payoffVo.setRiderId(values.get(0).getRiderId());
                payoffVo.setPayeeName(values.get(0).getPayeeName());
                List<Integer> deliveryTimes = new ArrayList<>(values.size());
                for (BicyclePayoffVo payoff : values) {
                    payoffVo.setShareAmount(null != payoffVo.getShareAmount() ? payoffVo.getShareAmount().add(payoff.getShareAmount()) : payoff.getShareAmount());
                    payoffVo.setReferralAmount(null != payoffVo.getReferralAmount() ? payoffVo.getReferralAmount().add(payoff.getReferralAmount()) : payoff.getReferralAmount());
                    payoffVo.setPieces(null != payoffVo.getPieces() ? payoffVo.getPieces() + payoff.getPieces() : payoff.getPieces());
                    deliveryTimes.add(payoff.getDeliveryTime());
                }
                OptionalDouble average = IntStream.of(deliveryTimes.stream().mapToInt(Integer::valueOf).toArray()).average();
                payoffVo.setDeliveryTime(average.isPresent() ? (int) Math.round(average.getAsDouble()) : 0);
                return payoffVo;
            }));
            //转换为报表类型
            List<BicyclePayoffDayReport> payoffReports = payoffRiderMap.values()
                    .stream().map(p -> buildPayOffReport(p,date,payoffRiderMaps.get(p.getRiderId()).stream().map(BicyclePayoffVo::getOrderSerialNo).distinct().count(), payoffRiderMaps.get(p.getRiderId()).stream().filter(x -> DecimalUtils.isPositive(x.getReferralAmount())).count())).collect(Collectors.toList());
            payoffDayReportService.addBatchOrUpdate(DateUtils.formatYearMonth(date,DateUtils.PATTERN_FULL_DATE), payoffReports,false);
        } finally {
            RedisUtils.del(lockKey);
        }
    }

    /**
     * 构建应付报表
     * @param payoff 应付
     * @param date 日期
     * @param orderNumber 订单数
     * @return 返回应付报表
     */
    public BicyclePayoffDayReport buildPayOffReport(BicyclePayoffVo payoff,LocalDate date,Long orderNumber, Long referralCount){
        BicyclePayoffDayReport payoffReport = new BicyclePayoffDayReport();
        payoffReport.setYearMonthDay(DateUtils.formatYearMonth(date, DateUtils.PATTERN_FULL_DATE));
        payoffReport.setYear(date.getYear());
        payoffReport.setMonth(date.getMonthValue());
        payoffReport.setDay(date.getDayOfMonth());
        payoffReport.setPayeeId(payoff.getRiderId());
        payoffReport.setPayeeName(payoff.getPayeeName());
        payoffReport.setReferralAmount(payoff.getReferralAmount());
        payoffReport.setReferralCount(referralCount.intValue());
        payoffReport.setTotalAmount(DecimalUtils.add(payoffReport.getReferralAmount(), payoff.getShareAmount()));
        payoffReport.setCreateTime(LocalDateTime.now());
        payoffReport.setModifiedTime(LocalDateTime.now());
        payoffReport.setPieces(payoff.getPieces());
        payoffReport.setOrderNumber(orderNumber.intValue());
        payoffReport.setAvgDeliveryTime(payoff.getDeliveryTime());
        AdminUserDto context = ContextUtils.getUserInContext(true);
        payoffReport.setCreatorId(context.getUserId());
        payoffReport.setCreatorName(context.getUsername());
        payoffReport.setModifierId(context.getUserId());
        payoffReport.setModifierName(context.getUsername());
        return payoffReport;
    }


    /**
     * 根据骑手和订单流水号查询应付账单
     * @param orderSerialNo
     * @return
     */
    public List<BicyclePayoff> findByRiderAndOrderSerialNo(List<Long> riderIdList, String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            throw new InvalidParameterException();
        }
        LambdaQueryWrapper<BicyclePayoff> lambda = new QueryWrapper<BicyclePayoff>().lambda();
        lambda.in(BicyclePayoff::getRiderId, riderIdList);
        lambda.eq(BicyclePayoff::getOrderSerialNo, orderSerialNo);
        return list(lambda);
    }

    /**
     * 生成账单月报表
     *
     * @param dateVo 日期
     */
    public void generateReportByMonth(BicycleDateVo dateVo) {
        String lockKey = String.format(TmsConst.Cache.KEY_BICYCLE_MONTH_PAYOFF_BILL_REPORT_LOCK, dateVo.getYear(), dateVo.getMonth());
        if (!RedisUtils.lock(lockKey, TmsConst.Cache.TTL_1H)) {
            throw new BusinessException(String.format("月付款账单报表[%s]-[%s]生成中...", dateVo.getYear(), dateVo.getMonth()));
        }
        try {
            log.info("【Bicycle payoff】开始生成[{}]-[{}]付款账单报表", dateVo.getYear(), dateVo.getMonth());
            BicyclePayoffReportSearchVo searchVo = new BicyclePayoffReportSearchVo();
            LocalDate date = LocalDate.of(dateVo.getYear(), dateVo.getMonth(), 1);
            searchVo.setStartTime(date.format(DateTimeFormatter.ofPattern(DateUtils.PATTERN_FULL_DATE)));
            searchVo.setEndTime(DateUtils.getLastDateInMonth(date).format(DateTimeFormatter.ofPattern(DateUtils.PATTERN_FULL_DATE)));
            searchVo.setPage(false);
            if (!StringUtils.isBlank(dateVo.getPayeeName())){
                searchVo.setPayeeName(dateVo.getPayeeName());
            }
            List<BicyclePayoffDayReport> list = payoffDayReportService.listPage(searchVo).getDataList();
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            //根据骑手id统计
            Map<Long, BicyclePayoffDayReport> payoffRiderMap = list.stream().collect(Collectors.toMap(BicyclePayoffDayReport::getPayeeId, Function.identity(), (b1, b2) -> {
                b1.setTotalAmount(b1.getTotalAmount().add(b2.getTotalAmount()));
                b1.setOrderNumber(b1.getOrderNumber() + b2.getOrderNumber());
                b1.setPieces(b1.getPieces() + b2.getPieces());
                return b1;
            }));
            List<BicyclePayoffMonthReport> payoffReports = payoffRiderMap.values().stream().map(p -> {
                BicyclePayoffMonthReport payoffReport = new BicyclePayoffMonthReport();
                payoffReport.setYearMonth(DateUtils.formatYearMonth(date, "yyyy-MM"));
                payoffReport.setYear(date.getYear());
                payoffReport.setMonth(date.getMonthValue());
                payoffReport.setPayeeId(p.getPayeeId());
                payoffReport.setPayeeName(p.getPayeeName());
                payoffReport.setTotalAmount(p.getTotalAmount());
                payoffReport.setCreateTime(LocalDateTime.now());
                payoffReport.setModifiedTime(LocalDateTime.now());
                payoffReport.setPieces(p.getPieces());
                payoffReport.setOrderNumber(p.getOrderNumber());
                AdminUserDto context = ContextUtils.getUserInContext(true);
                payoffReport.setCreatorId(context.getUserId());
                payoffReport.setCreatorName(context.getUsername());
                payoffReport.setModifierId(context.getUserId());
                payoffReport.setModifierName(context.getUsername());
                return payoffReport;
            }).collect(Collectors.toList());
            payoffMonthReportService.addBatchOrUpdate(date.format(DateTimeFormatter.ofPattern("yyyy-MM")), payoffReports);
        } finally {
            RedisUtils.del(lockKey);
        }
    }

    /**
     * 日报表列表
     *
     * @param searchVo 查询参数
     * @return 返回分页结果
     */
    public BicycleTotalPageResult<BicyclePayoffDayReport> reportListByDay(BicyclePayoffReportSearchVo searchVo) {
        return payoffDayReportService.listPage(searchVo);
    }

    /**
     * 月报表列表
     *
     * @param searchVo 查询参数
     * @return 返回分页结果
     */
    public BicycleTotalPageResult<BicyclePayoffMonthReport> reportListByMonth(BicyclePayoffReportSearchVo searchVo) {
        return payoffMonthReportService.listPage(searchVo);
    }

    /**
     * 更新报表
     * @param payoffDayReport 报表
     */
    public void reportUpdate(BicyclePayoffDayReport payoffDayReport) {
        if (!WrapperClassUtils.biggerThanLong(payoffDayReport.getId(), 0)) {
            return;
        }
        ContextUtils.initEntityModifier(payoffDayReport);
        payoffDayReport.setModifiedTime(LocalDateTime.now());
        payoffDayReportService.updateById(payoffDayReport);
    }
}

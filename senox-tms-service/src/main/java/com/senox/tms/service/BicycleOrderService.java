package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.tms.component.MerchantComponent;
import com.senox.tms.config.AppConfig;
import com.senox.tms.constant.*;
import com.senox.tms.convert.BicycleOrderConvert;
import com.senox.tms.convert.BicycleOrderGoodsDetailConvert;
import com.senox.tms.domain.*;
import com.senox.tms.dto.BicycleOrderImportDto;
import com.senox.tms.dto.BicycleRiderImportDto;
import com.senox.tms.event.BicycleOrderWebSocketEvent;
import com.senox.tms.event.MergedEvent;
import com.senox.tms.mapper.BicycleOrderMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import com.senox.user.constant.MerchantBillSettlePeriod;
import com.senox.user.vo.MerchantSearchVo;
import com.senox.user.vo.MerchantVo;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/18 10:22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BicycleOrderService extends ServiceImpl<BicycleOrderMapper, BicycleOrder> {

    private final AppConfig appConfig;
    private final BicycleOrderGoodsDetailService goodsDetailService;
    private final BicycleChargesService bicycleChargesService;
    private final BicycleChargesDetailService chargesDetailService;
    private final BicycleOrderMediaService bicycleOrderMediaService;
    private final ApplicationEventPublisher publisher;
    private final BicycleSettingService bicycleSettingService;
    private final BicycleBillSettlementService billSettlementService;
    private final MerchantComponent merchantComponent;
    private final BicycleOrderConvert bicycleOrderConvert;
    private final BicycleOrderGoodsDetailConvert bicycleOrderGoodsDetailConvert;
    private final BicycleRiderService riderService;

    public static final String PRE_ORDER_SERIAL_BO = "T_";


    /**
     * 添加配送订单
     * @param order
     * @param orderGoodsDetailVos
     * @param mediaUrls
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BicycleOrder addBicycleOrder(BicycleOrder order, List<BicycleOrderGoodsDetailVo> orderGoodsDetailVos, List<String> mediaUrls) {
        if (WrapperClassUtils.biggerThanLong(order.getSenderId(), 0)) {
            List<BicycleBillSettlementVo> billSettlementList = unpaidSearch(order.getSenderId(),MerchantBillSettlePeriod.AD_HOC);
            if (!CollectionUtils.isEmpty(billSettlementList)) {
                throw new BusinessException("请先结清上一单配送费");
            }
        }
        prepareOrder(order, bicycleOrderGoodsDetailConvert.toDo(orderGoodsDetailVos));
        //更新或修改
        boolean save = saveOrUpdate(order);
        if (save) {
            goodsDetailService.addBicycleOrderGoodsDetail(order.getId(), bicycleOrderGoodsDetailConvert.toDo(orderGoodsDetailVos));
            bicycleOrderMediaService.addBicycleOrderMedia(order.getId(), mediaUrls);
        }
        if (order.getState() != null && order.getState() == BicycleOrderState.FINALIZE.getNumber()){
            publisher.publishEvent(new BicycleOrderWebSocketEvent(publisher));
            autoMerged(order);
        }
        return save ? order : null;
    }

    /**
     * 订单参数初始化
     * @param order
     * @param orderGoodsDetails
     */
    private void prepareOrder(BicycleOrder order, List<BicycleOrderGoodsDetail> orderGoodsDetails) {
        //检验重复订单号
        checkOrderNo(order);
        BicycleOrder bicycleOrder = null;
        if (WrapperClassUtils.biggerThanLong(order.getId(), 0L)) {
            //更新操作，不用再次生成流水号
            bicycleOrder = findById(order.getId());
        }
        if (bicycleOrder == null && order.getCreateTime() == null) {
            order.setCreateTime(LocalDateTime.now());
        }
        if (order.getModifiedTime() == null) {
            order.setModifiedTime(LocalDateTime.now());
        }
        //计算件数
        order.setPieces(goodsDetailService.countPieces(orderGoodsDetails));
        prepareCharges(order);
        if (bicycleOrder == null && StringUtils.isBlank(order.getOrderSerialNo())) {
            //如果配送订单单不存在则生成配送订单流水号
            prepareOrderSerialNo(order);
        }

        //计算配送价格
        calculateCharge(order, orderGoodsDetails);

        //如果是正常状态则记录下单时间
        if (order.getState() != null && order.getState() == BicycleOrderState.FINALIZE.getNumber()) {
            order.setOrderTime(LocalDateTime.now());
        }
    }

    /**
     * 计算金额
     * @param order
     * @param orderGoodsDetails
     */
    public void calculateCharge(BicycleOrder order, List<BicycleOrderGoodsDetail> orderGoodsDetails) {
        log.info("预估费用：{}", JsonUtils.object2Json(orderGoodsDetails));
        if (WrapperClassUtils.biggerThanLong(order.getChargesId(), 0)) {
            order.setDeliveryCharge(bicycleChargesService.calculateEstimateCharges(order.getChargesId(), orderGoodsDetails).getTotalCharge());
            order.setTotalCharge(DecimalUtils.add(order.getDeliveryCharge(), order.getOtherCharge(), order.getUpstairsCharge(), order.getHandlingCharge()));
        }
    }

    /**
     * 计算实际金额
     * @param order
     * @param chargeMatters
     */
    public void calculateActualCharge(BicycleOrder order, List<? extends List<? extends BicycleChargeMatters>> chargeMatters) {
        log.info("实际费用：{}", JsonUtils.object2Json(chargeMatters));
        if (WrapperClassUtils.biggerThanLong(order.getChargesId(), 0) && !CollectionUtils.isEmpty(chargeMatters)) {
            order.setDeliveryCharge(bicycleChargesService.calculateActualCharges(bicycleChargesService.getById(order.getChargesId()).getMinAmount(), chargesDetailService.listByChargesId(order.getChargesId()), chargeMatters));
            order.setTotalCharge(DecimalUtils.add(order.getDeliveryCharge(), order.getOtherCharge(), order.getUpstairsCharge(), order.getHandlingCharge()));
        }
    }

    /**
     * 合并相同重量和体积的货物
     * @param goodsDetails
     * @return
     */
    public List<List<BicycleOrderGoodsDetail>> mergeGoodsDetail(List<List<BicycleOrderGoodsDetail>> goodsDetails) {
        List<List<BicycleOrderGoodsDetail>> goodsDetailList = new ArrayList<>(goodsDetails.size());
        //其他货物件数合计
        BicycleOrderGoodsDetail otherGoodsDetail = new BicycleOrderGoodsDetail();
        otherGoodsDetail.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        for (List<BicycleOrderGoodsDetail> detail : goodsDetails) {
            //其他类型件数合计
            otherGoodsDetail.setPieces(DecimalUtils.add(otherGoodsDetail.getPieces()
                    , detail.stream().filter(g -> Objects.equals(g.getGoodsType(), BicycleOrderGoodsType.OTHER.getNumber()))
                            .map(BicycleOrderGoodsDetail::getPieces).reduce(BigDecimal.ZERO, BigDecimal::add)));
            //所有非其他货物类型货物
            List<BicycleOrderGoodsDetail> orderGoodsDetails =  detail.stream()
                    //排除其他货物类型
                    .filter(g -> !Objects.equals(g.getGoodsType(), BicycleOrderGoodsType.OTHER.getNumber()))
                    //根据货物类型，体积，重量一致的
                    .collect(Collectors.groupingBy(g -> Arrays.asList(g.getGoodsType(), g.getSize(), g.getWeight())))
                    .values().stream()
                    //件数相加
                    .map(group -> group.stream().reduce((g1, g2) ->
                            new BicycleOrderGoodsDetail(g1.getGoodsType(), g1.getWeight(), g1.getSize(), DecimalUtils.add(g1.getPieces(), g2.getPieces()))))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            goodsDetailList.add(orderGoodsDetails);
        }
        if (DecimalUtils.isPositive(otherGoodsDetail.getPieces())) {
            //把其他类型的件数加上去
            goodsDetailList.add(Collections.singletonList(otherGoodsDetail));
        }
        return goodsDetailList;
    }

    /**
     * 收费标准构建
     * @param order
     */
    private void prepareCharges(BicycleOrder order) {
        if (WrapperClassUtils.biggerThanLong(order.getSenderId(), 0)) {
            MerchantVo merchant = merchantComponent.findById(order.getSenderId());
            BicycleCharges bicycleCharges = bicycleChargesService.getById(merchant.getBicycleChargesId());
            if (bicycleCharges == null) {
                //使用默认的收费标准
                order.setChargesId( bicycleChargesService.getCurrentEffectiveCharges().getId());
            } else if (BooleanUtils.isTrue(bicycleCharges.getFlexible())) {
                order.setChargesId(0L);
                return;
            } else {
                order.setChargesId(bicycleCharges.getId());
            }
        }
        //判断件数是否超过限制
        checkPieces(order.getChargesId(), order.getPieces());
    }

    /**
     * 自动合并
     * @param order
     */
    public void autoMerged(BicycleOrder order) {
        //获取自动合并开关的设置
        BicycleSetting bicycleSetting = bicycleSettingService.findByAlias(BicycleSettingType.AUTO_MERGED.getAlias());
        //超过30件不参与合并
        if (bicycleSetting != null && bicycleSetting.getEnable()
                && order.getPieces().compareTo(BigDecimal.valueOf(30)) < 0) {
            publisher.publishEvent(new MergedEvent(this, order));
        }
    }

    /**
     * 检验重复订单号
     * @param order
     */
    private void checkOrderNo(BicycleOrder order) {
        if (StringUtils.isBlank(order.getOrderSerialNo())) {
            return;
        }
        BicycleOrder bicycleOrder = findByOrderSerialNo(order.getOrderSerialNo());
        if (bicycleOrder != null) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "订单号重复，订单号为：" + order.getOrderSerialNo().replace(PRE_ORDER_SERIAL_BO, ""));
        }
    }

    /**
     * 根据订单号查询订单
     * @param orderSerialNo
     * @return
     */
    public BicycleOrder findByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return null;
        }
        return getOne(new QueryWrapper<BicycleOrder>().lambda().eq(BicycleOrder::getOrderSerialNo, orderSerialNo));
    }

    /**
     * 批量更新订单
     * @param orderList
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchOrder(List<BicycleOrder> orderList) {
        orderList.forEach(order -> {
            order.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityModifier(order);
        });
        updateBatchById(orderList);
    }

    /**
     * 更新三轮车订单状态
     * @param orderId
     * @param status
     * @param statusRemark
     * @param deliveryOrderSerialNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(Long orderId, Integer status, String statusRemark, String deliveryOrderSerialNo) {
        BicycleOrder order = new BicycleOrder();
        order.setId(orderId);
        order.setStatus(status);
        order.setStatusRemark(statusRemark);
        order.setDeliveryOrderSerialNo(deliveryOrderSerialNo);
        ContextUtils.initEntityModifier(order);
        order.setModifiedTime(LocalDateTime.now());
        updateById(order);
    }

    /**
     * 根据id查询三轮车配送订单
     * @param id
     * @return
     */
    public BicycleOrder findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据订单流水号查询订单
     * @param orderSerialNo
     * @return
     */
    public BicycleOrder findBySerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return null;
        }
        LambdaQueryWrapper<BicycleOrder> wrapper = new QueryWrapper<BicycleOrder>().lambda();
        wrapper.eq(BicycleOrder::getOrderSerialNo, orderSerialNo);
        return getOne(wrapper);
    }

    /**
     * 根据订单流水号集合查询订单
     * @param orderSerialNoList
     * @return
     */
    public List<BicycleOrder> findBySerialNoList(List<String> orderSerialNoList) {
        if (CollectionUtils.isEmpty(orderSerialNoList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BicycleOrder> wrapper = new QueryWrapper<BicycleOrder>().lambda();
        wrapper.in(BicycleOrder::getOrderSerialNo, orderSerialNoList);
        return list(wrapper);
    }

    /**
     * 根据id查询配送单及货物明细
     * @param id
     * @param containStatus
     * @return
     */
    public BicycleOrderVo findOrderVoById(Long id, Boolean containStatus) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleOrderVo bicycleOrderVo = getBaseMapper().findOrderVoById(id, containStatus);
        if (bicycleOrderVo == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "订单未找到！");
        }
        prepareMedia(Collections.singletonList(bicycleOrderVo));
        return bicycleOrderVo;
    }

    /**
     * 根据订单号查询订单
     * @param orderSerialNo
     * @return
     */
    public BicycleOrderVo findOrderVoByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            throw new BusinessException("订单号不能为空");
        }
        BicycleOrderVo bicycleOrderVo = getBaseMapper().findOrderVoByOrderSerialNo(orderSerialNo);
        if (bicycleOrderVo == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "订单未找到！");
        }
        prepareMedia(Collections.singletonList(bicycleOrderVo));
        return bicycleOrderVo;
    }

    /**
     * 删除配送订单及货物详细
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBicycleOrder(Long id, boolean checkState) {
        BicycleOrder order = findById(id);
        if (checkState && (order == null || order.getState() == BicycleOrderState.FINALIZE.getNumber())) {
            throw new BusinessException( "配送订单不存在或者不是草稿状态");
        }
        boolean result = removeById(id);
        if (result) {
            goodsDetailService.deleteBicycleOrderGoodsDetailByOrderId(id);
            bicycleOrderMediaService.deleteBicycleOrderMedia(id);
        }
    }

    /**
     * 配送订单列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderVo> listOrder(BicycleOrderSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().count(searchVo), () -> getBaseMapper().listOrder(searchVo));
    }

    /**
     * 配送订单列表视图
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderVo> listOrderView(BicycleOrderSearchVo searchVo) {
        searchVo.setPage(false);
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        Tuple2<Integer, List<BicycleOrderVo>> inDeliveryTuple = inDeliveryOrder(searchVo);
        log.info("配送中的订单,参数是:{}", JsonUtils.object2Json(searchVo));
        Tuple2<Integer, List<BicycleOrderVo>> unAssignTuple = unAssignOrder(searchVo);
        log.info("未分配的订单,参数是:{}", JsonUtils.object2Json(searchVo));
        int totalSize = unAssignTuple.getV1() + inDeliveryTuple.getV1();
        List<BicycleOrderVo> resultList = unAssignTuple.getV2();
        resultList.addAll(inDeliveryTuple.getV2());
        searchVo.prepare();
        resultList = resultList.stream().skip(searchVo.getOffset()).limit(searchVo.getPageSize()).collect(Collectors.toList());
        return PageUtils.resultPage(searchVo, totalSize, resultList);
    }

    /**
     * 未分配的订单
     * @param searchVo
     * @return
     */
    private Tuple2<Integer, List<BicycleOrderVo>> unAssignOrder(BicycleOrderSearchVo searchVo) {
        searchVo.setAssignRider(false);
        searchVo.setDelivery(null);
        searchVo.setStatus(Collections.emptyList());
        int count = getBaseMapper().count(searchVo);
        List<BicycleOrderVo> orderVos = getBaseMapper().listOrder(searchVo);
        return new Tuple2<>(count, orderVos);
    }

    /**
     * 配送中的订单
     * @param searchVo
     * @return
     */
    private Tuple2<Integer, List<BicycleOrderVo>> inDeliveryOrder(BicycleOrderSearchVo searchVo) {
        searchVo.setAssignRider(true);
        searchVo.setDelivery(true);
        int count = getBaseMapper().count(searchVo);
        List<BicycleOrderVo> orderVos = getBaseMapper().listOrder(searchVo);
        return new Tuple2<>(count, orderVos);
    }


    /**
     * 导出订单列表
     * @param searchVo
     * @return
     */
    public List<BicycleOrderVo> exportOrderList(BicycleOrderSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return Collections.emptyList();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return getBaseMapper().exportOrderList(searchVo);
    }

    /**
     * 客户每月下单统计
     * @param searchVo
     * @return
     */
    public List<BicycleCustomerMonthInfoVo> customerMonthInfoList(BicycleCustomerMonthInfoSearchVo searchVo) {
        return getBaseMapper().customerMonthInfoList(searchVo);
    }

    /**
     * 查询商户有效单数
     * @param merchantId
     * @return
     */
    public int countOrder(Long merchantId) {
        if (!WrapperClassUtils.biggerThanLong(merchantId, 0L)) {
            throw new InvalidParameterException();
        }
        return getBaseMapper().countOrder(merchantId);
    }

    /**
     * 配送订单列表
     * @param searchVo
     * @return
     */
    public List<BicycleOrderVo> list(BicycleOrderSearchVo searchVo){
        searchVo.setPage(false);
        return baseMapper.listOrder(searchVo);
    }

    /**
     * 订单合计
     * @param searchVo
     * @return
     */
    public BicycleOrderVo sumOrder(BicycleOrderSearchVo searchVo) {
        return getBaseMapper().sumOrder(searchVo);
    }

    /**
     * 订单合计
     * @param search
     * @return
     */
    public BicycleOrderVo sumOrders(BicycleOrderSearchVo search) {
        return getBaseMapper().sumOrders(search);
    }

    /**
     * 配送订单分页
     * @param search
     * @return
     */
    public PageResult<BicycleOrderVo> page(BicycleOrderSearchVo search) {
        // 合并结果集
        int totalSize = 0;
        List<BicycleOrderVo> resultList = new ArrayList<>(search.getPageNo() * search.getPageSize());

        //未分配的订单
        Tuple2<Integer, List<BicycleOrderVo>> unassignedOrders = listUnassignedOrders(search);
        totalSize += unassignedOrders.getV1();
        resultList.addAll(unassignedOrders.getV2());


        // 已分配的订单
        Tuple3<Integer, List<BicycleOrderVo>, Map<String, List<BicycleOrderVo>>> assignTuple = listAssignedOrders(search, totalSize);
        resultList.addAll(assignTuple.getV2());
        totalSize += assignTuple.getV1();

        // list skip分页
        search.prepare();
        resultList = resultList.stream()
                .skip(search.getOffset()).limit(search.getPageSize())
                .collect(Collectors.toList());

        //获取订单货物信息
        List<Long> orderIds = resultList.stream().map(BicycleOrderVo::getId).distinct().collect(Collectors.toList());
        orderIds.addAll(assignTuple.getV3().values().stream().flatMap(List::stream).map(BicycleOrderVo::getId).distinct().collect(Collectors.toList()));
        List<BicycleOrderGoodsDetail> goodsDetails = goodsDetailService.listByOrderIds(orderIds);

        //构建订单信息
        prepareOrderInfo(resultList, assignTuple.getV3(), search, goodsDetails);

        //构建媒体信息
        prepareMedia(resultList);
        //构建货物信息
        prepareGoodsDetail(resultList, goodsDetails);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    public PageResult<BicycleOrderVo> listOrderPage(BicycleOrderSearchVo search) {
        // 内存分页，查询的总记录数
        int resultSize = search.getPageNo() * search.getPageSize();
        // 未分配订单
        Tuple2<Integer, List<BicycleOrderVo>> unassignedOrders = listUnassignedOrders(search);
        log.info("Bicycle unassigned orders count : {}", unassignedOrders.get(0));

        // 已分派得订单
        Tuple2<Integer, List<BicycleOrderVo>> assignedOrders = listAssignedOrders(search, resultSize <= unassignedOrders.getV1());

        // list 内存分页
        search.prepare();
        int offset = search.getOffset();
        int pageSize = search.getPageSize();
        int totalSize = unassignedOrders.getV1() + assignedOrders.getV1();
        List<BicycleOrderVo> resultList = new ArrayList<>(search.getPageSize());

        // 未分派订单
        if (offset <= unassignedOrders.getV1()) {
            List<BicycleOrderVo> unassignedList = unassignedOrders.getV2().stream()
                    .skip(offset).limit(pageSize).collect(Collectors.toList());
            resultList.addAll(prepareUnAssignedOrderInfo(unassignedList));
            pageSize -= unassignedList.size();
        }

        // 已分派订单
        if (pageSize > 0) {
            offset = Math.max(offset - unassignedOrders.getV1(), 0);
            List<BicycleOrderVo> assignedList = assignedOrders.getV2().stream()
                    .skip(offset).limit(pageSize).collect(Collectors.toList());
            resultList.addAll(assignedList);

            if (!CollectionUtils.isEmpty(assignedList)) {
                List<String> deliverySerials = assignedList.stream()
                        .map(BicycleOrderVo::getDeliveryOrderSerialNo)
                        .filter(x -> !StringUtils.isBlank(x))
                        .distinct()
                        .collect(Collectors.toList());
                List<BicycleOrderVo> deliveryOrders = listOrdersByDeliverySerials(deliverySerials);
                Map<String, List<BicycleOrderInfoVo>> deliveryOrderMap = prepareOrderInfo(deliveryOrders);
                for (BicycleOrderVo order : assignedList) {
                    if (StringUtils.isBlank(order.getDeliveryOrderSerialNo())) {
                        continue;
                    }

                    order.setOrderInfoVos(deliveryOrderMap.get(order.getDeliveryOrderSerialNo()));
                }
            }
        }

        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 构建订单信息
     * @param resultList
     * @param resultMap
     * @param searchVo
     * @param goodsDetails
     */
    private void prepareOrderInfo(List<BicycleOrderVo> resultList, Map<String, List<BicycleOrderVo>> resultMap, BicycleOrderSearchVo searchVo, List<BicycleOrderGoodsDetail> goodsDetails) {
        //构建订单信息
        for (BicycleOrderVo item : resultList) {
            if (!CollectionUtils.isEmpty(resultMap)) {
                List<BicycleOrderVo> orderVos = resultMap.get(item.getDeliveryOrderSerialNo());
                if (!CollectionUtils.isEmpty(orderVos)) {
                    List<BicycleOrderInfoVo> infoVoList = new ArrayList<>(orderVos.size());
                    orderVos.forEach(orderVo -> {
                        BicycleOrderInfoVo infoVo = new BicycleOrderInfoVo(orderVo, searchVo);
                        infoVo.setOrderGoodsDetailVos(bicycleOrderGoodsDetailConvert.toVo(goodsDetails.stream().filter(k -> Objects.equals(k.getOrderId(), orderVo.getId())).collect(Collectors.toList())));
                        infoVoList.add(infoVo);
                    });
                    item.setOrderInfoVos(infoVoList);
                }
            }
            //如果订单信息不为空，计算合计
            if (!CollectionUtils.isEmpty(item.getOrderInfoVos())) {
                //计算总合计
                item.setPieces(item.getOrderInfoVos().stream().map(BicycleOrderInfoVo::getPieces).reduce(BigDecimal.ZERO, BigDecimal::add));
                item.setDeliveryCharge(item.getOrderInfoVos().stream().map(BicycleOrderInfoVo::getDeliveryCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
                item.setOtherCharge(item.getOrderInfoVos().stream().map(BicycleOrderInfoVo::getOtherCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
                item.setHandlingCharge(item.getOrderInfoVos().stream().map(BicycleOrderInfoVo::getHandlingCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
                item.setUpstairsCharge(item.getOrderInfoVos().stream().map(BicycleOrderInfoVo::getUpstairsCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
                item.setTotalCharge(item.getOrderInfoVos().stream().map(BicycleOrderInfoVo::getTotalCharge).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }
    }

    /**
     * 未分配的订单
     * @param search
     * @return v1：未分配总合计数，v2：未分配订单列表
     */
    public Tuple2<Integer, List<BicycleOrderVo>> listUnassignedOrders(BicycleOrderSearchVo search) {
        //未分配的订单
        BicycleOrderSearchVo cpSearch = copyBicycleOrderSearch(search);
        cpSearch.setPageSize(cpSearch.getPageNo() * cpSearch.getPageSize());
        cpSearch.setDelivery(false);

        int total = countOrderUnassigned(cpSearch);
        List<BicycleOrderVo> resultList = total < 1 ? Collections.emptyList() : listOrderUnassigned(cpSearch);
        return new Tuple2<>(total, resultList);
    }

    /**
     * 未分派订单合计
     * @param search
     * @return
     */
    private BicycleOrderVo sumOrderUnassigned(BicycleOrderSearchVo search) {
        if (BooleanUtils.isTrue(search.getDelivery()) || BooleanUtils.isTrue(search.getAssignRider())) {
            return null;
        }

        return getBaseMapper().sumOrderUnassigned(search);
    }

    /**
     * 未分派订单合计数
     * @param search
     * @return
     */
    private int countOrderUnassigned(BicycleOrderSearchVo search) {
        if (BooleanUtils.isTrue(search.getDelivery()) || BooleanUtils.isTrue(search.getAssignRider())) {
            return 0;
        }

        return getBaseMapper().countOrderUnassigned(search);
    }

    /**
     * 未分派订单列表
     * @param search
     * @return
     */
    private List<BicycleOrderVo> listOrderUnassigned(BicycleOrderSearchVo search) {
        if (BooleanUtils.isTrue(search.getDelivery()) || BooleanUtils.isTrue(search.getAssignRider())) {
            return Collections.emptyList();
        }

        return getBaseMapper().listOrderUnassigned(search);
    }

    /**
     * 已分配订单列表
     * @param search
     * @param countOnly
     * @return
     */
    public Tuple2<Integer, List<BicycleOrderVo>> listAssignedOrders(BicycleOrderSearchVo search, boolean countOnly) {
        BicycleOrderSearchVo assignedSearch = copyBicycleOrderSearch(search);
        assignedSearch.setDelivery(true);
        assignedSearch.setPageNo(1);
        assignedSearch.setPageSize(search.getPageNo() * search.getPageSize());

        // count
        int total = countOrderAssigned(assignedSearch);
        // list
        List<BicycleOrderVo> resultList = Collections.emptyList();
        if (!countOnly) {
            resultList = listOrderAssigned(assignedSearch);
        }

        return new Tuple2<>(total, resultList);
    }




    /**
     * 已分配的订单
     * @param search
     * @param totalSize 未分配的总数量
     * @return v1：已分配总合计数，v2：已分配订单列表，v3：分组的map
     */
    public Tuple3<Integer, List<BicycleOrderVo>, Map<String, List<BicycleOrderVo>>> listAssignedOrders(BicycleOrderSearchVo search, int totalSize) {
        //分配的配送单
        BicycleOrderSearchVo assignSearch = copyBicycleOrderSearch(search);
        assignSearch.setDelivery(true);
        assignSearch.setPageSize(assignSearch.getPageNo() * assignSearch.getPageSize());
        List<BicycleOrderVo> assignList = new ArrayList<>(assignSearch.getPageNo() * assignSearch.getPageSize());
        Map<String, List<BicycleOrderVo>> resultMap = new HashMap<>();
        // 如果未分配的总数小于当前页的offset，则需要查询分配的订单
        if (totalSize < search.getPageNo() * search.getPageSize()) {
            //查询当前页及之前的记录数，不计算offset
            List<String> deliveryOrderNoList = getBaseMapper().listAssignOrder(assignSearch);
            if (!CollectionUtils.isEmpty(deliveryOrderNoList)) {
                assignList = getBaseMapper().listByDeliveryOrderSerialList(deliveryOrderNoList);
            }
            if (!CollectionUtils.isEmpty(assignList)) {
                List<BicycleOrder> orderList = getBaseMapper().assignMinStatus(deliveryOrderNoList);
                Map<String, BicycleOrder> orderMap = orderList.stream().collect(Collectors.toMap(BicycleOrder::getOrderSerialNo, o -> o, (exist, replace) -> exist));

                assignList.forEach(orderVo -> {
                    BicycleOrder order = orderMap.get(orderVo.getOrderSerialNo());
                    orderVo.setStatus(order.getStatus());
                });
                //根据配送单流水号生成map
                resultMap = assignList.stream()
                        .collect(Collectors.groupingBy(BicycleOrderVo::getDeliveryOrderSerialNo));
                assignList = resultMap.values().stream().map(list -> list.stream().min(Comparator.comparingInt(BicycleOrderVo::getStatus)).orElse(null)) //去重复的配送单流水号
                        .sorted(Comparator.comparingInt(BicycleOrderVo::getStatus) //排序（状态、下单时间）
                                .thenComparing(Comparator.comparing(BicycleOrderVo::getOrderTime).reversed()))
                        .collect(Collectors.toList());
            }

        }
        int assignCount = getBaseMapper().assignCount(assignSearch);
        return new Tuple3<>(assignCount, assignList, resultMap);
    }

    /**
     * 未分派订单合计数
     * @param search
     * @return
     */
    private int countOrderAssigned(BicycleOrderSearchVo search) {
        if (BooleanUtils.isFalse(search.getDelivery())) {
            return 0;
        }

        return getBaseMapper().countOrderAssigned(search);
    }

    /**
     * 未分派订单列表
     * @param search
     * @return
     */
    private List<BicycleOrderVo> listOrderAssigned(BicycleOrderSearchVo search) {
        if (BooleanUtils.isFalse(search.getDelivery())) {
            return Collections.emptyList();
        }

        return getBaseMapper().listOrderAssigned(search);
    }

    /**
     * 根据配送单好查询订单详情
     * @param deliverySerials
     * @return
     */
    private List<BicycleOrderVo> listOrdersByDeliverySerials(List<String> deliverySerials) {
        if (CollectionUtils.isEmpty(deliverySerials)) {
            return Collections.emptyList();
        }

        return getBaseMapper().listOrdersByDeliverySerials(deliverySerials);
    }

    /**
     * 构建查询参数
     * @param searchVo
     * @return
     */
    private BicycleOrderSearchVo copyBicycleOrderSearch(BicycleOrderSearchVo searchVo) {
        BicycleOrderSearchVo search = new BicycleOrderSearchVo();
        return BeanUtils.copyProperties(searchVo, search);
    }

    private Map<String, List<BicycleOrderInfoVo>> prepareOrderInfo(List<BicycleOrderVo> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return Collections.emptyMap();
        }

        Map<String, List<BicycleOrderVo>> deliveryMap = orders.stream()
                .collect(Collectors.groupingBy(BicycleOrderVo::getDeliveryOrderSerialNo));
        Map<String, List<BicycleOrderInfoVo>> resultMap = new HashMap<>(deliveryMap.size());
        for (Map.Entry<String, List<BicycleOrderVo>> entry : deliveryMap.entrySet()) {
            List<BicycleOrderVo> deliveryOrders = entry.getValue();

            Map<String, List<BicycleOrderVo>> orderMap = deliveryOrders.stream()
                    .collect(Collectors.groupingBy(BicycleOrderVo::getOrderSerialNo));
            List<BicycleOrderInfoVo> list = new ArrayList<>(orderMap.size());
            for (Map.Entry<String, List<BicycleOrderVo>> orderEntry : orderMap.entrySet()) {
                if (CollectionUtils.isEmpty(orderEntry.getValue())) {
                    continue;
                }
                BicycleOrderInfoVo info = bicycleOrderConvert.toInfoVo(orderEntry.getValue().get(0));
                info.setDetailInfoVos(bicycleOrderConvert.toDetailInfo(orderEntry.getValue()));
                list.add(info);
            }

            resultMap.put(entry.getKey(), list);
        }
        return resultMap;
    }

    private List<BicycleOrderVo> prepareUnAssignedOrderInfo(List<BicycleOrderVo> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<BicycleOrderVo> resultList = new ArrayList<>(list.size());
        for (BicycleOrderVo item : list) {
            resultList.add(prepareUnAssignedOrderInfo(item));
        }
        return resultList;
    }

    private BicycleOrderVo prepareUnAssignedOrderInfo(BicycleOrderVo order) {
        BicycleOrderVo result = new BicycleOrderVo();
        result.setDeliveryOrderSerialNo(order.getDeliveryOrderSerialNo());
        result.setPieces(order.getPieces());
        result.setDeliveryCharge(DecimalUtils.nullToZero(order.getDeliveryCharge()));
        result.setHandlingCharge(DecimalUtils.nullToZero(order.getHandlingCharge()));
        result.setUpstairsCharge(DecimalUtils.nullToZero(order.getUpstairsCharge()));
        result.setOtherCharge(DecimalUtils.nullToZero(order.getOtherCharge()));
        result.setTotalCharge(DecimalUtils.nullToZero(order.getTotalCharge()));
        result.setOrderInfoVos(Collections.singletonList(bicycleOrderConvert.toInfoVo(order)));
        return result;
    }

    /**
     * 构建媒体信息
     * @param orderVoList
     */
    public void prepareMedia(List<BicycleOrderVo> orderVoList) {
        List<Long> orderIds = orderVoList.stream().map(BicycleOrderVo::getId).distinct().collect(Collectors.toList());
        List<BicycleOrderMedia> bicycleOrderMedia = bicycleOrderMediaService.listByOrderIds(orderIds);
        for (BicycleOrderVo orderVo : orderVoList) {
            orderVo.setMediaUrlList(bicycleOrderMedia.stream().filter(k-> Objects.equals(k.getOrderId(), orderVo.getId())).map(BicycleOrderMedia::getMediaUrl).collect(Collectors.toList()));
        }
    }

    /**
     * 构建货物信息
     * @param orderVoList
     * @param goodsDetails
     */
    public void prepareGoodsDetail(List<BicycleOrderVo> orderVoList, List<BicycleOrderGoodsDetail> goodsDetails) {
        for (BicycleOrderVo orderVo : orderVoList) {
            orderVo.setOrderGoodsDetailVos(bicycleOrderGoodsDetailConvert.toVo(goodsDetails.stream().filter(k -> Objects.equals(k.getOrderId(), orderVo.getId())).collect(Collectors.toList())));
        }
    }

    /**
     * 运营分析
     * @param startTime
     * @param endTime
     * @return
     */
    public BicycleOperateAnalysisVo findOperateAnalysis(LocalDateTime startTime, LocalDateTime endTime) {
        BicycleOperateAnalysisVo analysisVo = getBaseMapper().findOperateAnalysis(startTime, endTime);
        Integer avgSecond = getBaseMapper().avgSecond(startTime, endTime);
        if (analysisVo != null) {
            log.info("analysisVo ----- {}", JsonUtils.object2Json(analysisVo));
            analysisVo.setAvgUnitPrice(analysisVo.getTotalAmount().compareTo(BigDecimal.ZERO) == 0 && analysisVo.getTotalCount() <= 0
                    ? BigDecimal.ZERO : analysisVo.getTotalAmount().divide(BigDecimal.valueOf(analysisVo.getTotalCount()), 2, RoundingMode.HALF_UP));
            analysisVo.setAvgSecond(avgSecond == null ? 0 : avgSecond);
            return analysisVo;
        }
        return new BicycleOperateAnalysisVo();
    }

    /**
     * 查询最佳骑手
     * @param startTime
     * @param endTime
     * @return
     */
    public BicycleOperateAnalysisVo findBestRider(LocalDateTime startTime, LocalDateTime endTime) {
        return getBaseMapper().findBestRider(startTime, endTime);
    }

    /**
     * 查询最佳客户
     * @param startTime
     * @param endTime
     * @return
     */
    public BicycleOperateAnalysisVo findBestCustomer(LocalDateTime startTime, LocalDateTime endTime) {
        return getBaseMapper().findBestCustomer(startTime, endTime);
    }

    /**
     * 配送地点使用统计
     * @param isStart 查看终点还是起点
     * @return
     */
    public List<BicyclePointCountVo> listPointCount(Boolean isStart) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = LocalDateTime.of(endTime.toLocalDate(), LocalTime.MIN);
        return getBaseMapper().listPointCount(startTime, endTime, isStart);
    }

    /**
     * 排行列表
     * @param startTime
     * @param endTime
     * @return
     */
    public BicycleCountRankingVo countRanking(LocalDateTime startTime, LocalDateTime endTime) {
        return BicycleCountRankingVo.builder()
                .orderCountRankingVoList(getBaseMapper().listOrderCountRanking(startTime, endTime))
                .piecesCountRankingVoList(getBaseMapper().listPiecesCountRanking(startTime, endTime))
                .amountCountRankingVoList(getBaseMapper().listAmountCountRanking(startTime, endTime)).build();
    }

    /**
     * 未处理的订单数量
     * @return
     */
    public Integer undoOrderCount() {
        return getBaseMapper().undoOrderCount();
    }

    /**
     * 客户下单统计列表
     * @param searchVo
     * @return
     */
    public PageResult<BicycleCustomerCountVo> customerCountList(BicycleCustomerCountSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().customerCount(searchVo), () -> getBaseMapper().customerCountList(searchVo));
    }

    /**
     * 客户下单统计合计
     * @param searchVo
     * @return
     */
    public BicycleCustomerCountVo sumCustomerCount(BicycleCustomerCountSearchVo searchVo) {
        return getBaseMapper().sumCustomerCount(searchVo);
    }

    /**
     * 查询订单统计数量情况
     * @param searchVo
     * @return
     */
    public BicycleOrderCountVo orderCount(BicycleOrderCountSearchVo searchVo) {
        //未分派的订单数
        BicycleOrderCountVo orderCountVo = getBaseMapper().orderCount(searchVo);
        log.info("未分派的订单数 ----- :{}", JsonUtils.object2Json(orderCountVo));
        //分派了的订单数
        BicycleOrderCountVo deliveryOrderCount = getBaseMapper().deliveryOrderCount(searchVo);
        log.info("分派了的订单数 ----- :{}", JsonUtils.object2Json(deliveryOrderCount));
        if (deliveryOrderCount == null || deliveryOrderCount.getUndoCount() == null
                || deliveryOrderCount.getDoingCount() == null) {
            deliveryOrderCount = new BicycleOrderCountVo(0);
        }
        if (orderCountVo == null || orderCountVo.getUndoCount() == null
                || orderCountVo.getTotalCount() == null) {
            orderCountVo = new BicycleOrderCountVo(0);
        }
        deliveryOrderCount.setTotalCount(orderCountVo.getTotalCount() + deliveryOrderCount.getTotalCount());
        deliveryOrderCount.setUndoCount(orderCountVo.getUndoCount() + deliveryOrderCount.getUndoCount());
        return deliveryOrderCount;
    }

    /**
     * 订单流水号初始化
     * @param order
     */
    private void prepareOrderSerialNo(BicycleOrder order) {
        String prefix = appConfig.getOrderSerialNoPrefix().concat(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        // get serial from cache
        String cacheKey = String.format(TmsConst.Cache.KEY_BICYCLE_ORDER_SERIAL, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, TmsConst.Cache.TTL_2D);

        // compare serial from db and recorrect serial from cache
        Long dbSerial = findMaxOrderSerial(prefix);
        if (result <= dbSerial) {
            result = ++dbSerial;
            RedisUtils.set(cacheKey, result, TmsConst.Cache.TTL_2D);
        }
        String orderSerial = prefix.concat(StringUtils.fixLength(String.valueOf(result), appConfig.getOrderNoPostfixLength(), appConfig.getFillChar()));
        log.info("订单流水号号初始化完成...{}", orderSerial);
        order.setOrderSerialNo(orderSerial);
    }

    /**
     * 判断配送件数是否满足
     * @param chargesId
     * @param pieces
     */
    public void checkPieces(Long chargesId, BigDecimal pieces) {
        List<BicycleChargesDetail> chargeDetails = chargesDetailService.listByChargesId(chargesId);
        if (CollectionUtils.isEmpty(chargeDetails)) {
            throw new BusinessException("收费标准明细不存在！");
        }
        // 收费标准按最小件数升序排列
        List<BicycleChargesDetail> sortedCharges = chargeDetails.stream()
                .sorted(Comparator.comparingInt(BicycleChargesDetail::getMinCount))
                .collect(Collectors.toList());
        BicycleChargesDetail detail = sortedCharges.get(sortedCharges.size() - 1);
        if (detail.getMaxCount() == 0) {
            return;
        }
        int num = pieces.setScale(1, RoundingMode.CEILING).intValue();
        if (num > detail.getMaxCount()) {
            log.info("num:{}, maxCount:{}", num, detail.getMaxCount());
            throw new BusinessException("超出最大配送件数！");
        }
    }

    /**
     * 获取最大订单序号
     *
     * @param prefix
     * @return
     */
    private Long findMaxOrderSerial(String prefix) {
        String maxOrderNo = getBaseMapper().findMaxOrderNo(prefix);
        return (StringUtils.isBlank(maxOrderNo) ? 0L : NumberUtils.parseLong(maxOrderNo.substring(prefix.length()), 0L));
    }

    private List<BicycleBillSettlementVo> unpaidSearch(Long senderId,MerchantBillSettlePeriod settlePeriod){
        BicycleBillSettlementSearchVo searchVo = new BicycleBillSettlementSearchVo();
        searchVo.setPage(false);
        List<Long> merchantIds = new ArrayList<>();
        merchantIds.add(senderId);
        searchVo.setMerchantIds(merchantIds);
        searchVo.setStatus(false);
        searchVo.setSettlePeriod(settlePeriod.getNumber());
        return billSettlementService.list(searchVo);
    }


    /**
     * 订单幂等
     * @param orderVos 订单vo列表
     * @return 返回有效的订单列表
     */
    public List<BicycleOrderVo> orderIdempotent(List<BicycleOrderVo> orderVos) {
        LocalDateTime minOrderTime = orderVos.stream().min(Comparator.comparing(BicycleOrderVo::getOrderTime)).orElseThrow(() -> new BusinessException("order time error")).getOrderTime();
        LocalDateTime maxOrderTime = orderVos.stream().max(Comparator.comparing(BicycleOrderVo::getOrderTime)).orElseThrow(() -> new BusinessException("order time error")).getOrderTime();
        BicycleOrderSearchVo searchVo = new BicycleOrderSearchVo();
        searchVo.setOrderTimeStart(minOrderTime);
        searchVo.setOrderTimeEnd(maxOrderTime);
        searchVo.setOrigin(1);
        List<BicycleOrderVo> orderList = list(searchVo);
        List<BicycleOrderVo> validOrderVos = new ArrayList<>(orderVos.size());
        orderVos.forEach(orderVo -> {
            long size = orderList.stream().filter(o ->
                    //下单时间是否一致
                    o.getOrderTime().equals(orderVo.getOrderTime())
                            //寄件人是否一致
                            && o.getSenderId().equals(orderVo.getSenderId())
                            //起点是否一致
                            && o.getStartPointId().equals(orderVo.getStartPointId())
                            //终点是否一致
                            && o.getEndPointId().equals(orderVo.getEndPointId())
                            //总金额是否一致
                            && o.getTotalCharge().equals(orderVo.getTotalCharge())).count();
            if (size > 0) {
                log.info("【Bicycle order】排除重复订单数据: {}", orderVo);
                return;
            }
            validOrderVos.add(orderVo);
        });
        return validOrderVos;
    }

    /**
     * 订单导入dto转换成订单vo
     * @param orderImportDtoList dto列表
     * @return 返回转换后的订单vo列表
     */
    public List<BicycleOrderVo> orderImportDtoToOrder(List<BicycleOrderImportDto> orderImportDtoList) {
        List<BicycleOrderVo> auditOrderVos = new ArrayList<>(orderImportDtoList.size());
        MerchantSearchVo merchantSearchVo = new MerchantSearchVo();
        merchantSearchVo.setIds(orderImportDtoList.stream().map(BicycleOrderImportDto::getSenderId).distinct().collect(Collectors.toList()));
        List<MerchantVo> merchants = merchantComponent.list(merchantSearchVo);
        Map<Long, MerchantVo> merchantMap = merchants.stream().collect(Collectors.toMap(MerchantVo::getId, Function.identity()));
        BicycleRiderSearchVo riderSearchVo = new BicycleRiderSearchVo();
        riderSearchVo.setPage(false);
        List<BicycleRider> riderList = riderService.list(riderSearchVo).getDataList();
        for (BicycleOrderImportDto orderImportDto : orderImportDtoList) {
            BicycleOrderVo orderVo = buildOrderByDto(orderImportDto, merchantMap);
            orderImportDto.getRiders().forEach(r -> {
                BicycleRider rider = filterRiderByName(r.getRiderNo(), riderList);
                r.setRiderId(rider.getId());
            });
            orderVo.setRiders(orderImportDto.getRiders());
            auditOrderVos.add(orderVo);
        }
        return auditOrderVos;
    }

    /**
     * 根据dto构建订单vo
     * @param orderImportDto dto
     * @param merchantMap 商户集
     * @return 返回构建好的订单vo列表
     */
    private BicycleOrderVo buildOrderByDto(BicycleOrderImportDto orderImportDto, Map<Long, MerchantVo> merchantMap) {
        final String defaultValue = "未知";
        LocalDateTime orderTime = LocalDateTime.of(orderImportDto.getDate(), orderImportDto.getTime());
        LocalDateTime orderSendTime = orderTime.plusMinutes(orderImportDto.getMinutesConsumed());
        BicycleOrderVo orderVo = new BicycleOrderVo();
        orderVo.setOrderTime(orderTime);
        orderVo.setStartPointId(orderImportDto.getStartPointId());
        orderVo.setStartPointName(orderImportDto.getStartPointAddress());
        orderVo.setStartPointDetailName(orderImportDto.getStartPointDetailAddress());
        orderVo.setEndPointId(orderImportDto.getEndPointId());
        orderVo.setEndPointName(orderImportDto.getEndPointAddress());
        orderVo.setEndPointDetailName(orderImportDto.getEndPointDetailAddress());
        orderVo.setSendTimeStart(orderSendTime);
        orderVo.setSendTimeEnd(orderSendTime);
        orderVo.setSenderId(orderImportDto.getSenderId());
        orderVo.setSender(orderImportDto.getSender());
        orderVo.setSenderSerialNo(orderImportDto.getSenderSerialNo());
        orderVo.setSenderContact(orderImportDto.getSenderContact());
        orderVo.setSenderType(1);
        orderVo.setRecipient(defaultValue);
        orderVo.setRecipientContact(defaultValue);
        orderVo.setCarNo(defaultValue);
        orderVo.setRemark(orderImportDto.getRemark());
        orderVo.setState(BicycleOrderState.FINALIZE.getNumber());
        orderVo.setCreateOpenid(StringUtils.EMPTY);
        orderVo.setDeliveryCharge(orderImportDto.getCharge());
        orderVo.setOtherCharge(BigDecimal.ZERO);
        orderVo.setHandlingCharge(BigDecimal.ZERO);
        orderVo.setUpstairsCharge(BigDecimal.ZERO);
        orderVo.setTotalCharge(orderImportDto.getCharge());
        orderVo.setOrigin(1);
        orderVo.setPieces(orderImportDto.getPieces());
        orderVo.setMergeNumber(orderImportDto.getMergeNumber());
        orderVo.setRiders(orderImportDto.getRiders());
        orderVo.setChargesId(merchantMap.get(orderImportDto.getSenderId()).getBicycleChargesId());
        return orderVo;
    }

    /**
     * 订单处理
     *
     * @param orderVos 订单集
     * @return 返回构建的配送单
     */
    @Transactional(rollbackFor = Exception.class)
    public BicycleDeliveryOrderVo orderHandle(List<BicycleOrderVo> orderVos){
        BicycleDeliveryOrderVo deliveryOrderVo = buildBicycleDeliveryOrder(orderVos);
        for (BicycleOrderVo orderVo : orderVos) {
            BicycleOrder order = bicycleOrderConvert.toDo(orderVo);
            prepareOrderSerialNo(order);
            ContextUtils.initEntityCreator(order);
            ContextUtils.initEntityModifier(order);
            order.setCreateTime(LocalDateTime.now());
            order.setModifiedTime(LocalDateTime.now());
            List<List<BicycleOrderGoodsDetail>> goodsDetailList = orderVo.getRiders().stream().map(i-> bicycleOrderGoodsDetailConvert.toDo(i.getGoodsDetails())).collect(Collectors.toList());
            calculateActualCharge(order, mergeGoodsDetail(goodsDetailList));
            //添加订单和货物
            if (save(order)) {
                try {
                    List<BicycleOrderGoodsDetail> goodsDetails = orderGoodsDetailHandle(order.getId(), JsonUtils.json2List(JsonUtils.object2Json(orderVo.getRiders()), BicycleRiderImportDto.class));
                    goodsDetailService.addBicycleOrderGoodsDetail(order.getId(), goodsDetails);
                    Map<String, BicycleOrderGoodsDetail> goodsDetailMap = goodsDetails.stream().collect(Collectors.toMap(BicycleOrderGoodsDetail::getGoodsName, Function.identity()));
                    List<BicycleDeliveryOrderDetailVo> details = buildBicycleDeliveryOrderDetail(order.getOrderSerialNo(), orderVo.getRiders(), goodsDetailMap);
                    if (CollectionUtils.isEmpty(deliveryOrderVo.getDetailVos())) {
                        deliveryOrderVo.setDetailVos(details);
                    } else {
                        deliveryOrderVo.getDetailVos().addAll(details);
                    }
                    return deliveryOrderVo;
                } catch (IOException e) {
                    log.error("IO exception",e);
                }

            }
        }
        return null;
    }

    private  List<BicycleOrderGoodsDetail> orderGoodsDetailHandle(Long id, List<BicycleRiderImportDto> riders) {
        if (!WrapperClassUtils.biggerThanLong(id, 0) || CollectionUtils.isEmpty(riders)) {
            return Collections.emptyList();
        }
        Collection<BicycleOrderGoodsDetailVo> goodsDetailVos = riders
                .stream()
                .map(BicycleRiderImportDto::getGoodsDetails).flatMap(List::stream)
                .collect(Collectors.toMap(BicycleOrderGoodsDetailVo::getGoodsName, Function.identity(), (g1, g2) -> {
                    g1.setPieces(g1.getPieces().add(g2.getPieces()));
                    return g1;
                }))
                .values();
        return goodsDetailVos.stream().map(g -> {
            BicycleOrderGoodsDetail orderGoodsDetail = new BicycleOrderGoodsDetail();
            orderGoodsDetail.setGoodsName(g.getGoodsName());
            orderGoodsDetail.setPieces(g.getPieces());
            orderGoodsDetail.setWeight(g.getWeight());
            orderGoodsDetail.setSize(g.getSize());
            orderGoodsDetail.setGoodsType(g.getGoodsType());
            return orderGoodsDetail;
        }).collect(Collectors.toList());
    }

    /**
     * 订单列表
     * @param searchVo
     * @return
     */
    public List<BicycleOrder> orderList(BicycleOrderSearchVo searchVo) {
        LambdaQueryWrapper<BicycleOrder> lambda = new LambdaQueryWrapper<>();
        if (searchVo.getOrigin() != null) {
            lambda.eq(BicycleOrder::getOrigin, searchVo.getOrigin());
        }
        if (searchVo.getState() != null) {
            lambda.eq(BicycleOrder::getState, searchVo.getState());
        }
        if (searchVo.getModifiedTimeStart() != null) {
            lambda.ge(BicycleOrder::getModifiedTime, searchVo.getModifiedTimeStart());
        }
        if (searchVo.getModifiedTimeEnd() != null) {
            lambda.le(BicycleOrder::getModifiedTime, searchVo.getModifiedTimeEnd());
        }
        return list(lambda);
    }

    private BicycleRider filterRiderByName(String riderName,List<BicycleRider> riderList) {
        for (BicycleRider rider : riderList) {
            if (rider.getName().contains(riderName)) {
                return rider;
            }
        }
        throw new BusinessException(String.format("[%s]骑手不存在",riderName));
    }

    private BicycleDeliveryOrderVo buildBicycleDeliveryOrder(List<BicycleOrderVo> order) {
        BicycleDeliveryOrderVo deliveryOrderVo = new BicycleDeliveryOrderVo();
        deliveryOrderVo.setStatus(BicycleDeliveryOrderStatus.CONFIRM_COMPLETION.getNumber());
        deliveryOrderVo.setFinishTime(order.get(0).getOrderTime());
        deliveryOrderVo.setMerged(false);
        deliveryOrderVo.setDetailVos(Collections.emptyList());
        if (order.size() > 1) {
            deliveryOrderVo.setMerged(true);
        }
        return deliveryOrderVo;
    }

    private List<BicycleDeliveryOrderDetailVo> buildBicycleDeliveryOrderDetail(String orderSerialNo, List<BicycleRiderImportDto> riders, Map<String, BicycleOrderGoodsDetail> goodsDetailMap) {
        List<BicycleDeliveryOrderDetailVo> detailVos = new ArrayList<>();
        for (BicycleRiderImportDto rider : riders) {
            BicycleDeliveryOrderDetailVo detailVo = new BicycleDeliveryOrderDetailVo();
            detailVo.setRiderId(rider.getRiderId());
            detailVo.setOrderSerialNo(orderSerialNo);
            detailVo.setPickedPieces(rider.getCompletePieces());
            List<BicycleDeliveryOrderDetailItemVo> detailItemVos = buildOrderDetailItemVos(rider.getGoodsDetails(), goodsDetailMap);
            detailVo.setDetailItemVos(detailItemVos);
            detailVos.add(detailVo);
        }
        return detailVos;
    }

    @NotNull
    private static List<BicycleDeliveryOrderDetailItemVo> buildOrderDetailItemVos(List<BicycleOrderGoodsDetailVo> goodsDetails, Map<String, BicycleOrderGoodsDetail> goodsDetailMap) {
        List<BicycleDeliveryOrderDetailItemVo> detailItemVos = new ArrayList<>();
        for (BicycleOrderGoodsDetailVo goodsDetail : goodsDetails) {
            BicycleDeliveryOrderDetailItemVo itemVo = new BicycleDeliveryOrderDetailItemVo();
            itemVo.setGoodsId(goodsDetailMap.get(goodsDetail.getGoodsName()).getId());
            itemVo.setGoodsName(goodsDetail.getGoodsName());
            itemVo.setPieces(goodsDetail.getPieces());
            itemVo.setGoodsType(goodsDetail.getGoodsType());
            itemVo.setWeight(goodsDetail.getWeight());
            itemVo.setSize(goodsDetail.getSize());
            detailItemVos.add(itemVo);
        }
        return detailItemVos;
    }

    /**
     * 根据配送单号查询订单
     * @param deliveryOrderSerialNo
     * @return
     */
    public List<BicycleOrder> findByDeliveryOrderSerialNo(String deliveryOrderSerialNo) {
        if (StringUtils.isBlank(deliveryOrderSerialNo)) {
            throw new BusinessException("配送单号不能为空");
        }
        return list(new QueryWrapper<BicycleOrder>().lambda().eq(BicycleOrder::getDeliveryOrderSerialNo, deliveryOrderSerialNo));
    }

    /**
     * 订单取消配送
     * @param orderList
     */
    public void cancelDeliveryOrderSerialNo(List<BicycleOrder> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        orderList.forEach(order -> {
            order.setDeliveryOrderSerialNo(StringUtils.EMPTY);
            order.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityModifier(order);
        });
        updateBatchById(orderList);
    }

    /**
     * 三轮车配送订单数量V2
     * @param searchVo
     * @return
     */
    public int countOrderV2(BicycleOrderV2SearchVo searchVo) {
        return getBaseMapper().countOrderV2(searchVo);
    }

    /**
     * 三轮车配送订单列表V2
     * @param searchVo
     * @return
     */
    public List<BicycleOrderV2Vo> listOrderV2(BicycleOrderV2SearchVo searchVo) {
        if (searchVo.isPage()) {
            searchVo.prepare();
        }
        return getBaseMapper().listOrderV2(searchVo);
    }

    /**
     * 三轮车配送订单视图分页V2
     * @param searchVo
     * @return
     */
    public PageResult<BicycleOrderV2Vo> pageOrderViewV2(BicycleOrderV2SearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> countOrderV2(searchVo), () -> listOrderV2(searchVo));
    }

    /**
     * 三轮车配送订单分页V2
     * @param search
     * @return
     */
    public PageResult<BicycleDeliveryOrderV2Vo> pageOrderV2(BicycleOrderV2SearchVo search) {
        // 合并结果集
        int totalSize = 0;
        List<BicycleDeliveryOrderV2Vo> resultList = new ArrayList<>(search.getPageNo() * search.getPageSize());

        //未分配的订单
        Tuple2<Integer, List<BicycleDeliveryOrderV2Vo>> unassignedOrders = listUnassignedOrdersV2(search);
        totalSize += unassignedOrders.getV1();
        resultList.addAll(unassignedOrders.getV2());

        // 已分配的订单
        Tuple2<Integer, List<BicycleDeliveryOrderV2Vo>> assignTuple = listAssignedOrdersV2(search, totalSize);
        resultList.addAll(assignTuple.getV2());
        totalSize += assignTuple.getV1();

        // list skip分页
        search.prepare();
        resultList = resultList.stream()
                .skip(search.getOffset()).limit(search.getPageSize())
                .collect(Collectors.toList());
        resultList = buildDeliveryOrder(resultList);
        return PageUtils.resultPage(search, totalSize, resultList);
    }

    /**
     * 未分配的订单
     * @param search
     * @return v1：未分配总合计数，v2：未分配订单列表
     */
    public Tuple2<Integer, List<BicycleDeliveryOrderV2Vo>> listUnassignedOrdersV2(BicycleOrderV2SearchVo search) {
        //未分配的订单
        BicycleOrderV2SearchVo cpSearch = copyBicycleOrderV2Search(search);
        cpSearch.setPageSize(cpSearch.getPageNo() * cpSearch.getPageSize());
        if (BooleanUtils.isTrue(cpSearch.getDelivery())) {
            return new Tuple2<>(0, Collections.emptyList());
        }
        cpSearch.setDelivery(false);
        int total = countOrderV2(cpSearch);
        List<BicycleOrderV2Vo> orderV2VoList = total < 1 ? Collections.emptyList() : getBaseMapper().listOrderV2(cpSearch);
        List<BicycleDeliveryOrderV2Vo> resultList = new ArrayList<>(orderV2VoList.size());
        orderV2VoList.forEach(orderV2Vo -> resultList.add(new BicycleDeliveryOrderV2Vo(Collections.singletonList(orderV2Vo))));

        //合并但未分派的单
        BicycleOrderV2SearchVo cpMergedSearch = copyBicycleOrderV2Search(search);
        cpMergedSearch.setPageSize(cpMergedSearch.getPageNo() * cpMergedSearch.getPageSize());
        cpMergedSearch.setDelivery(true);
        cpMergedSearch.setAssignRider(false);
        List<BicycleDeliveryOrderV2Vo> unAssignList = new ArrayList<>(cpMergedSearch.getPageNo() * cpMergedSearch.getPageSize());
        if (total < search.getPageNo() * search.getPageSize()) {
            int unAssignCount = getBaseMapper().countPageDeliveryOrderV2(cpMergedSearch);
            total += unAssignCount;
            unAssignList = unAssignCount < 1 ? Collections.emptyList() : getBaseMapper().listPageDeliveryOrderV2(cpMergedSearch);
        }
        resultList.addAll(unAssignList);
        return new Tuple2<>(total, resultList);
    }

    /**
     * 已分配的订单
     * @param search
     * @param totalSize 未分配的总数量
     * @return v1：已分配总合计数，v2：已分配订单列表
     */
    public Tuple2<Integer, List<BicycleDeliveryOrderV2Vo>> listAssignedOrdersV2(BicycleOrderV2SearchVo search, int totalSize) {
        //分配的配送单
        BicycleOrderV2SearchVo assignSearch = copyBicycleOrderV2Search(search);
        if (assignSearch.getDelivery() != null && BooleanUtils.isFalse(assignSearch.getDelivery())) {
            return new Tuple2<>(0, Collections.emptyList());
        }
        assignSearch.setDelivery(true);
        assignSearch.setAssignRider(true);
        assignSearch.setPageSize(assignSearch.getPageNo() * assignSearch.getPageSize());
        List<BicycleDeliveryOrderV2Vo> assignList = new ArrayList<>(assignSearch.getPageNo() * assignSearch.getPageSize());
        int assignCount = getBaseMapper().countPageDeliveryOrderV2(assignSearch);
        if (totalSize < search.getPageNo() * search.getPageSize()) {
            assignList = assignCount < 1 ? Collections.emptyList() : getBaseMapper().listPageDeliveryOrderV2(assignSearch);
        }
        return new Tuple2<>(assignCount, assignList);
    }

    /**
     * 完善配送订单
     * @param resultList
     * @return
     */
    private List<BicycleDeliveryOrderV2Vo> buildDeliveryOrder(List<BicycleDeliveryOrderV2Vo> resultList) {
        List<BicycleDeliveryOrderV2Vo> filterUnDeliveryList = resultList.stream().filter(x -> !WrapperClassUtils.biggerThanLong(x.getId(), 0L)).collect(Collectors.toList());
        List<BicycleDeliveryOrderV2Vo> filterDeliveryList = resultList.stream().filter(x -> WrapperClassUtils.biggerThanLong(x.getId(), 0L)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterUnDeliveryList) && CollectionUtils.isEmpty(filterDeliveryList)) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(filterDeliveryList)) {
            return filterUnDeliveryList;
        }
        BicycleOrderV2SearchVo searchVo = new BicycleOrderV2SearchVo();
        searchVo.setPage(false);
        searchVo.setDeliveryOrderSerialNoList(filterDeliveryList.stream().map(BicycleDeliveryOrderV2Vo::getDeliveryOrderSerialNo).collect(Collectors.toList()));
        List<BicycleOrderV2Vo> orderV2Vos = getBaseMapper().listPageOrderV2(searchVo);
        List<BicycleDeliveryOrderInfoV2Vo> infoV2Vos = getBaseMapper().listDeliveryOrderInfoV2ByOrderIdList(orderV2Vos.stream().map(BicycleOrderV2Vo::getId).collect(Collectors.toList()));
        Map<Long, List<BicycleDeliveryOrderInfoV2Vo>> listMap = infoV2Vos.stream().collect(Collectors.groupingBy(BicycleDeliveryOrderInfoV2Vo::getOrderId));
        orderV2Vos.forEach(order -> {
            List<BicycleDeliveryOrderInfoV2Vo> orderInfoV2Vos = listMap.get(order.getId());
            if (!CollectionUtils.isEmpty(orderInfoV2Vos)) {
                order.setInfoV2Vos(orderInfoV2Vos);
            }
        });
        Map<String, List<BicycleOrderV2Vo>> groupMap = orderV2Vos.stream().collect(Collectors.groupingBy(BicycleOrderV2Vo::getDeliveryOrderSerialNo));
        filterDeliveryList.forEach(x -> {
            List<BicycleOrderV2Vo> list = groupMap.get(x.getDeliveryOrderSerialNo());
            if (!CollectionUtils.isEmpty(list)) {
                x.setOrderV2VoList(list);
            }
        });
        //合并
        filterUnDeliveryList.addAll(filterDeliveryList);
        return filterUnDeliveryList;
    }

    /**
     * 构建查询参数
     * @param searchVo
     * @return
     */
    private BicycleOrderV2SearchVo copyBicycleOrderV2Search(BicycleOrderV2SearchVo searchVo) {
        BicycleOrderV2SearchVo search = new BicycleOrderV2SearchVo();
        return BeanUtils.copyProperties(searchVo, search);
    }

    /**
     * 查询订单统计数量情况V2
     * @param searchVo
     * @return
     */
    public BicycleOrderCountVo orderCountV2(BicycleOrderCountSearchVo searchVo) {
        //为分派和处理中的数量
        BicycleOrderCountVo countVo = getBaseMapper().orderCountV2(searchVo);
        //今日总单数量
        BicycleOrderCountVo todayOrderCountV2 = getBaseMapper().todayOrderCountV2(searchVo);
        countVo.setTotalCount(todayOrderCountV2.getTotalCount());
        return countVo;
    }

    /**
     * 三轮车订单总计V2
     * @param searchVo
     * @return
     */
    public BicycleOrderV2Vo sumOrderV2(BicycleOrderV2SearchVo searchVo) {
        return getBaseMapper().sumOrderV2(searchVo);
    }

}

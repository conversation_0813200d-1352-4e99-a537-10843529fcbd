package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminContext;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.domain.BicycleDeliveryOrderDetail;
import com.senox.tms.domain.BicycleDeliveryOrderJob;
import com.senox.tms.event.BicycleDeliveryJobEvent;
import com.senox.tms.mapper.BicycleDeliveryOrderJobMapper;
import com.senox.tms.utils.ContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20 10:02
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BicycleDeliveryOrderJobService extends ServiceImpl<BicycleDeliveryOrderJobMapper, BicycleDeliveryOrderJob> {

    private final BicycleDeliveryOrderDetailService bicycleDeliveryOrderDetailService;
    private final BicycleDeliveryOrderJobMediaService bicycleDeliveryOrderJobMediaService;
    private final ApplicationEventPublisher eventPublisher;


    /**
     * 添加配送单任务
     * @param deliveryOrderJob
     * @param mediaUrls
     * @param rating
     * @param point 地点经纬度
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addBicycleDeliveryOrderJob(BicycleDeliveryOrderJob deliveryOrderJob, List<String> mediaUrls, Integer rating, String point) {
        log.info("【开始执行添加配送单任务。。。。】");
        BicycleDeliveryOrderJob dbJob = deliveryOrderJobByStatus(deliveryOrderJob.getDeliveryOrderDetailId(), deliveryOrderJob.getStatus());
        if (dbJob != null) {
            throw new BusinessException("配送单任务已更新，请勿重复操作！");
        }
        BicycleDeliveryOrderDetail orderDetail = bicycleDeliveryOrderDetailService.findById(deliveryOrderJob.getDeliveryOrderDetailId());
        if (orderDetail == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "配送单详细未找到");
        }
        log.info("【准备构建新的配送任务。。。。】");
        BicycleDeliveryOrderJob orderJob = null;
        Integer jobStatus = deliveryOrderJob.getStatus();
        //如果是配送中的状态，则再生成一条已揽货记录
        if (deliveryOrderJob.getStatus() == BicycleDeliveryOrderStatus.SEND.getNumber()) {
            deliveryOrderJob.setStatus(BicycleDeliveryOrderStatus.PICKED.getNumber());
            orderJob = new BicycleDeliveryOrderJob();
            orderJob.setRemark("配送中");
            orderJob.setDeliveryOrderDetailId(deliveryOrderJob.getDeliveryOrderDetailId());
            orderJob.setModifiedTime(LocalDateTime.now());
            orderJob.setStatus(BicycleDeliveryOrderStatus.SEND.getNumber());
        }
        deliveryOrderJob.setModifiedTime(LocalDateTime.now());
        log.info("【插入配送任务记录。。。。】");
        boolean save = save(deliveryOrderJob);
        if (save) {
            //保存多媒体信息
            bicycleDeliveryOrderJobMediaService.addBicycleDeliveryOrderJobMedia(deliveryOrderJob.getId(), mediaUrls);
            //三轮车订单配送任务配送更新
            eventPublisher.publishEvent(new BicycleDeliveryJobEvent(this, orderDetail, jobStatus, rating, point));
        }
        if (orderJob != null) {
            save(orderJob);
        }
        log.info("【添加配送单任务执行完成。。。。】");
        return save ? deliveryOrderJob.getId() : 0L;
    }

    /**
     * 根据配送订单详情Id及状态查询任务
     * @param deliveryDetailId
     * @param status
     * @return
     */
    private BicycleDeliveryOrderJob deliveryOrderJobByStatus(Long deliveryDetailId, Integer status) {
        if (!WrapperClassUtils.biggerThanLong(deliveryDetailId, 0L) || !WrapperClassUtils.biggerThanInt(status, 0)) {
            throw new InvalidParameterException();
        }
        return getOne(new QueryWrapper<BicycleDeliveryOrderJob>().lambda()
                .eq(BicycleDeliveryOrderJob::getDeliveryOrderDetailId, deliveryDetailId)
                .eq(BicycleDeliveryOrderJob::getStatus, status));
    }

    /**
     * 根据配送单详细查询任务
     * @param deliveryDetailId
     * @return
     */
    public List<BicycleDeliveryOrderJob> listJobByDeliveryDetailId(Long deliveryDetailId) {
        if (!WrapperClassUtils.biggerThanLong(deliveryDetailId, 0L)) {
            throw new InvalidParameterException();
        }
        return list(new QueryWrapper<BicycleDeliveryOrderJob>().lambda().eq(BicycleDeliveryOrderJob::getDeliveryOrderDetailId, deliveryDetailId));
    }

    /**
     * 根据id删除任务
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBicycleDeliveryOrderJob(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        boolean remove = removeById(id);
        if (remove) {
            bicycleDeliveryOrderJobMediaService.deleteBicycleDeliveryOrderJobMedia(id);
        }
    }
}

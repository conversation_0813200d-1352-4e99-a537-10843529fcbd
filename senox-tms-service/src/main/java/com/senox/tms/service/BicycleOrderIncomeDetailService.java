package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.StringUtils;
import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.domain.BicycleOrderIncomeDetail;
import com.senox.tms.mapper.BicycleOrderIncomeDetailMapper;
import com.senox.tms.vo.BicycleDeliveryOrderDetailVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单收益明细 服务实现
 *
 * <AUTHOR>
 * @date 2023-9-21
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class BicycleOrderIncomeDetailService extends ServiceImpl<BicycleOrderIncomeDetailMapper, BicycleOrderIncomeDetail> {
    private final BicycleSharesService sharesService;

    /**
     * 添加收益明细
     *
     * @param orderIncomeDetail 收益明细
     */
    public void add(BicycleOrderIncomeDetail orderIncomeDetail) {
        if (null == orderIncomeDetail
                || StringUtils.isBlank(orderIncomeDetail.getOrderSerialNo())
                || StringUtils.isBlank(orderIncomeDetail.getDeliveryOrderSerialNo())) {
            return;
        }
        orderIncomeDetail.setModifiedTime(LocalDateTime.now());
        save(orderIncomeDetail);
    }

    /**
     * 更新收益明细
     *
     * @param orderIncomeDetail 收益明细
     */
    public void update(BicycleOrderIncomeDetail orderIncomeDetail) {
        if (null == orderIncomeDetail
                || StringUtils.isBlank(orderIncomeDetail.getOrderSerialNo())
                || StringUtils.isBlank(orderIncomeDetail.getDeliveryOrderSerialNo())) {
            return;
        }
        LambdaUpdateWrapper<BicycleOrderIncomeDetail> lambdaUpdateWrapper = new UpdateWrapper<BicycleOrderIncomeDetail>().lambda();

        if (null != orderIncomeDetail.getTotalAmount()) {
            lambdaUpdateWrapper.set(BicycleOrderIncomeDetail::getTotalAmount, orderIncomeDetail.getTotalAmount());
        }
        if (null != orderIncomeDetail.getShareAmount()) {
            lambdaUpdateWrapper.set(BicycleOrderIncomeDetail::getShareAmount, orderIncomeDetail.getShareAmount());
        }
        if (null != orderIncomeDetail.getProfitAmount()) {
            lambdaUpdateWrapper.set(BicycleOrderIncomeDetail::getProfitAmount, orderIncomeDetail.getProfitAmount());
        }
        lambdaUpdateWrapper.set(BicycleOrderIncomeDetail::getModifiedTime, LocalDateTime.now());
        lambdaUpdateWrapper.eq(BicycleOrderIncomeDetail::getOrderSerialNo, orderIncomeDetail.getOrderSerialNo());
        lambdaUpdateWrapper.eq(BicycleOrderIncomeDetail::getDeliveryOrderSerialNo, orderIncomeDetail.getDeliveryOrderSerialNo());
        update(lambdaUpdateWrapper);
    }

    /**
     * 根据订单流水号查询订单收益明细
     *
     * @return 查询到的收益明细
     */
    public BicycleOrderIncomeDetail getByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            return null;
        }
        return getOne(new QueryWrapper<BicycleOrderIncomeDetail>()
                .lambda()
                .eq(BicycleOrderIncomeDetail::getOrderSerialNo, orderSerialNo)
        );
    }


    /**
     * 根据配送单流水号查询订单收益明细
     *
     * @return 查询到的收益明细
     */
    public BicycleOrderIncomeDetail getByDeliveryOrderSerialNo(String deliveryOrderSerialNo) {
        if (StringUtils.isBlank(deliveryOrderSerialNo)) {
            return null;
        }
        return getOne(new QueryWrapper<BicycleOrderIncomeDetail>()
                .lambda()
                .eq(BicycleOrderIncomeDetail::getDeliveryOrderSerialNo, deliveryOrderSerialNo)
        );
    }

    /**
     * 根据流水号查询订单收益明细
     * 优先查询订单，查询不到再查询配送单
     * 参数可以是{@link BicycleOrder#getOrderSerialNo()}或{@link BicycleDeliveryOrder#getDeliveryOrderSerialNo()}
     *
     * @return 查询到的收益明细
     */
    public BicycleOrderIncomeDetail getBySerialNo(String serialNo) {
        if (StringUtils.isBlank(serialNo)) {
            return null;
        }
        BicycleOrderIncomeDetail orderIncomeDetail = getByOrderSerialNo(serialNo);
        return null != orderIncomeDetail ? orderIncomeDetail : getByDeliveryOrderSerialNo(serialNo);
    }

    /**
     * 计算订单收益
     *
     * @param order                 订单号
     * @param deliveryOrderSerialNo 配送订单流水号
     */
    public BicycleOrderIncomeDetail calculateOrderIncome(BicycleOrder order, String deliveryOrderSerialNo) {
        //分佣比例
        BigDecimal riderShares = sharesService.getCurrentEffectiveShares().getRiderShares();
        //配送费
        BigDecimal deliveryCharge = order.getDeliveryCharge();
        //附加费
        BigDecimal otherCharge = order.getOtherCharge();
        //装卸费
        BigDecimal handlingCharge = order.getHandlingCharge();
        //上楼费
        BigDecimal upstairsCharge = order.getUpstairsCharge();
        //总费用 = 配送费+附加费+装卸费+上楼费
        BigDecimal totalAmount = DecimalUtils.add(deliveryCharge, otherCharge, handlingCharge, upstairsCharge);
        //分润费 = 总费用x分佣比例
        BigDecimal shareAmount = DecimalUtils.multiple(totalAmount,riderShares);

        BicycleOrderIncomeDetail orderIncomeDetail = new BicycleOrderIncomeDetail();
        orderIncomeDetail.setOrderSerialNo(order.getOrderSerialNo());
        orderIncomeDetail.setDeliveryOrderSerialNo(deliveryOrderSerialNo);
        orderIncomeDetail.setTotalAmount(totalAmount);
        orderIncomeDetail.setShareAmount(shareAmount);
        orderIncomeDetail.setProfitAmount(DecimalUtils.subtract(orderIncomeDetail.getTotalAmount(), orderIncomeDetail.getShareAmount()));
        return orderIncomeDetail;
    }

    /**
     * 添加收益明细
     * @param orderDetailVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void add(BicycleDeliveryOrderDetailVo orderDetailVo) {
        if (orderDetailVo == null
                || StringUtils.isBlank(orderDetailVo.getOrderSerialNo())
                || StringUtils.isBlank(orderDetailVo.getDeliveryOrderSerialNo())) {
            return;
        }
        BicycleOrder order = buildBicycleOrder(orderDetailVo);
        BicycleOrderIncomeDetail income = getByOrderSerialNo(order.getOrderSerialNo());
        BicycleOrderIncomeDetail incomeDetail = calculateOrderIncome(order, orderDetailVo.getDeliveryOrderSerialNo());
        if (income != null) {
            //已存在对应的收益明细 直接修改
            incomeDetail.setId(income.getId());
            incomeDetail.setModifiedTime(LocalDateTime.now());
            update(incomeDetail);
        } else {
            //否则，增加收益明细
            add(incomeDetail);
        }
    }

    /**
     * 构建三轮车订单
     * @param orderDetailVo
     * @return
     */
    private BicycleOrder buildBicycleOrder(BicycleDeliveryOrderDetailVo orderDetailVo) {
        BicycleOrder order = new BicycleOrder();
        order.setOrderSerialNo(orderDetailVo.getOrderSerialNo());
        order.setDeliveryCharge(orderDetailVo.getDeliveryCharge());
        order.setOtherCharge(orderDetailVo.getOtherCharge());
        order.setHandlingCharge(orderDetailVo.getHandlingCharge());
        order.setUpstairsCharge(orderDetailVo.getUpstairsCharge());
        return order;
    }

    /**
     * 根据订单流水号删除收益明细
     * @param orderSerialNo
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBicycleIncomeDetailByOrderSerialNo(String orderSerialNo) {
        if (StringUtils.isBlank(orderSerialNo)) {
            throw new InvalidParameterException("订单流水号为空");
        }
        BicycleOrderIncomeDetail bicycleOrderIncomeDetail = getByOrderSerialNo(orderSerialNo);
        if (bicycleOrderIncomeDetail == null) {
            log.info("【收益明细】记录不存在，{} 对应的收益明细不存在", orderSerialNo);
            return;
        }
        removeById(bicycleOrderIncomeDetail.getId());
    }
}

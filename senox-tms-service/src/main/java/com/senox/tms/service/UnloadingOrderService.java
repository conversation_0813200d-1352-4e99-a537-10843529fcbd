package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.tms.config.AppConfig;
import com.senox.tms.constant.*;
import com.senox.tms.convert.UnloadingOrderConvert;
import com.senox.tms.convert.UnloadingOrderGoodsConvert;
import com.senox.tms.convert.UnloadingOrderWorkersConvert;
import com.senox.tms.domain.UnloadingOrder;
import com.senox.tms.domain.UnloadingOrderGoods;
import com.senox.tms.domain.UnloadingOrderWorkers;
import com.senox.tms.mapper.UnloadingOrderMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/14 14:23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UnloadingOrderService extends ServiceImpl<UnloadingOrderMapper, UnloadingOrder> {

    private final AppConfig appConfig;
    private final UnloadingOrderGoodsService orderGoodsService;
    private final UnloadingOrderWorkersService orderWorkersService;
    private final UnloadingWorkerService workerService;
    private final UnloadingOrderWorkersConvert orderWorkersConvert;
    private final UnloadingOrderGoodsConvert orderGoodsConvert;
    private final UnloadingOrderConvert orderConvert;


    /**
     * 保存订单
     * @param order
     * @param goods
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveOrder(UnloadingOrder order, List<UnloadingOrderGoods> goods) {
        if (!WrapperClassUtils.biggerThanLong(order.getMerchantId(), 0L)) {
            throw new BusinessException("未知的商户！");
        }
        UnloadingOrder dbOrder = null;
        if (WrapperClassUtils.biggerThanLong(order.getId(), 0L)) {
            dbOrder = findById(order.getId());
        }
        //prepare orderNo
        if (dbOrder == null && StringUtils.isBlank(order.getOrderNo())) {
            log.info("【装卸订单】---订单编号开始生成---");
            prepareOrderNo(order);
            ContextUtils.initEntityCreator(order);
            order.setCreateTime(LocalDateTime.now());
        }
        //订单相关时间初始化
        prepareOrder(order);
        ContextUtils.initEntityModifier(order);
        order.setModifiedTime(LocalDateTime.now());
        boolean result = saveOrUpdate(order);
        if (result) {
            //add goods
            orderGoodsService.saveGoods(order.getId(), goods);
        }
    }

    /**
     * 订单相关时间初始化
     * @param order
     */
    private static void prepareOrder(UnloadingOrder order) {
        if (order.getSupplement() == null
                || (UnloadingOrderState.NORMAL.getNumber() == order.getState() && BooleanUtils.isFalse(order.getSupplement()))) {
            order.setOrderTime(LocalDateTime.now());
        }
        //是预约单但是没有预约时间段
        if (BooleanUtils.isTrue(order.getReservationOrder()) && order.getReservationTime() == null) {
            throw new BusinessException("未知的预约时间！");
        }
        if (BooleanUtils.isFalse(order.getReservationOrder())) {
            order.setReservationTime(order.getOrderTime());
        }
    }

    /**
     * 删除订单
     * @param id
     */
    public void deleteOrder(Long id) {
        UnloadingOrder unloadingOrder = findById(id);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (unloadingOrder.getWorkerStatus() == UnloadingOrderWorkerStatus.TRANSPORTING.getNumber()
                || unloadingOrder.getWorkerStatus() == UnloadingOrderWorkerStatus.DONE.getNumber()) {
            throw new BusinessException("订单已经分配或已完成");
        }
        removeById(id);
    }

    /**
     * 取消订单
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(Long id) {
        UnloadingOrder unloadingOrder = findById(id);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (unloadingOrder.getWorkerStatus() != UnloadingOrderWorkerStatus.TRANSPORTING.getNumber()) {
            throw new BusinessException("订单是分配中的才能取消！");
        }
        unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.INIT.getNumber());
        UnloadingOrderVo detail = findDetailById(id);
        if (detail == null || CollectionUtils.isEmpty(detail.getWorkersVoList())) {
            throw new BusinessException("未查询到该订单的分派信息！");
        }
        workerService.updateBatchWorkerStatus(detail.getWorkersVoList().stream().map(UnloadingOrderWorkersVo::getWorkerId).collect(Collectors.toList())
                , UnloadingWorkerStatus.ALREADY_LISTED.getNumber());
        orderWorkersService.saveWorkers(id, Collections.emptyList());
    }

    /**
     * 根据id查询订单
     * @param id
     * @return
     */
    public UnloadingOrder findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据orderNo查询订单
     * @param orderNo
     * @return
     */
    public UnloadingOrder findByOrderNo(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return null;
        }
        return getOne(new QueryWrapper<UnloadingOrder>().lambda().eq(UnloadingOrder::getOrderNo, orderNo));
    }

    /**
     * 订单列表
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderVo> listOrder(UnloadOrderSearchVo searchVo) {
        return getBaseMapper().listOrder(searchVo);
    }

    /**
     * 订单合计
     * @param searchVo
     * @return
     */
    public UnloadingOrderVo sumOrder(UnloadOrderSearchVo searchVo) {
        return getBaseMapper().sumOrder(searchVo);
    }

    /**
     * 订单分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderVo> pageResult(UnloadOrderSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countOrder(searchVo), () -> getBaseMapper().listOrder(searchVo));
    }

    /**
     * 订单详细分页
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingOrderVo> pageDetailResult(UnloadOrderSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> getBaseMapper().countOrder(searchVo), () -> {
            List<UnloadingOrderVo> orderVos = getBaseMapper().listOrder(searchVo);
            List<UnloadingOrderWorkers> workersList = orderWorkersService.listByOrderIds(orderVos.stream().map(UnloadingOrderVo::getId).collect(Collectors.toList()));
            List<UnloadingOrderGoods> goodsList = orderGoodsService.listByOrderIds(orderVos.stream().map(UnloadingOrderVo::getId).collect(Collectors.toList()));
            Map<Long, List<UnloadingOrderWorkers>> workersMap = workersList.stream().collect(Collectors.groupingBy(UnloadingOrderWorkers::getOrderId));
            Map<Long, List<UnloadingOrderGoods>> goodsMap = goodsList.stream().collect(Collectors.groupingBy(UnloadingOrderGoods::getOrderId));
            orderVos.forEach(order -> {
                List<UnloadingOrderWorkers> workers = workersMap.get(order.getId());
                List<UnloadingOrderGoods> goods = goodsMap.get(order.getId());
                order.setWorkersVoList(orderWorkersConvert.toVo(workers));
                order.setGoodsVoList(orderGoodsConvert.toVo(goods));
            });
            return orderVos;
        });
    }

    /**
     * 根据id查询订单详细
     * @param id
     * @return
     */
    public UnloadingOrderVo findDetailById(Long id) {
        return getBaseMapper().findDetailById(id);
    }

    /**
     * 批量更新订单应付金额
     * @param orderVos
     */
    public void updateBatchOrderPayoffAmount(List<UnloadingOrderVo> orderVos) {
        List<UnloadingOrder> orders = orderConvert.toDo(orderVos);
        orders.forEach(order -> {
            ContextUtils.initEntityModifier(order);
            order.setModifiedTime(LocalDateTime.now());
        });
        updateBatchById(orders);
    }

    /**
     * 指定搬运工人数
     * @param orderId
     * @param workerNum
     */
    public void appointWorkerNum(Long orderId, Integer workerNum) {
        UnloadingOrder unloadingOrder = findById(orderId);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "装卸订单未找到！");
        }
        unloadingOrder.setWorkerNum(workerNum);
        ContextUtils.initEntityModifier(unloadingOrder);
        unloadingOrder.setModifiedTime(LocalDateTime.now());
        updateById(unloadingOrder);
    }

    /**
     * 分派搬运工
     * @param orderId
     * @param workers
     */
    @Transactional(rollbackFor = Exception.class)
    public void assignWorkers(Long orderId, List<UnloadingOrderWorkers> workers) {
        UnloadingOrder unloadingOrder = findById(orderId);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "装卸订单未找到！");
        }
        unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.TRANSPORTING.getNumber());
        unloadingOrder.setWorkTime(LocalDateTime.now());
        if (CollectionUtils.isEmpty(workers)) {
            unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.INIT.getNumber());
            unloadingOrder.setWorkTime(null);
        }
        unloadingOrder.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(unloadingOrder);
        updateById(unloadingOrder);
        orderWorkersService.saveWorkers(orderId, workers);
        workerService.updateBatchWorkerStatus(workers.stream().map(UnloadingOrderWorkers::getWorkerId).collect(Collectors.toList())
                , UnloadingWorkerStatus.DURING_TRANSPORTATION.getNumber());
    }

    /**
     * 完成订单
     * @param orderId
     * @param amount
     */
    @Transactional(rollbackFor = Exception.class)
    public void finishOrder(Long orderId, BigDecimal amount) {
        UnloadingOrder unloadingOrder = findById(orderId);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        unloadingOrder.setAmount(amount);
        unloadingOrder.setTotalAmount(DecimalUtils.add(amount, DecimalUtils.nullToZero(unloadingOrder.getUrgentAmount())));
        unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.DONE.getNumber());
        unloadingOrder.setFinishTime(LocalDateTime.now());
        unloadingOrder.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(unloadingOrder);
        updateById(unloadingOrder);
        List<UnloadingOrderWorkers> workersList = orderWorkersService.listByOrderId(orderId);
        workersList.forEach(worker ->
            workerService.updateWorkerStatusByWorkerNo(worker.getWorkerNo(), UnloadingWorkerStatus.NOT_LISTED.getNumber())
        );
    }

    /**
     * 新增加急费
     * @param orderId
     * @param urgentAmount
     */
    public void addUrgentAmount(Long orderId, BigDecimal urgentAmount) {
        UnloadingOrder unloadingOrder = findById(orderId);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        unloadingOrder.setUrgentAmount(urgentAmount);
        unloadingOrder.setUrgentOrder(true);
        unloadingOrder.setTotalAmount(DecimalUtils.add(urgentAmount, DecimalUtils.nullToZero(unloadingOrder.getAmount())));
        unloadingOrder.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(unloadingOrder);
        if (!DecimalUtils.isPositive(urgentAmount)) {
            unloadingOrder.setUrgentOrder(false);
        }
        updateById(unloadingOrder);
    }

    /**
     * 更新订单金额
     * @param orderNo
     * @param amount
     */
    public void updateOrderAmount(String orderNo, BigDecimal amount) {
        UnloadingOrder order = findByOrderNo(orderNo);
        if (order == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "订单未找到！");
        }
        order.setAmount(amount);
        order.setTotalAmount(DecimalUtils.add(amount, DecimalUtils.nullToZero(order.getUrgentAmount())));
        ContextUtils.initEntityModifier(order);
        order.setModifiedTime(LocalDateTime.now());
        updateById(order);
    }

    /**
     * 订单编号初始化
     * @param order
     */
    private void prepareOrderNo(UnloadingOrder order) {
        String prefix = appConfig.getUnloadingOrderNoPrefix().concat(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        // get serial from cache
        String cacheKey = String.format(TmsConst.Cache.KEY_UNLOADING_ORDER_NO, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, TmsConst.Cache.TTL_2D);

        // compare serial from db and recorrect serial from cache
        Long dbSerial = findMaxOrderNo(prefix);
        if (result <= dbSerial) {
            result = ++dbSerial;
            RedisUtils.set(cacheKey, result, TmsConst.Cache.TTL_2D);
        }
        String orderNo = prefix.concat(StringUtils.fixLength(String.valueOf(result), appConfig.getUnloadingOrderNoPostfixLength(), appConfig.getFillChar()));
        log.info("装卸订单编号初始化完成...{}", orderNo);
        order.setOrderNo(orderNo);
    }

    /**
     * 获取最大订单序号
     * @param prefix
     * @return
     */
    private Long findMaxOrderNo(String prefix) {
        String maxOrderNo = getBaseMapper().findMaxOrderNo(prefix);
        return (StringUtils.isBlank(maxOrderNo) ? 0L : NumberUtils.parseLong(maxOrderNo.substring(prefix.length()), 0L));
    }

    /**
     * 补录订单
     * @param order
     * @param goods
     * @param workers
     * @return 返回的订单号
     */
    @Transactional(rollbackFor = Exception.class)
    public String supplementOrder(UnloadingOrder order, List<UnloadingOrderGoods> goods, List<UnloadingOrderWorkers> workers) {
        //添加订单
        saveOrder(order, goods);
        //分派搬运工
        supplement(order.getId(), workers);
        return order.getOrderNo();
    }

    /**
     * 补录搬运工信息
     * @param id
     * @param workers
     */
    private void supplement(Long id, List<UnloadingOrderWorkers> workers) {
        UnloadingOrder unloadingOrder = findById(id);
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "装卸订单未找到！");
        }
        unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.DONE.getNumber());
        unloadingOrder.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(unloadingOrder);
        updateById(unloadingOrder);
        orderWorkersService.saveWorkers(id, workers);
    }

    /**
     * 订单顺序分派
     * @param assignVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void sequenceAssignWorkers(UnloadingOrderWorkersAssignVo assignVo) {
        //更新订单状态
        UnloadingOrder unloadingOrder = findById(assignVo.getOrderId());
        if (unloadingOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "装卸订单未找到！");
        }
        unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.TRANSPORTING.getNumber());
        unloadingOrder.setWorkTime(LocalDateTime.now());
        unloadingOrder.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(unloadingOrder);
        if (CollectionUtils.isEmpty(assignVo.getAssignList())) {
            unloadingOrder.setWorkerStatus(UnloadingOrderWorkerStatus.INIT.getNumber());
        }
        updateById(unloadingOrder);
        List<UnloadingOrderWorkers> srcOrderWorkers = orderWorkersConvert.toDo(assignVo.getAssignList());
        log.info("【本次派单的搬运工】:{}", JsonUtils.object2Json(srcOrderWorkers));
        List<UnloadingOrderWorkers> workersList = orderWorkersService.listByOrderId(assignVo.getOrderId());
        log.info("【该订单之前有的搬运工】:{}", JsonUtils.object2Json(workersList));
        if (!CollectionUtils.isEmpty(workersList)) {
            //过滤已经分派过的搬运工
            assignVo.setAssignList(assignVo.getAssignList().stream().filter(x-> workersList.stream()
                    .noneMatch(y-> Objects.equals(x.getWorkerNo(), y.getWorkerNo()))).collect(Collectors.toList()));
        }
        //返回本次新增的搬运工信息
        List<UnloadingOrderWorkersVo> workersVos = workerService.assignWorkers(assignVo);
        List<UnloadingOrderWorkers> workers = orderWorkersConvert.toDo(workersVos);
        log.info("【该订单本次新增的搬运工】:{}", JsonUtils.object2Json(workers));
        //过滤到本次新增的搬运工、以及减少的搬运工
        workers.addAll(workersList.stream().filter(x -> srcOrderWorkers.stream().anyMatch(y -> Objects.equals(x.getWorkerNo(), y.getWorkerNo()))).collect(Collectors.toList()));
        log.info("该订单所有的搬运工：{}", JsonUtils.object2Json(workers));
        orderWorkersService.compareOrderWorkers(assignVo.getOrderId(), workers);
    }

    /**
     * 运营分析
     * @param startTime
     * @param endTime
     * @return
     */
    public UnloadingOperateAnalysisVo findOperateAnalysis(LocalDateTime startTime, LocalDateTime endTime) {
        UnloadingOperateAnalysisVo analysis = getBaseMapper().findOperateAnalysis(startTime, endTime);
        int totalQuantity = getBaseMapper().totalQuantity(startTime, endTime);
        analysis.setAvgUnitPrice(DecimalUtils.equals(BigDecimal.ZERO, BigDecimal.valueOf(analysis.getTotalCount()))
                ? BigDecimal.ZERO : analysis.getTotalAmount().divide(BigDecimal.valueOf(analysis.getTotalCount()), 2, RoundingMode.HALF_UP));
        analysis.setTotalQuantity(totalQuantity);
        return analysis;
    }

    /**
     * 排行列表
     * @param startTime
     * @param endTime
     * @return
     */
    public UnloadingCountRankingVo countRanking(LocalDateTime startTime, LocalDateTime endTime) {
        return UnloadingCountRankingVo.builder()
                .orderCountRankingVoList(getBaseMapper().listOrderCountRanking(startTime, endTime))
                .quantityCountRankingVoList(getBaseMapper().listQuantityCountRanking(startTime, endTime))
                .amountCountRankingVoList(getBaseMapper().listAmountCountRanking(startTime, endTime)).build();
    }

    private void buildStatisticsSearchUnitDay(UnloadingStatisticSearchVo searchVo) {
        searchVo.setSearchTypeFormat("%Y-%m-%d %H:00:00");
        searchVo.setSearchType("HOUR");
        StringBuilder stringBuilder = new StringBuilder("SELECT 0 AS number");
        long hours = Duration.between(searchVo.getStartTime(), searchVo.getEndTime()).toHours();
        for (long l = 1; l < hours; l++) {
            stringBuilder.append(" UNION ALL SELECT ").append(l);
        }
        searchVo.setSearchResultFormat(stringBuilder.toString());
    }

    private void buildStatisticsSearchUnitMonth(UnloadingStatisticSearchVo searchVo) {
        searchVo.setSearchTypeFormat("%Y-%m-%d");
        searchVo.setSearchType("DAY");
        StringBuilder stringBuilder = new StringBuilder("SELECT 0 AS number");
        int days = Period.between(searchVo.getStartTime().toLocalDate(), searchVo.getEndTime().toLocalDate()).getDays();
        for (int i = 0; i < days; i++) {
            stringBuilder.append(" UNION ALL SELECT ").append(i + 1);
        }
        searchVo.setSearchResultFormat(stringBuilder.toString());
    }

    private void buildStatisticsSearchUnitYear(UnloadingStatisticSearchVo searchVo) {
        searchVo.setSearchTypeFormat("%Y-%m-01");
        searchVo.setSearchType("MONTH");
        StringBuilder stringBuilder = new StringBuilder("SELECT 0 AS number");
        long months =  ChronoUnit.MONTHS.between(YearMonth.from(searchVo.getStartTime()), YearMonth.from(searchVo.getEndTime()));
        for (long l = 0; l < months; l++) {
            stringBuilder.append(" UNION ALL SELECT ").append(l + 1);
        }
        searchVo.setSearchResultFormat(stringBuilder.toString());
    }

    /**
     * 订单统计
     * @param searchVo
     * @return
     */
    public List<UnloadingOrderCountVo> listCountList(UnloadingStatisticSearchVo searchVo) {
        if (Objects.equals(BicycleStatisticsSearchUnit.DAY.getNumber(), searchVo.getSearchUnit())) {
            buildStatisticsSearchUnitDay(searchVo);
        } else if (Objects.equals(BicycleStatisticsSearchUnit.MONTH.getNumber(), searchVo.getSearchUnit())) {
            buildStatisticsSearchUnitMonth(searchVo);
        } else if (Objects.equals(BicycleStatisticsSearchUnit.YEAR.getNumber(), searchVo.getSearchUnit())) {
            buildStatisticsSearchUnitYear(searchVo);
        } else {
            throw new BusinessException("搜索单位有误！");
        }

        return getBaseMapper().listCountList(searchVo);
    }

    /**
     * 查询该时间后，搬运工关联的最新一个订单id
     * @param startTime
     * @param workerId
     * @return
     */
    public Long lastOrderByWorkerId(LocalDateTime startTime, Long workerId) {
        return getBaseMapper().lastOrderByWorkerId(startTime, workerId);
    }
}

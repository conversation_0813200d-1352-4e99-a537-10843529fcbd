package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.tms.domain.BicycleManager;
import com.senox.tms.mapper.BicycleManagerMapper;
import com.senox.tms.utils.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/27 10:27
 */
@Service
@Slf4j
public class BicycleManagerService extends ServiceImpl<BicycleManagerMapper, BicycleManager> {

    /**
     * 添加三轮车管理员
     * @param bicycleManager
     */
    public void addBicycleManager(BicycleManager bicycleManager) {
        ContextUtils.initEntityCreator(bicycleManager);
        bicycleManager.setCreateTime(LocalDateTime.now());
        bicycleManager.setModifiedTime(LocalDateTime.now());
        save(bicycleManager);
    }

    /**
     * 更新三轮车管理员
     * @param bicycleManager
     */
    public void updateBicycleManager(BicycleManager bicycleManager) {
        BicycleManager manager = findByAdminUserId(bicycleManager.getAdminUserId());
        //如果管理员id绑定的管理员不存在，则新增一个
        if (manager == null) {
            addBicycleManager(bicycleManager);
            return;
        }
        bicycleManager.setModifiedTime(LocalDateTime.now());
        LambdaUpdateWrapper<BicycleManager> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(BicycleManager::getAdminUserId, bicycleManager.getAdminUserId());
        update(bicycleManager, wrapper);
    }

    /**
     * 根据id获取三轮车管理员
     * @param id
     * @return
     */
    public BicycleManager findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 根据管理员id查询三轮车管理员
     * @param adminUserId
     * @return
     */
    public BicycleManager findByAdminUserId(Long adminUserId) {
        if (!WrapperClassUtils.biggerThanLong(adminUserId, 0L)) {
            throw new InvalidParameterException();
        }
        return getOne(new QueryWrapper<BicycleManager>().lambda().eq(BicycleManager::getAdminUserId, adminUserId));
    }

    /**
     * 根据状态查询三轮车管理员
     * @param status
     * @return
     */
    public List<BicycleManager> listBicycleManagerByStatus(Integer status) {
        if (status == null) {
            throw new InvalidParameterException();
        }
        return list(new QueryWrapper<BicycleManager>().lambda().eq(BicycleManager::getStatus, status));
    }
}

package com.senox.tms.service;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.constant.SystemParam;
import com.senox.common.domain.SystemSetting;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.service.SystemSettingService;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminUserDto;
import com.senox.tms.config.AppConfig;
import com.senox.tms.config.UnloadingConfig;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.constant.UnloadingVersion;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.domain.UnloadingAttendance;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.domain.UnloadingWorkerAccess;
import com.senox.tms.domain.UnloadingWorkerLog;
import com.senox.tms.event.UnloadingListedEvent;
import com.senox.tms.event.UnloadingWebSocketEvent;
import com.senox.tms.mapper.UnloadingWorkerMapper;
import com.senox.tms.utils.CheckResult;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/13 13:40
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UnloadingWorkerService extends ServiceImpl<UnloadingWorkerMapper, UnloadingWorker> {

    private final UnloadingAttendanceService attendanceService;
    private final AppConfig appConfig;
    private final UnloadingWorkerAccessService workerAccessService;
    private final ApplicationEventPublisher publisher;
    private final UnloadingConfig unloadingConfig;
    private final SystemSettingService systemSettingService;
    private final UnloadingWorkerLogService workerLogService;

    /**
     * 新增搬运工
     * @param worker
     */
    public void addWorker(UnloadingWorker worker) {
        if (isWorkerNoExists(null, worker.getWorkerNo())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "搬运工已存在");
        }
        //补全搬运工编号
        worker.setWorkerNo(StringUtils.fixLength(worker.getWorkerNo(), 2, '0'));
        if (StringUtils.isBlank(worker.getWorkerSign())) {
            log.info("【装卸搬运工标识】---搬运工标识开始生成---");
            prepareWorkerSign(worker);
        }
        worker.setStatus(UnloadingWorkerStatus.NOT_LISTED.getNumber());
        worker.setOrderNum(0);
        worker.setCreateTime(LocalDateTime.now());
        worker.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityCreator(worker);
        ContextUtils.initEntityModifier(worker);
        save(worker);
    }

    /**
     * 更新搬运工信息，不更新状态和人脸
     * @param worker
     */
    public void updateWorker(UnloadingWorker worker) {
        if (!WrapperClassUtils.biggerThanLong(worker.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        if (!StringUtils.isEmpty(worker.getWorkerNo())
                && isWorkerNoExists(worker.getId(), worker.getWorkerNo())) {
            throw new BusinessException(ResultConst.DUPLICATE_ERROR, "员工已存在");
        }
        if (!StringUtils.isBlank(worker.getWorkerNo())) {
            //补全搬运工编号
            worker.setWorkerNo(StringUtils.fixLength(worker.getWorkerNo(), 2, '0'));
        }
        ContextUtils.initEntityModifier(worker);
        worker.setModifiedTime(LocalDateTime.now());
        worker.setOrderNum(null);
        worker.setStatus(null);
        worker.setFaceUrl(null);
        worker.setWorkerSign(null);
        updateById(worker);
    }

    /**
     * 保存搬运工
     * @param worker
     */
    public void saveWorker(UnloadingWorker worker) {
        if (!WrapperClassUtils.biggerThanLong(worker.getId(), 0L)) {
            addWorker(worker);
        } else {
            updateWorker(worker);
        }
    }

    /**
     * 批量更新搬运工状态
     * @param ids
     * @param status
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchWorkerStatus(List<Long> ids, Integer status) {
        for (Long id : ids) {
            updateWorkerStatus(id, status);
        }
        publisher.publishEvent(new UnloadingWebSocketEvent(this));
    }

    /**
     * 更新搬运工状态
     * @param id
     * @param status
     */
    public void updateWorkerStatus(Long id, Integer status) {
        UnloadingWorker dbWorker = findById(id);
        if (dbWorker == null) {
            log.info("【搬运工】---搬运工未找到。。id为:{}", id);
            return;
        }
        updateStatus(status, dbWorker, true, true);
    }

    /**
     * 根据搬运工编号更新搬运工状态
     * @param workerNo
     * @param status
     */
    public void updateWorkerStatusByWorkerNo(String workerNo, Integer status) {
        UnloadingWorker worker = findByWorkerNo(workerNo);
        if (worker == null) {
            log.info("【搬运工】---搬运工未找到。。编号为:{}", workerNo);
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "该搬运工未存在！");
        }
        updateStatus(status, worker, false, true);
    }

    /**
     * 根据标识更新搬运工状态
     * @param workerSign
     * @param status
     * @param publish
     */
    public void updateWorkerStatus(String workerSign, Integer status, boolean publish) {
        UnloadingWorker dbWorker = findByWorkerSign(workerSign);
        if (dbWorker == null) {
            log.info("【搬运工】---搬运工未找到。。标识为:{}", workerSign);
            return;
        }
        updateStatus(status, dbWorker, false, publish);
    }

    /**
     * 批量更新搬运工状态
     * @param workerSigns
     * @param status
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchWorkerStatusBySigns(List<String> workerSigns, Integer status, boolean publish) {
        for (String workerSign : workerSigns) {
            updateWorkerStatus(workerSign, status, publish);
        }
        publisher.publishEvent(new UnloadingWebSocketEvent(this));
    }

    /**
     * 更新状态
     * @param status
     * @param dbWorker
     * @param statusValid
     * @param publish 重复发布
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Integer status, UnloadingWorker dbWorker, boolean statusValid, boolean publish) {
        if (Objects.equals(status, dbWorker.getStatus())) {
            log.info("原状态 {} 更新的状态 {}， 状态一致， 忽略", dbWorker.getStatus(), status);
            return;
        }
        if (!statusValid && Objects.equals(status, UnloadingWorkerStatus.ALREADY_LISTED.getNumber())
                && Objects.equals(dbWorker.getStatus(), UnloadingWorkerStatus.DURING_TRANSPORTATION.getNumber())) {
            log.info("未处于未挂牌的情况，不允许打卡，忽略，现状态：{}", dbWorker.getStatus());
            return;
        }
        UnloadingWorker worker = new UnloadingWorker();
        worker.setId(dbWorker.getId());
        worker.setStatus(status);
        if (UnloadingWorkerStatus.NOT_LISTED.getNumber() == status) {
            worker.setOrderNum(0);
            if (statusValid) {
                //管理操作下牌后，自动挂牌取消
                worker.setListing(Boolean.TRUE);
            }
        } else if (UnloadingWorkerStatus.ALREADY_LISTED.getNumber() == status) {
            SystemSetting setting = systemSettingService.findByParam(SystemParam.UNLOADING_VERSION);
            if (setting == null) {
                throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "未查询到系统参数！");
            }
            if (UnloadingVersion.VERSION_1.getName().equalsIgnoreCase(setting.getValue())) {
                checkListingTimeValid();
                //find max order
                worker.setOrderNum(getBaseMapper().getMaxOrderNum() + 2);
                //只要挂牌了就开启自动挂牌
                worker.setListing(Boolean.FALSE);
            }
        }
        ContextUtils.initEntityModifier(worker);
        worker.setModifiedTime(LocalDateTime.now());
        if (updateById(worker)) {
            UnloadingWorkerStatus workerStatus = UnloadingWorkerStatus.fromStatus(status);
            UnloadingAttendance attendance = new UnloadingAttendance();
            attendance.setWorkerId(worker.getId());
            attendance.setWorkerNo(dbWorker.getWorkerNo());
            attendance.setWorkerName(dbWorker.getName());
            attendance.setRemark(workerStatus == null ? StringUtils.EMPTY : workerStatus.getName());
            attendanceService.addAttendance(attendance);
        }
        publisher.publishEvent(new UnloadingWebSocketEvent(this));
        //手工下牌后，重新挂牌影响与其他搬运工挂牌
        if (publish && BooleanUtils.isFalse(dbWorker.getListing())
                && UnloadingWorkerStatus.ALREADY_LISTED.getNumber() == status) {
            publisher.publishEvent(new UnloadingListedEvent(this, worker.getId()));
        }
    }

    /**
     * 根据标识查询搬运工
     * @param workerSign
     * @return
     */
    public UnloadingWorker findByWorkerSign(String workerSign) {
        if (StringUtils.isBlank(workerSign)) {
            throw new InvalidParameterException();
        }
        return getOne(new QueryWrapper<UnloadingWorker>().lambda()
                .eq(UnloadingWorker::getWorkerSign, workerSign)
                .eq(UnloadingWorker::getDisabled, Boolean.FALSE));
    }

    /**
     * 根据搬运工编号查询搬运工
     * @param workerNo
     * @return
     */
    public UnloadingWorker findByWorkerNo(String workerNo) {
        if (StringUtils.isBlank(workerNo)) {
            return null;
        }
        return getOne(new QueryWrapper<UnloadingWorker>().lambda().eq(UnloadingWorker::getWorkerNo, workerNo));
    }

    /**
     * 根据id查询搬运工
     * @param id
     * @return
     */
    public UnloadingWorker findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 搬运工请假
     * @param workerVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void workerLeave(UnloadingWorkerVo workerVo) {
        UnloadingWorker worker = findById(workerVo.getId());
        if (worker == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "搬运工未找到");
        }
        worker.setStatus(UnloadingWorkerStatus.WORK_LEAVE.getNumber());
        worker.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(worker);
        updateById(worker);
        UnloadingWorkerLog workerLog = new UnloadingWorkerLog(worker, workerVo.getRemark());
        workerLogService.batchAddWorkerLog(Collections.singletonList(workerLog));
    }

    /**
     * 删除搬运工
     * @param workerId
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteWorker(Long workerId) {
        if (!WrapperClassUtils.biggerThanLong(workerId, 0L)) {
            throw new InvalidParameterException();
        }
        UnloadingWorker worker = findById(workerId);
        if (worker == null) {
            log.info("【逻辑删除测试】搬运工未找到，id为：{}", workerId);
            return;
        }

        log.info("【逻辑删除测试】开始删除搬运工，id={}，原名字={}，原disabled={}", workerId, worker.getName(), worker.getDisabled());

        // 清空搬运工权限
        workerAccessService.addWorkerAccess(worker, Collections.emptyList());

        // 逻辑删除：设置禁用标志并修改可能有唯一约束的字段避免冲突
        AdminUserDto adminUser = ContextUtils.getUserInContext();
        worker.setModifierId(adminUser.getUserId());
        worker.setModifierName(adminUser.getUsername());
        worker.setModifiedTime(LocalDateTime.now());
        worker.setDisabled(Boolean.TRUE);
        // 修改员工名字避免重复显示
        String originalName = worker.getName();
        if (!StringUtils.isBlank(worker.getName())) {
            String suffix = "_DEL_" + worker.getId();
            worker.setName(worker.getName().concat(suffix));
        }
        updateById(worker);

        log.info("【逻辑删除测试】搬运工删除完成，id={}，原名字={}，新名字={}，新disabled={}", workerId, originalName, worker.getName(), worker.getDisabled());
    }

    /**
     * 只更新人脸
     * @param faceUrlVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFaceUrl(UnloadingWorkerFaceUrlVo faceUrlVo) {
        UnloadingWorker worker = new UnloadingWorker();
        worker.setId(faceUrlVo.getId());
        worker.setFaceUrl(faceUrlVo.getFaceUrl());
        ContextUtils.initEntityModifier(worker);
        worker.setModifiedTime(LocalDateTime.now());
        updateById(worker);
        List<UnloadingWorkerAccess> workerAccesses = workerAccessService.accessListByWorkerIds(Collections.singletonList(faceUrlVo.getId()));
        if (!CollectionUtils.isEmpty(workerAccesses)) {
            workerAccessService.accessRight(getById(faceUrlVo.getId()), workerAccesses, Boolean.FALSE);
        }
    }

    /**
     * 指定位置排序
     * @param id
     * @param num
     */
    @Transactional(rollbackFor = Exception.class)
    public void appointResetOrderNum(Long id, Integer num) {
        if (!WrapperClassUtils.biggerThanInt(num, 0)) {
            throw new InvalidParameterException("指定排序的位置不正确");
        }
        UnloadingWorker dbWorker = findById(id);
        if (dbWorker == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "搬运工不存在");
        }
        List<UnloadingWorker> workerList = getUnloadingWorkerList(id);
        if (num > workerList.size()) {
            throw new BusinessException("指定的序号超出挂牌人数！");
        }
        UnloadingWorker worker = workerList.get(num - 1);
        //重新排序
        resetOrderNum(id, worker.getId());
    }

    /**
     * 搬运工排序置底
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void bottomUp(Long id) {
        List<UnloadingWorker> workerList = getUnloadingWorkerList(id);
        UnloadingWorker worker = workerList.get(workerList.size() - 1);
        resetOrderNum(id, worker.getId());
    }

    /**
     * 获取搬运工排序信息
     * @param id
     * @return
     */
    private List<UnloadingWorker> getUnloadingWorkerList(Long id) {
        UnloadingWorker dbWorker = findById(id);
        if (dbWorker == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "搬运工不存在");
        }
        UnloadingWorkerSearchVo searchVo = new UnloadingWorkerSearchVo();
        searchVo.setPage(false);
        searchVo.setStatusList(Collections.singletonList(UnloadingWorkerStatus.ALREADY_LISTED.getNumber()));
        return listWorker(searchVo);
    }

    /**
     * 重新排序
     * @param id
     * @param targetId
     */
    @Transactional(rollbackFor = Exception.class)
    public void resetOrderNum(Long id, Long targetId) {
        UnloadingWorker dbWorker = findById(id);
        UnloadingWorker targetWorker = findById(targetId);
        if (dbWorker == null || targetWorker == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        resetOrderNum(dbWorker, targetWorker);
    }

    /**
     * 重新排序
     * @param dbWorker
     * @param targetWorker
     */
    @Transactional(rollbackFor = Exception.class)
    public void resetOrderNum(UnloadingWorker dbWorker, UnloadingWorker targetWorker) {
        if (dbWorker.getStatus() != UnloadingWorkerStatus.ALREADY_LISTED.getNumber()
                || targetWorker.getStatus() != UnloadingWorkerStatus.ALREADY_LISTED.getNumber()) {
            throw new BusinessException("只能交换已挂牌的顺序！");
        }
        int srcOrderNum = dbWorker.getOrderNum();
        int targetOrderNum = targetWorker.getOrderNum();
        if (srcOrderNum == targetOrderNum) {
            throw new BusinessException("你已在当前位置了！");
        }
        dbWorker.setOrderNum(targetOrderNum);
        targetWorker.setOrderNum(targetOrderNum + 2);
        if (targetOrderNum > srcOrderNum) {
            //下移
            log.info("【正在执行下移操作】");
            dbWorker.setOrderNum(targetOrderNum + 2);
            targetWorker.setOrderNum(targetOrderNum);
        }
        dbWorker.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(dbWorker);
        targetWorker.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(targetWorker);
        updateBatchById(Lists.newArrayList(dbWorker, targetWorker));
        updateBatchOrderNum(dbWorker.getId(), targetWorker.getId(), srcOrderNum, targetOrderNum);
    }

    /**
     * 批量更新排序号
     * @param srcId
     * @param targetId
     * @param srcOrderNum
     * @param targetOrderNum
     */
    private void updateBatchOrderNum(Long srcId, Long targetId, Integer srcOrderNum, Integer targetOrderNum) {
        //取两个排序的最小值和最大值做筛选
        List<UnloadingWorker> workerList;
        if (srcOrderNum > targetOrderNum) {
            //上移
            workerList = getBaseMapper().filterListWorker(targetOrderNum, srcOrderNum);
        } else {
            //下移
            workerList = getBaseMapper().filterListWorker(targetOrderNum, null);
        }
        List<UnloadingWorker> filterWorkerList = workerList.stream().filter(worker -> !Objects.equals(worker.getId(), srcId)
                && !Objects.equals(worker.getId(), targetId)).collect(Collectors.toList());
        log.info("filterWorkerList :{}", JsonUtils.object2Json(filterWorkerList));
        filterWorkerList.forEach(worker -> {
            worker.setOrderNum(worker.getOrderNum() + 2);
            worker.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityModifier(worker);
        });
        updateBatchById(filterWorkerList);
        publisher.publishEvent(new UnloadingWebSocketEvent(this));
    }

    /**
     * 只更新班次
     * @param ids
     * @param classes
     */
    public void updateBatchClasses(List<Long> ids, Integer classes) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        update(new UpdateWrapper<UnloadingWorker>().lambda()
                .set(UnloadingWorker::getClasses, classes)
                .set(UnloadingWorker::getModifiedTime, LocalDateTime.now())
                .set(UnloadingWorker::getModifierId, ContextUtils.getUserInContext().getUserId())
                .in(UnloadingWorker::getId, ids));
    }

    /**
     * 根据id集合查询搬运工
     * @param ids
     * @return
     */
    public List<UnloadingWorker> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return list(new QueryWrapper<UnloadingWorker>().lambda().in(UnloadingWorker::getId, ids));
    }

    /**
     * 搬运工数量
     * @param searchVo
     * @return
     */
    public int countWorker(UnloadingWorkerSearchVo searchVo) {
        return getBaseMapper().countWorker(searchVo);
    }

    /**
     * 搬运工列表
     * @param searchVo
     * @return
     */
    public List<UnloadingWorker> listWorker(UnloadingWorkerSearchVo searchVo) {
        return getBaseMapper().listWorker(searchVo);
    }

    /**
     * 搬运工分页列表
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingWorker> pageWorker(UnloadingWorkerSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> countWorker(searchVo), () -> {
            List<UnloadingWorker> workerList = listWorker(searchVo);

            UnloadingWorkerSearchVo newSearchVo = copyUnloadingWorkerSearchVo(searchVo);
            newSearchVo.setPage(false);
            newSearchVo.setStatusList(Collections.singletonList(UnloadingWorkerStatus.ALREADY_LISTED.getNumber()));
            newSearchVo.setRows(true);
            newSearchVo.setClasses(null);
            List<UnloadingWorker> workers = listWorker(newSearchVo);
            Map<Long, UnloadingWorker> groupMap = workers.stream()
                    .collect(Collectors.toMap(UnloadingWorker::getId, Function.identity()));

            workerList.forEach(worker -> {
                UnloadingWorker item = groupMap.get(worker.getId());
                worker.setRowNum(item != null ? item.getRowNum() : 0);
            });

            return workerList;
        });
    }

    private UnloadingWorkerSearchVo copyUnloadingWorkerSearchVo(UnloadingWorkerSearchVo searchVo) {
        UnloadingWorkerSearchVo newSearchVo = new UnloadingWorkerSearchVo();
        return BeanUtils.copyProperties(searchVo, newSearchVo);
    }

    /**
     * 搬运工标识初始化
     * @param worker
     */
    private void prepareWorkerSign(UnloadingWorker worker) {
        String prefix = appConfig.getUnloadingWorkerSignPrefix().concat(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        // get serial from cache
        String cacheKey = String.format(TmsConst.Cache.KEY_UNLOADING_WORKER_SIGN, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, TmsConst.Cache.TTL_2D);

        // compare serial from db and recorrect serial from cache
        Long dbSerial = findMaxWorkerSign(prefix);
        if (result <= dbSerial) {
            result = ++dbSerial;
            RedisUtils.set(cacheKey, result, TmsConst.Cache.TTL_2D);
        }
        String workerSign = prefix.concat(StringUtils.fixLength(String.valueOf(result), appConfig.getUnloadingOrderNoPostfixLength(), appConfig.getFillChar()));
        log.info("装卸搬运工标识初始化完成...{}", workerSign);
        worker.setWorkerSign(workerSign);
    }

    /**
     * 获取最大搬运工标识
     * @param prefix
     * @return
     */
    private Long findMaxWorkerSign(String prefix) {
        String maxWorkerSign = getBaseMapper().findMaxWorkerSign(prefix);
        return (StringUtils.isBlank(maxWorkerSign) ? 0L : NumberUtils.parseLong(maxWorkerSign.substring(prefix.length()), 0L));
    }

    /**
     * 校验挂牌有效时间
     */
    private void checkListingTimeValid() {
        Integer start = null;
        Integer end = null;
        try {
            start = unloadingConfig.getNoListingTime().get("start");
            end = unloadingConfig.getNoListingTime().get("end");
            log.info("start:{}, end:{}", start, end);
        } catch (Exception e) {
            throw new BusinessException("获取配置时间失败！");
        }
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = LocalDateTime.of(now.getYear(), now.getMonthValue(), now.getDayOfMonth(), start, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(now.getYear(), now.getMonthValue(), now.getDayOfMonth(), end, 0, 0);
        log.info("【startTime】:{}  【endTime】:{}", startTime, endTime);
        // 判断当前时间是否在 startTime 和 endTime 之间，包括边界
        if ((now.isAfter(startTime) || now.isEqual(startTime)) &&
                (now.isBefore(endTime) || now.isEqual(endTime))) {
            throw new BusinessException(start + "点 - " + end + "点不允许打卡！");
        }
    }

    /**
     * 搬运工是否存在
     * @param id
     * @param workerNo
     * @return
     */
    private boolean isWorkerNoExists(Long id, String workerNo) {

        UnloadingWorker worker = findByWorkerNo(workerNo);
        if (worker == null) {
            return false;
        }
        return !WrapperClassUtils.biggerThanLong(id, 0L) || !Objects.equals(id, worker.getId());
    }

    /**
     * 根据搬运工编号集合查询搬运工
     * @param workerNoList
     * @return
     */
    public List<UnloadingWorker> findByWorkerNoList(List<String> workerNoList) {
        if (CollectionUtils.isEmpty(workerNoList)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<UnloadingWorker>().in(UnloadingWorker::getWorkerNo, workerNoList));
    }

    /**
     * 派单轮次改变
     * @param assignVo
     * @return
     */
    public List<UnloadingOrderWorkersVo> assignWorkers(UnloadingOrderWorkersAssignVo assignVo) {
        if (CollectionUtils.isEmpty(assignVo.getAssignList())) {
            log.info("未选择派工的人员，订单还原最初未分配的状态");
            return Collections.emptyList();
        }
        List<UnloadingOrderWorkersVo> orderWorkers = new ArrayList<>(assignVo.getAssignList().size());
        Map<String, UnloadingOrderWorkersVo> workersMap = assignVo.getAssignList().stream().collect(Collectors.toMap(UnloadingOrderWorkersVo::getWorkerNo, x ->x));
        List<UnloadingWorker> assignWorkers = limitWorkerList(assignVo.getAssignList().size());
        log.info("排序最新的搬运工：{}", JsonUtils.object2Json(assignWorkers));
        List<UnloadingWorker> assignWorkerList = findByWorkerNoList(assignVo.getAssignList().stream().map(UnloadingOrderWorkersVo::getWorkerNo).collect(Collectors.toList()));
        log.info("要分派的搬运工：{}", JsonUtils.object2Json(assignWorkerList));
        List<UnloadingWorker> addRoundsWorkers = new ArrayList<>(assignVo.getAssignList().size());
        SystemSetting setting = systemSettingService.findByParam(SystemParam.UNLOADING_CURRENT_ROUND);
        if (setting == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "未查询到系统参数！");
        }
        int currentRound = Integer.parseInt(setting.getValue());
        log.info("setting：{} 当前轮次：{}", JsonUtils.object2Json(setting), currentRound);
        UnloadingWorker lastWorker = getBaseMapper().findLastWorker(currentRound);
        log.info("{}轮次的的最大搬运工:{}", currentRound, JsonUtils.object2Json(lastWorker));
        //全部正好满足排序的
        if (!CollectionUtils.isEmpty(assignVo.getAssignList()) && new HashSet<>(assignWorkers.stream().map(UnloadingWorker::getWorkerNo).collect(Collectors.toList()))
                .containsAll(assignVo.getAssignList().stream().map(UnloadingOrderWorkersVo::getWorkerNo).collect(Collectors.toList()))) {
            //处理分配的搬运工
            assignHandle(assignWorkerList, currentRound, addRoundsWorkers, workersMap, orderWorkers);
            //处理惩罚的搬运工
            punishHandle(assignVo, currentRound);
            //处理休假的搬运工
            leaveHandle(assignVo, Collections.emptyList());
        } else {
            //查总共传过来的数量 workers（全部的）
            List<UnloadingWorker> workers = limitWorkerList(assignVo.getAssignList().size() + assignVo.getBusyList().size() + assignVo.getPunishList().size() + assignVo.getLeaveList().size());
            log.info("【总参与的搬运工信息】:{}", JsonUtils.object2Json(workers));
            //处理惩罚的搬运工
            punishHandle(assignVo, currentRound);
            //处理休假的搬运工
            leaveHandle(assignVo, workers);
            //处理繁忙的搬运工
            busyHandle(assignVo, workers);
            //处理分配的搬运工
            assignHandle(assignVo, assignWorkerList, workers, workersMap, currentRound, addRoundsWorkers, orderWorkers);
        }
        //系统轮次增加
        systemRoundPlus(addRoundsWorkers, lastWorker, setting, currentRound);
        return orderWorkers;
    }

    /**
     *
     * @param assignWorkerList 分派的搬运工信息
     * @param currentRound 系统当前轮次
     * @param addRoundsWorkers 增加轮次的搬运工
     * @param workersMap 分派的搬运工信息生成的Map
     * @param orderWorkers 实际返回的搬运工信息
     */
    private void assignHandle(List<UnloadingWorker> assignWorkerList, int currentRound, List<UnloadingWorker> addRoundsWorkers, Map<String, UnloadingOrderWorkersVo> workersMap, List<UnloadingOrderWorkersVo> orderWorkers) {
        //每个轮次等于当前轮次+1
        log.info("【搬运工正好满足选择的。。。】");
        for (UnloadingWorker worker : assignWorkerList) {
            worker.setStatus(UnloadingWorkerStatus.DURING_TRANSPORTATION.getNumber());
            worker.setRoundNum(currentRound > worker.getRoundNum() ? currentRound + 1 : worker.getRoundNum() + 1);
            worker.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityModifier(worker);
            //正常加轮次的搬运工
            addRoundsWorkers.add(worker);
            UnloadingOrderWorkersVo workersVo = workersMap.get(worker.getWorkerNo());
            workersVo.setFlag(true);
            orderWorkers.add(workersVo);
        }
        updateBatchById(assignWorkerList);
    }

    /**
     * 处理分配的搬运工
     * @param assignVo 搬运工信息
     * @param assignWorkerList 分派的搬运工信息
     * @param workers 当前排序前的搬运工
     * @param workersMap 分派的搬运工信息生成的Map
     * @param currentRound 系统当前轮次
     * @param addRoundsWorkers 增加轮次的搬运工
     * @param orderWorkers 实际返回的搬运工信息
     */
    private void assignHandle(UnloadingOrderWorkersAssignVo assignVo, List<UnloadingWorker> assignWorkerList, List<UnloadingWorker> workers
            , Map<String, UnloadingOrderWorkersVo> workersMap, int currentRound, List<UnloadingWorker> addRoundsWorkers, List<UnloadingOrderWorkersVo> orderWorkers) {
        if (!CollectionUtils.isEmpty(assignVo.getAssignList())) {

            for (UnloadingWorker worker : assignWorkerList) {
                CheckResult checkResult = checkPreviousWorkers(workers, worker.getWorkerNo());
                UnloadingOrderWorkersVo workersVo = workersMap.get(worker.getWorkerNo());

                // 默认设置 flag 为 true（加轮次），除非满足特定条件
                boolean shouldAddRound = true;
                String logMessage = "【前面既没有休假也没有忙碌的 加轮次】";

                if (checkResult.isHasBoth()) {
                    // 前面又有休假又有忙碌 不加轮次
                    logMessage = "【前面又有休假又有忙碌 不加轮次】";
                    shouldAddRound = false;
                } else if (checkResult.isHasOnLeave()) {
                    // 前面有休假 加轮次
                    logMessage = "【前面有休假 加轮次】";
                } else if (checkResult.isHasBusy()) {
                    // 前面有忙碌 不加轮次
                    logMessage = "【前面有忙碌 不加轮次】";
                    shouldAddRound = false;
                }

                log.info(logMessage);
                if (shouldAddRound) {
                    worker.setRoundNum(currentRound > worker.getRoundNum() ? currentRound + 1 : worker.getRoundNum() + 1);
                    addRoundsWorkers.add(worker);
                }
                workersVo.setFlag(shouldAddRound);
                orderWorkers.add(workersVo);

                // 更新 worker 状态
                worker.setStatus(UnloadingWorkerStatus.DURING_TRANSPORTATION.getNumber());
                worker.setModifiedTime(LocalDateTime.now());
                ContextUtils.initEntityModifier(worker);
            }

            log.info("【总派工的搬运工，及变化后的轮次】:{}", JsonUtils.object2Json(assignWorkerList));
            updateBatchById(assignWorkerList);
        }
    }

    /**
     * 系统轮次增加
     * @param addRoundsWorkers 增加轮次的搬运工
     * @param lastWorker 本轮次最后一个搬运工
     * @param setting 系统设置
     * @param currentRound 系统当前轮次
     */
    private void systemRoundPlus(List<UnloadingWorker> addRoundsWorkers, UnloadingWorker lastWorker, SystemSetting setting, int currentRound) {
        UnloadingWorker maxRoundWorker = addRoundsWorkers.stream().min((o1, o2) -> Integer.parseInt(o2.getWorkerNo()) - Integer.parseInt(o1.getWorkerNo())).orElse(null);
        log.info("正常分派的最大搬运工:{}", JsonUtils.object2Json(maxRoundWorker));
        if (maxRoundWorker != null && Objects.equals(maxRoundWorker.getWorkerNo(), lastWorker.getWorkerNo())) {
            //如果轮次是最大的工号，当前总轮次+1，之前的罚轮次都-1
            setting.setValue(Integer.toString(currentRound + 1));
            setting.setModifiedTime(LocalDateTime.now());
            systemSettingService.saveOrUpdate(setting);
            log.info("【系統轮次+1】");
            List<UnloadingWorker> listedPunishWorker = getBaseMapper().listPunishWorker();
            for (UnloadingWorker worker : listedPunishWorker) {
                worker.setPunishRoundNum(worker.getPunishRoundNum() - 1);
                worker.setModifiedTime(LocalDateTime.now());
                ContextUtils.initEntityModifier(worker);
            }
            updateBatchById(listedPunishWorker);
            log.info("【惩罚轮次-1】");
        }
    }

    /**
     * 处理繁忙的搬运工
     * @param assignVo 搬运工信息
     * @param workers 当前排序前的搬运工
     */
    private void busyHandle(UnloadingOrderWorkersAssignVo assignVo, List<UnloadingWorker> workers) {
        //处理繁忙的
        if (!CollectionUtils.isEmpty(assignVo.getBusyList())) {
            List<UnloadingWorker> busyWorkerList = findByWorkerNoList(assignVo.getBusyList().stream().map(UnloadingOrderWorkersVo::getWorkerNo).collect(Collectors.toList()));
            log.info("【忙碌的搬运工】:{}", JsonUtils.object2Json(busyWorkerList));
            List<UnloadingWorkerLog> workerLogs = new ArrayList<>(assignVo.getLeaveList().size());
            //设为忙碌
            for (UnloadingWorker worker : workers) {
                if (busyWorkerList.stream().anyMatch(x-> Objects.equals(x.getWorkerNo(), worker.getWorkerNo()))) {
                    worker.setBusy(true);
                }
            }
            assignVo.getBusyList().forEach(workersVo -> workerLogs.add(new UnloadingWorkerLog(workersVo)));
            log.info("【添加搬运工请假日志】:{}", JsonUtils.object2Json(workerLogs));
            workerLogService.batchAddWorkerLog(workerLogs);
        }
    }

    /**
     * 处理休假的搬运工
     * @param assignVo 搬运工信息
     * @param workers 当前排序前的搬运工
     */
    private void leaveHandle(UnloadingOrderWorkersAssignVo assignVo, List<UnloadingWorker> workers) {
        //处理休假的
        if (!CollectionUtils.isEmpty(assignVo.getLeaveList())) {
            List<UnloadingWorker> leaveWorkerList = findByWorkerNoList(assignVo.getLeaveList().stream().map(UnloadingOrderWorkersVo::getWorkerNo).collect(Collectors.toList()));
            log.info("【休假的搬运工】:{}", JsonUtils.object2Json(leaveWorkerList));
            List<UnloadingWorkerLog> workerLogs = new ArrayList<>(assignVo.getLeaveList().size());
            //设为休假
            for (UnloadingWorker worker : workers) {
                if (leaveWorkerList.stream().anyMatch(x-> Objects.equals(x.getWorkerNo(), worker.getWorkerNo()))) {
                    worker.setStatus(UnloadingWorkerStatus.WORK_LEAVE.getNumber());
                }
            }
            for (UnloadingWorker worker : leaveWorkerList) {
                worker.setStatus(UnloadingWorkerStatus.WORK_LEAVE.getNumber());
                worker.setModifiedTime(LocalDateTime.now());
                ContextUtils.initEntityModifier(worker);
            }
            assignVo.getLeaveList().forEach(workersVo -> workerLogs.add(new UnloadingWorkerLog(workersVo)));
            log.info("【添加搬运工请假日志】:{}", JsonUtils.object2Json(workerLogs));
            workerLogService.batchAddWorkerLog(workerLogs);
            updateBatchById(leaveWorkerList);
        }
    }

    /**
     * 处理惩罚的搬运工
     * @param assignVo 搬运工信息
     * @param currentRound 当前系统轮次
     */
    private void punishHandle(UnloadingOrderWorkersAssignVo assignVo, int currentRound) {
        //先处理惩罚的，给轮次+1
        if (!CollectionUtils.isEmpty(assignVo.getPunishList())) {
            List<UnloadingWorker> punishWorkerList = findByWorkerNoList(assignVo.getPunishList().stream().map(UnloadingOrderWorkersVo::getWorkerNo).collect(Collectors.toList()));
            log.info("【惩罚的搬运工】:{}", JsonUtils.object2Json(punishWorkerList));
            List<UnloadingWorkerLog> workerLogs = new ArrayList<>(assignVo.getPunishList().size());
            for (UnloadingWorker worker : punishWorkerList) {
                worker.setRoundNum(currentRound + 2);
                worker.setPunishRoundNum(worker.getPunishRoundNum() + 2);
                worker.setModifiedTime(LocalDateTime.now());
                ContextUtils.initEntityModifier(worker);
            }
            updateBatchById(punishWorkerList);
            assignVo.getPunishList().forEach(workersVo -> workerLogs.add(new UnloadingWorkerLog(workersVo)));
            log.info("【添加搬运工惩罚日志】:{}", JsonUtils.object2Json(workerLogs));
            workerLogService.batchAddWorkerLog(workerLogs);
        }
    }

    /**
     * 检查指定搬运工前面的状态
     *
     * @param workers 搬运工列表
     * @param workerNo 需要检查的搬运工编号
     * @return 包含三种判断结果的对象
     */
    public CheckResult checkPreviousWorkers(List<UnloadingWorker> workers, String workerNo) {
        boolean hasBusy = false; // 是否遇到过忙碌的搬运工
        boolean hasOnLeave = false; // 是否遇到过请假的搬运工

        // 遍历搬运工列表，找到需要检查的搬运工
        for (UnloadingWorker worker : workers) {
            if (Objects.equals(worker.getWorkerNo(), workerNo)) {
                // 找到目标搬运工，停止遍历
                break;
            }
            // 检查前面的搬运工是否忙碌或请假
            if (worker.isBusy()) {
                hasBusy = true;
            }
            if (worker.getStatus() == UnloadingWorkerStatus.WORK_LEAVE.getNumber()) {
                hasOnLeave = true;
            }
        }

        // 返回包含三种判断结果的对象
        return new CheckResult(hasBusy, hasOnLeave, hasBusy && hasOnLeave);
    }

    /**
     * 获取最近指定个数搬运工
     * @param limit
     * @return
     */
    private List<UnloadingWorker> limitWorkerList(int limit) {
        UnloadingWorkerSearchVo searchVo = new UnloadingWorkerSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(limit);
        searchVo.setStatusList(Lists.newArrayList(UnloadingWorkerStatus.NOT_LISTED.getNumber(), UnloadingWorkerStatus.ALREADY_LISTED.getNumber(), UnloadingWorkerStatus.DURING_TRANSPORTATION.getNumber()));
        return getBaseMapper().listSequenceWorker(searchVo);
    }

    /**
     * 搬运工顺序列表
     * @param searchVo
     * @return
     */
    public List<UnloadingWorker> listSequenceWorker(UnloadingWorkerSearchVo searchVo) {
        return getBaseMapper().listSequenceWorker(searchVo);
    }

    /**
     * 搬运工信息列表
     * @return
     */
    public UnloadingDayCountWorkerVo dayCountWorker() {
        return getBaseMapper().dayCountWorker();
    }

    /**
     * 根据订单id查询该单参与的搬运工
     * @param orderId
     * @return
     */
    public List<UnloadingWorker> listWorkerByOrderId(Long orderId) {
        return getBaseMapper().listWorkerByOrderId(orderId);
    }
}

package com.senox.tms;

import com.senox.common.spring.SenoxBaseConfigure;
import com.senox.common.spring.SenoxWebConfigure;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

/**
 * <AUTHOR>
 * @date 2023/9/12 9:09
 */

@Import({SenoxBaseConfigure.class, SenoxWebConfigure.class})
@SpringBootApplication
public class TmsApplication {

    public static void main(String[] args) {
        SpringApplication.run(TmsApplication.class, args);
    }
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@TableName("t_dict_logistic")
public class DictLogistic extends TableIdEntity {

    /**
     * key
     */
    @TableField("`key`")
    private String key;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private Integer category;

    /**
     * 保留字段1
     */
    private String attr1;

    /**
     * 保留字段2
     */
    private String attr2;

    /**
     * 排序
     */
    private Long sort;
}

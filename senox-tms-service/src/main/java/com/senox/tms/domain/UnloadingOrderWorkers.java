package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/20 16:44
 */
@TableName("px_unloading_order_workers")
@Getter
@Setter
public class UnloadingOrderWorkers extends TableIdEntity {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 搬运工id
     */
    private Long workerId;

    /**
     * 搬运工编号
     */
    private String workerNo;

    /**
     * 搬运工姓名
     */
    private String workerName;

    /**
     * 状态 0：非正常轮 1：正常轮
     */
    private Boolean flag;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnloadingOrderWorkers workers = (UnloadingOrderWorkers) o;
        return Objects.equals(getOrderId(), workers.getOrderId()) && Objects.equals(getWorkerId(), workers.getWorkerId()) && Objects.equals(getWorkerNo(), workers.getWorkerNo());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getOrderId(), getWorkerId(), getWorkerNo());
    }
}

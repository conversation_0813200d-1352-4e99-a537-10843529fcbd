package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.OperateEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-10-13
 */
@Getter
@Setter
@TableName("t_bicycle_payoff_day_report")
public class BicyclePayoffDayReport extends OperateEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 年月日
     */
    private String yearMonthDay;

    /**
     * 年
     */
    private Integer year;

    /**
     * 月
     */
    private Integer month;

    /**
     * 日
     */
    private Integer day;

    /**
     * 收款人id
     */
    private Long payeeId;

    /**
     * 收款人
     */
    private String payeeName;

    /**
     * 推荐费
     */
    private BigDecimal referralAmount;

    /**
     * 推荐数量
     */
    private Integer referralCount;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 件数
     */
    private Integer pieces;

    /**
     * 订单数
     */
    private Integer orderNumber;

    /**
     * 平均交货时间
     */
    private Integer avgDeliveryTime;
}

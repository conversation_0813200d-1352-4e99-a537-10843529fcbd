package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-06-09
 **/
@Getter
@Setter
@TableName("t_logistic_transport_bill_settlement")
public class LogisticTransportBillSettlement extends TableIdEntity {

    /**
     * 账单日期
     */
    private LocalDate billDate;

    /**
     * 账单年月
     */
    private String billYearMonth;

    /**
     * 年
     */
    private Integer billYear;

    /**
     * 月
     */
    private Integer billMonth;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 付款人
     *
     * @see com.senox.tms.constant.LogisticTransportOrderPayer
     */
    private Integer payer;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 待支付金额
     */
    private BigDecimal paidStillAmount;

    /**
     * 支付方式
     */
    private Integer payWay;

    /**
     * 支付时间
     */
    private LocalDateTime paidTime;

    /**
     * 订单id
     */
    private Long remoteOrderId;

    /**
     * 下发状态
     */
    private Boolean send;

    /**
     * 下发时间
     */
    private LocalDateTime sendTime;

    /**
     * 收费员
     */
    private Long tollManId;

    /**
     * 账单状态
     */
    private Boolean status;

}

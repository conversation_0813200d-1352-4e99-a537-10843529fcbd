package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/10/17 9:03
 */
@Getter
@Setter
@ToString
@TableName("t_bicycle_order_month_report")
public class BicycleOrderMonthReport extends TableIdEntity {

    /**
     * 月报年月
     */
    private String reportYearMonth;
    /**
     * 月报年份
     */
    private Integer reportYear;
    /**
     * 月报月份
     */
    private Integer reportMonth;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 总件数
     */
    private BigDecimal totalPieces;
    /**
     * 总单数
     */
    private Integer totalCount;
    /**
     * 其他费用
     */
    private BigDecimal deliveryCharge;
    /**
     * 其他费用
     */
    private BigDecimal otherCharge;
    /**
     * 装卸费
     */
    private BigDecimal handlingCharge;
    /**
     * 上楼费
     */
    private BigDecimal upstairsCharge;
    /**
     * 合计
     */
    private BigDecimal totalCharge;
}

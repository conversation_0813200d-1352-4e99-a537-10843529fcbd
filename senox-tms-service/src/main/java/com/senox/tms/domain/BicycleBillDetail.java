package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @date 2023/9/25 8:55
 */
@Data
@TableName("t_bicycle_bill_detail")
public class BicycleBillDetail {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 账单id
     */
    private Long billId;

    /**
     * 费项id
     */
    private Long feeId;

    /**
     * 费项名
     */
    private String feeName;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/19 9:15
 */
@Getter
@Setter
@TableName("t_logistic_statistics_day_report")
public class LogisticStatisticsDayReport extends TableIdEntity {

    /**
     * 日期
     */
    private LocalDate reportDate;

    /**
     * 运营部门
     */
    private String operationsDepartment;

    /**
     * 发货人
     */
    private String shipper;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 收入类别
     */
    private Integer incomeType;

    /**
     * 司机名
     */
    private String driverName;

    /**
     * 车牌
     */
    private String carNo;

    /**
     * 是否包车
     */
    private Boolean charteredBus;

    /**
     * 始发站
     */
    private String departureStation;

    /**
     * 目的站
     */
    private String destinationStation;

    /**
     * 件数
     */
    private BigDecimal pieces;

    /**
     * 装载重量
     */
    private BigDecimal loadingWeight;

    /**
     * 入库重量
     */
    private BigDecimal storageWeight;

    /**
     * 未入库重量
     */
    private BigDecimal unStockedWeight;

    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 运费收入金额
     */
    private BigDecimal freightIncomeAmount;

    /**
     * 收款日期
     */
    private LocalDateTime paymentTime;

    /**
     * 实收运费金额
     */
    private BigDecimal actualFreightAmount;

    /**
     * 进仓单号
     */
    private String warehousingNo;

    /**
     * 进口冻品优惠
     */
    private BigDecimal frozenGoodsDiscounts;

    /**
     * 未收款金额
     */
    private BigDecimal unpaidAmount;

    /**
     * 备注
     */
    private String remark;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogisticStatisticsDayReport that = (LogisticStatisticsDayReport) o;
        return Objects.equals(logisticsNo, that.logisticsNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(logisticsNo);
    }
}

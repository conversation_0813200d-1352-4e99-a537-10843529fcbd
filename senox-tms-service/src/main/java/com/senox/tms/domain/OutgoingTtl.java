package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/8 10:54
 */
@TableName("t_outgoing_ttl")
@Getter
@Setter
public class OutgoingTtl extends TableIdEntity {

    /**
     * 收货日期
     */
    private LocalDate receivingDate;

    /**
     * 托运方客户
     */
    private String shipperName;

    /**
     * 收货方客户
     */
    private String recipientName;

    /**
     * 收货方地址
     */
    private String recipientAddress;

    /**
     * 收货方电话
     */
    private String recipientContact;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 物流单号
     */
    private String logisticsNo;

    /**
     * 托运方件数
     */
    private Integer shipperPieces;

    /**
     * 实收运费
     */
    private BigDecimal actualFreightAmount;

    /**
     * 实发运费
     */
    private BigDecimal actualShippingAmount;

    /**
     * 利润
     */
    private BigDecimal profitAmount;

    /**
     * 结算类型(1:到付;2:现付)
     */
    private Integer settlementType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OutgoingTtl that = (OutgoingTtl) o;
        return Objects.equals(getLogisticsNo(), that.getLogisticsNo());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getLogisticsNo());
    }
}

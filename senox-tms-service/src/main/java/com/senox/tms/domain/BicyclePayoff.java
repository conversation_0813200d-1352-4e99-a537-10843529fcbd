package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 应付账单
 *
 * <AUTHOR>
 * @date 2023-9-22
 */
@Getter
@Setter
@TableName("t_bicycle_payoff")
public class BicyclePayoff extends TableIdEntity {

    /**
     * 账单年
     */
    private Integer billYear;

    /**
     * 账单月
     */
    private Integer billMonth;

    /**
     * 订单流水号
     */
    private String orderSerialNo;

    /**
     * 配送单流水号
     */
    private String deliveryOrderSerialNo;

    /**
     * 收款人姓名
     */
    private String payeeName;

    /**
     * 收款人类型
     */
    private Integer payeeType;

    /**
     * 收款人联系方式
     */
    private String payeeContact;

    /**
     * 骑手id
     */
    private Long riderId;

    /**
     * 件数
     */
    private BigDecimal pieces;

    /**
     * 分佣金额
     */
    private BigDecimal shareAmount;

    /**
     * 推荐费
     */
    private BigDecimal referralAmount;

    /**
     * 是否推荐费
     */
    private Boolean referral;

    /**
     * 交货时间
     */
    private Integer deliveryTime;

    /**
     * 支付方式
     */
    private Integer payway;

    /**
     * 状态(0:未支付;1:已支付)
     */
    private Integer status;

    /**
     * 支付时间
     */
    private LocalDateTime paidTime;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        BicyclePayoff payoff = (BicyclePayoff) obj;
        return Objects.equals(deliveryOrderSerialNo, payoff.getDeliveryOrderSerialNo())
                && Objects.equals(riderId, payoff.getRiderId())
                && Objects.equals(orderSerialNo, payoff.getOrderSerialNo())
                && pieces.compareTo(payoff.getPieces()) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(deliveryOrderSerialNo, riderId, orderSerialNo, pieces);
    }

}

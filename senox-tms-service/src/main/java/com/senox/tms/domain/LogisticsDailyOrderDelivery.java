package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023-12-5
 */
@Getter
@Setter
@TableName("t_logistics_daily_order_delivery")
public class LogisticsDailyOrderDelivery extends TableIdEntity {

    /**
     * 配送单号
     */
    private String orderDeliveryNo;

    /**
     * 配送车牌
     */
    private String orderDeliveryCarNo;

    /**
     * 订单件数
     */
    private Integer orderPieces;

    /**
     * 订单总千克
     */
    private BigDecimal orderTotalKilograms;

    /**
     * 订单时间
     */
    private LocalDate orderTime;

    /**
     * 下单人
     */
    private String orderPerson;

    /**
     * 送货时间
     */
    private LocalDate sendTime;

    /**
     * 类型(1:干货线;2:冻品线)
     */
    private Integer type;

    /**
     * 件数
     */
    private Integer pieces;

    /**
     * 千克
     */
    private BigDecimal kilograms;

    /**
     * 运费单价
     */
    private BigDecimal freightUnitPrice;

    /**
     * 应收运费金额
     */
    private BigDecimal receivableFreightCharge;

    /**
     * 实收运费金额
     */
    private BigDecimal receivedFreightCharge;

    /**
     * 差额
     */
    private BigDecimal discrepancyAmount;

    /**
     * 备注
     */
    private String remark;
}

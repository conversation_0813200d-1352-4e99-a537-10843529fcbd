package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-05-19
 **/
@Getter
@Setter
@TableName("t_logistic_transport_bill")
public class LogisticTransportBill extends TableIdEntity {

    /**
     * 结算单id
     */
    private Long settlementId;

    /**
     * 账单日期
     */
    private LocalDate billDate;

    /**
     * 年份
     */
    private Integer billYear;

    /**
     * 月份
     */
    private Integer billMonth;

    /**
     * 订单编号
     */
    private String orderSerialNo;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 运费
     */
    @ApiModelProperty("运费")
    private BigDecimal freightCharge;

    /**
     * 其他费用
     */
    @ApiModelProperty("其他费用")
    private BigDecimal otherCharge;

    /**
     * 应收运费
     */
    @ApiModelProperty("应收运费")
    private BigDecimal receivableFreightCharge;

    /**
     * 状态
     */
    private Boolean status;

    /**
     * 支付时间
     */
    private LocalDateTime paidTime;

    /**
     * 支付方式
     */
    private Integer payWay;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        LogisticTransportBill bill = (LogisticTransportBill) obj;
        return orderSerialNo.equals(bill.orderSerialNo) && billDate.equals(bill.getBillDate());
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderSerialNo, billDate);
    }
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单收益明细
 *
 * <AUTHOR>
 * @date 2023-9-21
 */
@Getter
@Setter
@TableName("t_bicycle_order_income_detail")
public class BicycleOrderIncomeDetail {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单流水号
     */
    private String orderSerialNo;

    /**
     * 配送单流水号
     */
    private String deliveryOrderSerialNo;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 分润金额
     */
    private BigDecimal shareAmount;

    /**
     * 利润金额
     */
    private BigDecimal profitAmount;

    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

}

package com.senox.tms.domain;

import java.math.BigDecimal;

/**
 * 三轮车配送计费条件
 * <AUTHOR>
 * @date 2023/9/21 8:40
 */
public interface BicycleChargeMatters {

    /**
     * 件数
     * @return
     */
    BigDecimal getPieces();

    /**
     * 货物类型
     * @return
     */
    Integer getGoodsType();

    /**
     * 重量
     * @return
     */
    BigDecimal getWeight();

    /**
     * 体积
     * @return
     */
    BigDecimal getSize();


    void setPieces(BigDecimal pieces);


    void setWeight(BigDecimal weight);


    void setSize(BigDecimal size);
}

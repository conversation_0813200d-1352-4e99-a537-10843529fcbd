package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2025/3/10 10:56
 */
@TableName("px_unloading_night_schedule")
@Getter
@Setter
public class UnloadingNightSchedule extends TableIdEntity {

    /**
     * 排期日期
     */
    private LocalDate scheduleDate;

    /**
     * 排序年份
     */
    private Integer scheduleYear;

    /**
     * 排期月份
     */
    private Integer scheduleMonth;

    /**
     * 搬运工id
     */
    private Long workerId;

    /**
     * 搬运工编号
     */
    private String workerNo;

    /**
     * 搬运工姓名
     */
    private String workerName;
}

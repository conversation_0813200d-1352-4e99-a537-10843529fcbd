package com.senox.tms.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/29 16:00
 */
@Getter
@Setter
@ToString
public class LogisticOrder {

    /**
     * 商品订单
     */
    private LogisticOrderProduct product;
    /**
     * 配送订单
     */
    private LogisticOrderShip ship;
    /**
     * 账单
     */
    private LogisticOrderBill bill;


    public LogisticOrder() {
    }

    public LogisticOrder(LogisticOrderProduct product, LogisticOrderShip ship, LogisticOrderBill bill) {
        this.product = product;
        this.ship = ship;
        this.bill = bill;
    }
}

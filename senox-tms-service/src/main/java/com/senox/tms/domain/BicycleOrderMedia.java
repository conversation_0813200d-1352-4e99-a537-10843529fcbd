package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/18 9:58
 */
@Getter
@Setter
@TableName("t_bicycle_order_media")
public class BicycleOrderMedia {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 多媒体资料访问连接
     */
    private String mediaUrl;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    public BicycleOrderMedia(Long orderId, String mediaUrl) {
        this.orderId = orderId;
        this.mediaUrl = mediaUrl;
        this.modifiedTime = LocalDateTime.now();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BicycleOrderMedia that = (BicycleOrderMedia) o;
        return Objects.equals(orderId, that.orderId)
                && Objects.equals(mediaUrl, that.mediaUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderId, mediaUrl);
    }
}

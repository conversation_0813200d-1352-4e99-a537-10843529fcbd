package com.senox.tms.domain;

import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-4-1
 */
@Getter
@Setter
public class BicyclePayoffCharges extends TableIdEntity {

    /**
     * 基础金额
     */
    private BigDecimal basicAmount;

    /**
     * 基础件数
     */
    private BigDecimal basicPieces;

    /**
     * 超量价格
     */
    private BigDecimal excessPrice;
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/20 9:13
 */
@Data
@TableName("t_bicycle_delivery_order_job")
public class BicycleDeliveryOrderJob {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 配送单详细id
     */
    private Long deliveryOrderDetailId;
    /**
     * 状态
     * @see com.senox.tms.constant.BicycleDeliveryOrderStatus
     */
    private Integer status;
    /**
     * 骑手备注
     */
    private String remark;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;
}

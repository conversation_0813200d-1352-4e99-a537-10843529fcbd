package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/15 11:18
 */
@Getter
@Setter
@ToString
@TableName("t_logistic_payoff")
public class LogisticPayoff extends TableIdEntity {

    /**
     * 账单日
     */
    private LocalDate billDate;
    /**
     * 商户
     */
    private String merchant;
    /**
     * 订单件数
     */
    private BigDecimal productCount;
    /**
     * 订单总价
     */
    private BigDecimal productAmount;
    /**
     * 满减额
     */
    private BigDecimal productFullReduction;
    /**
     * 应收货款
     */
    private BigDecimal productToPaid;
    /**
     * 已收货款
     */
    private BigDecimal productPaid;
    /**
     * 欠款货款
     */
    private BigDecimal productOwe;
    /**
     * 其他减款
     */
    private BigDecimal productDeduction;
    /**
     * 差异金额
     */
    private BigDecimal productDiversity;
    /**
     * 物流费
     */
    private BigDecimal shipAmount;
    /**
     * 应结算金额
     */
    private BigDecimal totalAmount;
    /**
     * 备注
     */
    private String remark;

    public LogisticPayoff() {
    }

    public LogisticPayoff(LocalDate billDate, String merchant) {
        this.billDate = billDate;
        this.merchant = merchant;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogisticPayoff that = (LogisticPayoff) o;
        return Objects.equals(billDate, that.billDate)
                && Objects.equals(merchant, that.merchant);
    }

    @Override
    public int hashCode() {
        return Objects.hash(billDate, merchant);
    }
}

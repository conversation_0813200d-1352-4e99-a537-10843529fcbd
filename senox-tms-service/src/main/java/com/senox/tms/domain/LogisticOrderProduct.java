package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.common.utils.DecimalUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * 物流订单明细
 * <AUTHOR>
 * @date 2023/11/27 10:17
 */
@Getter
@Setter
@ToString
@TableName("t_logistic_order_product")
public class LogisticOrderProduct extends TableIdEntity {

    /**
     * 发货日期
     */
    private LocalDate shipDate;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 商户
     */
    private String merchant;
    /**
     * 会员
     */
    private String member;
    /**
     * 货品
     */
    private String product;
    /**
     * 商品总类
     */
    private String productType1;
    /**
     * 商品分类
     */
    private String productType2;
    /**
     * 商品单价
     */
    private BigDecimal productPrice;
    /**
     * 商品件数
     */
    private BigDecimal productCount;
    /**
     * 商品总价
     */
    private BigDecimal productAmount;
    /**
     * 商品总重量
     */
    private BigDecimal productWeight;
    /**
     * 商品总体积
     */
    private BigDecimal productSize;
    /**
     * 商品优惠金额
     */
    private BigDecimal productDeduction;
    /**
     * 商品满减额
     */
    private BigDecimal productFullReduction;
    /**
     * 合计金额
     */
    private BigDecimal totalAmount;
    /**
     * 备注
     */
    private String remark;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogisticOrderProduct that = (LogisticOrderProduct) o;
        return Objects.equals(shipDate, that.shipDate)
                && Objects.equals(orderNo, that.orderNo)
                && Objects.equals(merchant, that.merchant)
                && Objects.equals(product, that.product)
                && DecimalUtils.equals(productCount, that.productCount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(shipDate, orderNo, merchant, product, productCount);
    }
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.BicycleStatus;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 分佣 domain
 *
 * <AUTHOR>
 * @date 2023-9-15
 */
@Getter
@Setter
@TableName("t_bicycle_shares")
public class BicycleShares extends TableIdEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 比例
     */
    private BigDecimal riderShares;

    /**
     * 默认比例
     */
    private BigDecimal riderDefaultShares;

    /**
     * 推荐金额
     */
    private BigDecimal referralAmount;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 失效时间
     */
    private LocalDateTime ineffectiveTime;

    /**
     * 状态
     *
     * @see BicycleStatus
     */
    private Integer status;
}

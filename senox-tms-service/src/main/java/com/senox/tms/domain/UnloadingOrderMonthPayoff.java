package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/8 10:21
 */
@Getter
@Setter
@TableName("px_unloading_order_month_payoff")
public class UnloadingOrderMonthPayoff extends TableIdEntity {

    /**
     * 应付年份
     */
    private Integer payoffYear;

    /**
     * 应付月份
     */
    private Integer payoffMonth;

    /**
     * 搬运工id
     */
    private Long workerId;

    /**
     * 搬运工编号
     */
    private String workerNo;

    /**
     * 搬运工姓名
     */
    private String workerName;

    /**
     * 佣金
     */
    private BigDecimal payoffAmount;

    /**
     * 收益
     */
    private BigDecimal shareAmount;

    /**
     * 支付状态
     * @see UnloadingOrderBillPayoffStatus
     */
    private Integer status;

    /**
     * 应付时间
     */
    private LocalDateTime payoffTime;

    /**
     * 备注
     */
    private String remark;
}

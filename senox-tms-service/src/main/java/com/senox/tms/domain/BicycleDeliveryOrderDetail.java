package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/20 8:44
 */
@Getter
@Setter
@TableName("t_bicycle_delivery_order_detail")
public class BicycleDeliveryOrderDetail extends TableIdEntity implements Serializable {

    private static final long serialVersionUID = 5849712772551883757L;
    /**
     * 配送单流水号
     */
    private String deliveryOrderSerialNo;
    /**
     * 子配送单流水号
     */
    private String deliveryOrderSerialNoItem;
    /**
     * 订单流水号
     */
    private String orderSerialNo;
    /**
     * 骑手id
     */
    private Long riderId;
    /**
     * 揽货件数
     */
    private BigDecimal pickedPieces;
    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    private Integer status;
    /**
     * 揽货点经纬度
     */
    private String pickingPoint;
    /**
     * 送货点经纬度
     */
    private String deliveryPoint;
    /**
     * 揽货时间
     */
    private LocalDateTime pickingTime;
    /**
     * 配送时间
     */
    private LocalDateTime sendTime;
    /**
     * 收货时间
     */
    private LocalDateTime receivingTime;
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
    /**
     * 评分星级
     */
    private Integer rating;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        BicycleDeliveryOrderDetail detail = (BicycleDeliveryOrderDetail) obj;
        return Objects.equals(deliveryOrderSerialNo, detail.getDeliveryOrderSerialNo())
                && Objects.equals(deliveryOrderSerialNoItem, detail.getDeliveryOrderSerialNoItem())
                && Objects.equals(orderSerialNo, detail.getOrderSerialNo())
                && pickedPieces.compareTo(detail.getPickedPieces()) == 0;
    }

    @Override
    public int hashCode() {
        return Objects.hash(deliveryOrderSerialNo, deliveryOrderSerialNoItem, orderSerialNo, pickedPieces);
    }
}

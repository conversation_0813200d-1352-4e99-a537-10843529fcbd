package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/10/27 10:21
 */
@Getter
@Setter
@TableName("t_bicycle_manager")
public class BicycleManager extends TableIdEntity {

    /**
     * 管理员id
     */
    private Long adminUserId;

    /**
     * 状态 0 离线; 1 在线;
     */
    private Integer status;
}

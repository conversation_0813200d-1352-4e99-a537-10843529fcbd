package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Data;

import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023/9/12 13:57
 */
@Data
@TableName("dict_bicycle_point")
public class BicyclePoint extends TableIdEntity {

    /**
     * 名称
     */
    private String name;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BicyclePoint that = (BicyclePoint) o;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }
}

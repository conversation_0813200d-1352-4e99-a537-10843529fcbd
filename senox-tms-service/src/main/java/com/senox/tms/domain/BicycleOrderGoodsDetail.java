package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.utils.DecimalUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/18 9:58
 */
@Data
@TableName("t_bicycle_order_goods_detail")
public class BicycleOrderGoodsDetail implements BicycleChargeMatters{

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 货物名
     */
    private String goodsName;
    /**
     * 货物类型 0其他，1重货，2抛货
     */
    private Integer goodsType;
    /**
     * 件数
     */
    private BigDecimal pieces;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 体积
     */
    private BigDecimal size;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }

        BicycleOrderGoodsDetail detail = (BicycleOrderGoodsDetail) obj;
        return Objects.equals(orderId, detail.getOrderId())
                && Objects.equals(goodsName, detail.getGoodsName())
                && Objects.equals(goodsType, detail.getGoodsType())
                && Objects.equals(weight, detail.getWeight())
                && Objects.equals(size, detail.getSize());
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderId, goodsName, goodsType, weight, size);
    }

    public BicycleOrderGoodsDetail() {
        pieces = BigDecimal.ZERO;
        weight = BigDecimal.ZERO;
        size = BigDecimal.ZERO;
    }

    public BicycleOrderGoodsDetail(Integer goodsType, BigDecimal weight, BigDecimal size, BigDecimal pieces) {
        this.goodsType = goodsType;
        this.weight = weight;
        this.size = size;
        this.pieces = pieces;
    }

    public BicycleOrderGoodsDetail(Long orderId, String goodsName, Integer goodsType, BigDecimal pieces, BigDecimal weight, BigDecimal size) {
        this.orderId = orderId;
        this.goodsName = goodsName;
        this.goodsType = goodsType;
        this.pieces = pieces;
        this.weight = weight;
        this.size = size;
    }
}

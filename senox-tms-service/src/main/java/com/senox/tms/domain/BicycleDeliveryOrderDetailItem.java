package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/3 14:05
 */
@TableName("t_bicycle_delivery_order_detail_item")
@Getter
@Setter
public class BicycleDeliveryOrderDetailItem extends TableIdEntity implements BicycleChargeMatters {

    /**
     * 配送单流水号
     */
    private String deliveryOrderSerialNo;

    /**
     * 子配送单流水号
     */
    private String deliveryOrderSerialNoItem;

    /**
     * 订单流水号
     */
    private String orderSerialNo;

    /**
     * 货物id
     */
    private Long goodsId;

    /**
     * '货物类型 0其他，1重货，2抛货
     */
    private Integer goodsType;

    /**
     * 货物名
     */
    private String goodsName;

    /**
     * 件数
     */
    private BigDecimal pieces;

    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 体积
     */
    private BigDecimal size;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BicycleDeliveryOrderDetailItem that = (BicycleDeliveryOrderDetailItem) o;
        return Objects.equals(deliveryOrderSerialNoItem, that.deliveryOrderSerialNoItem) && Objects.equals(goodsId, that.goodsId) && Objects.equals(goodsName, that.goodsName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(deliveryOrderSerialNoItem, goodsId, goodsName);
    }

    public BicycleDeliveryOrderDetailItem() {
        pieces = BigDecimal.ZERO;
        weight = BigDecimal.ZERO;
        size = BigDecimal.ZERO;
    }

    public BicycleDeliveryOrderDetailItem(Integer goodsType, BigDecimal weight, BigDecimal size, BigDecimal pieces) {
        this.goodsType = goodsType;
        this.weight = weight;
        this.size = size;
        this.pieces = pieces;
    }
}

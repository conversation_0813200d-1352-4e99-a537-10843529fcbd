package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
@Getter
@Setter
@TableName("t_bicycle_charges_scheduled_task")
public class BicycleChargesScheduledTask extends TableIdEntity {

    /**
     * 任务名
     */
    private String taskName;

    /**
     * 任务次数
     */
    private Integer taskCount;

    /**
     * 模式(0:增量更新;1:全量更新)
     */
    private Integer model;

    /**
     * 费用标准id
     */
    private Long chargesId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;
}

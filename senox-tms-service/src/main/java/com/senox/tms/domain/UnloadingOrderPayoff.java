package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/11/1 10:14
 */
@TableName("px_unloading_order_payoff")
@Getter
@Setter
public class UnloadingOrderPayoff extends TableIdEntity {

    /**
     * 应付日期
     */
    private LocalDate payoffDate;

    /**
     * 应付年份
     */
    private Integer payoffYear;

    /**
     * 应付月份
     */
    private Integer payoffMonth;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 搬运工id
     */
    private Long workerId;

    /**
     * 搬运工编号
     */
    private String workerNo;

    /**
     * 搬运工姓名
     */
    private String workerName;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 佣金
     */
    private BigDecimal payoffAmount;

    /**
     * 月应付id
     */
    private Long monthPayoffId;

    /**
     * 支付状态
     * @see UnloadingOrderBillPayoffStatus
     */
    private Integer status;

    /**
     * 应付时间
     */
    private LocalDateTime payoffTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnloadingOrderPayoff that = (UnloadingOrderPayoff) o;
        return Objects.equals(getPayoffDate(), that.getPayoffDate())
                && Objects.equals(getPayoffYear(), that.getPayoffYear())
                && Objects.equals(getPayoffMonth(), that.getPayoffMonth())
                && Objects.equals(getOrderNo(), that.getOrderNo())
                && Objects.equals(getWorkerId(), that.getWorkerId())
                && Objects.equals(getWorkerNo(), that.getWorkerNo())
                && Objects.equals(getStatus(), that.getStatus())
                && Objects.equals(getWorkerName(), that.getWorkerName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPayoffDate(), getPayoffYear(), getPayoffMonth(), getOrderNo(), getWorkerId(), getWorkerNo(), getStatus(), getWorkerName());
    }
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/20 9:25
 */
@Getter
@Setter
@TableName("t_bicycle_delivery_order_job_media")
public class BicycleDeliveryOrderJobMedia {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 配送单任务id
     */
    private Long jobId;
    /**
     * 多媒体资料访问连接
     */
    private String mediaUrl;
    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;

    public BicycleDeliveryOrderJobMedia() {
    }

    public BicycleDeliveryOrderJobMedia(Long jobId, String mediaUrl) {
        this.jobId = jobId;
        this.mediaUrl = mediaUrl;
        this.modifiedTime = LocalDateTime.now();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BicycleDeliveryOrderJobMedia that = (BicycleDeliveryOrderJobMedia) o;
        return Objects.equals(jobId, that.jobId)
                && Objects.equals(mediaUrl, that.mediaUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(jobId, mediaUrl);
    }
}

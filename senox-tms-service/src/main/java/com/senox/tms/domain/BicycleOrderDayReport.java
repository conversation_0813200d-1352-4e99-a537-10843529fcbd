package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/10/16 9:25
 */
@Getter
@Setter
@TableName("t_bicycle_order_day_report")
public class BicycleOrderDayReport extends TableIdEntity {
    /**
     * 报表日期
     */
    private LocalDate reportDate;
    /**
     * 商户id
     */
    private Long merchantId;
    /**
     * 总件数
     */
    private BigDecimal totalPieces;
    /**
     * 总单数
     */
    private Integer totalCount;
    /**
     * 配送费用
     */
    private BigDecimal deliveryCharge;
    /**
     * 其他费用
     */
    private BigDecimal otherCharge;
    /**
     * 装卸费
     */
    private BigDecimal handlingCharge;
    /**
     * 上楼费
     */
    private BigDecimal upstairsCharge;
    /**
     * 合计
     */
    private BigDecimal totalCharge;

}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/20 8:32
 */
@Getter
@Setter
@TableName("t_bicycle_delivery_order")
public class BicycleDeliveryOrder extends TableIdEntity implements Serializable {

    private static final long serialVersionUID = 7413834484908324138L;
    /**
     * 配送单流水号
     */
    private String deliveryOrderSerialNo;
    /**
     * 是否合并订单
     */
    private Boolean merged;
    /**
     * 推荐分配
     */
    private Boolean referralDelivery;
    /**
     * 状态
     * @see BicycleDeliveryOrderStatus
     */
    private Integer status;
    /**
     * 完成时间
     */
    private LocalDateTime finishTime;
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.tms.vo.UnloadingOrderWorkersVo;
import com.senox.tms.vo.UnloadingWorkerVo;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/3/24 8:22
 */
@Data
@TableName("px_unloading_worker_log")
public class UnloadingWorkerLog {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 搬运工id
     */
    private Long workerId;

    /**
     * 搬运工姓名
     */
    private String workerName;

    /**
     * 搬运工编号
     */
    private String workerNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作时间
     */
    private LocalDateTime modifiedTime;

    public UnloadingWorkerLog(UnloadingOrderWorkersVo workersVo) {
        this.setWorkerId(workersVo.getWorkerId());
        this.setWorkerNo(workersVo.getWorkerNo());
        this.setWorkerName(workersVo.getWorkerName());
        this.setRemark(workersVo.getRemark());
    }

    public UnloadingWorkerLog(UnloadingWorker worker, String remark) {
        this.setWorkerId(worker.getId());
        this.setWorkerNo(worker.getWorkerNo());
        this.setWorkerName(worker.getName());
        this.setRemark(remark);
    }
}

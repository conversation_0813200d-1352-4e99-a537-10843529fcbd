package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/10/29 9:13
 */
@TableName("px_unloading_worker_access")
@Getter
@Setter
public class UnloadingWorkerAccess extends TableIdEntity {

    /**
     * 搬运工id
     */
    private Long workerId;

    /**
     * 搬运工标识
     */
    private String workerSign;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * ip
     */
    private String deviceIp;

    /**
     * 拥有权限
     */
    private Boolean access;

    /**
     * 是否生效（0：未生效，1：已生效）
     */
    private Boolean state;
}

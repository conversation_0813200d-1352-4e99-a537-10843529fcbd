package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/9/22 9:59
 */
@Getter
@Setter
@TableName("t_bicycle_bill")
public class BicycleBill extends TableIdEntity {

    /**
     * 账单日
     */
    private LocalDate billDate;

    /**
     * 账单年份
     */
    private Integer billYear;

    /**
     * 账单月份
     */
    private Integer billMonth;

    /**
     * 配送单流水号
     */
    private String deliveryOrderSerialNo;

    /**
     * 订单流水号
     */
    private String orderSerialNo;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 结算id
     */
    private Long settlementId;

    /**
     * 金额
     */
    private BigDecimal amount;

}

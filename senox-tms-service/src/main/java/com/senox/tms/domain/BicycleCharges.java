package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.BicycleStatus;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 收费标准 domain
 *
 * <AUTHOR>
 * @date 2023-9-12
 */
@Getter
@Setter
@TableName("t_bicycle_charges")
public class BicycleCharges extends TableIdEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 失效时间
     */
    private LocalDateTime ineffectiveTime;

    /**
     * 状态
     *
     * @see BicycleStatus
     */
    private Integer status;

    /**
     * 默认生效
     */
    private Boolean defaultEffective;

    /**
     * 灵活计算
     */
    private Boolean flexible;

    /**
     * 低消
     */
    private BigDecimal minAmount;
}

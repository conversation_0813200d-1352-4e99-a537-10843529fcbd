package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.BicycleRiderStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/13 15:39
 */
@EqualsAndHashCode(callSuper = false)
@Data
@TableName("t_bicycle_rider")
public class BicycleRider extends TableIdEntity {

    /**
     * 骑手编号
     */
    private String riderNo;
    /**
     * 密码
     */
    private String password;
    /**
     * 盐
     */
    private String salt;
    /**
     * 姓名
     */
    private String name;
    /**
     * 推荐码
     */
    private String referralCode;
    /**
     * 联系方式
     */
    private String contact;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 出生日期
     */
    private LocalDate birthday;
    /**
     * 状态
     * @see BicycleRiderStatus
     */
    private Integer status;
    /**
     * 上一次修改密码时间
     */
    private LocalDateTime lastModifyPasswordTime;
}

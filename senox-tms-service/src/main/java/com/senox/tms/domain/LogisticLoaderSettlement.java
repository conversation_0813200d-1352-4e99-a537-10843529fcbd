package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.LogisticLoaderFreightType;
import com.senox.tms.constant.LogisticLoaderGoodsType;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@TableName("t_logistic_loader_settlement")
public class LogisticLoaderSettlement extends TableIdEntity {

    /**
     * 日期
     */
    @TableField("`date`")
    private LocalDate date;

    /**
     * 装卸类型
     *
     * @see LogisticLoaderFreightType
     */
    private Integer freightType;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 商品类型
     * @see LogisticLoaderGoodsType
     */
    private Integer goodsType;

    /**
     * 平均搬运量
     */
    private BigDecimal transportAvg;

    /**
     * 总搬运量
     */
    private BigDecimal transportTotal;

    /**
     * 参与人数
     */
    private Integer participationNumber;

    /**
     * 装卸单价
     */
    private BigDecimal freightUnitPrice;

    /**
     * 装卸总金额
     */
    private BigDecimal freightTotalAmount;

    /**
     * 分拣费
     */
    private BigDecimal sortingFee;

    /**
     * 件数
     */
    private BigDecimal pieces;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 工时
     */
    private BigDecimal workingHours;

    /**
     * 餐补
     */
    private BigDecimal mealAllowanceAmount;

    /**
     * 其他费用
     */
    private BigDecimal otherCharge;

    /**
     * 小计
     */
    private BigDecimal subtotalAmount;

    /**
     * 合计
     */
    private BigDecimal totalAmount;
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.pm.constant.OrderStatus;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-11-7
 */
@Getter
@Setter
@TableName("t_bicycle_bill_settlement")
public class BicycleBillSettlement extends TableIdEntity {

    /**
     * 账单日期
     */
    private LocalDate billDate;
    /**
     * 账单年月
     */
    private String billYearMonth;

    /**
     * 年
     */
    private Integer billYear;

    /**
     * 月
     */
    private Integer billMonth;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 商户名
     */
    private String merchantName;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 支付方式
     */
    private Integer payWay;

    /**
     * 结算周期
     */
    private Integer settlePeriod;

    /**
     * 支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 支付时间
     */
    private LocalDateTime paidTime;

    /**
     * 支付订单id
     */
    private Long remoteOrderId;

    /**
     * 下发状态
     */
    private Boolean send;

    /**
     * 下发时间
     */
    private LocalDateTime sendTime;

    /**
     * 收费员
     */
    private Long tollManId;

    /**
     * 状态
     *
     * @see OrderStatus
     */
    private Boolean status;

}

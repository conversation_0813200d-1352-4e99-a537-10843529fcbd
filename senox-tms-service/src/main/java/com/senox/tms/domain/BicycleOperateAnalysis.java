package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/12 14:36
 */
@Data
@TableName("t_bicycle_operate_analysis")
public class BicycleOperateAnalysis {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 日期
     */
    private LocalDate operateAnalysisDate;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 总件数
     */
    private BigDecimal totalPieces;

    /**
     * 平均单价
     */
    private BigDecimal avgUnitPrice;

    /**
     * 总单数
     */
    private Integer totalCount;

    /**
     * 平均每单用时
     */
    private Integer avgSecond;

    /**
     * 寄件人id
     */
    private Long bestSenderId;

    /**
     * 客户名
     */
    private String bestCustomerName;

    /**
     * 最佳骑手id
     */
    private Long bestRiderId;

    /**
     * 最佳骑手
     */
    private String bestRiderName;

    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;
}

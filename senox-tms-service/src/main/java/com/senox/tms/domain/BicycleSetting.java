package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/10/25 9:29
 */
@Getter
@Setter
@TableName("t_bicycle_setting")
public class BicycleSetting extends TableIdEntity {

    /**
     * 配置名
     */
    private String name;

    /**
     * 别名
     */
    private String alias;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 单位间隔，单位：分钟
     */
    private Integer intervalMinute;
}

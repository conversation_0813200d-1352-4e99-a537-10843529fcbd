package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.LogisticTransportCategory;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-05-19
 **/
@Getter
@Setter
@TableName("t_logistic_transport_order")
public class LogisticTransportOrder extends TableIdEntity {

    /**
     * 编号
     */
    private String serialNo;

    /**
     * 订单年月日
     */
    private String yearMonthDay;

    /**
     * 发货人id
     */
    private Long consignorId;

    /**
     * 发货人姓名
     */
    private String consignorName;

    /**
     * 发货人编码
     */
    private String consignorCode;

    /**
     * 发货人电话
     */
    private String consignorPhone;

    /**
     * 收货人姓名
     */
    private String consigneeName;

    /**
     * 收货人电话
     */
    private String consigneePhone;

    /**
     * 类别id
     *
     * @see LogisticTransportCategory
     */
    private Integer category;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 司机手机
     */
    private String driverPhone;

    /**
     * 车牌号
     */
    private String licensePlateNumber;

    /**
     * 是否包车
     */
    @TableField("is_charter")
    private Boolean charter;

    /**
     * 始发站
     */
    private String departureStation;

    /**
     * 目的站
     */
    private String destinationStation;

    /**
     * 件数
     */
    private Integer pieces;

    /**
     * 装载重量(kg)
     */
    private BigDecimal loadingWeight;

    /**
     * 运费
     */
    private BigDecimal freightCharge;

    /**
     * 其他费用
     */
    private BigDecimal otherCharge;

    /**
     * 应收运费
     */
    private BigDecimal receivableFreightCharge;

    /**
     * 付款人
     *
     * @see LogisticTransportOrderPayer
     */
    private Integer payer;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核状态
     */
    private Boolean auditStatus;

    /**
     * 审核人id
     */
    private Long auditorId;

    /**
     * 审核人姓名
     */
    private String auditorName;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        LogisticTransportOrder order = (LogisticTransportOrder) obj;
        return serialNo.equals(order.serialNo) && yearMonthDay.equals(order.yearMonthDay);
    }

    @Override
    public int hashCode() {
        return Objects.hash(serialNo, yearMonthDay);
    }
}

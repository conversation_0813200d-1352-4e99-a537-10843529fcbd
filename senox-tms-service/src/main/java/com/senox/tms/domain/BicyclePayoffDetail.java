package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 应付账单明细
 *
 * <AUTHOR>
 * @date 2023-9-22
 */
@Getter
@Setter
@TableName("t_bicycle_payoff_detail")
public class BicyclePayoffDetail {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 应付帐单id
     */
    private Long payoffId;

    /**
     * 费项id
     */
    private Long feeId;

    /**
     * 费项名
     */
    private String feeName;


    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;
}

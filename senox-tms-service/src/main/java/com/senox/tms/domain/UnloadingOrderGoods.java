package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/14 11:04
 */
@TableName("px_unloading_order_goods")
@Getter
@Setter
public class UnloadingOrderGoods extends TableIdEntity {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 类型 0 冻品，1 干货
     */
    private Integer category;

    /**
     * 货物名
     */
    private String goodsName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数量
     */
    private BigDecimal quantity;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnloadingOrderGoods that = (UnloadingOrderGoods) o;
        return Objects.equals(getOrderId(), that.getOrderId())
                && Objects.equals(getCategory(), that.getCategory())
                && Objects.equals(getGoodsName(), that.getGoodsName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getOrderId(), getCategory(), getGoodsName());
    }
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/12 13:29
 */
@TableName("px_unloading_dict")
@Getter
@Setter
public class UnloadingDict extends TableIdEntity {


    /**
     * 类型
     * @see com.senox.tms.constant.UnloadingCategory
     */
    private Integer category;

    /**
     * 名称
     */
    private String name;

    /**
     * 单位
     */
    private String unit;

    /**
     * 保留字段1
     */
    private String attr1;

    /**
     * 保留字段2
     */
    private String attr2;

    /**
     * 保留字段3
     */
    private String attr3;

    /**
     * 保留字段4
     */
    private String attr4;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UnloadingDict that = (UnloadingDict) o;
        return Objects.equals(getCategory(), that.getCategory()) && Objects.equals(getName(), that.getName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getCategory(), getName());
    }
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023-12-6
 */
@Getter
@Setter
@TableName("t_logistics_freight")
public class LogisticsFreight extends TableIdEntity {

    /**
     * 运营部门
     */
    private String operationsDepartment;

    /**
     * 寄件客户名
     */
    private String senderCustomerName;

    /**
     * 寄件客户联系方式
     */
    private String senderCustomerContact;

    /**
     * 寄件数
     */
    private Integer senderPieces;

    /**
     * 寄件运费
     */
    private BigDecimal senderFreightCharge;

    /**
     * 寄件结算类型
     */
    private Integer senderSettlementType;

    /**
     * 收货日期
     */
    private LocalDate receivingDate;

    /**
     * 收货单号
     */
    private String receivingNo;

    /**
     * 收货客户名
     */
    private String receivingCustomerName;

    /**
     * 收货客户地址
     */
    private String receivingCustomerAddress;

    /**
     * 收货客户联系方式
     */
    private String receivingCustomerContact;

    /**
     * 中转物流公司
     */
    private String transferLogisticsCompany;

    /**
     * 中转物流单号
     */
    private String transferLogisticsNo;

    /**
     * 中转运费
     */
    private BigDecimal transferCharge;

    /**
     * 利润
     */
    private BigDecimal profitAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 支付时间
     */
    private LocalDateTime paidTime;

    /**
     * 订单id
     */
    private Long remoteOrderId;

    /**
     * 账单状态：0初始化 1已支付
     */
    private Integer status;

    /**
     * 支付备注
     */
    private String paidRemark;

    /**
     * 收费员id
     */
    private Long tollManId;
}

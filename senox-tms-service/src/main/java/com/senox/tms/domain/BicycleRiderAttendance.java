package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/14 16:48
 */
@Data
@TableName("t_bicycle_rider_attendance")
public class BicycleRiderAttendance {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 骑手id
     */
    private Long riderId;
    /**
     * 上线时间
     */
    private LocalDateTime onlineTime;
    /**
     * 下线时间
     */
    private LocalDateTime offlineTime;
    /**
     * 在线时长
     */
    private Integer duration;
    /**
     * 完成单数
     */
    private Integer completeCount;
    /**
     * 完成件数
     */
    private BigDecimal completePieces;
    /**
     * 收益金额
     */
    private BigDecimal shareAmount;
}

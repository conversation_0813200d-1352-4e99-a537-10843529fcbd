package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/27 15:56
 */
@Getter
@Setter
@ToString
@TableName("t_logistic_order_ship")
public class LogisticOrderShip extends TableIdEntity {

    /**
     * 发货日期
     */
    private LocalDate shipDate;
    /**
     * 物流配送单id
     */
    private Long orderProductId;
    /**
     * 收货人
     */
    private String receiver;
    /**
     * 收货人联系方式
     */
    private String receiverContact;
    /**
     * 送货地址
     */
    private String destination;
    /**
     * 市场
     */
    private String market;
    /**
     * 区域
     */
    private String area;
    /**
     * 车牌
     */
    private String vehicleNo;
    /**
     * 司机
     */
    private String driver;
    /**
     * 实发件数
     */
    private BigDecimal shipCount;
    /**
     * 发货差异
     */
    private BigDecimal shipDiversity;
    /**
     * 件重
     */
    private BigDecimal pieceWeight;
    /**
     * 实发重量
     */
    private BigDecimal shipWeight;
    /**
     * 件体积
     */
    private BigDecimal pieceSize;
    /**
     * 实发体积
     */
    private BigDecimal shipSize;
    /**
     * 运费单价
     */
    private BigDecimal shipPrice;
    /**
     * 运费倍率
     */
    private BigDecimal shipMultiplying;
    /**
     * 物流费
     */
    private BigDecimal shipAmount;
    /**
     * 折扣率
     */
    private BigDecimal shipDiscount;
    /**
     * 分拣费
     */
    private BigDecimal sortAmount;
    /**
     * 是否收取分拣费
     */
    @TableField("is_sort_charge")
    private Boolean sortCharge;
    /**
     * 实际运费
     */
    private BigDecimal totalAmount;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogisticOrderShip that = (LogisticOrderShip) o;
        return Objects.equals(orderProductId, that.orderProductId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderProductId);
    }
}

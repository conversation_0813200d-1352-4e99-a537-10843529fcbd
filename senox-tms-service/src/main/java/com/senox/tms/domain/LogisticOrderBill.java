package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/27 16:06
 */
@Getter
@Setter
@ToString
@TableName("t_logistic_order_bill")
public class LogisticOrderBill extends TableIdEntity {

    /**
     * 物流配送单id
     */
    private Long orderProductId;
    /**
     * 物流单id
     */
    private Long shipId;
    /**
     * 订单金额
     */
    private BigDecimal orderAmount;
    /**
     * 已收货款金额
     */
    private BigDecimal productPaid;
    /**
     * 人工调整货款金额
     */
    @TableField("is_product_paid_manual")
    private Boolean productPaidManual;
    /**
     * 预付款人
     */
    private String productPaidMan;
    /**
     * 已收货款差异金额
     */
    private BigDecimal productDiversity;
    /**
     * 已收货款欠款金额
     */
    private BigDecimal productOwe;
    /**
     * 运费
     */
    private BigDecimal shipAmount;
    /**
     * 应付合计
     */
    private BigDecimal totalAmount;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogisticOrderBill that = (LogisticOrderBill) o;
        return Objects.equals(orderProductId, that.orderProductId)
                && Objects.equals(shipId, that.shipId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderProductId, shipId);
    }
}

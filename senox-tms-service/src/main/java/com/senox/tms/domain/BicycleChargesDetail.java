package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 收费标准明细 domain
 *
 * <AUTHOR>
 * @date 2023-9-13
 */
@Getter
@Setter
@TableName("t_bicycle_charges_detail")
public class BicycleChargesDetail {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 收费标准id
     */
    private Long chargesId;

    /**
     * 货物类型 0其他，1重货，2抛货
     */
    private Integer goodsType;

    /**
     * 最少件数
     */
    private Integer minCount;

    /**
     * 最多件数
     */
    private Integer maxCount;

    /**
     * 最少单位
     */
    private BigDecimal minUnit;

    /**
     * 最多单位
     */
    private BigDecimal maxUnit;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 区间计算
     */
    @TableField("is_cal_anyway")
    private Boolean calAnyway;

    /**
     * 默认标准
     */
    private Boolean defaultCharges;

    /**
     * 禁用
     */
    @TableField("is_disabled")
    private Boolean disabled;

}

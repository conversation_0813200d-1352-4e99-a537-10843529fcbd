package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/30 14:25
 */
@TableName("px_unloading_shares")
@Getter
@Setter
public class UnloadingShares extends TableIdEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 分佣比例
     */
    private BigDecimal sharesRate;

    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 失效时间
     */
    private LocalDateTime ineffectiveTime;

    /**
     * 状态
     * @see com.senox.tms.constant.UnloadingSharesStatus
     */
    private Integer status;
}

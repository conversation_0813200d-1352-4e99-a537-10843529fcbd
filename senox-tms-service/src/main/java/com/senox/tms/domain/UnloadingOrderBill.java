package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.UnloadingOrderBillPayoffStatus;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/10/31 15:17
 */
@TableName("px_unloading_order_bill")
@Getter
@Setter
public class UnloadingOrderBill extends TableIdEntity {

    /**
     * 账单日
     */
    private LocalDate billDate;

    /**
     * 年份
     */
    private Integer billYear;

    /**
     * 月份
     */
    private Integer billMonth;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 支付状态
     * @see UnloadingOrderBillPayoffStatus
     */
    private Integer status;

    /**
     * 支付时间
     */
    private LocalDateTime paidTime;

    /**
     * 收费人
     */
    private Long tollManId;
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/14 9:06
 */
@TableName("px_unloading_attendance")
@Getter
@Setter
public class UnloadingAttendance {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 搬运工id
     */
    private Long workerId;

    /**
     * 搬运工编号
     */
    private String workerNo;

    /**
     * 搬运工姓名
     */
    private String workerName;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改时间
     */
    private LocalDateTime modifiedTime;
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Getter
@Setter
@TableName("t_logistic_loader_income")
public class LogisticLoaderIncome extends TableIdEntity {

    /**
     * 日期
     */
    @TableField("`date`")
    private LocalDate date;

    /**
     * 搬运工编号
     */
    private String loaderNumber;

    /**
     * 搬运工姓名
     */
    private String loaderName;

    /**
     * 结算id
     */
    private Long settlementId;

    /**
     * 总金额
     */
    private BigDecimal amount;
}

package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/9/14 10:41
 */
@TableName("px_unloading_order")
@Getter
@Setter
public class UnloadingOrder extends TableIdEntity {

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 客户名
     */
    private String customerName;

    /**
     * 地址
     */
    private String address;

    /**
     * 车牌
     */
    private String licensePlate;

    /**
     * 联系方式
     */
    private String contact;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     * @see com.senox.tms.constant.UnloadingOrderState
     */
    private Integer state;

    /**
     * 搬运状态
     * @see com.senox.tms.constant.UnloadingOrderWorkerStatus
     */
    private Integer workerStatus;

    /**
     * 是否拼车
     */
    private Boolean carpool;

    /**
     * 车型
     */
    private Integer carCategory;

    /**
     * 车型名
     */
    private String carCategoryName;

    /**
     * 车辆数
     */
    private Integer carNum;

    /**
     * 搬运工人数
     */
    private Integer workerNum;

    /**
     * 是否预约单
     */
    private Boolean reservationOrder;

    /**
     * 预约时间
     */
    private LocalDateTime reservationTime;

    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 搬运时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime workTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 加急单
     */
    private Boolean urgentOrder;

    /**
     * 加急金额
     */
    private BigDecimal urgentAmount;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 应付金额
     */
    private BigDecimal payoffAmount;

    /**
     * 补录
     */
    private Boolean supplement;

    /**
     * 微信openid
     */
    private String openid;
}

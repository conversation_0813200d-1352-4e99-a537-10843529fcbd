package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.OperateEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023-10-16
 */
@Getter
@Setter
@TableName("t_bicycle_payoff_month_report")
public class BicyclePayoffMonthReport extends OperateEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 年月日
     */
    @TableField("`year_month`")
    private String yearMonth;

    /**
     * 年
     */
    private Integer year;

    /**
     * 月
     */
    private Integer month;

    /**
     * 日
     */
    private Integer day;

    /**
     * 收款人id
     */
    private Long payeeId;

    /**
     * 收款人
     */
    private String payeeName;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 件数
     */
    private Integer pieces;

    /**
     * 订单数
     */
    private Integer orderNumber;
}

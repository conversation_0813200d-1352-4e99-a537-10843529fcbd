package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/9/13 11:38
 */
@TableName("px_unloading_worker")
@Getter
@Setter
public class UnloadingWorker extends TableIdEntity {

    /**
     * 搬运工编号
     */
    private String workerNo;

    /**
     * 搬运工标识
     */
    private String workerSign;

    /**
     * 姓名
     */
    private String name;

    /**
     * 出生日期
     */
    private LocalDate bornDate;

    /**
     * 联系方式
     */
    private String contact;

    /**
     * 人脸
     */
    private String faceUrl;

    /**
     * 状态
     * @see com.senox.tms.constant.UnloadingWorkerStatus
     */
    private Integer status;

    /**
     * 挂牌
     */
    private Boolean listing;

    /**
     * @see com.senox.tms.constant.UnloadingClasses
     */
    private Integer classes;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 轮次
     */
    private Integer roundNum;

    /**
     * 惩罚轮次
     */
    private Integer punishRoundNum;

    /**
     * 忙碌
     */
    @TableField(exist = false)
    private boolean busy;

    /**
     * 序号
     */
    @TableField(exist = false)
    private int rowNum;
}

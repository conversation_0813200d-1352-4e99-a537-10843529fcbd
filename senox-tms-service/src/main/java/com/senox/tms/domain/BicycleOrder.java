package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import com.senox.tms.constant.BicycleOrderSenderType;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/18 9:33
 */
@Getter
@Setter
@TableName("t_bicycle_order")
public class BicycleOrder extends TableIdEntity {

    /**
     * 订单流水号
     */
    private String orderSerialNo;

    /**
     * 配送单号
     */
    private String deliveryOrderSerialNo;

    /**
     * 起点id
     */
    private Long startPointId;

    /**
     * 起点
     */
    private String startPointName;

    /**
     * 详细起点地址
     */
    private String startPointDetailName;

    /**
     * 终点id
     */
    private Long endPointId;

    /**
     * 终点
     */
    private String endPointName;

    /**
     * 详细终点地址
     */
    private String endPointDetailName;

    /**
     * 预约配送时间起
     */
    private LocalDateTime sendTimeStart;

    /**
     * 预约配送时间止
     */
    private LocalDateTime sendTimeEnd;

    /**
     * 寄件人id
     */
    private Long senderId;

    /**
     * 寄件人
     */
    private String sender;

    /**
     * 发货人冷藏编号
     */
    private String senderSerialNo;

    /**
     * 寄件人联系方式
     */
    private String senderContact;

    /**
     * 寄件人类别
     * @see BicycleOrderSenderType
     */
    private Integer senderType;

    /**
     * 收件人
     */
    private String recipient;

    /**
     * 收件人联系方式
     */
    private String recipientContact;

    /**
     * 车牌
     */
    private String carNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 其他备注
     */
    private String otherRemark;

    /**
     * 件数
     */
    private BigDecimal pieces;

    /**
     * 配送费用
     */
    private BigDecimal deliveryCharge;

    /**
     * 其他费用
     */
    private BigDecimal otherCharge;

    /**
     * 装卸费
     */
    private BigDecimal handlingCharge;

    /**
     * 上楼费
     */
    private BigDecimal upstairsCharge;

    /**
     * 总费用
     */
    private BigDecimal totalCharge;

    /**
     * 收费标准id
     */
    private Long chargesId;

    /**
     * 状态 0：草稿 1：正常
     */
    private Integer state;

    /**
     * 下单时间
     */
    private LocalDateTime orderTime;

    /**
     * 来源(0:系统生成;1:导入)
     */
    private Integer origin;

    /**
     * 状态 0：正常 1：取消
     */
    private Integer status;

    /**
     * 订单状态备注
     */
    private String statusRemark;

    /**
     * 微信创建用户
     */
    private String createOpenid;
}

package com.senox.tms.listener;

import com.senox.tms.event.UnloadingWebSocketEvent;
import com.senox.tms.websocket.UnloadingWebSocketServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 2023-10-19
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class UnloadingWebSocketEventListener {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT,classes = {UnloadingWebSocketEvent.class},fallbackExecution = true)
    public void onWebSocketEvent(UnloadingWebSocketEvent event) {
        String refresh = "refresh";
        UnloadingWebSocketServer.sendMessage(refresh);
    }
}

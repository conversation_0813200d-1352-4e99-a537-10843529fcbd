package com.senox.tms.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.BicycleBillSettlement;
import com.senox.tms.event.BicycleBillSettlementEvent;
import com.senox.tms.event.BicycleBillSettlementNotifyEvent;
import com.senox.tms.template.BicycleBillSettlementTemplateMessage;
import com.senox.user.constant.MerchantBillSettlePeriod;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-11-13
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class BicycleBillSettlementNotifyEventListener {
    private final RabbitTemplate rabbitTemplate;

    @Async
    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT, classes = BicycleBillSettlementNotifyEvent.class, fallbackExecution = true)
    public void onBicycleBillSettlementNotify(BicycleBillSettlementNotifyEvent event) {
        List<BicycleBillSettlement> billSettlements = event.getEventData();
        if (CollectionUtils.isEmpty(billSettlements)) {
            return;
        }
        List<BicycleBillSettlementTemplateMessage> billSettlementTemplateMessages = new ArrayList<>(billSettlements.size());
        Map<Long, List<BicycleBillSettlement>> billSettlementMap = billSettlements.stream().collect(Collectors.groupingBy(BicycleBillSettlement::getMerchantId));
        billSettlementMap.forEach((k, v) -> {
            BicycleBillSettlementTemplateMessage billSettlementTemplateMessage = new BicycleBillSettlementTemplateMessage();
            billSettlementTemplateMessage.setMerchantId(k);
            billSettlementTemplateMessage.setName(BicycleBillSettlementEvent.PROJECT_NAME);
            billSettlementTemplateMessage.setAmount(v.stream().map(BicycleBillSettlement::getAmount).parallel().reduce(BigDecimal.ZERO, BigDecimal::add));
            billSettlementTemplateMessage.setSettlePeriod(MerchantBillSettlePeriod.fromNumber(v.get(0).getSettlePeriod()).getName());
            billSettlementTemplateMessages.add(billSettlementTemplateMessage);
        });
        log.info("【Bicycle】Settlement notify: {}", JsonUtils.object2Json(billSettlementTemplateMessages));
        rabbitTemplate.convertAndSend(TmsConst.MQ.MQ_TMS_BICYCLE_BILL_SETTLEMENT_MESSAGE, billSettlementTemplateMessages);
    }
}

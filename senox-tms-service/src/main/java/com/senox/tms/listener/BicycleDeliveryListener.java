package com.senox.tms.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.domain.BicycleDeliveryOrderDetail;
import com.senox.tms.event.BicycleCancelBillPayoffEvent;
import com.senox.tms.event.BicycleDeliveryMessageEvent;
import com.senox.tms.event.BicycleGenerateBillPayoffEvent;
import com.senox.tms.service.BicycleDeliveryOrderDetailItemService;
import com.senox.tms.service.BicycleDeliveryOrderDetailService;
import com.senox.tms.service.BicycleDeliveryOrderService;
import com.senox.tms.vo.BicycleDeliveryOrderDetailVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/11 15:48
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BicycleDeliveryListener {

    private final BicycleDeliveryOrderService bicycleDeliveryOrderService;
    private final BicycleDeliveryOrderDetailItemService deliveryOrderDetailItemService;
    private final BicycleDeliveryOrderDetailService bicycleDeliveryOrderDetailService;

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void generateBillAndPayoff(BicycleGenerateBillPayoffEvent event) {
        log.info("收到账单生成事件 {}", JsonUtils.object2Json(event.getOrderSerialList()));
        //生成应收和应付账单 收益明细
        bicycleDeliveryOrderService.generateBillAndPayoff(event.getOrderSerialList(), LocalDateTime.now());
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void cancelBillAndPayoff(BicycleCancelBillPayoffEvent event) {
        log.info("收到账单取消事件 {}", JsonUtils.object2Json(event));
        List<BicycleDeliveryOrderDetail> orderDetails = event.getOrderDetails();
        List<BicycleDeliveryOrderDetailVo> detailVos = event.getDetailVos();
        BicycleDeliveryOrder deliveryOrder = event.getBicycleDeliveryOrder();
        if (CollectionUtils.isEmpty(detailVos) && !CollectionUtils.isEmpty(orderDetails)) {
            log.info("【取消骑手】删除对应的应收应付，配送单详情为{}", JsonUtils.object2Json(orderDetails));
             //删除配送子详细
            deliveryOrderDetailItemService.deleteByDeliveryOrderSerialNo(deliveryOrder.getDeliveryOrderSerialNo());
            bicycleDeliveryOrderDetailService.deleteDeliveryOrderDetailByDeliveryOrderSerialNo(deliveryOrder.getDeliveryOrderSerialNo());
            bicycleDeliveryOrderService.cancelBillAndPayoff(orderDetails.stream().map(BicycleDeliveryOrderDetail::getOrderSerialNo).distinct().collect(Collectors.toList()));
        }
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void sendMessage(BicycleDeliveryMessageEvent event) {
        log.info("收到发送模板消息事件 {}", JsonUtils.object2Json(event));
        //发送消息
        bicycleDeliveryOrderService.sendMessage(event.getBicycleDeliveryOrder(), event.getRiderIdList());
    }

}

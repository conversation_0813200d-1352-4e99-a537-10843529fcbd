package com.senox.tms.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.dto.LogisticTransportBillSettlementSendNotifyDto;
import com.senox.tms.event.LogisticTransportBillNotifySendEvent;
import com.senox.tms.vo.LogisticTransportBillSettlementVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogisticTransportBillListener {
    private final RabbitTemplate rabbitTemplate;
    private static final String NOTIFY_NAME = "城际配送费";

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = LogisticTransportBillNotifySendEvent.class, fallbackExecution = true)
    public void onSettlementSendNotify(LogisticTransportBillNotifySendEvent event) {
        List<LogisticTransportBillSettlementVo> eventData = event.getEventData();
        log.info("【Logistic transport】账单结算单下发通知: {}", JsonUtils.object2Json(eventData));
        if (CollectionUtils.isEmpty(eventData)) {
            log.info("【Logistic transport】账单结算单为空");
        }
        List<LogisticTransportBillSettlementSendNotifyDto> sendNotifyList = new ArrayList<>(eventData
                .stream()
                //过滤掉非发货方支付的
                .filter(x -> x.getPayer().equals(LogisticTransportOrderPayer.CONSIGNOR))
                //按商户分组并统计每个商户的金额
                .collect(Collectors.groupingBy(LogisticTransportBillSettlementVo::getMerchantId, Collectors.collectingAndThen(Collectors.toList(), x -> {
                    BigDecimal amount = x.stream().map(LogisticTransportBillSettlementVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    LogisticTransportBillSettlementSendNotifyDto notify = new LogisticTransportBillSettlementSendNotifyDto();
                    notify.setMerchantId(x.get(0).getMerchantId());
                    notify.setName(NOTIFY_NAME);
                    notify.setAmount(amount);
                    return notify;
                }))).values());
        if (CollectionUtils.isEmpty(sendNotifyList)) {
            return;
        }
        log.info("【Logistic transport】发送账单结算单缴费通知至MQ: {}", JsonUtils.object2Json(sendNotifyList));
        rabbitTemplate.convertAndSend(TmsConst.MQ.EX_TMS_LOGISTIC_TRANSPORT, TmsConst.MQ.MQ_TMS_LOGISTIC_TRANSPORT_BILL_SETTLEMENT_SEND_MESSAGE, sendNotifyList);
    }
}

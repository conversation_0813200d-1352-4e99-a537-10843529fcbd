package com.senox.tms.listener;

import com.senox.tms.event.BicycleOrderWebSocketEvent;
import com.senox.tms.websocket.WebSocketServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * <AUTHOR>
 * @date 2023-10-19
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class BicycleOrderWebSocketEventListener {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT,classes = {BicycleOrderWebSocketEvent.class},fallbackExecution = true)
    public void onWebSocketEvent(BicycleOrderWebSocketEvent event) {
        String refresh = "refresh";
        WebSocketServer.sendMessage(refresh);
    }
}

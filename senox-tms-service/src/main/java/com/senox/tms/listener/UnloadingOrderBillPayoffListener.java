package com.senox.tms.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.event.UnloadingOrderBillPayoffEvent;
import com.senox.tms.service.UnloadingOrderBillService;
import com.senox.tms.service.UnloadingOrderPayoffService;
import com.senox.tms.vo.UnloadingMonthVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4 13:41
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UnloadingOrderBillPayoffListener {

    private final UnloadingOrderPayoffService payoffService;
    private final UnloadingOrderBillService billService;

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void updateOrderBillPayoff(UnloadingOrderBillPayoffEvent event) {
        log.info("收到鹏翔订单更新金额事件。。。{}", JsonUtils.object2Json(event));
        List<String> orderNoList = event.getOrderNoList();
        UnloadingMonthVo monthVo = new UnloadingMonthVo();
        monthVo.setOrderNoList(orderNoList);
        billService.generateBill(monthVo);
        payoffService.generateOrderPayoff(monthVo);
    }
}

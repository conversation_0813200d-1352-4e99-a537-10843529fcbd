package com.senox.tms.listener;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.constant.BicycleOrderState;
import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.event.CancelMergedEvent;
import com.senox.tms.event.MergedEvent;
import com.senox.tms.service.BicycleDeliveryOrderService;
import com.senox.tms.service.BicycleOrderService;
import com.senox.tms.vo.BicycleOrderSearchVo;
import com.senox.tms.vo.BicycleOrderVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/24 11:03
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MergedListener {

    private final BicycleDeliveryOrderService bicycleDeliveryOrderService;
    private final BicycleOrderService bicycleOrderService;

    @Async
    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT, classes = CancelMergedEvent.class, fallbackExecution = true)
    public void cancelMergedListener(CancelMergedEvent event) {
        log.info("收到取消合并事件 {}", JsonUtils.object2Json(event));
        BicycleOrderVo bicycleOrderVo = event.getEventData();
        //查询是否有分配骑手
        if (bicycleOrderVo.getStatus() == null || bicycleOrderVo.getStatus() == BicycleDeliveryOrderStatus.UN_ASSIGN.getNumber()) {
            //取消合并
            bicycleDeliveryOrderService.cancelMerged(Collections.singletonList(bicycleOrderVo.getOrderSerialNo()));
        } else {
            throw new BusinessException("订单已分配骑手，不能更新状态");
        }
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = MergedEvent.class, fallbackExecution = true)
    public void mergedListener(MergedEvent event) {
        log.info("收到合并事件 {}", JsonUtils.object2Json(event));
        BicycleOrder bicycleOrder = event.getEventData();
        BicycleOrderSearchVo searchVo = new BicycleOrderSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(5);
        searchVo.setPage(false);
        searchVo.setDelivery(false);
        searchVo.setAssignRider(false);
        searchVo.setState(BicycleOrderState.FINALIZE.getNumber());
        //查找未分配的订单
        List<BicycleOrderVo> dataList = bicycleOrderService.listOrder(searchVo).getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("无未合并的订单");
            return;
        }
        //过滤始发地和目的地一致的，总计件数不超过50件的，因为是在事务之后执行，则排除自身订单号
        dataList = dataList.stream().filter(data -> Objects.equals(data.getStartPointId(), bicycleOrder.getStartPointId())
                                && Objects.equals(data.getEndPointId(), bicycleOrder.getEndPointId())
                                && DecimalUtils.add(data.getPieces(), bicycleOrder.getPieces()).compareTo(BigDecimal.valueOf(50)) < 0
                                && !Objects.equals(data.getOrderSerialNo(), bicycleOrder.getOrderSerialNo()))
                .sorted(Comparator.comparing(BicycleOrderVo::getOrderTime)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("无匹配的订单，不进行合并操作");
            return;
        }
        //获取最近的一个订单
        BicycleOrderVo orderVo = dataList.get(0);
        List<String> orderSerialNoList = new ArrayList<>();
        orderSerialNoList.add(bicycleOrder.getOrderSerialNo());
        orderSerialNoList.add(orderVo.getOrderSerialNo());
        //合并订单
        bicycleDeliveryOrderService.merged(orderSerialNoList, null);
    }
}

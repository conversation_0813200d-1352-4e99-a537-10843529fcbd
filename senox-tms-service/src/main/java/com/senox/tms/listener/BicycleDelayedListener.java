package com.senox.tms.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.rabbitmq.client.Channel;
import com.senox.common.utils.JsonUtils;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.event.UnpickedEvent;
import com.senox.tms.vo.BicycleRiderUnpickedMessageVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/10/19 17:17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BicycleDelayedListener {

    private final ApplicationEventPublisher eventPublisher;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = TmsConst.MQ.MQ_TMS_UNPICKED_GOODS, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = TmsConst.MQ.EX_TMS_DELAYED, type = "x-delayed-message",
                    arguments = @Argument(name = "x-delayed-type", value = "topic")),
            key = TmsConst.MQ.KEY_TMS_UNPICKED_GOODS), containerFactory = "containerFactory")
    public void unpickedListener(Message message, Channel channel) throws IOException {
        String mqMessage = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("收到骑手未揽货延时消息 {}", mqMessage);
        BicycleRiderUnpickedMessageVo messageVo = JsonUtils.json2GenericObject(mqMessage, new TypeReference<BicycleRiderUnpickedMessageVo>() {});

        log.info("发送事件 {}", JsonUtils.object2Json(messageVo));
        eventPublisher.publishEvent(new UnpickedEvent(this, messageVo));
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);

    }
}

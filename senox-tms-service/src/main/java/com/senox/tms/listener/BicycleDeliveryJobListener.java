package com.senox.tms.listener;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.domain.BicycleDeliveryOrderDetail;
import com.senox.tms.event.BicycleDeliveryJobEvent;
import com.senox.tms.service.BicycleDeliveryOrderDetailService;
import com.senox.tms.service.BicycleDeliveryOrderService;
import com.senox.tms.service.BicycleOrderService;
import com.senox.tms.vo.BicycleOrderVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/27 14:42
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class BicycleDeliveryJobListener {

    public static final String PRE_ORDER_SERIAL_BO = "T_";

    private final BicycleDeliveryOrderDetailService bicycleDeliveryOrderDetailService;
    private final BicycleDeliveryOrderService bicycleDeliveryOrderService;
    private final BicycleOrderService orderService;
    private final RabbitTemplate rabbitTemplate;

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = {BicycleDeliveryJobEvent.class},fallbackExecution = true)
    public void bicycleDeliveryJobListener(BicycleDeliveryJobEvent event) {
        log.info("收到三轮车订单配送任务配送更新 event: {}", JsonUtils.object2Json(event));
        BicycleDeliveryOrderDetail orderDetail = event.getOrderDetail();
        //更新该配送单详细的状态
        bicycleDeliveryOrderDetailService.updateDeliveryOrderDetailStatus(orderDetail.getId(), BicycleDeliveryOrderStatus.fromStatus(event.getStatus()), event.getRating(), orderDetail.getOrderSerialNo(), event.getPoint());
        //查询该配送单下的索引配送单详细
        List<BicycleDeliveryOrderDetail> orderDetails = bicycleDeliveryOrderDetailService.listOrderDetailByDeliveryOrderSerialNo(orderDetail.getDeliveryOrderSerialNo());
        if (CollectionUtils.isEmpty(orderDetails)) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        BicycleDeliveryOrder deliveryOrder = bicycleDeliveryOrderService.findBySerialNo(orderDetail.getDeliveryOrderSerialNo());
        if (deliveryOrder == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "配送单未找到");
        }
        //如果都状态都一致 则更改配送单状态
        if (orderDetails.stream().noneMatch(item -> Objects.equals(item.getStatus(), deliveryOrder.getStatus()))) {
            log.info("【配送单任务状态一致，更新主配送单状态。。。。】");
            deliveryOrder.setStatus(event.getStatus());
            bicycleDeliveryOrderService.updateDeliveryOrderStatus(deliveryOrder);
            //如果是第三方的订单
            if (orderDetail.getOrderSerialNo().startsWith(PRE_ORDER_SERIAL_BO)) {
                BicycleOrderVo orderVo = orderService.findOrderVoByOrderSerialNo(orderDetail.getOrderSerialNo());
                rabbitTemplate.convertAndSend(TmsConst.MQ.MQ_TMS_AC_ORDER_STATUS_NOTIFY, orderVo);
                log.info("发送第三方订单配送单状态更新到消息队列 {}", JsonUtils.object2Json(orderVo));
            }
        }
    }
}

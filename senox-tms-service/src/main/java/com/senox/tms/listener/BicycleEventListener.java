package com.senox.tms.listener;

import com.rabbitmq.client.Channel;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.context.AdminContext;
import com.senox.context.PluginEnv;
import com.senox.tms.component.MerchantComponent;
import com.senox.tms.config.NoticeBicycleConfig;
import com.senox.tms.constant.*;
import com.senox.tms.domain.*;
import com.senox.tms.event.UnpickedEvent;
import com.senox.tms.service.*;
import com.senox.tms.utils.RabbitMqSenderUtil;
import com.senox.tms.vo.*;
import com.senox.user.vo.MerchantVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/10/20 8:09
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BicycleEventListener {

    private final BicycleDeliveryOrderService bicycleDeliveryOrderService;
    private final BicycleDeliveryOrderDetailService deliveryOrderDetailService;
    private final NoticeBicycleConfig noticeBicycleConfig;
    private final RabbitTemplate rabbitTemplate;
    private final BicycleSettingService bicycleSettingService;
    private final BicycleOrderService orderService;
    private final MerchantComponent merchantComponent;
    private final BicycleRiderService riderService;


    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = UnpickedEvent.class, fallbackExecution = true)
    public void unpickedEvent(UnpickedEvent event) {
        log.info("收到事件 {}", JsonUtils.object2Json(event));
        BicycleRiderUnpickedMessageVo messageVo = event.getEventData();
        //获取超时未揽货开关设置
        BicycleSetting bicycleSetting = bicycleSettingService.findByAlias(BicycleSettingType.UNPICKED.getAlias());
        if (bicycleSetting == null || BooleanUtils.isFalse(bicycleSetting.getEnable())) {
            return;
        }

        BicycleDeliveryOrder deliveryOrder = bicycleDeliveryOrderService.findBySerialNo(messageVo.getDeliveryOrderSerialNo());
        log.info("配送单信息为 {}", JsonUtils.object2Json(deliveryOrder));
        if (deliveryOrder == null) {
            return;
        }
        List<BicycleDeliveryOrderDetail> orderDetails = deliveryOrderDetailService.listOrderDetailByDeliveryOrderSerialNo(messageVo.getDeliveryOrderSerialNo());
        orderDetails.forEach(detail -> {
            BicycleOrder order = orderService.findBySerialNo(detail.getOrderSerialNo());
            //订单如果被取消
            if (order == null || order.getState() == BicycleOrderState.DRAFT.getNumber() || order.getStatus() == BicycleOrderStatus.CANCEL.getNumber()) {
                log.info("订单未存在或处于草稿、取消状态， 单号未：{}， 结果为：{}", detail.getOrderSerialNo(), JsonUtils.object2Json(order));
                return;
            }
            //如果还未揽货则发模板消息提示并且骑手一致
            if (detail.getStatus() == BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber() && Objects.equals(detail.getRiderId(), messageVo.getRiderId())) {
                //发送模板消息提示
                rabbitTemplate.convertAndSend(TmsConst.MQ.MQ_TMS_AC_TIMEOUT_UNPICKED_GOODS, messageVo);
                log.info("发送骑手超时未拣货到消息队列 {}", JsonUtils.object2Json(messageVo));

                //继续下一档
                int unpickedSecond = noticeBicycleConfig.getUnpickedSecond();
                log.info("发送骑手未揽货延时消息------，延时时间:{}, 参数为:{}", unpickedSecond, JsonUtils.object2Json(messageVo));
                RabbitMqSenderUtil.sendUnpickedMessage(unpickedSecond, messageVo, rabbitTemplate);
            }
        });
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = TmsConst.MQ.MQ_TMS_AC_AUTO_TAKING_ORDER, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = TmsConst.MQ.EX_TMS_AC_AUTO_TAKING_ORDER),
            key = TmsConst.MQ.MQ_TMS_AC_AUTO_TAKING_ORDER
    ), containerFactory = "containerFactory")
    public void autoTakingOrderListener(Message message, Channel channel) throws IOException {
        try {
            String mqMessage = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("收到骑手自动接单消息 {}", mqMessage);
            BicycleSetting bicycleSetting = bicycleSettingService.findByAlias(BicycleSettingType.RIDER_AUTO_TAKING_ORDER.getAlias());
            if (bicycleSetting == null || BooleanUtils.isFalse(bicycleSetting.getEnable())) {
                log.info("骑手自动接单开关未启用");
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            //订单id
            Long orderId = Long.valueOf(mqMessage);
            BicycleOrderVo orderVo = orderService.findOrderVoById(orderId, false);
            MerchantVo merchantVo = merchantComponent.findById(orderVo.getSenderId());
            log.info("商户信息:{}", JsonUtils.object2Json(merchantVo));
            BicycleRider rider = riderService.findByReferralCode(merchantVo.getReferralCode());
            if (rider == null) {
                log.info("骑手不存在");
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            log.info("骑手自动接单，骑手信息为:{}", JsonUtils.object2Json(rider));
            //需要mock上下文信息
            AdminContext.setPluginEnv(PluginEnv.builder().mq(true).build());
            //配送单
            bicycleDeliveryOrderService.saveDeliveryOrder(orderVo, rider);
            log.info("骑手接单成功");
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("骑手自动接单出错，原因是:{}", JsonUtils.object2Json(e));
        }
    }


}

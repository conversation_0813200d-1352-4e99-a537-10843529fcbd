package com.senox.tms.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.event.BicycleOperateAnalysisEvent;
import com.senox.tms.service.BicycleOperateAnalysisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/20 14:06
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BicycleOperateAnalysisListener {

    private final BicycleOperateAnalysisService bicycleOperateAnalysisService;

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = BicycleOperateAnalysisEvent.class, fallbackExecution = true)
    public void bicycleOperateAnalysisListener(BicycleOperateAnalysisEvent event) {
        List<LocalDate> dateList = event.getEventData();
        log.info("生成运营分析报表，时间列表：{}", JsonUtils.object2Json(dateList));
        dateList.forEach(data -> {
            LocalDateTime startTime = data.atTime(LocalTime.MIN);
            LocalDateTime endTime = data.atTime(LocalTime.MAX);
            bicycleOperateAnalysisService.addBicycleOperateAnalysis(startTime, endTime);
        });
    }
}

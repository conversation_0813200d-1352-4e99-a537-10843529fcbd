package com.senox.tms.listener;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.senox.common.utils.JsonUtils;
import com.senox.tms.domain.BicyclePayoff;
import com.senox.tms.event.*;
import com.senox.tms.service.BicycleDeliveryOrderService;
import com.senox.tms.service.BicyclePayoffService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.BicycleDateVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/15 10:32
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BicycleOrderListener {

    private final BicycleDeliveryOrderService deliveryOrderService;
    private final BicyclePayoffService payoffService;
    private final ApplicationEventPublisher publisher;

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = BicycleOrderBillEvent.class, fallbackExecution = true)
    public void onBicycleOrderBill(BicycleOrderBillEvent event) {
        MultiValueMap<LocalDateTime, String> orderSerialNoMap = event.getEventData();
        log.info("【Bicycle】Order serial no: {}", orderSerialNoMap);
        if (CollectionUtils.isEmpty(orderSerialNoMap)) {
            return;
        }
        //生成应收和应付账单 收益明细
        orderSerialNoMap.forEach((orderTime, orderSerialNos) -> deliveryOrderService.generateBillAndPayoff(orderSerialNos, orderTime));
        //根据时间生成应付账单日报表
        publisher.publishEvent(new BicyclePayoffEvent(this, orderSerialNoMap.keySet()));
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = BicyclePayoffEvent.class, fallbackExecution = true)
    public void onBicyclePayoff(BicyclePayoffEvent event) {
        Collection<LocalDateTime> orderTimes = event.getEventData();
        log.info("【Bicycle】Payoff order times: {}", orderTimes);
        if (CollectionUtils.isEmpty(orderTimes)) {
            return;
        }
        //去重并生成应付账单日报表
        orderTimes.stream().distinct().forEach(orderTime -> {
            BicycleDateVo date = new BicycleDateVo();
            date.setYear(orderTime.getYear());
            date.setMonth(orderTime.getMonthValue());
            date.setDay(orderTime.getDayOfMonth());
            payoffService.generateReportByDay(date);
        });
        List<LocalDate> orderDates = orderTimes.stream().map(LocalDateTime::toLocalDate).collect(Collectors.toList());
        //根据时间生成订单单日报表
        publisher.publishEvent(new BicycleOrderDayReportGenerateEvent(this, orderDates));
        //根据时间生成运营分析报表
        publisher.publishEvent(new BicycleOperateAnalysisEvent(this, orderDates));
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = BicyclePayoffDeliveryTimeEvent.class, fallbackExecution = true)
    public void onBicyclePayoffDeliveryTime(BicyclePayoffDeliveryTimeEvent event) {
        List<BicyclePayoff> payoffs = event.getEventData();
        log.info("【Bicycle】Payoff order delivery time: {}", JsonUtils.object2Json(payoffs));
        payoffs.forEach(payoff -> {
            ContextUtils.initEntityModifier(payoff);
            payoffService.update(payoff, new UpdateWrapper<BicyclePayoff>()
                    .lambda()
                    .eq(BicyclePayoff::getOrderSerialNo, payoff.getOrderSerialNo())
                    .eq(BicyclePayoff::getDeliveryOrderSerialNo, payoff.getDeliveryOrderSerialNo())
                    .eq(BicyclePayoff::getRiderId, payoff.getRiderId())
            );
        });
    }
}

package com.senox.tms.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.event.BicycleOrderDayReportGenerateEvent;
import com.senox.tms.service.BicycleOrderDayReportService;
import com.senox.tms.vo.BicycleReportDateVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/20 9:29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BicycleOrderDayReportGenerateListener {

    private final BicycleOrderDayReportService bicycleOrderDayReportService;

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, classes = BicycleOrderDayReportGenerateEvent.class, fallbackExecution = true)
    public void bicycleOrderDayReportGenerateListener(BicycleOrderDayReportGenerateEvent event) {
        List<LocalDate> dateList = event.getEventData();
        log.info("生成日报表，时间列表：{}", JsonUtils.object2Json(dateList));
        dateList.forEach(date -> {
            BicycleReportDateVo dateVo = new BicycleReportDateVo();
            dateVo.setYear(date.getYear());
            dateVo.setMonth(date.getMonthValue());
            dateVo.setDay(date.getDayOfMonth());
            bicycleOrderDayReportService.generateDayReport(dateVo);
        });
    }
}

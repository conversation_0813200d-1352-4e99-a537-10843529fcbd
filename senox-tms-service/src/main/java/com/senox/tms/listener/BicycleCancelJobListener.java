package com.senox.tms.listener;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.domain.BicycleDeliveryOrderDetail;
import com.senox.tms.domain.BicycleDeliveryOrderJob;
import com.senox.tms.event.BicycleCancelJobEvent;
import com.senox.tms.service.BicycleDeliveryOrderJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/24 17:15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BicycleCancelJobListener {

    private final BicycleDeliveryOrderJobService bicycleDeliveryOrderJobService;

    @Async
    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT, classes = BicycleCancelJobEvent.class, fallbackExecution = true)
    public void bicycleCancelJobListener(BicycleCancelJobEvent event) {
        log.info("收到事件 {}", JsonUtils.object2Json(event));
        List<BicycleDeliveryOrderDetail> orderDetails = event.getEventData();
        for (BicycleDeliveryOrderDetail orderDetail : orderDetails) {
            List<BicycleDeliveryOrderJob> orderJobs = bicycleDeliveryOrderJobService.listJobByDeliveryDetailId(orderDetail.getId());
            orderJobs.forEach(orderJob -> bicycleDeliveryOrderJobService.deleteBicycleDeliveryOrderJob(orderJob.getId()));
        }
    }
}

package com.senox.tms.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.rabbitmq.client.Channel;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.context.AdminContext;
import com.senox.context.PluginEnv;
import com.senox.dm.vo.EmployeeAccessStateVo;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.event.UnloadingListedEvent;
import com.senox.tms.service.UnloadingOrderService;
import com.senox.tms.service.UnloadingWorkerAccessService;
import com.senox.tms.service.UnloadingWorkerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/30 13:46
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UnloadingWorkerListener {

    private final UnloadingWorkerAccessService workerAccessService;
    private final UnloadingWorkerService workerService;
    private final UnloadingOrderService orderService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = TmsConst.MQ.MQ_DEVICE_AC_WORKER_RIGHT_AUTH, durable = "true", exclusive = "false", autoDelete = "false"),
            exchange = @Exchange(value = TmsConst.MQ.EX_DEVICE_AC_WORKER_RIGHT_AUTH, type = ExchangeTypes.TOPIC),
            key = TmsConst.MQ.MQ_DEVICE_AC_WORKER_RIGHT_AUTH
    ), containerFactory = "containerFactory")
    public void accessRightAuthListener(Message message, Channel channel) throws IOException {
        String mqMessage = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("收到门禁授权成功消息 {}", mqMessage);

        EmployeeAccessStateVo accessAuthState = JsonUtils.json2GenericObject(mqMessage, new TypeReference<EmployeeAccessStateVo>() {});
        workerAccessService.updateWorkerAccessState(accessAuthState);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = TmsConst.MQ.MQ_DEVICE_AC_UW_WORKER, durable = "true", autoDelete = "false"),
            exchange = @Exchange(value = TmsConst.MQ.EX_DEVICE_AC_UW_WORKER),
            key = TmsConst.MQ.MQ_DEVICE_AC_UW_WORKER
    ), containerFactory = "containerFactory")
    public void workerClockListener(Message message, Channel channel) throws IOException {
        String mqMessage = new String(message.getBody(), StandardCharsets.UTF_8);
        String workerSign = JsonUtils.json2GenericObject(mqMessage, new TypeReference<String>() {});
        log.info("收到搬运工扫脸消息 {}", workerSign);

        AdminContext.setPluginEnv(PluginEnv.builder().mq(true).build());
        workerService.updateWorkerStatus(workerSign, UnloadingWorkerStatus.ALREADY_LISTED.getNumber(), true);
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    public void workerListedListener(UnloadingListedEvent event) {
        log.info("收到搬运工挂牌，同订单其他搬运工挂牌事件：{}", JsonUtils.object2Json(event));
        //凌晨三点开始算
        LocalDateTime startTime = LocalDateTime.now().withHour(3).withMinute(0).withSecond(0);
        Long orderId = orderService.lastOrderByWorkerId(startTime, event.getWorkerId());
        log.info("最近参与的订单id为:{}", orderId);
        if (orderId == null) {
            return;
        }
        List<UnloadingWorker> workers = workerService.listWorkerByOrderId(orderId);
        //排除自身
        workers = workers.stream().filter(x -> !Objects.equals(x.getId(), event.getWorkerId()) && BooleanUtils.isFalse(x.getListing())).collect(Collectors.toList());
        log.info("订单id：{} 排除掉管理自动下牌的，剩余其他参与的搬运工有：{}", orderId, JsonUtils.object2Json(workers));
        if (CollectionUtils.isEmpty(workers)) {
            return;
        }
        workerService.updateBatchWorkerStatusBySigns(workers.stream().map(UnloadingWorker::getWorkerSign).collect(Collectors.toList())
                , UnloadingWorkerStatus.ALREADY_LISTED.getNumber(), false);
    }
}

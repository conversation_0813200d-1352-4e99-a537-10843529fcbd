package com.senox.tms.component;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.user.api.clients.MerchantClient;
import com.senox.user.vo.MerchantSearchVo;
import com.senox.user.vo.MerchantVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-21
 */
@RequiredArgsConstructor
@Component
public class MerchantComponent {
    private final MerchantClient merchantClient;

    /**
     * 根据id查询商户
     *
     * @param id id
     * @return 返回查询到的商户
     */
    public MerchantVo findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0)) {
            throw new BusinessException(ResultConst.INVALID_PARAMETER);
        }
        try {
            return merchantClient.findById(id);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }


    /**
     * 商户列表
     *
     * @param searchVo 查询
     * @return 返回查询到的商户
     */
    public List<MerchantVo> list(MerchantSearchVo searchVo) {
        try {
            return merchantClient.list(searchVo);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return Collections.emptyList();
    }

    /**
     * 批量更新收费标准
     *
     * @param ids       id集
     * @param chargesId 收费标准id
     * @param fullData  true:全量;false:增量
     */
    public void updateChargesBatch(Collection<Long> ids, Long chargesId, Boolean fullData) {
        try {
            merchantClient.updateCharges(ids, chargesId, fullData);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
    }
}

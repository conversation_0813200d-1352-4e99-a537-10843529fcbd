package com.senox.tms.component;

import com.senox.common.utils.FeignUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.pm.api.clients.OrderClient;
import com.senox.pm.vo.OrderResultVo;
import com.senox.pm.vo.OrderVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-11-10
 */
@RequiredArgsConstructor
@Component
public class OrderComponent {
    private final OrderClient orderClient;

    /**
     * 根据id查找订单
     *
     * @param id id
     * @return 返回查找到的订单
     */
    public OrderVo findOrderById(Long id) {
        if (WrapperClassUtils.biggerThanLong(id, 0L)) {
            try {
                return orderClient.findById(id);
            } catch (FeignException e) {
                FeignUtils.handleFeignException(e);
            }
        }
        return null;
    }

    /**
     * 下单
     *
     * @param order 订单
     * @return 返回下单结果
     */
    public OrderResultVo addOrder(OrderVo order) {
        try {
            return orderClient.addOrder(order);
        } catch (FeignException e) {
            FeignUtils.handleFeignException(e);
        }
        return null;
    }
}

package com.senox.tms.websocket;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.user.api.clients.AdminUserClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2023-10-19
 */
@Slf4j
@ServerEndpoint("/ws/tms/unloading/{userId}")
@Component
public class UnloadingWebSocketServer {

    private static AdminUserClient adminUserClient;

    @Autowired
    public void setAdminUserClient(AdminUserClient adminUserClient){
        UnloadingWebSocketServer.adminUserClient = adminUserClient;
    }

    private static final List<String> AUTH_CODES = new ArrayList<>();

    /**
     * 静态变量，用来记录当前在线连接数
     */
    private static final AtomicInteger ONLINE_COUNT = new AtomicInteger();
    /**
     * concurrent 包的线程安全Set，用来存放每个客户端对应的 myWebSocket对象
     * 根据userId来获取对应的 WebSocket
     */
    private static final Map<String, UnloadingWebSocketServer> CONNECTED_CLIENTS = new ConcurrentHashMap<>();

    /**
     * 接收 userId
     */
    private String userId;
    /**
     * 与某个客户端的连接会话
     */
    private Session session;

   @PostConstruct
   public void init(){
       AUTH_CODES.add("unloading_worker_list");
   }


    /**
     * 连接建立成功调用的方法
     *
     * @param session
     * @param userId
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) throws IOException {
        this.userId = userId;
        if (!auth(Long.valueOf(userId))) {
            //鉴权没过，断开连接
            log.info("【鹏翔装卸】用户{}鉴权未通过，关闭通道",userId);
            session.close();
            return ;

        }
        this.session = session;

        // 在线数 +1
        addOnlineCount();
        log.info("【鹏翔装卸】有新窗口开始监听：{}，当前在线人数为 {}。", userId, getOnlineCount());

        CONNECTED_CLIENTS.put(userId, this);
        log.info("【鹏翔装卸】webSocketMap -> {}", JsonUtils.object2Json(CONNECTED_CLIENTS));

        try {
            sendMessage(ResultConst.SUCCESS.name());
        } catch (Exception e) {
            log.warn("【鹏翔装卸】用户 " + userId + " 建立连接发送消息成功", e);
            throw new BusinessException("【鹏翔装卸】建立websocket连接，通信失败");
        }
    }

    /**
     * 关闭连接
     */
    @OnClose
    public void onClose() {
        if (CONNECTED_CLIENTS.containsKey(this.userId)) {
            CONNECTED_CLIENTS.remove(this.userId);
            subOnlineCount();
            log.info("【鹏翔装卸】有一连接关闭，当前在线人数为：{}", getOnlineCount());
        }
    }

    /**
     * websocket 连接错误处理
     *
     * @param session
     * @param t
     */
    @OnError
    public void onError(Session session, Throwable t) {
        log.error("【鹏翔装卸】WebSocket 处理客户端请求 " + this.userId + " 错误", t);
    }


    /**
     * 心跳监测
     *
     * @param session 会话
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(Session session, String message) throws IOException {
        log.info("【鹏翔装卸】收到客户端发送的数据:{}", message);
        session.getBasicRemote().sendText(message);
    }

    /**
     * 给所有用户推送消息
     *
     * @param message
     */
    public static void sendMessage(String message)  {
        for (String key : CONNECTED_CLIENTS.keySet()) {
            UnloadingWebSocketServer server = getWebSocketClient(key);
            try {
                server.session.getBasicRemote().sendText(message);
            }catch (IOException e){
                log.error("【鹏翔装卸】sendMessage error.",e);
                throw new BusinessException(e.getMessage());
            }
        }
    }

    /**
     * 获取 WebSocket 客户连接客户端
     *
     * @param userId
     * @return
     */
    public static UnloadingWebSocketServer getWebSocketClient(String userId) {
        return CONNECTED_CLIENTS.get(userId);
    }

    /**
     * 递增在线人数
     */
    private static void addOnlineCount() {
        ONLINE_COUNT.incrementAndGet();
    }

    /**
     * 递减在线人数
     */
    private static void subOnlineCount() {
        ONLINE_COUNT.decrementAndGet();
    }

    /**
     * 获取当前在线人数
     *
     * @return
     */
    private static int getOnlineCount() {
        return ONLINE_COUNT.get();
    }

    /**
     * 鉴权
     * @return
     */
    private boolean auth(Long userId){
        //鉴权
        List<String> userCos = adminUserClient.listUserCos(userId);
        for (String u : userCos) {
            if (AUTH_CODES.contains(u)) {
                return true;
            }
        }
        return false;
    }
}

package com.senox.tms.event;

import com.senox.tms.domain.BicycleBillSettlement;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-13
 */
@Getter
public class BicycleBillSettlementNotifyEvent extends BaseEvent<List<BicycleBillSettlement>> {

    public BicycleBillSettlementNotifyEvent(Object source, List<BicycleBillSettlement> billSettlements) {
        super(source, billSettlements);
    }
}

package com.senox.tms.event;

import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2023/8/2 16:10
 */
public class BaseEvent<T> extends ApplicationEvent {

    protected T eventData;

    public BaseEvent(Object source) {
        super(source);
    }

    public BaseEvent(Object source, T eventData) {
        super(source);
        this.eventData = eventData;
    }


    public T getEventData() {
        return eventData;
    }

    public void setEventData(T eventData) {
        this.eventData = eventData;
    }
}

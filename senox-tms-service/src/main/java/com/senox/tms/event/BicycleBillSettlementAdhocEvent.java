package com.senox.tms.event;

import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.dto.BicycleBillSettlementContentAdditional;
import com.senox.user.constant.MerchantBillSettlePeriod;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

@Getter
public class BicycleBillSettlementAdhocEvent extends BicycleBillSettlementEvent {

    private final transient List<BicycleOrder> orders;

    public BicycleBillSettlementAdhocEvent(Object source, List<BicycleOrder> orders) {
        super(source, BicycleBillSettlementContentAdditional.builder().send(true).settlePeriod(MerchantBillSettlePeriod.AD_HOC).build());
        this.orders = orders;
    }

    public BicycleBillSettlementAdhocEvent(Object source, BicycleOrder order)  {
        this(source, Collections.singletonList(order));
    }
}

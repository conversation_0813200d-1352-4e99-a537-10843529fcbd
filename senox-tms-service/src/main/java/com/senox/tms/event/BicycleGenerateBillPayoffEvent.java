package com.senox.tms.event;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 15:41
 */
@Getter
@Setter
@ToString
public class BicycleGenerateBillPayoffEvent extends ApplicationEvent {


    private static final long serialVersionUID = 2932404511402523225L;

    private List<String> orderSerialList;

    public BicycleGenerateBillPayoffEvent(Object source, List<String> orderSerialList) {
        super(source);
        this.orderSerialList = orderSerialList;
    }
}

package com.senox.tms.event;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2025/4/21 9:49
 */
@Getter
@Setter
@ToString
public class UnloadingListedEvent extends ApplicationEvent {

    private static final long serialVersionUID = -2544821027603932693L;

    private Long workerId;

    public UnloadingListedEvent(Object source, Long workerId) {
        super(source);
        this.workerId = workerId;
    }
}

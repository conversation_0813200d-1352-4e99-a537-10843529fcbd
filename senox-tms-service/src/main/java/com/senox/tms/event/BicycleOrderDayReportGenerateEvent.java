package com.senox.tms.event;

import lombok.Getter;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/20 9:26
 */
@Getter
public class BicycleOrderDayReportGenerateEvent extends BaseEvent<List<LocalDate>>{

    private static final long serialVersionUID = 731151379377827140L;

    public BicycleOrderDayReportGenerateEvent(Object source, List<LocalDate> dateList) {
        super(source, dateList);
    }
}

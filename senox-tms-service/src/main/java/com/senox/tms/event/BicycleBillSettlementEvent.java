package com.senox.tms.event;


import com.senox.tms.dto.BicycleBillSettlementContentAdditional;
import com.senox.tms.vo.BicycleBillVo;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-8
 */
@Getter
public class BicycleBillSettlementEvent extends BaseEvent<List<BicycleBillVo>> {
    public static final String PROJECT_NAME = "三轮车配送服务费";
    protected final transient BicycleBillSettlementContentAdditional contentAdditional;
    public BicycleBillSettlementEvent(Object source, BicycleBillSettlementContentAdditional contentAdditional) {
        super(source);
        this.contentAdditional = contentAdditional;
    }


    public BicycleBillSettlementEvent(Object source, List<BicycleBillVo> bills, BicycleBillSettlementContentAdditional contentAdditional) {
        super(source, bills);
        this.contentAdditional = contentAdditional;
    }

    public BicycleBillSettlementEvent(Object source, BicycleBillVo bill, BicycleBillSettlementContentAdditional contentAdditional) {
        this(source, Collections.singletonList(bill), contentAdditional);
    }

}

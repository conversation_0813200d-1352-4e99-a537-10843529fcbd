package com.senox.tms.event;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4 13:38
 */
@Getter
@Setter
@ToString
public class UnloadingOrderBillPayoffEvent extends ApplicationEvent {


    private List<String> orderNoList;

    public UnloadingOrderBillPayoffEvent(Object source, List<String> orderNoList) {
        super(source);
        this.orderNoList = orderNoList;
    }
}

package com.senox.tms.event;

import com.senox.tms.vo.BicycleOrderVo;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/15 10:30
 */
@Getter
public class BicycleOrderEvent extends BaseEvent<List<BicycleOrderVo>> {

    private static final long serialVersionUID = 6719275323036103708L;

    public BicycleOrderEvent(Object source, List<BicycleOrderVo> orderList) {
        super(source, orderList);
    }
}

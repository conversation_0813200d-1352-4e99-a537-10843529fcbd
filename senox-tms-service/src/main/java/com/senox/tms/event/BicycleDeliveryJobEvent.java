package com.senox.tms.event;

import com.senox.tms.domain.BicycleDeliveryOrderDetail;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2024/8/27 14:39
 */
@Getter
@Setter
public class BicycleDeliveryJobEvent extends ApplicationEvent {

    private static final long serialVersionUID = 5868252590107777900L;

    private final BicycleDeliveryOrderDetail orderDetail;
    private final Integer status;
    private final Integer rating;
    private final String point;

    public BicycleDeliveryJobEvent(Object source, BicycleDeliveryOrderDetail orderDetail, Integer status, Integer rating, String point) {
        super(source);
        this.orderDetail = orderDetail;
        this.status = status;
        this.rating = rating;
        this.point = point;
    }

}

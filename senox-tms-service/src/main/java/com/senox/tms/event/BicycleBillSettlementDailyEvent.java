package com.senox.tms.event;

import com.senox.tms.dto.BicycleBillSettlementContentAdditional;
import com.senox.tms.vo.BicycleBillVo;
import com.senox.user.constant.MerchantBillSettlePeriod;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-15
 */
@Getter
public class BicycleBillSettlementDailyEvent extends BicycleBillSettlementEvent {

    public BicycleBillSettlementDailyEvent(Object source, List<BicycleBillVo> bills) {
        super(source, bills, BicycleBillSettlementContentAdditional.builder().send(false).settlePeriod(MerchantBillSettlePeriod.DAILY).build());
    }

}

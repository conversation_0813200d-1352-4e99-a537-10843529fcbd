package com.senox.tms.event;

import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.domain.BicycleDeliveryOrderDetail;
import com.senox.tms.vo.BicycleDeliveryOrderDetailVo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 15:55
 */
@Getter
@Setter
@ToString
public class BicycleCancelBillPayoffEvent extends ApplicationEvent {


    private static final long serialVersionUID = -8304551994641187861L;

    private List<BicycleDeliveryOrderDetail> orderDetails;

    private List<BicycleDeliveryOrderDetailVo> detailVos;

    private BicycleDeliveryOrder bicycleDeliveryOrder;

    public BicycleCancelBillPayoffEvent(Object source, List<BicycleDeliveryOrderDetail> orderDetails, List<BicycleDeliveryOrderDetailVo> detailVos, BicycleDeliveryOrder bicycleDeliveryOrder) {
        super(source);
        this.orderDetails = orderDetails;
        this.detailVos = detailVos;
        this.bicycleDeliveryOrder = bicycleDeliveryOrder;
    }

}

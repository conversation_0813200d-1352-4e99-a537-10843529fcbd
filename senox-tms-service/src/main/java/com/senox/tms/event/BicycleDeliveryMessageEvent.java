package com.senox.tms.event;

import com.senox.tms.domain.BicycleDeliveryOrder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 15:45
 */
@Getter
@Setter
@ToString
public class BicycleDeliveryMessageEvent extends ApplicationEvent {

    private static final long serialVersionUID = 4008871035893633060L;
    private BicycleDeliveryOrder bicycleDeliveryOrder;
    private List<Long> riderIdList;


    public BicycleDeliveryMessageEvent(Object source, BicycleDeliveryOrder bicycleDeliveryOrder, List<Long> riderIdList) {
        super(source);
        this.bicycleDeliveryOrder = bicycleDeliveryOrder;
        this.riderIdList = riderIdList;
    }
}

package com.senox.tms.schedule;

import com.senox.common.constant.SystemParam;
import com.senox.common.domain.SystemSetting;
import com.senox.common.service.SystemSettingService;
import com.senox.context.AdminContext;
import com.senox.context.PluginEnv;
import com.senox.tms.constant.UnloadingClasses;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.service.UnloadingNightScheduleService;
import com.senox.tms.service.UnloadingWorkerService;
import com.senox.tms.vo.UnloadingNightScheduleSearchVo;
import com.senox.tms.vo.UnloadingNightScheduleVo;
import com.senox.tms.vo.UnloadingWorkerSearchVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2024/12/30 8:18
 */
@Component
@RequiredArgsConstructor
public class UnloadingSchedule {

    private final UnloadingWorkerService workerService;
    private final SystemSettingService systemSettingService;
    private final UnloadingNightScheduleService scheduleService;


    @XxlJob("executeOffWork")
    public void executeOffWork() {
        XxlJobHelper.log("搬运工下班重置状态开始...");
        long execStartTime = System.currentTimeMillis();
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());
        SystemSetting setting = systemSettingService.findByParam(SystemParam.UNLOADING_VERSION);
        if (setting != null) {
            UnloadingWorkerSearchVo searchVo = new UnloadingWorkerSearchVo();
            searchVo.setPage(false);
            searchVo.setStatusList(Collections.singletonList(UnloadingWorkerStatus.ALREADY_LISTED.getNumber()));
            List<UnloadingWorker> workers =
                    workerService.listWorker(searchVo);
            if (!CollectionUtils.isEmpty(workers)) {
                workerService.updateBatchWorkerStatus(workers.stream().map(UnloadingWorker::getId).collect(Collectors.toList()), UnloadingWorkerStatus.NOT_LISTED.getNumber());
            }
        }

        XxlJobHelper.log("搬运工下班重置状态结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }


    @XxlJob("executeMorningOffWork")
    public void executeMorningOffWork() {
        XxlJobHelper.log("搬运工白班下班重置状态开始...");
        long execStartTime = System.currentTimeMillis();
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());

        UnloadingWorkerSearchVo searchVo = new UnloadingWorkerSearchVo();
        searchVo.setPage(false);
        searchVo.setClasses(UnloadingClasses.MORNING_SHIFT.getNumber());
        searchVo.setStatusList(Collections.singletonList(UnloadingWorkerStatus.ALREADY_LISTED.getNumber()));
        List<UnloadingWorker> workers =
                workerService.listWorker(searchVo);
        if (!CollectionUtils.isEmpty(workers)) {
            workerService.updateBatchWorkerStatus(workers.stream().map(UnloadingWorker::getId).collect(Collectors.toList()), UnloadingWorkerStatus.NOT_LISTED.getNumber());
        }

        XxlJobHelper.log("搬运工白班下班重置状态结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    @XxlJob("changeShiftSchedule")
    public void changeShiftSchedule() {
        XxlJobHelper.log("搬运工更改班次任务开始...");
        long execStartTime = System.currentTimeMillis();
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());

        List<UnloadingNightScheduleVo> scheduleVos = getUnloadingNightScheduleVos();
        List<UnloadingWorker> workerList = getUnloadingWorkerList();

        List<Long> filterIds = workerList.stream()
                .filter(worker -> scheduleVos.stream().noneMatch(schedule -> Objects.equals(schedule.getWorkerNo(), worker.getWorkerNo())))
                .map(UnloadingWorker::getId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filterIds)) {
            //下晚班的搬运工
            workerService.updateBatchClasses(filterIds, UnloadingClasses.MORNING_SHIFT.getNumber());
        }
        //上晚班的搬运工
        workerService.updateBatchClasses(scheduleVos.stream().map(UnloadingNightScheduleVo::getWorkerId).collect(Collectors.toList()), UnloadingClasses.NIGHT_SHIFT.getNumber());
        XxlJobHelper.log("搬运工更改班次任务结束，耗时：{}...", System.currentTimeMillis() - execStartTime);

    }

    private List<UnloadingWorker> getUnloadingWorkerList() {
        UnloadingWorkerSearchVo searchVo = new UnloadingWorkerSearchVo();
        searchVo.setPage(false);
        searchVo.setClasses(UnloadingClasses.NIGHT_SHIFT.getNumber());
        return workerService.listWorker(searchVo);
    }

    private List<UnloadingNightScheduleVo> getUnloadingNightScheduleVos() {
        LocalDate now = LocalDate.now();
        UnloadingNightScheduleSearchVo scheduleSearchVo = new UnloadingNightScheduleSearchVo();
        scheduleSearchVo.setScheduleDate(now);
        scheduleSearchVo.setPage(false);
        return scheduleService.listSchedule(scheduleSearchVo);
    }
}

package com.senox.tms.schedule;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.context.AdminContext;
import com.senox.context.PluginEnv;
import com.senox.tms.service.BicyclePayoffService;
import com.senox.tms.vo.BicycleDateVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023-10-19
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class BicyclePayoffReportSchedule {

    private final BicyclePayoffService payoffService;

    @XxlJob("payoffReportByDayJob")
    public void payoffReportByDayJob() {
        BicycleDateVo dateVo = dateInit(XxlJobHelper.getJobParam());
        long startTimeMillis = System.currentTimeMillis();
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());
        XxlJobHelper.log("开始生成[{}]-[{}]-[{}]应付表",dateVo.getYear(),dateVo.getMonth(),dateVo.getDay());
        payoffService.generateReportByDay(dateVo);
        XxlJobHelper.log("生成[{}]-[{}]-[{}]应付表结束，耗时:{}",dateVo.getYear(),dateVo.getMonth(),dateVo.getDay(), System.currentTimeMillis() - startTimeMillis);
    }

    @XxlJob("payoffReportByMonthJob")
    public void payoffReportByMonthJob(){
        BicycleDateVo dateVo = dateInit(XxlJobHelper.getJobParam());
        XxlJobHelper.log("开始生成[{}]-[{}]应付表",dateVo.getYear(),dateVo.getMonth());
        long startTimeMillis = System.currentTimeMillis();
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());
        payoffService.generateReportByMonth(dateVo);
        XxlJobHelper.log("生成[{}]-[{}]应付表结束，耗时:{}",dateVo.getYear(),dateVo.getMonth(),dateVo.getDay(), System.currentTimeMillis() - startTimeMillis);
    }

    private BicycleDateVo dateInit(String dateStr){
        BicycleDateVo dateVo = new BicycleDateVo();
        if (!StringUtils.isBlank(dateStr)) {
            dateVo = JsonUtils.json2Object(dateStr,BicycleDateVo.class);
        }
        LocalDate now = LocalDate.now().minusDays(1);
        if (null==dateVo.getYear()){
            dateVo.setYear(now.getYear());
        }
        if (null==dateVo.getMonth()){
            dateVo.setMonth(now.getMonthValue());
        }
        if (null==dateVo.getDay()){
            dateVo.setDay(now.getDayOfMonth());
        }
        return dateVo;
    }


}

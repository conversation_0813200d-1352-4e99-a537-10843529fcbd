package com.senox.tms.schedule;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.context.AdminContext;
import com.senox.context.PluginEnv;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.constant.BicycleOrderState;
import com.senox.tms.constant.BicycleSettingType;
import com.senox.tms.domain.*;
import com.senox.tms.service.*;
import com.senox.tms.utils.RabbitMqSenderUtil;
import com.senox.tms.vo.*;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/10/12 10:26
 */
@Component
@RequiredArgsConstructor
public class BicycleSchedule {
    private final BicycleOperateAnalysisService bicycleOperateAnalysisService;
    private final BicycleDeliveryOrderDetailService bicycleDeliveryOrderDetailService;
    private final BicycleDeliveryOrderJobService bicycleDeliveryOrderJobService;
    private final BicycleOrderDayReportService bicycleOrderDayReportService;
    private final BicycleOrderMonthReportService bicycleOrderMonthReportService;
    private final BicycleOrderService bicycleOrderService;
    private final BicycleSettingService bicycleSettingService;
    private final BicycleRiderService bicycleRiderService;
    private final BicycleDeliveryOrderService bicycleDeliveryOrderService;
    private final BicycleManagerService bicycleManagerService;
    private final RabbitTemplate rabbitTemplate;

    @XxlJob("executeOperateAnalysis")
    public void executeOperateAnalysis() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("生成运营分析记录任务开始...");
        long execStartTime = System.currentTimeMillis();
        LocalDateTime endTime = initDateTime(param);
        LocalDateTime startTime = LocalDateTime.of(endTime.minusDays(1).toLocalDate(), LocalTime.MIN);
        bicycleOperateAnalysisService.addBicycleOperateAnalysis(startTime, endTime);

        XxlJobHelper.log("生成运营分析记录任务结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    @XxlJob("executeOrderAutoConfirm")
    public void executeOrderAutoConfirm() {
        XxlJobHelper.log("配送订单自动确认任务开始...");
        long execStartTime = System.currentTimeMillis();
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());
        List<BicycleDeliveryOrderDetail> orderDetails = bicycleDeliveryOrderDetailService.listDeliveryOrderDetailByStatus(BicycleDeliveryOrderStatus.SEND_COMPLETED.getNumber());
        orderDetails.forEach(orderDetail -> {
            BicycleDeliveryOrderJob orderJob = new BicycleDeliveryOrderJob();
            orderJob.setDeliveryOrderDetailId(orderDetail.getId());
            orderJob.setStatus(BicycleDeliveryOrderStatus.CONFIRM_COMPLETION.getNumber());
            bicycleDeliveryOrderJobService.addBicycleDeliveryOrderJob(orderJob, Collections.emptyList(), 5, StringUtils.EMPTY);
        });

        XxlJobHelper.log("配送订单自动确认任务结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    @XxlJob("executeGenerateOrderDayReport")
    public void executeGenerateOrderDayReport() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("生成日报表任务开始...");
        long execStartTime = System.currentTimeMillis();
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());
        LocalDateTime date = initDateTime(param).minusDays(1);
        BicycleReportDateVo dateVo = new BicycleReportDateVo();
        dateVo.setYear(date.getYear());
        dateVo.setMonth(date.getMonthValue());
        dateVo.setDay(date.getDayOfMonth());
        bicycleOrderDayReportService.generateDayReport(dateVo);

        XxlJobHelper.log("生成日报表任务结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    @XxlJob("executeGenerateOrderMonthReport")
    public void executeGenerateOrderMonthReport() {
        String param = XxlJobHelper.getJobParam();
        XxlJobHelper.log("生成月报表任务开始...");
        long execStartTime = System.currentTimeMillis();
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());
        LocalDateTime date = initDateTime(param).minusDays(1);
        BicycleReportDateVo dateVo = new BicycleReportDateVo();
        dateVo.setYear(date.getYear());
        dateVo.setMonth(date.getMonthValue());
        bicycleOrderMonthReportService.generateMonthReport(dateVo);

        XxlJobHelper.log("生成月报表任务结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    @XxlJob("executeAutoDispatch")
    public void executeAutoDispatch() {
        //获取自动派工的设置
        BicycleSetting bicycleSetting = bicycleSettingService.findByAlias(BicycleSettingType.AUTO_DISPATCH.getAlias());
        if (bicycleSetting != null && bicycleSetting.getEnable()) {
            XxlJobHelper.log("自动派工任务开始...");
            long execStartTime = System.currentTimeMillis();
            AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());
            //获取所有未分配的订单
            BicycleOrderSearchVo searchVo = new BicycleOrderSearchVo();
            searchVo.setPageNo(1);
            searchVo.setPageSize(100);
            searchVo.setDelivery(false);
            searchVo.setAssignRider(false);
            searchVo.setState(BicycleOrderState.FINALIZE.getNumber());
            List<BicycleOrderVo> dataList = bicycleOrderService.listOrder(searchVo).getDataList();

            dataList.forEach(bicycleOrderVo -> {
                //获取在线的骑手
                List<BicycleRiderInfoVo> infoVoList = bicycleRiderService.listRider();
                if (CollectionUtils.isEmpty(infoVoList)) {
                    //如果一个在线的骑手都没有直接return
                    XxlJobHelper.log("当前没有在线的骑手可派单！");
                    return;
                }
                //筛选空闲的骑手
                infoVoList = infoVoList.stream().filter(rider -> rider.getRiderStatus() == 0).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(infoVoList)) {
                    //没有骑手可分配直接return
                    XxlJobHelper.log("当前没有空闲的骑手可派单！");
                    return;
                }
                Long riderId = infoVoList.get(0).getRiderId();
                BicycleRider rider = bicycleRiderService.findById(riderId);
                if (rider == null) {
                    XxlJobHelper.log("骑手未存在！");
                    return;
                }
                bicycleDeliveryOrderService.saveDeliveryOrder(bicycleOrderVo, rider);
            });

            XxlJobHelper.log("自动派工任务结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
        }
    }

    @XxlJob("executeUnAssignNotice")
    public void executeUnAssignNotice() {
        //获取未分配提醒的设置
        BicycleSetting bicycleSetting = bicycleSettingService.findByAlias(BicycleSettingType.UN_ASSIGN.getAlias());
        if (bicycleSetting != null && bicycleSetting.getEnable()) {
            XxlJobHelper.log("订单未分配提醒任务开始...");
            AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());
            long execStartTime = System.currentTimeMillis();
            //获取所有未分配的订单
            BicycleOrderSearchVo searchVo = new BicycleOrderSearchVo();
            searchVo.setPageNo(1);
            searchVo.setPageSize(100);
            searchVo.setDelivery(false);
            searchVo.setAssignRider(false);
            searchVo.setState(BicycleOrderState.FINALIZE.getNumber());
            List<BicycleOrderVo> dataList = bicycleOrderService.listOrder(searchVo).getDataList();
            if (CollectionUtils.isEmpty(dataList)) {
                return;
            }
            //获取在线的管理员
            List<BicycleManager> managers = bicycleManagerService.listBicycleManagerByStatus(1);
            managers.forEach(manager -> RabbitMqSenderUtil.sendUnAssignMessage(manager, dataList.size(), rabbitTemplate));

            XxlJobHelper.log("订单未分配提醒任务结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
        }
    }

    @XxlJob("executeGenerateReport")
    public void executeGenerateReport() {
        XxlJobHelper.log("重新生成报表开始...");
        long execStartTime = System.currentTimeMillis();
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());

        BicycleOrderSearchVo searchVo = new BicycleOrderSearchVo();
        searchVo.setState(BicycleOrderState.FINALIZE.getNumber());
        searchVo.setOrigin(1);
        searchVo.setModifiedTimeStart(LocalDate.now().atStartOfDay());
        searchVo.setModifiedTimeEnd(LocalDate.now().atTime(LocalTime.MAX));
        List<BicycleOrder> orderList = bicycleOrderService.orderList(searchVo);
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        List<LocalDate> dateList = orderList.stream().map(BicycleOrder::getOrderTime).map(LocalDateTime::toLocalDate).distinct().collect(Collectors.toList());
        dateList.forEach(date -> {
            BicycleReportDateVo dateVo = new BicycleReportDateVo();
            dateVo.setYear(date.getYear());
            dateVo.setMonth(date.getMonthValue());
            dateVo.setDay(date.getDayOfMonth());
            bicycleOrderDayReportService.generateDayReport(dateVo);
            LocalDateTime startTime = date.atTime(LocalTime.MIN);
            LocalDateTime endTime = date.atTime(LocalTime.MAX);
            bicycleOperateAnalysisService.addBicycleOperateAnalysis(startTime, endTime);
        });
        XxlJobHelper.log("重新生成报表结束，耗时：{}...", System.currentTimeMillis() - execStartTime);
    }

    private LocalDateTime initDateTime(String param) {
        LocalDateTime dateTime = LocalDateTime.now();
        if (!StringUtils.isBlank(param)) {
            dateTime = JsonUtils.json2Object(param, LocalDateTime.class);
        }
        return dateTime;
    }
}

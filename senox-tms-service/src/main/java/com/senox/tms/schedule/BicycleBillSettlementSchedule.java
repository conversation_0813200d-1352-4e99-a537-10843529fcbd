package com.senox.tms.schedule;

import com.senox.context.AdminContext;
import com.senox.context.PluginEnv;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.event.BicycleBillSettlementDailyEvent;
import com.senox.tms.service.BicycleBillService;
import com.senox.tms.vo.BicycleBillSearchVo;
import com.senox.tms.vo.BicycleBillVo;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023-11-15
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class BicycleBillSettlementSchedule {
    private final BicycleBillService billService;
    private final ApplicationEventPublisher publisher;

    @XxlJob("billSettlementDailyJob")
    public void billSettlementDailyJob() {
        LocalDate now = LocalDate.now().minusDays(1);
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());
        LocalDate startDate = now.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate endDate =now.with(TemporalAdjusters.lastDayOfMonth());
        Set<Integer> statusSet = new HashSet<>();
        BicycleBillSearchVo searchVo = new BicycleBillSearchVo();
        searchVo.setStartDate(startDate);
        searchVo.setEndDate(endDate);
        statusSet.add(BicycleDeliveryOrderStatus.SEND_COMPLETED.getNumber());
        statusSet.add(BicycleDeliveryOrderStatus.CONFIRM_COMPLETION.getNumber());
        searchVo.setStatusSet(statusSet);
        List<BicycleBillVo> billVoList = billService.listBill(searchVo);
        publisher.publishEvent(new BicycleBillSettlementDailyEvent(this, billVoList));

    }
}

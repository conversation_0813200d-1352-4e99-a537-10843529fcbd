package com.senox.tms.schedule;

import com.senox.context.AdminContext;
import com.senox.context.PluginEnv;
import com.senox.tms.component.MerchantComponent;
import com.senox.tms.domain.BicycleChargesScheduledTask;
import com.senox.tms.service.BicycleChargesScheduledTaskService;
import com.senox.tms.vo.BicycleChargesScheduledTaskSearchVo;
import com.senox.tms.vo.BicycleChargesScheduledTaskVo;
import com.senox.tms.vo.MerchantInfoVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class BicycleChargesSchedule {
    private final BicycleChargesScheduledTaskService taskService;
    private final MerchantComponent merchantComponent;
    private static final int MAX_COUNT = 1;

    @XxlJob("bicycleChargesScheduledTaskJob")
    public void scheduledTaskJob() {
        long startTimeMillis = System.currentTimeMillis();
        XxlJobHelper.log("开始执行三轮车费用计划任务");
        AdminContext.setPluginEnv(PluginEnv.builder().xxl(true).build());
        BicycleChargesScheduledTaskSearchVo searchVo = new BicycleChargesScheduledTaskSearchVo();
        searchVo.setEndTime(LocalDateTime.now());
        searchVo.setMaxTaskCount(MAX_COUNT);
        List<BicycleChargesScheduledTaskVo> chargesScheduledTasks = taskService.list(searchVo);
        if (!CollectionUtils.isEmpty(chargesScheduledTasks)) {
            for (BicycleChargesScheduledTaskVo chargesScheduledTask : chargesScheduledTasks) {
                Collection<Long> merchantIds = chargesScheduledTask.getMerchantInfos().stream().map(MerchantInfoVo::getMerchantId).collect(Collectors.toList());
                merchantComponent.updateChargesBatch(merchantIds, chargesScheduledTask.getChargesId(), chargesScheduledTask.getModel().equals(1));
            }
            List<BicycleChargesScheduledTask> updateChargesScheduledTasks = chargesScheduledTasks.stream().map(vo -> {
                BicycleChargesScheduledTask domain = new BicycleChargesScheduledTask();
                domain.setId(vo.getId());
                domain.setTaskCount(vo.getTaskCount() + 1);
                return domain;
            }).collect(Collectors.toList());
            taskService.updateBatch(updateChargesScheduledTasks);
        }

        XxlJobHelper.log("[{}]三轮车费用计划任务执行结束", System.currentTimeMillis() - startTimeMillis);
    }
}

package com.senox.tms.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.BicycleSharesConvert;
import com.senox.tms.service.BicycleSharesService;
import com.senox.tms.vo.BicycleSharesSearchVo;
import com.senox.tms.vo.BicycleSharesVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 分佣 controller
 *
 * <AUTHOR>
 * @date 2023-9-15
 */
@Api(tags = "分佣")
@RequiredArgsConstructor
@RequestMapping("/bicycle/shares")
@RestController
public class BicycleSharesController {
    private final BicycleSharesService sharesService;
    private final BicycleSharesConvert sharesConvert;


    @ApiOperation("添加佣金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void add(@Validated({Add.class}) @RequestBody BicycleSharesVo sharesVo) {
        sharesService.add(sharesConvert.todo(sharesVo));
    }

    @ApiOperation("删除佣金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        sharesService.delete(id);
    }

    @ApiOperation("修改佣金")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody BicycleSharesVo sharesVo) {
        sharesService.update(sharesConvert.todo(sharesVo));
    }

    @ApiOperation("佣金列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<BicycleSharesVo> list(@RequestBody BicycleSharesSearchVo searchVo) {
        return PageResult.convertPage(sharesService.list(searchVo), sharesConvert::toVo);
    }
}

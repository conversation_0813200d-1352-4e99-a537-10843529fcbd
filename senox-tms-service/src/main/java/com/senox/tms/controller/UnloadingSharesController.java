package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.UnloadingSharesConvert;
import com.senox.tms.domain.UnloadingShares;
import com.senox.tms.service.UnloadingSharesService;
import com.senox.tms.vo.UnloadingSharesSearchVo;
import com.senox.tms.vo.UnloadingSharesVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/10/31 15:43
 */
@Api(tags = "鹏翔分佣")
@RequiredArgsConstructor
@RequestMapping("/unloading/shares")
@RestController
public class UnloadingSharesController {

    private final UnloadingSharesService sharesService;
    private final UnloadingSharesConvert sharesConvert;

    @ApiOperation("添加分佣")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/save")
    public void saveShares(@RequestBody UnloadingSharesVo sharesVo) {
        sharesService.saveShares(sharesConvert.toDo(sharesVo));
    }

    @ApiOperation("根据id获取分佣")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public UnloadingSharesVo findSharesById(@PathVariable Long id) {
        return sharesConvert.toVo(sharesService.findById(id));
    }

    @ApiOperation("根据id删除分佣")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteSharesById(@PathVariable Long id) {
        sharesService.deleteById(id);
    }

    @ApiOperation("分佣分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<UnloadingSharesVo> pageShares(@RequestBody UnloadingSharesSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageResult.convertPage(sharesService.pageShares(searchVo), sharesConvert::toVo);
    }

}

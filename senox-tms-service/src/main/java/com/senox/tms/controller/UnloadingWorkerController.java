package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.UnloadingNightScheduleConvert;
import com.senox.tms.convert.UnloadingWorkerAccessConvert;
import com.senox.tms.convert.UnloadingWorkerConvert;
import com.senox.tms.domain.UnloadingNightSchedule;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.domain.UnloadingWorkerAccess;
import com.senox.tms.service.UnloadingAttendanceService;
import com.senox.tms.service.UnloadingNightScheduleService;
import com.senox.tms.service.UnloadingWorkerAccessService;
import com.senox.tms.service.UnloadingWorkerLogService;
import com.senox.tms.service.UnloadingWorkerService;
import com.senox.tms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26 10:57
 */
@Api(tags = "鹏翔搬运工")
@RequiredArgsConstructor
@RequestMapping("/unloading/worker")
@RestController
public class UnloadingWorkerController {

    private final UnloadingWorkerService workerService;
    private final UnloadingWorkerConvert workerConvert;
    private final UnloadingAttendanceService attendanceService;
    private final UnloadingWorkerAccessConvert workerAccessConvert;
    private final UnloadingWorkerAccessService workerAccessService;
    private final UnloadingWorkerLogService workerLogService;
    private final UnloadingNightScheduleService nightScheduleService;
    private final UnloadingNightScheduleConvert nightScheduleConvert;


    @ApiOperation("保存搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/save")
    public void saveWorker(@RequestBody UnloadingWorkerVo workerVo) {
        UnloadingWorker worker = workerConvert.toDo(workerVo);
        workerService.saveWorker(worker);
    }

    @ApiOperation("更新搬运工状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/updateStatus/{id}")
    public void updateWorkerStatus(@PathVariable Long id, @RequestParam Integer status) {
        workerService.updateWorkerStatus(id, status);
    }

    @ApiOperation("根据搬运工编号更新搬运工状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/updateStatusByWorkerNo")
    public void updateWorkerStatusByWorkerNo(@RequestParam String workerNo, @RequestParam Integer status) {
        workerService.updateWorkerStatusByWorkerNo(workerNo, status);
    }

    @ApiOperation("只更新人脸")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/updateFaceUrl")
    public void updateFaceUrl(@RequestBody UnloadingWorkerFaceUrlVo faceUrlVo) {
        workerService.updateFaceUrl(faceUrlVo);
    }

    @ApiOperation("指定位置排序")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/appoint/resetOrderNum/{id}")
    public void appointResetOrderNum(@PathVariable Long id, @RequestParam Integer num) {
        workerService.appointResetOrderNum(id, num);
    }

    @ApiOperation("搬运工排序置底")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/appoint/bottomUp/{id}")
    public void bottomUp(@PathVariable Long id) {
        workerService.bottomUp(id);
    }

    @ApiOperation("根据id查询搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public UnloadingWorkerVo findById(@PathVariable Long id) {
        return workerConvert.toVo(workerService.findById(id));
    }

    @ApiOperation("搬运工请假")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/leave")
    public void workerLeave(@RequestBody UnloadingWorkerVo workerVo) {
        workerService.workerLeave(workerVo);
    }

    @ApiOperation("根据id删除搬运工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteWorker(@PathVariable Long id) {
        workerService.deleteWorker(id);
    }

    @ApiOperation("搬运工总数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count")
    public int countWorker(@RequestBody UnloadingWorkerSearchVo searchVo) {
        return workerService.countWorker(searchVo);
    }

    @ApiOperation("搬运工列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<UnloadingWorkerVo> listWorker(@RequestBody UnloadingWorkerSearchVo searchVo) {
        return workerConvert.toVo(workerService.listWorker(searchVo));
    }

    @ApiOperation("搬运工分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<UnloadingWorkerVo> pageWorker(@RequestBody UnloadingWorkerSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageResult.convertPage(workerService.pageWorker(searchVo), workerConvert::toVo);
    }

    @ApiOperation("搬运工考勤记录分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/attendance/page")
    public PageResult<UnloadingAttendanceVo> pageAttendance(@RequestBody UnloadingAttendanceSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return attendanceService.pageAttendance(searchVo);
    }

    @ApiOperation("更新考勤记录备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/attendance/updateRemark/{id}")
    public void updateRemark(@PathVariable Long id, @RequestParam String remark) {
        attendanceService.updateRemark(id, remark);
    }

    @ApiOperation("添加搬运工设备权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/save")
    public void addWorkerAccess(@RequestBody List<UnloadingWorkerAccessVo> accessVoList) {
        List<UnloadingWorkerAccess> accessList = workerAccessConvert.toDo(accessVoList);
        Long workerId = accessList.stream().map(UnloadingWorkerAccess::getWorkerId).distinct().findFirst().orElse(0L);
        workerAccessService.addWorkerAccess(workerService.findById(workerId), accessList);
    }

    @ApiOperation("根据id删除搬运工设备权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/access/delete/{id}")
    public void deleteAccessById(@PathVariable Long id) {
        UnloadingWorkerAccess access = workerAccessService.findById(id);
        if (access == null) {
            return;
        }
        workerAccessService.deleteAccess(access, workerService.findById(access.getWorkerId()));
    }

    @ApiOperation("根据Id获取搬运工设备权限")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/access/get/{workerId}")
    public List<UnloadingWorkerAccessVo> listAccessByWorkerId(@PathVariable Long workerId) {
        return workerAccessConvert.toVo(workerAccessService.accessListByWorkerIds(Collections.singletonList(workerId)));
    }

    @ApiOperation("搬运工顺序列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sequence/list")
    public List<UnloadingWorkerVo> listSequenceWorker(@RequestBody UnloadingWorkerSearchVo searchVo) {
        return workerConvert.toVo(workerService.listSequenceWorker(searchVo));
    }

    @ApiOperation("根据id查询搬运工异常日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/log/get/{id}")
    public UnloadingWorkerLogVo findWorkerLogById(@PathVariable Long id) {
        return workerLogService.findWorkerLogById(id);
    }

    @ApiOperation("搬运工异常日志分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/log/page")
    public PageResult<UnloadingWorkerLogVo> pageWorkerLog(@RequestBody UnloadingWorkerLogSearchVo searchVo) {
        return workerLogService.pageWorkerLog(searchVo);
    }

    @ApiOperation("批量添加排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/schedule/batchAdd")
    public void batchAddSchedule(@RequestBody UnloadingNightScheduleBatchVo batchVo) {
        nightScheduleService.batchAddSchedule(batchVo);
    }

    @ApiOperation("添加排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/schedule/add")
    public void addSchedule(@RequestBody UnloadingNightScheduleVo scheduleVo) {
        UnloadingNightSchedule schedule = nightScheduleConvert.toDo(scheduleVo);
        nightScheduleService.addSchedule(schedule);
    }

    @ApiOperation("更新排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/schedule/update")
    public void updateSchedule(@RequestBody UnloadingNightScheduleVo scheduleVo) {
        UnloadingNightSchedule schedule = nightScheduleConvert.toDo(scheduleVo);
        nightScheduleService.updateSchedule(schedule);
    }

    @ApiOperation("根据id查询排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/schedule/get/{id}")
    public UnloadingNightScheduleVo findScheduleById(@PathVariable Long id) {
        return nightScheduleConvert.toVo(nightScheduleService.findById(id));
    }

    @ApiOperation("根据日期获取排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/schedule/findByScheduleDate")
    public List<UnloadingNightScheduleVo> findByScheduleDate(@RequestParam @DateTimeFormat(pattern="yyyy-MM-d") LocalDate scheduleDate) {
        return nightScheduleConvert.toVo(nightScheduleService.findByScheduleDate(scheduleDate));
    }

    @ApiOperation("根据id删除排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/schedule/delete/{id}")
    public void deleteScheduleById(@PathVariable Long id) {
        nightScheduleService.deleteById(id);
    }

    @ApiOperation("根据日期删除排期计划")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/schedule/deleteByScheduleDate")
    public void deleteByScheduleDate(@RequestParam @DateTimeFormat(pattern="yyyy-MM-d") LocalDate scheduleDate) {
        nightScheduleService.deleteByScheduleDate(scheduleDate);
    }

    @ApiOperation("搬运工排期计划分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/schedule/page")
    public PageResult<UnloadingNightScheduleVo> pageSchedule(@RequestBody UnloadingNightScheduleSearchVo searchVo) {
        return nightScheduleService.pageSchedule(searchVo);
    }
}

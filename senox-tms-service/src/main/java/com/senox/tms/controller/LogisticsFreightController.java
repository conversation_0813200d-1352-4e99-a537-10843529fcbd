package com.senox.tms.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.LogisticsFreightConvert;
import com.senox.tms.service.LogisticsFreightService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticFreightRemarkVo;
import com.senox.tms.vo.LogisticsFreightSearchVo;
import com.senox.tms.vo.LogisticsFreightStatisticsVo;
import com.senox.tms.vo.LogisticsFreightVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-6
 */
@Api(tags = "物流货运")
@RestController
@RequiredArgsConstructor
@RequestMapping("/logistics/freight")
public class LogisticsFreightController {
    private final LogisticsFreightService freightService;
    private final LogisticsFreightConvert freightConvert;

    @ApiOperation("添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void add(@RequestBody List<LogisticsFreightVo> freightVos) {
        freightService.addBatch(freightConvert.toDo(freightVos));
    }

    @ApiOperation("根据id查找")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public LogisticsFreightVo findById(@PathVariable Long id) {
        return freightService.findById(id);
    }

    @ApiOperation("更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody LogisticsFreightVo freightVo) {
        freightService.update(freightConvert.toDo(freightVo));
    }

    @ApiOperation("删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        freightService.deleteById(id);
    }

    @ApiOperation("列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<LogisticsFreightVo> list(@RequestBody LogisticsFreightSearchVo searchVo) {
        return freightService.list(searchVo);
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/page")
    public PageStatisticsResult<LogisticsFreightVo, LogisticsFreightStatisticsVo> listPage(@RequestBody LogisticsFreightSearchVo searchVo) {
        return freightService.listPage(searchVo);
    }

    @ApiOperation("根据id集合查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/listByIds")
    public List<LogisticsFreightVo> listByIds(@RequestBody List<Long> ids) {
        return freightConvert.toV(freightService.listByIds(ids));
    }

    @ApiOperation("更新订单状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/paid/update")
    public void updateBillStatus(@Validated @RequestBody BillPaidVo billPaid) {
        if (!WrapperClassUtils.biggerThanLong(billPaid.getOrderId(), 0L)) {
            throw new InvalidParameterException("无效的订单id");
        }
        if (!WrapperClassUtils.biggerThanLong(billPaid.getTollMan(), 0L)) {
            throw new InvalidParameterException("无效的收费员");
        }

        freightService.updateBillStatus(billPaid);
    }

    @ApiOperation("更新账单备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/remark/update")
    public void updateBillRemark(@Validated @RequestBody LogisticFreightRemarkVo remarkVo) {
        if (CollectionUtils.isEmpty(remarkVo.getIds())) {
            throw new InvalidParameterException();
        }
        remarkVo.setOperatorId(ContextUtils.getUserInContext().getUserId());
        remarkVo.setOperatorName(ContextUtils.getUserInContext().getUsername());
        freightService.updateBillRemark(remarkVo);
    }
}

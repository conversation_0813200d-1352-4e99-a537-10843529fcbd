package com.senox.tms.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.pm.constant.PayWay;
import com.senox.tms.convert.BicyclePayoffReportConvert;
import com.senox.tms.domain.BicyclePayoffDayReport;
import com.senox.tms.service.BicyclePayoffService;
import com.senox.tms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/25 14:13
 */
@Api(tags = "三轮车配送应付账单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bicycle/payoff")
public class BicyclePayoffController {

    private final BicyclePayoffService bicyclePayoffService;
    private final BicyclePayoffReportConvert payoffReportConvert;

    @ApiOperation("更新账单状态")
    @PostMapping("/paid/update")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    public void updatePayoffStatus(@Validated @RequestBody BillPaidVo billPaid, @RequestParam("payway") int payway) {
        if (CollectionUtils.isEmpty(billPaid.getBillIds())) {
            throw new InvalidParameterException();
        }
        PayWay pw = PayWay.fromValue(payway);
        if (pw != PayWay.CASH && pw != PayWay.TRANSFER) {
            throw new InvalidParameterException("应付账单仅支持现金及转账");
        }
        if (billPaid.getPaidTime() == null) {
            billPaid.setPaidTime(LocalDateTime.now());
        }

        bicyclePayoffService.updatePayoffStatus(billPaid, pw);
    }
    @ApiOperation("报表应付金额更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/report/amount/update/{id}/{amount}")
    public void updateAmountFromReport(@PathVariable Long id,@PathVariable BigDecimal amount){
        BicyclePayoffDayReport payoffDayReport = new BicyclePayoffDayReport();
        payoffDayReport.setId(id);
        payoffDayReport.setTotalAmount(amount);
        bicyclePayoffService.reportUpdate(payoffDayReport);
    }

    @ApiOperation("应付账单合计")
    @PostMapping("/sum")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    public BicyclePayoffVo sumPayoff(@RequestBody BicyclePayoffSearchVo searchVo) {
        return bicyclePayoffService.sumPayoff(searchVo);
    }

    @ApiOperation("应付账单列表页")
    @PostMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    public PageResult<BicyclePayoffVo> listPayoff(@RequestBody BicyclePayoffSearchVo searchVo) {
        return bicyclePayoffService.listPayoff(searchVo);
    }

    @ApiOperation("生成日报表")
    @PostMapping("/report/generate/day")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    public void generateReportByDay(@RequestBody BicycleDateVo dateVo) {
        if (!WrapperClassUtils.biggerThanInt(dateVo.getYear(),0)){
            dateVo.setYear(LocalDate.now().getYear());
        }
        if (!WrapperClassUtils.biggerThanInt(dateVo.getMonth(), 0)) {
            dateVo.setMonth(LocalDate.now().getMonthValue());
        }
        if (!WrapperClassUtils.biggerThanInt(dateVo.getDay(), 0)) {
            dateVo.setMonth(LocalDate.now().getDayOfMonth());
        }
        bicyclePayoffService.generateReportByDay(dateVo);
    }

    @ApiOperation("生成月报表")
    @PostMapping("/report/generate/month")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    public void generateReportByMonth(@RequestBody BicycleDateVo dateVo) {
        if (!WrapperClassUtils.biggerThanInt(dateVo.getYear(),0)){
            dateVo.setYear(LocalDate.now().getYear());
        }
        if (!WrapperClassUtils.biggerThanInt(dateVo.getMonth(), 0)) {
            dateVo.setMonth(LocalDate.now().getMonthValue());
        }
        bicyclePayoffService.generateReportByMonth(dateVo);
    }

    @ApiOperation("日报表列表")
    @PostMapping("/report/day/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    public BicycleTotalPageResult<BicyclePayoffReportVo> reportDayList(@RequestBody BicyclePayoffReportSearchVo searchVo) {
        return BicycleTotalPageResult.convertPage(bicyclePayoffService.reportListByDay(searchVo), payoffReportConvert::toV);
    }

    @ApiOperation("月报表列表")
    @PostMapping("/report/month/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    public BicycleTotalPageResult<BicyclePayoffReportVo> reportMonthList(@RequestBody BicyclePayoffReportSearchVo searchVo) {
        return BicycleTotalPageResult.convertPage(bicyclePayoffService.reportListByMonth(searchVo), payoffReportConvert::toV);
    }

}

package com.senox.tms.controller;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.BooleanUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.component.MerchantComponent;
import com.senox.tms.constant.BicycleOrderStatus;
import com.senox.tms.convert.BicycleChargesDetailConvert;
import com.senox.tms.convert.BicycleOperateAnalysisConvert;
import com.senox.tms.convert.BicycleOrderConvert;
import com.senox.tms.convert.BicycleOrderGoodsDetailConvert;
import com.senox.tms.domain.BicycleChargesDetail;
import com.senox.tms.domain.BicycleOperateAnalysis;
import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.domain.BicycleOrderGoodsDetail;
import com.senox.tms.dto.BicycleOrderImportDto;
import com.senox.tms.service.*;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import com.senox.user.vo.MerchantVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18 10:23
 */
@Api(tags = "三轮车配送订单")
@RequiredArgsConstructor
@RequestMapping("/bicycle/order")
@RestController
public class BicycleOrderController {

    private final BicycleOrderService bicycleOrderService;
    private final BicycleOrderConvert bicycleOrderConvert;
    private final BicycleOrderGoodsDetailConvert bicycleOrderGoodsDetailConvert;
    private final BicycleOrderGoodsDetailService bicycleOrderGoodsDetailService;
    private final BicycleChargesService bicycleChargesService;
    private final BicycleChargesDetailConvert chargesDetailConvert;
    private final BicycleOperateAnalysisService bicycleOperateAnalysisService;
    private final BicycleOperateAnalysisConvert bicycleOperateAnalysisConvert;
    private final MerchantComponent merchantComponent;
    private final BicycleDeliveryOrderService bicycleDeliveryOrderService;
    private final BicycleOrderUtilityService orderUtilityService;

    @ApiOperation("添加三轮车配送订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addBicycleOrder(@Validated(Add.class) @RequestBody BicycleOrderVo orderVo) {
        BicycleOrder bicycleOrder = saveBicycleOrder(orderVo);
        return bicycleOrder == null ? 0L : bicycleOrder.getId();
    }

    @ApiOperation("批量添加三轮车配送订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/batchAdd")
    public List<String> batchAddBicycleOrder(@Validated(Add.class) @RequestBody List<BicycleOrderVo> orderVoList) {
        List<String> orderSerialNo = new ArrayList<>();
        orderVoList.forEach(orderVo -> {
            BicycleOrder bicycleOrder = saveBicycleOrder(orderVo);
            if (bicycleOrder != null) {
                orderSerialNo.add(bicycleOrder.getOrderSerialNo());
            }
        });
        return orderSerialNo;
    }

    private BicycleOrder saveBicycleOrder(BicycleOrderVo orderVo) {
        BicycleOrder order = bicycleOrderConvert.toDo(orderVo);
        ContextUtils.initEntityCreator(order);
        ContextUtils.initEntityModifier(order);
        return bicycleOrderService.addBicycleOrder(order, orderVo.getOrderGoodsDetailVos(), orderVo.getMediaUrlList());
    }

    @ApiOperation("更新配送订单状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/updateState/{id}")
    public void updateBicycleOrderState(@PathVariable Long id, @RequestParam Integer state) {
        bicycleDeliveryOrderService.updateBicycleOrderState(id, state);
    }

    @ApiOperation("根据id获取配送订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleOrderVo findOrderVoById(@PathVariable Long id, @RequestParam(required = false) Boolean containStatus) {
        return bicycleOrderService.findOrderVoById(id, containStatus);
    }

    @ApiOperation("根据订单号查询订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/byOrderSerialNo/{orderSerialNo}")
    public BicycleOrderVo findOrderVoByOrderSerialNo(@PathVariable String orderSerialNo) {
        return bicycleOrderService.findOrderVoByOrderSerialNo(orderSerialNo);
    }

    @ApiOperation("根据id删除配送订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteBicycleOrder(@PathVariable Long id) {
        bicycleOrderService.deleteBicycleOrder(id, true);
    }

    @ApiOperation("配送订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<BicycleOrderVo> listOrder(@RequestBody BicycleOrderSearchVo searchVo) {
        return bicycleOrderService.listOrder(searchVo);
    }

    @ApiOperation("配送订单列表视图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/view")
    public PageResult<BicycleOrderVo> listOrderView(@RequestBody BicycleOrderSearchVo searchVo) {
        return bicycleOrderService.listOrderView(searchVo);
    }

    @ApiOperation("导出订单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/exportList")
    public List<BicycleOrderVo> exportOrderList(@RequestBody BicycleOrderSearchVo searchVo) {
        return bicycleOrderService.exportOrderList(searchVo);
    }

    @ApiOperation("客户每月下单统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/customer/info/list")
    public List<BicycleCustomerMonthInfoVo> customerMonthInfoList(@RequestBody BicycleCustomerMonthInfoSearchVo searchVo) {
        return bicycleOrderService.customerMonthInfoList(searchVo);
    }

    @ApiOperation("配送订单合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sum")
    public BicycleOrderVo sumOrder(@RequestBody BicycleOrderSearchVo searchVo) {
        return bicycleOrderService.sumOrder(searchVo);
    }

    @ApiOperation("配送订单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<BicycleOrderVo> page(@RequestBody BicycleOrderSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return bicycleOrderService.page(searchVo);
    }

    @ApiOperation("三轮车配送订单合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delivery/sum")
    public BicycleOrderVo sumDeliveryOrder(@RequestBody BicycleOrderSearchVo search) {
        return bicycleOrderService.sumOrders(search);
    }

    @ApiOperation("三轮车配送订单页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delivery/page")
    public PageResult<BicycleOrderVo> listOrderPage(@RequestBody BicycleOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }
        return bicycleOrderService.listOrderPage(search);
    }

    @ApiOperation("计算配送费用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/calculate")
    public BicycleOrderCalculateEstimateVo calculateEstimateCharges(@Validated(Update.class) @RequestBody BicycleOrderVo orderVo, @RequestParam Boolean useChargesId) {
        BicycleOrder order = bicycleOrderConvert.toDo(orderVo);
        List<BicycleOrderGoodsDetail> goodsDetails = bicycleOrderGoodsDetailConvert.toDo(orderVo.getOrderGoodsDetailVos());
        if (BooleanUtils.isTrue(useChargesId)) {
            if (WrapperClassUtils.biggerThanLong(order.getSenderId(), 0)) {
                MerchantVo merchant = merchantComponent.findById(order.getSenderId());
                order.setChargesId(WrapperClassUtils.biggerThanLong(merchant.getBicycleChargesId(), 0)
                        ? merchant.getBicycleChargesId()
                        : bicycleChargesService.getCurrentEffectiveCharges().getId()
                );
            }
            return bicycleChargesService.calculateEstimateCharges(order.getChargesId(), goodsDetails);
        } else {
            if (CollectionUtils.isEmpty(orderVo.getChargesDetails())) {
                throw new BusinessException("收费标准详细为空！");
            }
            List<BicycleChargesDetail> chargesDetails = chargesDetailConvert.toDo(orderVo.getChargesDetails());
            return bicycleChargesService.calculateEstimateCharges(orderVo.getMinAmount(), chargesDetails, goodsDetails);
        }
    }

    @ApiOperation("运营分析")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/operate/analysis/statistics")
    public BicycleOperateAnalysisVo operateAnalysisStatistics(@RequestBody BicycleStatisticsSearchVo searchVo) {
        return bicycleOrderService.findOperateAnalysis(searchVo.getStartTime(), searchVo.getEndTime());
    }

    @ApiOperation("配送地点使用统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/point/count")
    public List<BicyclePointCountVo> listPointCount(@RequestParam Boolean isStart) {
        return bicycleOrderService.listPointCount(isStart);
    }

    @ApiOperation("排行榜")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/ranking/statistics")
    public BicycleCountRankingVo rankingStatistics(@RequestBody BicycleStatisticsSearchVo searchVo) {
        return bicycleOrderService.countRanking(searchVo.getStartTime(), searchVo.getEndTime());
    }

    @ApiOperation("未处理的订单数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/undoCount")
    public Integer undoOrderCount() {
        return bicycleOrderService.undoOrderCount();
    }

    @ApiOperation("历史运营分析记录列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/analysis/list")
    public PageResult<BicycleOperateAnalysisVo> listOperateAnalysis(@RequestBody BicycleOperateAnalysisSearchVo searchVo) {
        return bicycleOperateAnalysisService.listOperateAnalysis(searchVo);
    }

    @ApiOperation("历史运营分析记录合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/analysis/sum")
    public BicycleOperateAnalysisVo sumOperateAnalysis(@RequestBody  BicycleOperateAnalysisSearchVo searchVo) {
        BicycleOperateAnalysis analysis = bicycleOperateAnalysisService.sumOperateAnalysis(searchVo);
        return analysis == null ? null : bicycleOperateAnalysisConvert.toVo(analysis);
    }

    @ApiOperation("客户下单统计列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/customer/count/list")
    public PageResult<BicycleCustomerCountVo> customerCountList(@RequestBody BicycleCustomerCountSearchVo searchVo) {
        return bicycleOrderService.customerCountList(searchVo);
    }

    @ApiOperation("客户下单统计合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/customer/count/sum")
    public BicycleCustomerCountVo sumCustomerCount(@RequestBody BicycleCustomerCountSearchVo searchVo) {
        return bicycleOrderService.sumCustomerCount(searchVo);
    }

    @ApiOperation("查询订单统计数量情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count")
    public BicycleOrderCountVo orderCount(@RequestBody BicycleOrderCountSearchVo searchVo) {
        return bicycleOrderService.orderCount(searchVo);
    }

    @ApiOperation("删除未生成结算单的订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/cancel/{id}")
    public void cancelBicycleOrderById(@PathVariable Long id) {
        bicycleDeliveryOrderService.cancelBicycleOrderById(id, BicycleOrderStatus.FINALIZE.getNumber(), null);
    }

    @ApiOperation("批量删除未生成结算单的订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cancel")
    public void cancelBicycleOrderByIds(@RequestBody List<Long> ids) {
        bicycleDeliveryOrderService.cancelBicycleOrderByIds(ids, BicycleOrderStatus.FINALIZE.getNumber(), null);
    }

    @ApiOperation("取消订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cancel/order")
    public void cancelBicycleOrder(@RequestBody BicycleOrderCancelVo cancelVo) {
        bicycleDeliveryOrderService.cancelBicycleOrderByIds(cancelVo.getOrderIds(), cancelVo.getStatus(), cancelVo.statusRemark);
    }

    @ApiOperation("订单导入")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/import")
    public void orderImport(@RequestBody List<BicycleOrderImportDto> orderImportDtoList){
        orderUtilityService.orderImport(orderImportDtoList);
    }

    @ApiOperation("根据id删除未配送的订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/undelivered/{id}")
    public void deleteUndeliveredBicycleOrder(@PathVariable Long id) {
        bicycleDeliveryOrderService.deleteUndeliveredBicycleOrder(id);
    }

    @ApiOperation("三轮车配送订单数量V2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count/order/v2")
    public int countOrderV2(@RequestBody BicycleOrderV2SearchVo searchVo) {
        return bicycleOrderService.countOrderV2(searchVo);
    }

    @ApiOperation("三轮车配送订单列表V2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/order/v2")
    public List<BicycleOrderV2Vo> listOrderV2(@RequestBody BicycleOrderV2SearchVo searchVo) {
        return bicycleOrderService.listOrderV2(searchVo);
    }

    @ApiOperation("三轮车配送订单视图分页V2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page/order/view/v2")
    public PageResult<BicycleOrderV2Vo> pageOrderViewV2(@RequestBody BicycleOrderV2SearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return bicycleOrderService.pageOrderViewV2(searchVo);
    }

    @ApiOperation("三轮车配送订单分页V2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page/order/v2")
    public PageResult<BicycleDeliveryOrderV2Vo> pageOrderV2(@RequestBody BicycleOrderV2SearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return bicycleOrderService.pageOrderV2(searchVo);
    }

    @ApiOperation("根据订单id查询货物信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/goodsDetail/{orderId}")
    public List<BicycleOrderGoodsDetailVo> goodsDetailByOrderId(@PathVariable Long orderId) {
        return bicycleOrderGoodsDetailConvert.toVo(bicycleOrderGoodsDetailService.orderGoodsDetailByOrderId(orderId));
    }

    @ApiOperation("查询订单统计数量情况V2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count/v2")
    public BicycleOrderCountVo orderCountV2(@RequestBody BicycleOrderCountSearchVo searchVo) {
        return bicycleOrderService.orderCountV2(searchVo);
    }

    @ApiOperation("三轮车配送订单合计V2")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sum/order/v2")
    public BicycleOrderV2Vo sumOrderV2(@RequestBody BicycleOrderV2SearchVo searchVo) {
        return bicycleOrderService.sumOrderV2(searchVo);
    }
}

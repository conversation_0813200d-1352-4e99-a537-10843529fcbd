package com.senox.tms.controller;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.BicycleChargesConvert;
import com.senox.tms.convert.BicycleChargesDetailConvert;
import com.senox.tms.domain.BicycleCharges;
import com.senox.tms.domain.BicycleChargesDetail;
import com.senox.tms.service.BicycleChargesService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.BicycleChargesDetailVo;
import com.senox.tms.vo.BicycleChargesSearchVo;
import com.senox.tms.vo.BicycleChargesVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 收费标准 controller
 *
 * <AUTHOR>
 * @date 2023-9-12
 */
@Api(tags = "收费标准")
@RequiredArgsConstructor
@RequestMapping("/bicycle/charges")
@RestController
public class BicycleChargesController {

    private final BicycleChargesService chargesService;
    private final BicycleChargesConvert chargesConvert;
    private final BicycleChargesDetailConvert chargesDetailConvert;

    @ApiOperation("添加收费标准")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long add(@Validated({Add.class}) @RequestBody BicycleChargesVo chargesVo) {
        BicycleCharges bicycleDeliveryCharges = chargesConvert.toDo(chargesVo);
        ContextUtils.initEntityCreator(bicycleDeliveryCharges);
        ContextUtils.initEntityModifier(bicycleDeliveryCharges);
        chargesService.add(bicycleDeliveryCharges, chargesDetailConvert.toDo(chargesVo.getChargesDetails()));
        return bicycleDeliveryCharges.getId();
    }

    @ApiOperation("根据id查询收费标准")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleChargesVo getById(@PathVariable Long id) {
        return chargesConvert.toVo(chargesService.getById(id));
    }

    @ApiOperation("删除收费标准")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void delete(@PathVariable Long id) {
        chargesService.delete(id);
    }

    @ApiOperation("修改收费标准")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@Validated({Update.class}) @RequestBody BicycleChargesVo chargesVo) {
        BicycleCharges charges = chargesConvert.toDo(chargesVo);
        List<BicycleChargesDetail> chargesDetails = chargesDetailConvert.toDo(chargesVo.getChargesDetails());
        ContextUtils.initEntityModifier(charges);
        chargesService.update(charges, chargesDetails);
    }

    @ApiOperation("收费标准列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<BicycleChargesVo> list(@RequestBody BicycleChargesSearchVo searchVo) {
        return PageResult.convertPage(chargesService.list(searchVo),chargesConvert::toVo);
    }

    @ApiOperation("添加收费标准明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/detail/add")
    public void addDetail(@Validated({Add.class}) @RequestBody BicycleChargesDetailVo chargesDetailVo) {
        BicycleChargesDetail chargesDetail = chargesDetailConvert.toDo(chargesDetailVo);
        chargesService.addDetailBatch(Collections.singletonList(chargesDetail));
    }

    @ApiOperation("修改收费标准明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/detail/update")
    public void updateDetail(@Validated({Update.class}) @RequestBody BicycleChargesDetailVo chargesDetailVo) {
        BicycleChargesDetail chargesDetail = chargesDetailConvert.toDo(chargesDetailVo);
        chargesService.updateDetailBatch(Collections.singletonList(chargesDetail));
    }

    @ApiOperation("删除收费标准明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/detail/delete")
    public void deleteDetail(@RequestBody List<Long> chargesDetailIds) {
        chargesService.deleteDetailBatch(chargesDetailIds);
    }


    @ApiOperation("根据收费标准查询明细列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/detail/list/{chargesId}")
    public List<BicycleChargesDetailVo> listDetailByCharges(@PathVariable Long chargesId) {
        if (!WrapperClassUtils.biggerThanLong(chargesId, 0)) {
            //获取默认收费标准id
            chargesId = chargesService.getCurrentEffectiveCharges().getId();
        }
        return chargesService.listDetailByCharges(chargesId);
    }

    @ApiOperation("获取当前默认收费标准")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/current/charges")
    public BicycleChargesVo getCurrentEffectiveCharges() {
        return chargesConvert.toVo(chargesService.getCurrentEffectiveCharges());
    }
}

package com.senox.tms.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.LoaderSettlementConvert;
import com.senox.tms.domain.LogisticLoaderSettlement;
import com.senox.tms.service.LogisticLoaderSettlementService;
import com.senox.tms.vo.LogisticLoaderSettlementFormVo;
import com.senox.tms.vo.LogisticLoaderSettlementSearchVo;
import com.senox.tms.vo.LogisticLoaderSettlementVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Api(tags = "物流搬运工结算")
@RestController
@RequiredArgsConstructor
@RequestMapping("/logistic/loader/settlement")
public class LogisticLoaderSettlementController {
    private final LogisticLoaderSettlementService loaderSettlementService;

    private final LoaderSettlementConvert settlementConvert;

    @ApiOperation("添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void add(@Validated({Add.class}) @RequestBody LogisticLoaderSettlementFormVo settlementFormVo) {
        LogisticLoaderSettlement settlement = settlementConvert.toDo(settlementFormVo);
        loaderSettlementService.addAndIncome(settlement,settlementFormVo.getLoaderIds());
    }

    @ApiOperation("更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@Validated({Update.class}) @RequestBody LogisticLoaderSettlementFormVo settlementFormVo) {
        LogisticLoaderSettlement settlement = settlementConvert.toDo(settlementFormVo);
        loaderSettlementService.updateAndRefreshIncome(settlement,settlementFormVo.getLoaderIds());

    }

    @ApiOperation("根据id获取结算")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public LogisticLoaderSettlementVo findById(@PathVariable Long id) {
        return loaderSettlementService.getById(id);
    }

    @ApiOperation("删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        loaderSettlementService.deleteById(id);
    }


    @ApiOperation("列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<LogisticLoaderSettlementVo> list(@RequestBody LogisticLoaderSettlementSearchVo searchVo) {
        return loaderSettlementService.list(searchVo);
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/page")
    public PageResult<LogisticLoaderSettlementVo> listPage(@RequestBody LogisticLoaderSettlementSearchVo searchVo) {
        return loaderSettlementService.listPage(searchVo);
    }

}

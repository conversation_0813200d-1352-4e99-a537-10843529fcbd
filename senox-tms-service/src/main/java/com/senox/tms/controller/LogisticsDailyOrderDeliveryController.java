package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.LogisticsDailyOrderDeliveryConvert;
import com.senox.tms.domain.LogisticsDailyOrderDelivery;
import com.senox.tms.service.LogisticsDailyOrderDeliveryService;
import com.senox.tms.vo.LogisticsDailyOrderDeliverySearchVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryTotalAmountVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-12-5
 */
@Api(tags = "物流每日订单配送")
@RestController
@RequiredArgsConstructor
@RequestMapping("/logistics/daily/order/delivery")
public class LogisticsDailyOrderDeliveryController {
    private final LogisticsDailyOrderDeliveryService dailyOrderDeliveryService;
    private final LogisticsDailyOrderDeliveryConvert dailyOrderDeliveryConvert;

    @ApiOperation("批量添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/batch/add")
    public void addBatch(@RequestBody List<LogisticsDailyOrderDeliveryVo> dailyOrderDeliveryVos) {
        dailyOrderDeliveryService.addBatch(dailyOrderDeliveryConvert.toDo(dailyOrderDeliveryVos));
    }

    @ApiOperation("根据id查找配送")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public LogisticsDailyOrderDeliveryVo findById(@PathVariable Long id) {
        return dailyOrderDeliveryConvert.toV(dailyOrderDeliveryService.findById(id));
    }

    @ApiOperation("修改")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody LogisticsDailyOrderDeliveryVo dailyOrderDeliveryVo) {
        dailyOrderDeliveryService.update(dailyOrderDeliveryConvert.toDo(dailyOrderDeliveryVo));
    }

    @ApiOperation("删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/deleteById/{id}")
    public void deleteById(@PathVariable Long id) {
        dailyOrderDeliveryService.deleteById(id);
    }

    @ApiOperation("列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<LogisticsDailyOrderDeliveryVo> list(@RequestBody LogisticsDailyOrderDeliverySearchVo searchVo) {
        List<LogisticsDailyOrderDelivery> list = dailyOrderDeliveryService.list(searchVo);
        return list.stream().map(dailyOrderDeliveryConvert::toV).collect(Collectors.toList());
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/page")
    public PageStatisticsResult<LogisticsDailyOrderDeliveryVo, LogisticsDailyOrderDeliveryTotalAmountVo> listPage(@RequestBody LogisticsDailyOrderDeliverySearchVo searchVo) {
        PageStatisticsResult<LogisticsDailyOrderDelivery, LogisticsDailyOrderDeliveryTotalAmountVo> result = dailyOrderDeliveryService.listPage(searchVo);
        return new PageStatisticsResult<>(PageResult.convertPage(result, dailyOrderDeliveryConvert::toV), result.getStatistics());
    }
}

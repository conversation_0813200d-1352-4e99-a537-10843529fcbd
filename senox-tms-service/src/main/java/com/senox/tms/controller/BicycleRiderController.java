package com.senox.tms.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.BicycleRiderConvert;
import com.senox.tms.domain.BicycleRider;
import com.senox.tms.service.BicycleRiderService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/13 15:48
 */
@Api(tags = "三轮车配送骑手")
@RequiredArgsConstructor
@RequestMapping("/bicycle/rider")
@RestController
public class BicycleRiderController {

    private final BicycleRiderService bicycleRiderService;
    private final BicycleRiderConvert bicycleRiderConvert;

    @ApiOperation("添加三轮车配送骑手")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addBicycleRider(@Validated(Add.class) @RequestBody BicycleRiderVo bicycleRiderVo) {
        BicycleRider rider = bicycleRiderConvert.toDo(bicycleRiderVo);
        ContextUtils.initEntityCreator(rider);
        ContextUtils.initEntityModifier(rider);
        return bicycleRiderService.addBicycleRider(rider);
    }

    @ApiOperation("修改三轮车配送骑手")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateBicycleRider(@Validated(Update.class) @RequestBody BicycleRiderVo bicycleRiderVo) {
        BicycleRider rider = bicycleRiderConvert.toDo(bicycleRiderVo);
        ContextUtils.initEntityModifier(rider);
        bicycleRiderService.updateBicycleRider(rider);
    }

    @ApiOperation("生成推荐码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/generate/referralCode/{id}")
    public void generateReferralCode(@PathVariable Long id) {
        bicycleRiderService.generateReferralCode(id);
    }

    @ApiOperation("修改三轮车配送骑手密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/password/modify")
    public void modifyPassword(@Validated @RequestBody BicycleRiderChangerPwdVo pwdVo) {
        bicycleRiderService.changePassword(pwdVo);
    }

    @ApiOperation("根据id获取三轮车骑手")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleRiderVo findById(@PathVariable Long id) {
        BicycleRider rider = bicycleRiderService.findById(id, false);
        return rider != null ? bicycleRiderConvert.toVo(rider) : null;
    }

    @ApiOperation("校验密码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/password/check")
    public BicycleRiderVo checkRiderByContactAndPassword(@Validated @RequestBody BicycleRiderLoginVo loginVo) {
        BicycleRider rider = bicycleRiderService.findByContactAndPassword(loginVo.getContact(), loginVo.getPassword());
        return rider != null ? bicycleRiderConvert.toVo(rider) : null;
    }

    @ApiOperation("删除三轮车配送骑手")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteBicycleRider(@PathVariable Long id) {
        bicycleRiderService.deleteBicycleRider(id);
    }

    @ApiOperation("三轮车配送骑手列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<BicycleRiderVo> list(@RequestBody BicycleRiderSearchVo searchVo) {
        PageResult<BicycleRider> result = bicycleRiderService.list(searchVo);
        return result != null ? PageResult.convertPage(result, bicycleRiderConvert::toVo) : PageResult.emptyPage();
    }

    @ApiOperation("三轮车配送骑手信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/info")
    public List<BicycleRiderInfoVo> listRider() {
        return bicycleRiderService.listRider();
    }
}

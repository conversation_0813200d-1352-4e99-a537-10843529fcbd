package com.senox.tms.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.convert.LogisticOrderConvert;
import com.senox.tms.domain.LogisticOrder;
import com.senox.tms.domain.LogisticOrderBill;
import com.senox.tms.domain.LogisticOrderProduct;
import com.senox.tms.domain.LogisticOrderShip;
import com.senox.tms.service.LogisticOrderService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticOrderEditBatchVo;
import com.senox.tms.vo.LogisticOrderSearchVo;
import com.senox.tms.vo.LogisticOrderVo;
import com.senox.tms.vo.ShipOrderDiscountSearchVo;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:19
 */
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/logistic/order")
public class LogisticOrderController {

    private final LogisticOrderConvert orderConvert;
    private final LogisticOrderService orderService;

    @ApiOperation("添加物流单")
    @PostMapping("/add")
    public Long addLogisticOrder(@Validated @RequestBody LogisticOrderVo order) {
        LogisticOrder entity = toDo(order);
        return orderService.addLogisticOrder(entity);
    }

    @ApiOperation("更新物流单")
    @PostMapping("/update")
    public void updateLogisticOrder(@RequestBody LogisticOrderVo order) {
        if (!WrapperClassUtils.biggerThanLong(order.getProductOrderId(), 0L)) {
            throw new InvalidParameterException();
        }

        LogisticOrder entity= toDo(order);
        orderService.updateLogisticOrder(entity);
    }


    @ApiOperation("批量保存物流单")
    @PostMapping("/saveBatch")
    public void saveLogisticOrderBatch(@Valid @RequestBody List<LogisticOrderVo> list, @RequestParam(required = false) Boolean overwrite) {
        if (CollectionUtils.isEmpty(list)) {
            throw new InvalidParameterException();
        }

        List<LogisticOrder> orders = list.stream().map(this::toDo).collect(Collectors.toList());
        orderService.saveLogisticOrderBatch(orders, overwrite);
    }

    @ApiOperation("批量更新物流单信息")
    @PostMapping("/editBatch")
    public void editLogisticOrderBatch(@RequestBody LogisticOrderEditBatchVo editInfo) {
        if (CollectionUtils.isEmpty(editInfo.getProductOrderIds())) {
            throw new InvalidParameterException();
        }

        editInfo.setOperatorId(ContextUtils.getUserInContext(false).getUserId());
        editInfo.setOperatorName(ContextUtils.getUserInContext(false).getUsername());
       orderService.updateLogisticOrderInfo(editInfo);
    }

    @ApiOperation("删除物流配送单")
    @PostMapping("/delete")
    public void deleteLogisticOrder(@RequestBody List<Long> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            throw new InvalidParameterException();
        }

        orderService.deleteLogisticOrders(orderIds);
    }

    @ApiOperation("批量更新物流单信息")
    @PostMapping("/ship/discount/get")
    public BigDecimal calShipDiscount(@Validated @RequestBody ShipOrderDiscountSearchVo search) {
        return orderService.calShipOrderDiscount(search);
    }

    @ApiOperation("获取物流单详情")
    @GetMapping("/get/{id}")
    public LogisticOrderVo findLogisticOrderById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return orderService.findByProductOrderId(id);
    }

    @ApiOperation("物流单合计")
    @PostMapping("/sum")
    public LogisticOrderVo sumLogisticOrder(@RequestBody LogisticOrderSearchVo search) {
        return orderService.sumOrder(search);
    }

    @ApiOperation("物流单列表（无分页）")
    @PostMapping("/list")
    public List<LogisticOrderVo> listLogisticOrder(@RequestBody LogisticOrderSearchVo search) {
        search.setPage(false);
        return orderService.listOrder(search);
    }

    @ApiOperation("物流单列表")
    @PostMapping("/page")
    public PageResult<LogisticOrderVo> listLogisticOrderPage(@RequestBody LogisticOrderSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        return PageUtils.commonPageResult(search, () -> orderService.countOrder(search), () -> orderService.listOrder(search));
    }

    /**
     * 视图转列表
     * @param vo
     * @return
     */
    private LogisticOrder toDo(LogisticOrderVo vo) {
        LogisticOrderProduct productOrder = orderConvert.toProductOrder(vo);
        LogisticOrderShip ship = orderConvert.toShipOrder(vo);
        LogisticOrderBill bill = orderConvert.toOrderBill(vo);

        ContextUtils.initEntityCreator(productOrder);
        ContextUtils.initEntityModifier(productOrder);
        ContextUtils.initEntityCreator(ship);
        ContextUtils.initEntityModifier(ship);
        ContextUtils.initEntityCreator(bill);
        ContextUtils.initEntityModifier(bill);
        return new LogisticOrder(productOrder, ship, bill);
    }

}

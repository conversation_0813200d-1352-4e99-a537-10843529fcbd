package com.senox.tms.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.service.BicycleBillService;
import com.senox.tms.vo.BicycleBillSearchVo;
import com.senox.tms.vo.BicycleBillVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22 10:14
 */
@Api(tags = "三轮车配送应收账单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bicycle/bill")
public class BicycleBillController {

    private final BicycleBillService bicycleBillService;

    @ApiOperation("获取账单详情")
    @GetMapping("/get/{id}")
    public BicycleBillVo getBill(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        return bicycleBillService.findDetailById(id);
    }

    @ApiOperation("根据id列表获取账单详情")
    @PostMapping("/listByIds")
    public List<BicycleBillVo> listBillByIds(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        return bicycleBillService.listDetailByIds(ids);
    }

    @ApiOperation("应收账单合计")
    @PostMapping("/sum")
    public BicycleBillVo sumBill(@RequestBody BicycleBillSearchVo searchVo) {
        return bicycleBillService.sumBill(searchVo);
    }

    @ApiOperation("应收账单列表页")
    @PostMapping("/list")
    public PageResult<BicycleBillVo> listBillPage(@RequestBody BicycleBillSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }

       return bicycleBillService.listBillPage(searchVo);
    }

}

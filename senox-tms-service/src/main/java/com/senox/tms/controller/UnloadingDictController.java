package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.UnloadingDictConvert;
import com.senox.tms.domain.UnloadingDict;
import com.senox.tms.service.UnloadingDictService;
import com.senox.tms.vo.UnloadingDictSearchVo;
import com.senox.tms.vo.UnloadingDictVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/26 10:43
 */
@Api(tags = "鹏翔搬运字典")
@RequiredArgsConstructor
@RequestMapping("/unloading/dict")
@RestController
public class UnloadingDictController {

    private final UnloadingDictService dictService;
    private final UnloadingDictConvert dictConvert;

    @ApiOperation("批量添加字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/batchAdd")
    public void batchAddDict(@RequestBody List<UnloadingDictVo> dictVos) {
        List<UnloadingDict> dictList = dictConvert.toDo(dictVos);
        dictService.batchAddDict(dictList);
    }

    @ApiOperation("添加字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void addDict(@RequestBody UnloadingDictVo dictVo) {
        UnloadingDict dict = dictConvert.toDo(dictVo);
        dictService.addDict(dict);
    }

    @ApiOperation("更新字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateDict(@RequestBody UnloadingDictVo dictVo) {
        UnloadingDict dict = dictConvert.toDo(dictVo);
        dictService.updateDict(dict);
    }

    @ApiOperation("根据id查询字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public UnloadingDictVo findById(@PathVariable Long id) {
        return dictConvert.toVo(dictService.findById(id));
    }

    @ApiOperation("根据id删除字典")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteDict(@PathVariable Long id) {
        UnloadingDict dict = new UnloadingDict();
        dict.setId(id);
        dictService.deleteDict(dict);
    }

    @ApiOperation("字典分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<UnloadingDictVo> dictPageResult(@RequestBody UnloadingDictSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageResult.convertPage(dictService.dictPageResult(searchVo), dictConvert::toVo);
    }
}

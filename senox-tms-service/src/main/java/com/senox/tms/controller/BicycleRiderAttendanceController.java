package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.BicycleRiderAttendanceConvert;
import com.senox.tms.domain.BicycleRiderAttendance;
import com.senox.tms.service.BicycleRiderAttendanceService;
import com.senox.tms.vo.BicycleRiderAttendanceSearchVo;
import com.senox.tms.vo.BicycleRiderAttendanceVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/9/14 16:57
 */
@Api(tags = "三轮车配送骑手考勤")
@RequiredArgsConstructor
@RequestMapping("/bicycle/rider/attendance")
@RestController
public class BicycleRiderAttendanceController {

    private final BicycleRiderAttendanceService bicycleRiderAttendanceService;
    private final BicycleRiderAttendanceConvert bicycleRiderAttendanceConvert;

    @ApiOperation("骑手上线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/online/{riderId}")
    public Long riderOnline(@PathVariable Long riderId) {
        return bicycleRiderAttendanceService.riderOnline(riderId);
    }

    @ApiOperation("骑手下线")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/offline/{riderId}")
    public void riderOffline(@PathVariable Long riderId) {
        bicycleRiderAttendanceService.riderOffline(riderId);
    }

    @ApiOperation("根据id获取三轮车骑手考勤信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleRiderAttendanceVo findById(@PathVariable Long id) {
        BicycleRiderAttendance riderAttendance = bicycleRiderAttendanceService.findById(id);
        return riderAttendance != null ? bicycleRiderAttendanceConvert.toVo(riderAttendance) :null;
    }

    @ApiOperation("三轮车骑手考勤信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public PageResult<BicycleRiderAttendanceVo> listRiderAttendance(@RequestBody BicycleRiderAttendanceSearchVo searchVo) {
        return bicycleRiderAttendanceService.listRiderAttendance(searchVo);
    }
}

package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.BicycleChargesScheduledTaskConvert;
import com.senox.tms.domain.BicycleChargesScheduledTask;
import com.senox.tms.service.BicycleChargesScheduledTaskService;
import com.senox.tms.vo.BicycleChargesScheduledTaskSearchVo;
import com.senox.tms.vo.BicycleChargesScheduledTaskVo;
import com.senox.tms.vo.MerchantInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 三轮车费用标准计划任务
 *
 * <AUTHOR>
 * @date 2023-9-12
 */
@Api(tags = "三轮车费用标准计划任务")
@RequiredArgsConstructor
@RequestMapping("/bicycle/charges/task")
@RestController
public class BicycleChargesScheduledTaskController {
    private final BicycleChargesScheduledTaskService taskService;
    private final BicycleChargesScheduledTaskConvert taskConvert;

    @ApiOperation("添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public void add(@RequestBody BicycleChargesScheduledTaskVo taskVo) {
        BicycleChargesScheduledTask task = taskConvert.toDo(taskVo);
        taskService.addBatch(Collections.singleton(task));
        List<Long> merchantIds = taskVo.getMerchantInfos().stream().map(MerchantInfoVo::getMerchantId).collect(Collectors.toList());
        taskService.addMerchant(task.getId(), merchantIds);
    }

    @ApiOperation("更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody BicycleChargesScheduledTaskVo task) {
        BicycleChargesScheduledTask updateTask = taskConvert.toDo(task);
        List<Long> merchantIds = task.getMerchantInfos().stream().map(MerchantInfoVo::getMerchantId).collect(Collectors.toList());
        taskService.update(updateTask, merchantIds);
    }

    @ApiOperation("删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{taskId}")
    public void delete(@PathVariable Long taskId) {
        taskService.delete(taskId);
    }

    @ApiOperation("列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<BicycleChargesScheduledTaskVo> list(@RequestBody BicycleChargesScheduledTaskSearchVo searchVo) {
        return taskService.list(searchVo);
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/page")
    public PageResult<BicycleChargesScheduledTaskVo> listPage(@RequestBody BicycleChargesScheduledTaskSearchVo searchVo) {
        return taskService.listPage(searchVo);
    }

    @ApiOperation("根据id查找计划任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/find/{id}")
    public BicycleChargesScheduledTaskVo findById(@PathVariable Long id){
         return taskService.findById(id);
    }
}

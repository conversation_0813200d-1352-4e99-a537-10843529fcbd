package com.senox.tms.controller;

import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.BicycleOrderDayReportConvert;
import com.senox.tms.convert.BicycleOrderMonthReportConvert;
import com.senox.tms.domain.BicycleOrderDayReport;
import com.senox.tms.domain.BicycleOrderMonthReport;
import com.senox.tms.service.BicycleOrderDayReportService;
import com.senox.tms.service.BicycleOrderMonthReportService;
import com.senox.tms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/7 16:57
 */
@Api(tags = "三轮车报表")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bicycle/order/report")
public class BicycleOrderReportController {

    private final BicycleOrderDayReportService bicycleOrderDayReportService;
    private final BicycleOrderDayReportConvert dayReportConvert;
    private final BicycleOrderMonthReportService bicycleOrderMonthReportService;
    private final BicycleOrderMonthReportConvert monthReportConvert;

    @ApiOperation("生成日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/generate")
    public void generateDayReport(@RequestBody BicycleReportDateVo dateVo) {
        if (!WrapperClassUtils.biggerThanInt(dateVo.getYear(),0)){
            dateVo.setYear(LocalDate.now().getYear());
        }
        if (!WrapperClassUtils.biggerThanInt(dateVo.getMonth(), 0)) {
            dateVo.setMonth(LocalDate.now().getMonthValue());
        }
        if (!WrapperClassUtils.biggerThanInt(dateVo.getDay(), 0)) {
            dateVo.setDay(LocalDate.now().getDayOfMonth());
        }
        bicycleOrderDayReportService.generateDayReport(dateVo);
    }


    @ApiOperation("更新日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/update")
    public void updateDayReport(@Validated(Update.class) @RequestBody BicycleOrderDayReportVo dayReportVo) {
        BicycleOrderDayReport dayReport = dayReportConvert.toDo(dayReportVo);
        bicycleOrderDayReportService.updateDayReport(dayReport);
    }

    @ApiOperation("根据id集合查询日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/list/byIds")
    public List<BicycleOrderDayReportVo> listDayReport(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        List<BicycleOrderDayReport> dayBillList = bicycleOrderDayReportService.listByIds(ids);
        return dayBillList == null ? null : dayReportConvert.toVo(dayBillList);
    }

    @ApiOperation("删除日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/delete")
    public void deleteDayReport(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        bicycleOrderDayReportService.deleteByIds(ids);
    }

    @ApiOperation("获取日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/day/get/{id}")
    public BicycleOrderDayReportVo findDayReportById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        BicycleOrderDayReport bill = bicycleOrderDayReportService.findById(id);
        return bill == null ? null : dayReportConvert.toVo(bill);
    }

    @ApiOperation("日报表合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/sum")
    public BicycleOrderDayReportVo sumDayReport(@RequestBody BicycleOrderDayReportSearchVo searchVo) {
        BicycleOrderDayReport bill = bicycleOrderDayReportService.sumDayReport(searchVo);
        return dayReportConvert.toVo(bill);
    }

    @ApiOperation("日报表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/day/list")
    public PageResult<BicycleOrderDayReportVo> listDayReport(@RequestBody BicycleOrderDayReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }

        return bicycleOrderDayReportService.listDayReport(searchVo);
    }

    @ApiOperation("生成月报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/generate")
    public void generateMonthReport(@RequestBody BicycleReportDateVo dateVo) {
        if (!WrapperClassUtils.biggerThanInt(dateVo.getYear(),0)){
            dateVo.setYear(LocalDate.now().getYear());
        }
        if (!WrapperClassUtils.biggerThanInt(dateVo.getMonth(), 0)) {
            dateVo.setMonth(LocalDate.now().getMonthValue());
        }
        bicycleOrderMonthReportService.generateMonthReport(dateVo);
    }

    @ApiOperation("更新月报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/update")
    public void updateMonthReport(@Validated(Update.class) @RequestBody BicycleOrderMonthReportVo monthReportVo) {
        BicycleOrderMonthReport monthReport = monthReportConvert.toDo(monthReportVo);
        bicycleOrderMonthReportService.updateMonthReport(monthReport);
    }

    @ApiOperation("根据id集合查询月报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/list/byIds")
    public List<BicycleOrderMonthReportVo> listMonthReport(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        List<BicycleOrderMonthReport> monthBillList = bicycleOrderMonthReportService.listByIds(ids);
        return monthBillList == null ? null : monthReportConvert.toVo(monthBillList);
    }

    @ApiOperation("删除月报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/delete")
    public void deleteMonthReport(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        bicycleOrderMonthReportService.deleteByIds(ids);
    }

    @ApiOperation("获取月报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/month/get/{id}")
    public BicycleOrderMonthReportVo findMonthReportById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }

        BicycleOrderMonthReport bill = bicycleOrderMonthReportService.findById(id);
        return bill == null ? null : monthReportConvert.toVo(bill);
    }

    @ApiOperation("月报表明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/dayList/{id}")
    public List<BicycleOrderDayReportVo> listMonthDayReport(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleOrderMonthReport monthReport = bicycleOrderMonthReportService.findById(id);
        if (monthReport == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        List<BicycleOrderDayReport> list = bicycleOrderDayReportService.listByMonthReport(monthReport);
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : dayReportConvert.toVo(list);
    }

    @ApiOperation("月报表合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/sum")
    public BicycleOrderMonthReportVo sumMonthReport(@RequestBody BicycleOrderMonthReportSearchVo searchVo) {
        BicycleOrderMonthReport bill = bicycleOrderMonthReportService.sumMonthReport(searchVo);
        return monthReportConvert.toVo(bill);
    }

    @ApiOperation("月报表列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/list")
    public PageResult<BicycleOrderMonthReportVo> listMonthReport(@RequestBody BicycleOrderMonthReportSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }

        return bicycleOrderMonthReportService.listMonthReport(searchVo);
    }
}

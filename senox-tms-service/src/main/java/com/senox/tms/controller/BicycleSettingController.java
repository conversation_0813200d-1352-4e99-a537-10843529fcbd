package com.senox.tms.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.tms.convert.BicycleSettingConvert;
import com.senox.tms.domain.BicycleSetting;
import com.senox.tms.service.BicycleSettingService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.BicycleSettingVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/25 9:50
 */
@Api(tags = "三轮车配置信息")
@RequiredArgsConstructor
@RequestMapping("/bicycle/setting")
@RestController
public class BicycleSettingController {

    private final BicycleSettingService bicycleSettingService;
    private final BicycleSettingConvert bicycleSettingConvert;

    @ApiOperation("添加三轮车配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addBicycleSetting(@Validated(Add.class) @RequestBody BicycleSettingVo settingVo) {
        BicycleSetting setting = bicycleSettingConvert.toDo(settingVo);
        ContextUtils.initEntityModifier(setting);
        ContextUtils.initEntityCreator(setting);
        return bicycleSettingService.addBicycleSetting(setting);
    }

    @ApiOperation("修改三轮车配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateBicycleSetting(@Validated(Update.class) @RequestBody BicycleSettingVo settingVo) {
        BicycleSetting setting = bicycleSettingConvert.toDo(settingVo);
        ContextUtils.initEntityModifier(setting);
        bicycleSettingService.updateBicycleSetting(setting);
    }

    @ApiOperation("根据id获取三轮车配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleSettingVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleSetting setting = bicycleSettingService.findById(id);
        return setting == null ? null : bicycleSettingConvert.toVo(setting);
    }

    @ApiOperation("根据id删除三轮车配置信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        bicycleSettingService.deleteById(id);
    }

    @ApiOperation("三轮车配置信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<BicycleSettingVo> listBicycleSetting() {
        List<BicycleSetting> list = bicycleSettingService.listBicycleSetting();
        return list == null ? Collections.emptyList() : bicycleSettingConvert.toVo(list);
    }

    @ApiOperation("获取设置是否启用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/checkEnable")
    public boolean checkEnable(@RequestParam("name") String name) {
        return bicycleSettingService.checkEnable(name);
    }
}

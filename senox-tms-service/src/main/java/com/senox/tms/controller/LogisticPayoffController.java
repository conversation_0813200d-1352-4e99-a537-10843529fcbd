package com.senox.tms.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.PageUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.convert.LogisticPayoffConvert;
import com.senox.tms.domain.LogisticPayoff;
import com.senox.tms.service.LogisticOrderService;
import com.senox.tms.service.LogisticPayoffService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticPayoffGenerateVo;
import com.senox.tms.vo.LogisticPayoffSearchVo;
import com.senox.tms.vo.LogisticPayoffVo;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/15 15:49
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/logistic/payoff")
public class LogisticPayoffController {

    private final LogisticPayoffConvert payoffConvert;
    private final LogisticPayoffService payoffService;
    private final LogisticOrderService orderService;

    @ApiOperation("添加物流客户应付账单")
    @PostMapping("/add")
    public Long addPayoff(@Validated @RequestBody LogisticPayoffVo payoff) {
        LogisticPayoff entity = payoffConvert.toDo(payoff);
        ContextUtils.initEntityCreator(entity);

        return payoffService.add(entity);
    }

    @ApiOperation("更新物流客户应付账单")
    @PostMapping("/update")
    public void updatePayoff(@Validated @RequestBody LogisticPayoffVo payoff) {
        if (!WrapperClassUtils.biggerThanLong(payoff.getId(), 0L)) {
            throw new InvalidParameterException();
        }

        LogisticPayoff entity = payoffConvert.toDo(payoff);
        ContextUtils.initEntityModifier(entity);
        payoffService.update(entity);
    }

    @ApiOperation("删除应付账单")
    @PostMapping("/delete")
    public void deletePayoff(@RequestBody List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new InvalidParameterException();
        }

        payoffService.delete(ids);
    }

    @ApiOperation("生成物流客户应付账单")
    @PostMapping("/generate")
    public void generatePayoff(@Validated @RequestBody LogisticPayoffGenerateVo generate) {
        generate.setOperatorId(ContextUtils.getUserInContext(false).getUserId());
        generate.setOperateName(ContextUtils.getUserInContext(false).getUsername());
        orderService.generatePayoff(generate);
    }

    @ApiOperation("获取物流客户应付账单")
    @GetMapping("/get/{id}")
    public LogisticPayoffVo findById(@PathVariable Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            return null;
        }
        LogisticPayoff result = payoffService.findById(id);
        return result == null ? null : payoffConvert.toVo(result);
    }

    @ApiOperation("获取物流客户应付账单合计")
    @PostMapping("/sum")
    public LogisticPayoffVo sumPayoff(@RequestBody LogisticPayoffSearchVo search) {
        LogisticPayoff result = payoffService.sumPayoff(search);
        return payoffConvert.toVo(result);
    }

    @ApiOperation("物流客户应付账单列表")
    @PostMapping("/list")
    public List<LogisticPayoffVo> listPayoff(@RequestBody LogisticPayoffSearchVo search) {
        search.setPage(false);
        List<LogisticPayoff> list = payoffService.listPayoff(search);
        return CollectionUtils.isEmpty(list) ? Collections.emptyList() : payoffConvert.toVo(list);
    }

    @ApiOperation("物流客户应付账单页")
    @PostMapping("/page")
    public PageResult<LogisticPayoffVo> listPayoffPage(@RequestBody LogisticPayoffSearchVo search) {
        if (search.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (search.getPageNo() < 1) {
            search.setPageNo(1);
        }

        PageResult<LogisticPayoff> page = PageUtils.commonPageResult(search, () -> payoffService.countPayoff(search), () -> payoffService.listPayoff(search));
        return PageResult.convertPage(page, payoffConvert::toVo);
    }
}

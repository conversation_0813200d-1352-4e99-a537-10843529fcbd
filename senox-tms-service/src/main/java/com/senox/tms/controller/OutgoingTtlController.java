package com.senox.tms.controller;

import com.senox.common.vo.PageStatisticsResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.OutgoingTtlConvert;
import com.senox.tms.domain.OutgoingTtl;
import com.senox.tms.service.OutgoingTtlService;
import com.senox.tms.vo.OutgoingTtlSearchVo;
import com.senox.tms.vo.OutgoingTtlVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8 11:54
 */
@Api(tags = "太太乐外发")
@RequiredArgsConstructor
@RequestMapping("/logistic/outgoing/ttl")
@RestController
public class OutgoingTtlController {

    private final OutgoingTtlService outgoingTtlService;
    private final OutgoingTtlConvert outgoingTtlConvert;

    @ApiOperation("批量添加太太乐外发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/batch/add")
    public void batchAddOutgoingTtl(@RequestBody List<OutgoingTtlVo> outgoingTtlVos) {
        List<OutgoingTtl> outgoingTtlList = outgoingTtlConvert.toDo(outgoingTtlVos);
        outgoingTtlService.batchAdd(outgoingTtlList);
    }

    @ApiOperation("添加太太乐外发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addOutgoingTtl(@RequestBody OutgoingTtlVo outgoingTtlVo) {
        OutgoingTtl outgoingTtl = outgoingTtlConvert.toDo(outgoingTtlVo);
        return outgoingTtlService.addOutgoingTtl(outgoingTtl);
    }

    @ApiOperation("更新太太乐外发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateOutgoingTtl(@RequestBody OutgoingTtlVo outgoingTtlVo) {
        OutgoingTtl outgoingTtl = outgoingTtlConvert.toDo(outgoingTtlVo);
        outgoingTtlService.updateOutgoingTtl(outgoingTtl);
    }

    @ApiOperation("根据Id获取太太乐外发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public OutgoingTtlVo findById(@PathVariable Long id) {
        OutgoingTtl outgoingTtl = outgoingTtlService.findById(id);
        return outgoingTtl == null ? null : outgoingTtlConvert.toVo(outgoingTtl);
    }

    @ApiOperation("根据Id删除太太乐外发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        outgoingTtlService.deleteById(id);
    }

    @ApiOperation("太太乐外发分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageStatisticsResult<OutgoingTtlVo, OutgoingTtlVo> pageResult(@RequestBody OutgoingTtlSearchVo searchVo) {
        return outgoingTtlService.pageOutgoing(searchVo);
    }
}

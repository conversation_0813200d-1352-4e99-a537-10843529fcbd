package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.constant.DictLogisticCategory;
import com.senox.tms.convert.DictLogisticConvert;
import com.senox.tms.service.DictLogisticService;
import com.senox.tms.vo.DictLogisticSearchVo;
import com.senox.tms.vo.DictLogisticVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Api(tags = "物流字典")
@RestController
@RequiredArgsConstructor
@RequestMapping("/logistic/dict")
public class DictLogisticController {
    private final DictLogisticService dictLogisticService;
    private final DictLogisticConvert dictLogisticConvert;

    @ApiOperation("添加")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/{category}/add")
    public void add(@PathVariable DictLogisticCategory category, @RequestBody DictLogisticVo dictLogisticVo) {
        dictLogisticVo.setCategory(category);
        dictLogisticService.add(dictLogisticConvert.toDo(dictLogisticVo));
    }

    @ApiOperation("更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody DictLogisticVo dictLogisticVo) {
        dictLogisticService.update(dictLogisticConvert.toDo(dictLogisticVo));
    }

    @ApiOperation("删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        dictLogisticService.deleteById(id);
    }

    @ApiOperation("分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/{category}/list/page")
    public PageResult<DictLogisticVo> listPage(@PathVariable DictLogisticCategory category, @RequestBody DictLogisticSearchVo searchVo) {
        searchVo.setCategory(category);
        return dictLogisticService.listPage(searchVo);
    }

}

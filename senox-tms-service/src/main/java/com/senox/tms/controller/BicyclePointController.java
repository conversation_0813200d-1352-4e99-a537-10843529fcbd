package com.senox.tms.controller;

import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.tms.convert.BicyclePointConvert;
import com.senox.tms.domain.BicyclePoint;
import com.senox.tms.service.BicyclePointService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.BicyclePointVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/13 14:51
 */
@Api(tags = "三轮车配送地点")
@RequiredArgsConstructor
@RequestMapping("/bicycle/point")
@RestController
public class BicyclePointController {

    private final BicyclePointService bicyclePointService;
    private final BicyclePointConvert bicyclePointConvert;

    @ApiOperation("添加三轮车配送地点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addBicyclePoint(@Validated(Add.class) @RequestBody BicyclePointVo bicyclePointVo) {
        BicyclePoint bicyclePoint = bicyclePointConvert.toDo(bicyclePointVo);
        ContextUtils.initEntityCreator(bicyclePoint);
        ContextUtils.initEntityModifier(bicyclePoint);
        return bicyclePointService.addBicyclePoint(bicyclePoint);
    }

    @ApiOperation("修改三轮车配送地点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateBicyclePoint(@Validated(Update.class) @RequestBody BicyclePointVo bicyclePointVo) {
        BicyclePoint bicyclePoint = bicyclePointConvert.toDo(bicyclePointVo);
        ContextUtils.initEntityModifier(bicyclePoint);
        bicyclePointService.updateBicyclePoint(bicyclePoint);
    }

    @ApiOperation("删除三轮车配送地点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/delete/{id}")
    public void deleteBicyclePoint(@PathVariable Long id) {
        bicyclePointService.deleteBicyclePoint(id);
    }

    @ApiOperation("根据id获取三轮车配送地点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicyclePointVo findById(@PathVariable Long id) {
        BicyclePoint bicyclePoint = bicyclePointService.findById(id);
        return bicyclePoint != null ? bicyclePointConvert.toVo(bicyclePoint) : null;
    }

    @ApiOperation("三轮车配送地点列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<BicyclePointVo> listBicyclePoint() {
        List<BicyclePoint> list = bicyclePointService.listBicyclePoint();
        return list == null ? Collections.emptyList() : bicyclePointConvert.toVo(list);
    }
}

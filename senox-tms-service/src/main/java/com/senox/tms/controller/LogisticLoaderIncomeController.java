package com.senox.tms.controller;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminContext;
import com.senox.tms.service.LogisticLoaderIncomeService;
import com.senox.tms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Api(tags = "物流搬运工收益")
@RestController
@RequiredArgsConstructor
@RequestMapping("/logistic/loader/income")
public class LogisticLoaderIncomeController {
    private final LogisticLoaderIncomeService incomeService;

    @ApiOperation("生成日报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/report/generate")
    public void generateDayReport(@RequestBody BicycleDateVo dateVo) {
        if (!WrapperClassUtils.biggerThanInt(dateVo.getYear(), 0)) {
            dateVo.setYear(LocalDate.now().getYear());
        }
        if (!WrapperClassUtils.biggerThanInt(dateVo.getMonth(), 0)) {
            dateVo.setMonth(LocalDate.now().getMonthValue());
        }
        if (!WrapperClassUtils.biggerThanInt(dateVo.getDay(), 0)) {
            dateVo.setMonth(LocalDate.now().getDayOfMonth());
        }
        incomeService.generateDayReport(dateVo);

    }

    @ApiOperation("分页报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/report/list/page")
    public BicycleTotalPageResult<LogisticLoaderIncomeVo> listPageStatistics(@RequestBody LogisticLoaderIncomeSearchVo searchVo) {
        return incomeService.listPageStatistics(searchVo);
    }

    @ApiOperation("统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/report/statistics")
    public List<LogisticLoaderIncomeReportVo> reportStatistics(@RequestBody LogisticLoaderIncomeSearchVo searchVo) {
        return incomeService.reportStatistics(searchVo);
    }

}

package com.senox.tms.controller;

import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.convert.UnloadingOrderMonthPayoffConvert;
import com.senox.tms.convert.UnloadingOrderPayoffConvert;
import com.senox.tms.service.UnloadingOrderMonthPayoffService;
import com.senox.tms.service.UnloadingOrderPayoffService;
import com.senox.tms.service.UnloadingWorkerService;
import com.senox.tms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 13:44
 */
@Api(tags = "鹏翔应付账单")
@RequiredArgsConstructor
@RequestMapping("/unloading/payoff")
@RestController
public class UnloadingOrderPayoffController {

    private final UnloadingOrderPayoffService payoffService;
    private final UnloadingOrderPayoffConvert payoffConvert;
    private final UnloadingOrderMonthPayoffService monthPayoffService;
    private final UnloadingOrderMonthPayoffConvert monthPayoffConvert;
    private final UnloadingWorkerService workerService;

    @ApiOperation("生成应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public void generateOrderPayoff(@RequestBody UnloadingMonthVo monthVo) {
        payoffService.generateOrderPayoff(monthVo);
    }

    @ApiOperation("根据id查询应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public UnloadingOrderPayoffVo findPayoffById(@PathVariable Long id) {
        return payoffConvert.toVo(payoffService.findPayoffById(id));
    }

    @ApiOperation("根据订单号删除应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/deleteByOrderNo")
    public void deletePayoffByOrderNo(@RequestParam String orderNo) {
        payoffService.deletePayoffByOrderNo(orderNo);
    }

    @ApiOperation("更新应付金额")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update/sharesAmount")
    public void updateSharesAmount(@RequestBody List<UnloadingOrderPayoffVo> payoffVoList) {
        payoffService.updateSharesAmount(payoffConvert.toDo(payoffVoList));
    }

    @ApiOperation("应付分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public PageResult<UnloadingOrderPayoffVo> pagePayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo) {
        return payoffService.pagePayoff(searchVo);
    }

    @ApiOperation("应付合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/sum")
    public UnloadingOrderPayoffVo sumPayoff(@RequestBody UnloadingOrderPayoffSearchVo searchVo) {
        return payoffConvert.toVo(payoffService.sumPayoff(searchVo));
    }

    @ApiOperation("根据订单编号查询应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findByOrderNo")
    public List<UnloadingOrderPayoffVo> listPayoffByOrderNo(@RequestParam String orderNo) {
        return payoffConvert.toVo(payoffService.listPayoffByOrderNo(orderNo));
    }

    @ApiOperation("搬运工应付日信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/daily/list")
    public List<UnloadingOrderPayoffVo> payoffDailyList(@RequestBody UnloadingOrderPayoffSearchVo searchVo) {
        return payoffService.payoffDailyList(searchVo);
    }

    @ApiOperation("根据应付月账单id查询应付记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findByMonthPayoffId/{monthPayoffId}")
    public List<UnloadingOrderPayoffVo> findByMonthPayoffId(@PathVariable Long monthPayoffId) {
        return payoffConvert.toVo(payoffService.findByMonthPayoffId(monthPayoffId));
    }

    @ApiOperation("生成月应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/generate")
    public void generateMonthPayoff(@RequestBody UnloadingMonthPayoffVo payoffVo) {
        monthPayoffService.generateMonthPayoff(payoffVo);
    }

    @ApiOperation("更新月应付账单备注")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/updateRemark")
    public void updateRemark(@RequestBody UnloadingMonthPayoffRemarkVo remarkVo) {
        monthPayoffService.updateRemark(remarkVo);
    }

    @ApiOperation("根据id查询月应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/month/get/{id}")
    public UnloadingOrderMonthPayoffVo findMonthPayoffById(@PathVariable Long id) {
        return monthPayoffConvert.toVo(monthPayoffService.findById(id));
    }

    @ApiOperation("根据id删除月应付账单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/month/delete/{id}")
    public void deleteMonthPayoff(@PathVariable Long id) {
        monthPayoffService.deleteMonthPayoff(id);
    }

    @ApiOperation("月应付账单批量支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/batch")
    public void batchMonthPayoffByIds(@RequestBody List<Long> ids) {
        monthPayoffService.batchMonthPayoffByIds(ids);
    }

    @ApiOperation("月应付账单分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/page")
    public PageResult<UnloadingOrderMonthPayoffVo> pageMonthPayoff(@RequestBody UnloadingOrderMonthPayoffSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return PageResult.convertPage(monthPayoffService.pageMonthPayoff(searchVo), monthPayoffConvert::toVo);
    }

    @ApiOperation("月应付账单合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/month/sum")
    public UnloadingOrderMonthPayoffVo sumMonthPayoff(@RequestBody UnloadingOrderMonthPayoffSearchVo searchVo) {
        return monthPayoffConvert.toVo(monthPayoffService.sumMonthPayoff(searchVo));
    }

    @ApiOperation("查询搬运工情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/worker/statistics")
    public UnloadingDayCountWorkerVo workerStatistics(@RequestBody UnloadingStatisticSearchVo searchVo) {
        UnloadingDayCountWorkerVo countWorkerVo = workerService.dayCountWorker();
        List<UnloadingDayBestWorkerVo> bestWorkerVoList = payoffService.listBestWorker(searchVo.getStartTime(), searchVo.getEndTime());
        if (countWorkerVo != null) {
            countWorkerVo.setBestWorkerVoList(bestWorkerVoList);
        }
        return countWorkerVo;
    }
}

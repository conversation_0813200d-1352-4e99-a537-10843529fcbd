package com.senox.tms.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.DecimalUtils;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.tms.convert.LogisticStatisticsDayReportConvert;
import com.senox.tms.domain.LogisticStatisticsDayReport;
import com.senox.tms.service.LogisticStatisticsDayReportService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.LogisticStatisticsDayReportBatchUpdateVo;
import com.senox.tms.vo.LogisticStatisticsDayReportPageResult;
import com.senox.tms.vo.LogisticStatisticsDayReportSearchVo;
import com.senox.tms.vo.LogisticStatisticsDayReportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/12/19 11:28
 */
@Api(tags = "货物统计")
@RequiredArgsConstructor
@RequestMapping("/logistic/statistics")
@RestController
public class LogisticStatisticsDayReportController {

    private final LogisticStatisticsDayReportService logisticStatisticsDayReportService;
    private final LogisticStatisticsDayReportConvert logisticStatisticsDayReportConvert;

    @ApiOperation("批量添加货物统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/batch/add")
    public void batchAddLogisticStatisticsDayReport(@RequestBody List<LogisticStatisticsDayReportVo> reportVoList) {
        List<LogisticStatisticsDayReport> reportList = logisticStatisticsDayReportConvert.toDo(reportVoList);
        reportList.forEach(item -> {
            //未入库重量 = 装载重量 - 入库重量
            item.setUnStockedWeight(DecimalUtils.subtract(item.getLoadingWeight()
                    , item.getStorageWeight()));
            //未收款金额 = 运费收入 - 实际运费 - 冻品优惠
            item.setUnpaidAmount(DecimalUtils.subtract(item.getFreightIncomeAmount()
                    , item.getActualFreightAmount(), item.getFrozenGoodsDiscounts()));
            ContextUtils.initEntityModifier(item);
            item.setModifiedTime(LocalDateTime.now());
        });
        logisticStatisticsDayReportService.batchAdd(reportList);
    }

    @ApiOperation("添加货物统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    public Long addLogisticStatisticsDayReport(@RequestBody LogisticStatisticsDayReportVo logisticStatisticsDayReportVo) {
        LogisticStatisticsDayReport report = logisticStatisticsDayReportConvert.toDo(logisticStatisticsDayReportVo);
        //未入库重量 = 装载重量 - 入库重量
        report.setUnStockedWeight(DecimalUtils.subtract(report.getLoadingWeight()
                , report.getStorageWeight()));
        //未收款金额 = 运费收入 - 实际运费 - 冻品优惠
        report.setUnpaidAmount(DecimalUtils.subtract(report.getFreightIncomeAmount()
                , report.getActualFreightAmount(), report.getFrozenGoodsDiscounts()));
        ContextUtils.initEntityCreator(report);
        ContextUtils.initEntityModifier(report);
        report.setCreateTime(LocalDateTime.now());
        report.setModifiedTime(LocalDateTime.now());
        return logisticStatisticsDayReportService.addLogisticStatisticsDayReport(report);
    }

    @ApiOperation("更新货物统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateLogisticStatisticsDayReport(@Validated({Update.class}) @RequestBody LogisticStatisticsDayReportVo logisticStatisticsDayReportVo) {
        LogisticStatisticsDayReport logisticStatisticsDayReport = logisticStatisticsDayReportConvert.toDo(logisticStatisticsDayReportVo);
        logisticStatisticsDayReportService.updateLogisticStatisticsDayReport(logisticStatisticsDayReport);
    }

    @ApiOperation("根据Id获取货物统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public LogisticStatisticsDayReportVo findById(@PathVariable Long id) {
        LogisticStatisticsDayReport logisticStatisticsDayReport = logisticStatisticsDayReportService.findById(id);
        return logisticStatisticsDayReport == null ? null : logisticStatisticsDayReportConvert.toVo(logisticStatisticsDayReport);
    }

    @ApiOperation("根据Id删除货物统计报表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/delete/{id}")
    public void deleteById(@PathVariable Long id) {
        logisticStatisticsDayReportService.deleteById(id);
    }

    @ApiOperation("货物统计报表分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/page")
    public LogisticStatisticsDayReportPageResult<LogisticStatisticsDayReportVo> page(@RequestBody LogisticStatisticsDayReportSearchVo searchVo) {
        return logisticStatisticsDayReportService.page(searchVo);
    }


    @ApiOperation("货物统计批量收款")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/batch/update")
    public void batchUpdate(@RequestBody LogisticStatisticsDayReportBatchUpdateVo batchUpdateVo) {
        if (batchUpdateVo.getPaymentTime() == null) {
            throw new InvalidParameterException("收款时间不能为空");
        }
        logisticStatisticsDayReportService.batchUpdate(batchUpdateVo.getIds(), batchUpdateVo.getPaymentTime());
    }
}

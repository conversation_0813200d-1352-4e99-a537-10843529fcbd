package com.senox.tms.controller;

import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.validation.groups.Update;
import com.senox.context.AdminContext;
import com.senox.tms.convert.BicycleManagerConvert;
import com.senox.tms.domain.BicycleManager;
import com.senox.tms.service.BicycleManagerService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.BicycleManagerVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/10/27 10:45
 */
@Api(tags = "三轮车管理员")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bicycle/manager")
public class BicycleManagerController {

    private final BicycleManagerConvert bicycleManagerConvert;
    private final BicycleManagerService bicycleManagerService;

    @ApiOperation("更新三轮车管理员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void updateBicycleManager(@Validated({Update.class}) @RequestBody BicycleManagerVo bicycleManagerVo) {
        BicycleManager bicycleManager = bicycleManagerConvert.toDo(bicycleManagerVo);
        ContextUtils.initEntityModifier(bicycleManager);
        bicycleManagerService.updateBicycleManager(bicycleManager);
    }

    @ApiOperation("根据管理员id查询三轮车管理员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/byAdminUserId/{adminUserId}")
    public BicycleManagerVo findByAdminUserId(@PathVariable Long adminUserId) {
        if (!WrapperClassUtils.biggerThanLong(adminUserId, 0L)) {
            throw new InvalidParameterException();
        }
        BicycleManager bicycleManager = bicycleManagerService.findByAdminUserId(adminUserId);
        return bicycleManager == null ? null : bicycleManagerConvert.toVo(bicycleManager);
    }
}

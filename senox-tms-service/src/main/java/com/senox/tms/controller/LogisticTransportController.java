package com.senox.tms.controller;

import com.senox.common.vo.BillPaidVo;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import com.senox.tms.convert.LogisticTransportOrderConvert;
import com.senox.tms.service.LogisticTransportBillService;
import com.senox.tms.service.LogisticTransportBillSettlementService;
import com.senox.tms.service.LogisticTransportBusinessService;
import com.senox.tms.service.LogisticTransportOrderService;
import com.senox.tms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-26
 **/
@Api(tags = "城际运输")
@RestController
@RequiredArgsConstructor
@RequestMapping("/logistic/transport")
public class LogisticTransportController {
    private final LogisticTransportOrderService orderService;
    private final LogisticTransportBillService billService;
    private final LogisticTransportBusinessService businessService;
    private final LogisticTransportBillSettlementService billSettlementService;
    private final LogisticTransportOrderConvert orderConvert;

    @ApiOperation("添加订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/add")
    public void addOrder(@RequestBody List<LogisticTransportOrderVo> orders) {
        orderService.addBatch(orderConvert.toDo(orders));
    }

    @ApiOperation("更新订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/update")
    public void updateOrder(@RequestBody List<LogisticTransportOrderVo> orders) {
        businessService.updateOrder(orderConvert.toDo(orders), true);
    }

    @ApiOperation("订单审核")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/audit")
    public void auditOrder(@RequestBody LogisticTransportOrderAuditVo orderAudit) {
        businessService.auditOrder(orderAudit);
        //如果是到付，则生成账单结算单
        LogisticTransportBillGenerateVo billGenerate = new LogisticTransportBillGenerateVo();
        billGenerate.setOrderSerialNos(orderAudit.getOrderSerialNos());
        billGenerate.setPayer(LogisticTransportOrderPayer.CONSIGNEE);
        businessService.generateBill(billGenerate);
    }

    @ApiOperation("根据id查找订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/order/findById/{orderId}")
    public LogisticTransportOrderVo findOrderById(@PathVariable Long orderId) {
        return orderService.findById(orderId);
    }

    @ApiOperation("根据编号查找订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/order/findBySerialNo/{orderSerialNo}")
    public LogisticTransportOrderVo findOrderBySerialNo(@PathVariable String orderSerialNo) {
        return orderService.findBySerialNo(orderSerialNo);
    }

    @ApiOperation("根据订单编号删除订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/order/deleteBySerialNo/{orderSerialNo}")
    public void deleteOrderBySerialNo(@PathVariable String orderSerialNo) {
        businessService.deleteOrderBySerialNo(orderSerialNo);
    }

    @ApiOperation("订单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/list")
    public List<LogisticTransportOrderVo> orderList(@RequestBody LogisticTransportOrderSearchVo search) {
        return orderService.list(search);
    }

    @ApiOperation("订单分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/list/page")
    public PageResult<LogisticTransportOrderVo> orderPageList(@RequestBody LogisticTransportOrderSearchVo search) {
        return orderService.pageList(search);
    }

    @ApiOperation("订单统计")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/statistics")
    public LogisticTransportOrderStatisticsVo orderStatistics(@RequestBody LogisticTransportOrderSearchVo search) {
        return orderService.orderStatistics(search);
    }

    @ApiOperation("订单数量")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/order/count")
    public Integer orderCount(@RequestBody LogisticTransportOrderSearchVo search) {
        return orderService.countList(search);
    }

    @ApiOperation("账单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/list")
    public List<LogisticTransportBillVo> billList(@RequestBody LogisticTransportBillSearchVo search) {
        search.setPage(false);
        return billService.list(search);
    }

    @ApiOperation("根据账单id获取账单列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/list/id")
    public List<LogisticTransportBillVo> billListById(@RequestBody List<Long> billIds) {
        return billService.billListById(billIds);
    }

    @ApiOperation("根据订单编号查询账单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/bill/findByOrderSerialNo/{orderSerialNo}")
    public LogisticTransportBillVo findByOrderSerialNo(@PathVariable String orderSerialNo) {
        return billService.findByOrderSerialNo(orderSerialNo);
    }

    @ApiOperation("根据账单id删除账单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/bill/deleteById/{billId}")
    public void billDeleteById(@PathVariable Long billId) {
        billService.deleteBatch(Collections.singletonList(billId));
    }

    @ApiOperation("账单分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/list/page")
    public PageResult<LogisticTransportBillVo> billPageList(@RequestBody LogisticTransportBillSearchVo search) {
        return billService.pageList(search);
    }

    @ApiOperation("生成账单结算单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/generate")
    public List<Long> generateBillSettlement(@RequestBody LogisticTransportBillGenerateVo billGenerate) {
        return businessService.generateBill(billGenerate);
    }

    @ApiOperation("账单结算列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/list")
    public List<LogisticTransportBillSettlementVo> billSettlementList(@RequestBody LogisticTransportBillSettlementSearchVo search) {
        return billSettlementService.list(search);
    }

    @ApiOperation("账单结算分页列表")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/list/page")
    public PageResult<LogisticTransportBillSettlementVo> billSettlementPageList(@RequestBody LogisticTransportBillSettlementSearchVo search) {
        return billSettlementService.pageList(search);
    }

    @ApiOperation("账单结算统计")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/count")
    public Integer billSettlementCount(@RequestBody LogisticTransportBillSettlementSearchVo search) {
        return billSettlementService.countList(search);
    }

    @ApiOperation("结算单统计")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/statistics")
    public LogisticTransportBillSettlementStatisticsVo statisticsSettlement(@RequestBody LogisticTransportBillSettlementSearchVo search) {
        return billSettlementService.statistics(search);
    }

    @ApiOperation("结算单支付结果")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/paid/update")
    public void billSettlementStatus(@Validated @RequestBody BillPaidVo billPaid) {
        businessService.billSettlementPaidStatus(billPaid);
    }

    @ApiOperation("结算单更新支付订单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/paid/order/update")
    public void billSettlementUpdatePaidOrder(@RequestBody BillPaidVo billPaid) {
        businessService.billSettlementUpdatePaidOrder(billPaid);
    }

    @ApiOperation("结算单下发")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlement/send")
    public void billSettlementSend(@RequestBody LogisticTransportBillSettlementSendVo send) {
        billSettlementService.send(send);
    }

    @ApiOperation("结算单删除")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @PostMapping("/bill/settlementDeleteById")
    public void billSettlementDeleteById(@RequestBody List<Long> ids) {
        billSettlementService.deleteByIds(ids);
    }

    @ApiOperation("结算单详情")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/bill/settlement/detail/{settlementId}")
    public LogisticTransportBillSettlementDetailVo billSettlementDetail(@PathVariable Long settlementId) {
        return billSettlementService.billSettlementDetail(settlementId);
    }

    @ApiOperation("根据id查找结算单")
    @ApiImplicitParams({@ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)})
    @GetMapping("/bill/settlementFindById/{settlementId}")
    public LogisticTransportBillSettlementVo billSettlementFindById(@PathVariable Long settlementId) {
        return billSettlementService.billSettlementFindById(settlementId);
    }
}
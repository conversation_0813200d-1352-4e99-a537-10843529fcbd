package com.senox.tms.controller;

import com.senox.common.annotation.Debounce;
import com.senox.common.exception.BusinessException;
import com.senox.common.validation.groups.Add;
import com.senox.common.validation.groups.Update;
import com.senox.common.vo.PageResult;
import com.senox.context.AdminContext;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.convert.BicycleDeliveryOrderConvert;
import com.senox.tms.convert.BicycleDeliveryOrderJobConvert;
import com.senox.tms.convert.BicycleOrderGoodsDetailConvert;
import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.domain.BicycleDeliveryOrderJob;
import com.senox.tms.domain.BicycleOrderGoodsDetail;
import com.senox.tms.dto.BicycleDeliveryOrderBuild;
import com.senox.tms.service.*;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/20 8:53
 */
@Api(tags = "三轮车配送单")
@RequiredArgsConstructor
@RequestMapping("/bicycle/delivery/order")
@RestController
public class BicycleDeliveryOrderController {

    private final BicycleDeliveryOrderService bicycleDeliveryOrderService;
    private final BicycleDeliveryOrderDetailService bicycleDeliveryOrderDetailService;
    private final BicycleDeliveryOrderConvert bicycleDeliveryOrderConvert;
    private final BicycleDeliveryOrderJobService bicycleDeliveryOrderJobService;
    private final BicycleDeliveryOrderJobConvert bicycleDeliveryOrderJobConvert;
    private final BicycleRiderService bicycleRiderService;
    private final BicycleOrderGoodsDetailConvert goodsDetailConvert;

    @ApiOperation("添加三轮车配送单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/add")
    @Debounce(expire = 2)
    public Long addBicycleDeliveryOrder(@Validated(Add.class) @RequestBody BicycleDeliveryOrderVo orderVo) {
        BicycleDeliveryOrder order = bicycleDeliveryOrderConvert.toDo(orderVo);

        ContextUtils.initEntityCreator(order);
        ContextUtils.initEntityModifier(order);
        Map<String, List<List<BicycleOrderGoodsDetail>>> goodsDetailMap = bicycleDeliveryOrderService.buildOrderGoodsDetail(orderVo.getDetailVos());
        BicycleDeliveryOrderBuild buildBuilder = BicycleDeliveryOrderBuild.builder().goodsDetailMap(goodsDetailMap).referralDelivery(false).build();
        return bicycleDeliveryOrderService.addBicycleDeliveryOrder(order, orderVo.getDetailVos(), buildBuilder);
    }

    @ApiOperation("更新货物明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update/goodsDetail")
    public void updateBicycleOrderDetail(@RequestBody List<BicycleOrderGoodsDetailVo> goodsDetailVos) {
        List<BicycleOrderGoodsDetail> goodsDetails = goodsDetailConvert.toDo(goodsDetailVos);
        Map<Long, List<BicycleOrderGoodsDetail>> goodsDetailsMap = goodsDetails.stream().collect(Collectors.groupingBy(BicycleOrderGoodsDetail::getOrderId));
        bicycleDeliveryOrderService.updateBicycleOrderDetail(goodsDetailsMap);
    }

    @ApiOperation("更新部分货物明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update/part/goodsDetail")
    public void updatePartBicycleOrderDetail(@RequestBody List<BicycleOrderGoodsDetailVo> goodsDetailVos) {
        List<BicycleOrderGoodsDetail> goodsDetails = goodsDetailConvert.toDo(goodsDetailVos);
        String deliveryOrderSerialNoItem = goodsDetailVos.stream().map(BicycleOrderGoodsDetailVo::getDeliveryOrderSerialNoItem).distinct().findFirst().orElse(null);
        bicycleDeliveryOrderService.updatePartBicycleOrderDetail(goodsDetails, deliveryOrderSerialNoItem);
    }

    @ApiOperation("添加费用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/addCharge")
    public void addBicycleOrderCharge(@Validated(Add.class) @RequestBody BicycleOrderChargeVo chargeVo) {
        bicycleDeliveryOrderService.addBicycleOrderCharge(chargeVo);
    }

    @ApiOperation("根据id获取配送单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/get/{id}")
    public BicycleDeliveryOrderVo findDeliveryOrderById(@PathVariable Long id) {
        return bicycleDeliveryOrderService.findDeliveryOrderById(id);
    }

    @ApiOperation("根据id获取配送详细单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/detail/get/{id}")
    public BicycleDeliveryOrderDetailVo findDeliveryOrderDetailById(@PathVariable Long id) {
        return bicycleDeliveryOrderDetailService.findDeliveryOrderDetailById(id);
    }

    @ApiOperation("根据订单流水号获取配送详细单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/detail/getByOrderSerialNo/{orderSerialNo}")
    public List<BicycleDeliveryOrderDetailVo> findDeliveryOrderDetailByOrderSerialNo(@PathVariable String orderSerialNo) {
        return bicycleDeliveryOrderDetailService.findDeliveryOrderDetailByOrderSerialNo(orderSerialNo);
    }

    @ApiOperation("指定骑手配送三轮车配送单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/appoint")
    public void appointBicycleDeliveryOrderReider(@Validated(Update.class) @RequestBody BicycleDeliveryOrderVo orderVo) {
        //bicycleDeliveryOrderService.appointBicycleDeliveryOrderReider(orderVo.getId(), orderVo.getRiderId(), orderVo.getChargesId());
    }

    @ApiOperation("取消骑手配送")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/cancel/{id}")
    public void cancelRiderDeliveryOrderById(@PathVariable Long id) {
        BicycleDeliveryOrder order = new BicycleDeliveryOrder();
        order.setId(id);
        order.setStatus(BicycleDeliveryOrderStatus.UN_ASSIGN.getNumber());
        bicycleDeliveryOrderService.cancelRiderDeliveryOrderById(order);
    }

    @ApiOperation("添加三轮车配送单任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @Debounce(expire = 2)
    @PostMapping("/job/add")
    public Long addBicycleDeliveryOrderJob(@Validated(Add.class) @RequestBody BicycleDeliveryOrderJobVo orderJobVo) {
        BicycleDeliveryOrderJob orderJob = bicycleDeliveryOrderJobConvert.toDo(orderJobVo);
        return bicycleDeliveryOrderJobService.addBicycleDeliveryOrderJob(orderJob, orderJobVo.getMediaUrlList(), orderJobVo.getRating(), orderJobVo.getPoint());
    }

    @ApiOperation("批量添加三轮车配送单任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @Debounce(expire = 2)
    @PostMapping("/job/batchAdd")
    public void batchAddBicycleDeliveryOrderJob(@Validated(Add.class) @RequestBody List<BicycleDeliveryOrderJobVo> orderJobVoList) {
        orderJobVoList.forEach(orderJobVo -> {
            BicycleDeliveryOrderJob orderJob = bicycleDeliveryOrderJobConvert.toDo(orderJobVo);
            bicycleDeliveryOrderJobService.addBicycleDeliveryOrderJob(orderJob, orderJobVo.getMediaUrlList(), orderJobVo.getRating(), orderJobVo.getPoint());
        });
    }

    @ApiOperation("查询骑手当日当月统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/count/{riderId}")
    public BicycleRiderCountVo countByRiderId(@PathVariable Long riderId) {
        return bicycleDeliveryOrderDetailService.riderCountByRiderId(riderId, null , null);
    }

    @ApiOperation("查询骑手情况")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/rider/statistics")
    public BicycleDayCountRiderVo riderStatistics(@RequestBody BicycleStatisticsSearchVo searchVo) {
        BicycleDayCountRiderVo countRiderVo = bicycleRiderService.dayCountRider();
        List<BicycleDayBestRiderVo> bestRiderVoList = bicycleDeliveryOrderDetailService.listDayBestRider(searchVo.getStartTime(), searchVo.getEndTime());
        if (countRiderVo != null) {
            countRiderVo.setBestRiderVoList(bestRiderVoList);
        }
        return countRiderVo;
    }

    @ApiOperation("订单统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/statistics")
    public List<BicycleOrderCountVo> statistics(@RequestBody BicycleStatisticsSearchVo searchVo) {
        return bicycleDeliveryOrderDetailService.listOrderCount(searchVo);
    }

    @ApiOperation("合并配送单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/merged")
    public void merged(@RequestParam("orderSerialNoList") List<String> orderSerialNoList, @RequestParam(required = false) String deliveryOrderSerialNo) {
        if (CollectionUtils.isEmpty(orderSerialNoList)) {
            throw new BusinessException("订单流水号集合不能为空！");
        }
        bicycleDeliveryOrderService.merged(orderSerialNoList, deliveryOrderSerialNo);
    }

    @ApiOperation("取消合并")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/cancel/merged")
    public void cancelMerged(@RequestParam("orderSerialNoList") List<String> orderSerialNoList) {
        if (CollectionUtils.isEmpty(orderSerialNoList)) {
            throw new BusinessException("订单流水号集合不能为空！");
        }
        bicycleDeliveryOrderService.cancelMerged(orderSerialNoList);
    }

    @ApiOperation("查询骑手未处理完的配送单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/undoCount/{riderId}")
    public Integer undoDeliveryOrderCount(@PathVariable Long riderId) {
        return bicycleDeliveryOrderDetailService.undoDeliveryOrderCount(riderId);
    }

    @ApiOperation("查询骑手当日当月统计列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/rider/count/list")
    public PageResult<BicycleRiderCountVo> riderCountList(@RequestBody BicycleRiderCountSearchVo searchVo) {
        return bicycleDeliveryOrderDetailService.riderCountList(searchVo);
    }

    @ApiOperation("骑手当日统计合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/rider/count/sum")
    public BicycleRiderCountVo sumRiderCount(@RequestBody BicycleRiderCountSearchVo searchVo) {
        return bicycleDeliveryOrderDetailService.sumRiderCount(searchVo);
    }

    @ApiOperation("骑手配送历史统计列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/rider/history/count/list")
    public PageResult<BicycleRiderCountVo> riderHistoryCountList(@RequestBody BicycleRiderCountSearchVo searchVo) {
        return bicycleDeliveryOrderDetailService.riderHistoryCountList(searchVo);
    }

    @ApiOperation("骑手历史配送统计合计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/rider/history/count/sum")
    public BicycleRiderCountVo sumRiderHistoryCount(@RequestBody BicycleRiderCountSearchVo searchVo) {
        return bicycleDeliveryOrderDetailService.sumRiderHistoryCount(searchVo);
    }

    @ApiOperation("配送订单详细列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/detail/list")
    public PageResult<BicycleDeliveryOrderDetailVo> listDeliveryDetail(@RequestBody BicycleDeliverySearchVo searchVo) {
        return bicycleDeliveryOrderDetailService.listDeliveryDetail(searchVo);
    }


    @ApiOperation("配送单提货信息分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/info/page")
    public PageResult<BicycleDeliveryInfoVo> deliveryInfoPage(@RequestBody BicycleDeliveryInfoSearchVo searchVo) {
        if (searchVo.getPageSize() < 1) {
            return PageResult.emptyPage();
        }
        if (searchVo.getPageNo() < 1) {
            searchVo.setPageNo(1);
        }
        return bicycleDeliveryOrderService.deliveryInfoPage(searchVo);
    }

    @ApiOperation("根据订单编号查询配送订单详细信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/deliveryDetailInfo")
    public List<BicycleDeliveryDetailInfoVo> deliveryDetailInfoByOrderSerialNo(@RequestParam String orderSerialNo) {
        return bicycleDeliveryOrderDetailService.deliveryDetailInfoByOrderSerialNo(orderSerialNo);
    }
}

package com.senox.tms.controller;

import com.senox.common.vo.BillPaidVo;
import com.senox.context.AdminContext;
import com.senox.tms.service.BicycleBillSettlementService;
import com.senox.tms.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-9
 */
@Api(tags = "三轮车配送应收账单")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bicycle/bill/settlement")
public class BicycleBillSettlementController {
    private final BicycleBillSettlementService billSettlementService;

    @ApiOperation("根据id查询结算单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/findById/{id}")
    public BicycleBillSettlementVo findById(@PathVariable Long id) {
        return billSettlementService.findById(id);
    }

    @ApiOperation("账单支付")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/pay")
    public OrderResultVo payBill(@Validated @RequestBody BicycleBillOrderVo billOrderVo) {
        return billSettlementService.payBillOrder(billOrderVo);
    }

    @ApiOperation("账单状态更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/paid/update")
    public void updateStatus(@RequestBody BillPaidVo billPaidVo) {
        billSettlementService.updateStatus(billPaidVo);
    }

    @ApiOperation("结算单列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list")
    public List<BicycleBillSettlementVo> list(@RequestBody BicycleBillSettlementSearchVo searchVo) {
        return billSettlementService.list(searchVo);
    }

    @ApiOperation("结算单列表数统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/count")
    public Integer listCount(@RequestBody BicycleBillSettlementSearchVo searchVo) {
        return billSettlementService.countList(searchVo);
    }

    @ApiOperation("结算单分页列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/list/page")
    public BicycleBillSettlementPageResult<BicycleBillSettlementVo> listPage(@RequestBody BicycleBillSettlementSearchVo searchVo) {
        return billSettlementService.listPage(searchVo);
    }

    @ApiOperation("结算单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/detail/{settlementId}")
    public BicycleBillSettlementDetailVo detailBySettlement(@PathVariable Long settlementId) {
        return billSettlementService.detailBySettlement(settlementId);
    }

    @ApiOperation("结算单下发")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/send")
    public void send(@RequestBody BicycleBillSettlementSendVo sendVo){
        billSettlementService.send(sendVo);
    }

    @ApiOperation("结算单下发通知")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/send/notify")
    public void notifySend(@RequestBody BicycleBillSettlementSendVo sendVo){
        billSettlementService.notifySend(sendVo);
    }

    @ApiOperation("结算单生成")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/generate")
    public void generate(@RequestBody BicycleBillSettlementSendVo sendVo) {
        billSettlementService.generate(sendVo);
    }

}


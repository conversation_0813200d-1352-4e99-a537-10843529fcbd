package com.senox.tms.controller;

import com.senox.context.AdminContext;
import com.senox.tms.convert.BicyclePayoffChargesConvert;
import com.senox.tms.domain.BicyclePayoffCharges;
import com.senox.tms.service.BicyclePayoffChargesService;
import com.senox.tms.vo.BicyclePayoffChargesVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-4-1
 */
@Api(tags = "三轮车应付费用")
@RestController
@RequiredArgsConstructor
@RequestMapping("/bicycle/payoff/charges")
public class BicyclePayoffChargesController {
    private final BicyclePayoffChargesService chargesService;
    private final BicyclePayoffChargesConvert chargesConvert;

    @ApiOperation("更新")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @PostMapping("/update")
    public void update(@RequestBody BicyclePayoffChargesVo payoffCharges) {
        chargesService.update(chargesConvert.toDo(payoffCharges));
    }

    @ApiOperation("查找")
    @ApiImplicitParams({
            @ApiImplicitParam(name = AdminContext.HEADER_AUTHORIZATION, value = "签名", paramType = "header", dataTypeClass = String.class)
    })
    @GetMapping("/find")
    public BicyclePayoffCharges find() {
        return chargesService.findOne();
    }
}

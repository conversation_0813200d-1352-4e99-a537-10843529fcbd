package com.senox.tms.dto;

import com.senox.tms.domain.BicycleOrderGoodsDetail;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/23 14:53
 */
@Getter
@Setter
@Builder
public class BicycleDeliveryOrderBuild {

    /**
     * 货物
     */
    private Map<String, List<List<BicycleOrderGoodsDetail>>> goodsDetailMap;


    /**
     * 是否自动接单
     */
    private boolean referralDelivery;

}

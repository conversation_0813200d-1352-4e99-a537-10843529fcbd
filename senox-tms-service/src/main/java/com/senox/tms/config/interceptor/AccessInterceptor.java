package com.senox.tms.config.interceptor;

import com.senox.common.exception.UnAuthorizedException;
import com.senox.context.AdminContext;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-9-12
 */
public class AccessInterceptor implements HandlerInterceptor {

    /**
     * 无需拦截的url
     */
    private final List<String> excludeUrls;


    public AccessInterceptor(List<String> excludeUrls) {
        this.excludeUrls = excludeUrls;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!isUrlExcluded(request.getRequestURI()) && !AdminContext.isUserValid()) {
            throw new UnAuthorizedException();
        }

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
                                Exception ex) {
        AdminContext.clear();
    }

    private boolean isUrlExcluded(String url) {
        for (String excludeUrl : excludeUrls) {
            if (url.contains(excludeUrl)) {
                return true;
            }
        }
        return false;
    }
}

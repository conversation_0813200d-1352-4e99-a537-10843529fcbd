package com.senox.tms.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/9/14 8:17
 */
@RefreshScope
@Configuration
@Data
public class AppConfig {

    /**
     * 盐长度
     */
    @Value("${senox.salt.length:6}")
    private Integer saltLength;
    /**
     * 加密迭代次数
     */
    @Value("${senox.hash.iterations:2}")
    private Integer hashIterations;

    /**
     * 骑手编号前缀
     */
    @Value("${senox.rider.prefix:BDR}")
    private String riderNoPrefix;

    /**
     * 骑手编号拼接长度
     */
    @Value("${senox.rider.length:8}")
    private Integer riderNoPostfixLength;

    /**
     * 订单流水号前缀
     */
    @Value("${senox.order.prefix:BO}")
    private String orderSerialNoPrefix;

    /**
     * 订单流水号拼接长度
     */
    @Value("${senox.order.length:5}")
    private Integer orderNoPostfixLength;

    /**
     * 配送单流水号前缀
     */
    @Value("${senox.delivery.order.prefix:BDO}")
    private String deliveryOrderSerialNoPrefix;

    /**
     * 配送单流水号拼接长度
     */
    @Value("${senox.delivery.order.length:5}")
    private Integer deliveryOrderNoPostfixLength;

    /**
     * 订单编号前缀
     */
    @Value("${senox.unloadingOrderNo.prefix:PX}")
    private String unloadingOrderNoPrefix;

    /**
     * 订单编号拼接长度
     */
    @Value("${senox.unloadingOrderNo.length:5}")
    private Integer unloadingOrderNoPostfixLength;

    /**
     * 搬运工标识前缀
     */
    @Value("${senox.unloadingWorkerSign.prefix:UW}")
    private String unloadingWorkerSignPrefix;

    /**
     * 搬运工标识拼接长度
     */
    @Value("${senox.unloadingWorkerSign.length:5}")
    private Integer unloadingWorkerSignPostfixLength;

    @Value("${senox.delivery.rider.fill-char:0}")
    private Character fillChar;

    /**
     * 城际运输订单长度
     */
    @Value("${senox.logistic.transport.order.length:4}")
    private Integer logisticTransportOrderLength;

}

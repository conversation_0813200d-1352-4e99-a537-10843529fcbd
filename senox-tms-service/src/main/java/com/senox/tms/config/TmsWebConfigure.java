package com.senox.tms.config;

import com.senox.tms.config.interceptor.AccessInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-9-12
 */
@Configuration
public class TmsWebConfigure implements WebMvcConfigurer {

    @Value("#{'${senox.adminFilter.excludeUrls:}'.split(',')}")
    private List<String> excludeUrls;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AccessInterceptor(excludeUrls))
                .addPathPatterns("/**");
    }
}

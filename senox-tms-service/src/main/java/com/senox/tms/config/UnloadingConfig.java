package com.senox.tms.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/10 9:39
 */
@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "senox.unloading")
public class UnloadingConfig {

    private Map<String, Integer> noListingTime;
}

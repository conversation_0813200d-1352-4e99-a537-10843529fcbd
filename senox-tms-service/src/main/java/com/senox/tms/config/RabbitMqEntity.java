package com.senox.tms.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * rabbitmq 配置
 * @date 2023/10/8 16:39
 */
@Get<PERSON>
@Setter
@ToString
public class RabbitMqEntity {

    private String addresses;
    private String username;
    private String password;
    private String virtualHost;
    private int connectionTimeout;
    private int consumerThreads;
}

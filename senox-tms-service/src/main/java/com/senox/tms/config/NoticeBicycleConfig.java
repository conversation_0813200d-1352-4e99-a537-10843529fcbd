package com.senox.tms.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/8/10 8:51
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "senox.notice.bicycle")
public class NoticeBicycleConfig {

    /**
     * 未拣货时间 单位：秒
     */
    private int unpickedSecond;

}

package com.senox.tms.config;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.RequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023-9-12
 */
@Slf4j
@Aspect
@Component
public class AppLogAspect {

    private static final ThreadLocal<Long> execStartTime = new ThreadLocal<>();

    /**
     * 以 controller 包下定义的所有请求为切入点
     */
    @Pointcut("execution(public * com.senox.tms.controller..*.*(..))")
    public void logPoint() {
    }

    @Before("logPoint()")
    public void doBefore(JoinPoint joinPoint) {
        execStartTime.set(System.currentTimeMillis());
        try {
            HttpServletRequest request = RequestUtils.getRequest();
            Object[] arguments = {};
            if (joinPoint.getArgs() != null) {
                arguments = Stream.of(joinPoint.getArgs()).filter(x -> !isServletRequestOrResponseArgument(x)).toArray();
            }
            log.info("Get Request ip:{}, URL:{}, arguments:{}", RequestUtils.getIpAddr(request), request.getRequestURI(),
                    JsonUtils.object2Json(arguments));
        } catch (Exception e) {
            log.error("打印请求日志出错", e);
        }
    }

    @After("logPoint()")
    public void doAfterReturning() {
        log.info("Request cost time: {}", System.currentTimeMillis() - execStartTime.get());
        execStartTime.remove();
    }

    private boolean isServletRequestOrResponseArgument(Object o) {
        return o instanceof ServletRequest || o instanceof ServletResponse;
    }

}

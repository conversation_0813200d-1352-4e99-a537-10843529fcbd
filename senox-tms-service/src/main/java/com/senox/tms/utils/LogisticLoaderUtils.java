package com.senox.tms.utils;

import com.senox.common.utils.NumberUtils;
import com.senox.tms.domain.LogisticLoaderIncome;
import com.senox.tms.domain.LogisticLoaderSettlement;
import com.senox.tms.vo.DictLogisticVo;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-11-30
 */
public class LogisticLoaderUtils {


    /**
     * 构建搬运工收益
     *
     * @param settlement 结算
     * @param loaders    搬运工列表
     * @return 返回收益列表
     */
    public static List<LogisticLoaderIncome> builderLoaderIncome(LogisticLoaderSettlement settlement, List<DictLogisticVo> loaders) {
        if (CollectionUtils.isEmpty(loaders)) {
            return Collections.emptyList();
        }
        BigDecimal totalAmount = settlement.getTotalAmount();
        Deque<BigDecimal> incomeLists = incomeAllocation(totalAmount, loaders.size());
        return loaders.stream().map(l -> {
            LogisticLoaderIncome income = new LogisticLoaderIncome();
            income.setDate(settlement.getDate());
            income.setLoaderNumber(l.getKey());
            income.setLoaderName(l.getName());
            income.setSettlementId(settlement.getId());
            income.setAmount(incomeLists.pop());
            return income;
        }).collect(Collectors.toList());
    }

    /**
     * 分配收益
     *
     * @param totalAmount 总金额
     * @param size        分配数
     * @return 返回收益栈
     */
    public static Deque<BigDecimal> incomeAllocation(BigDecimal totalAmount, int size) {
        BigDecimal avgAmount = totalAmount.divide(BigDecimal.valueOf(size), 2, RoundingMode.HALF_EVEN);
        List<BigDecimal> amountArray = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            amountArray.add(avgAmount);
        }
        BigDecimal remainder = totalAmount.subtract(amountArray.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
        int luckyIndex = NumberUtils.randInt(0, size);
        amountArray.set(luckyIndex, amountArray.get(luckyIndex).add(remainder));
        return new ArrayDeque<>(amountArray);
    }
}

package com.senox.tms.utils;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/19 16:09
 */
@Data
public class CheckResult {

    /**
     * 前面是否有忙碌的搬运工
     */
    private boolean hasBusy;

    /**
     * 前面是否有请假的搬运工
     */
    private boolean hasOnLeave;

    /**
     * 前面是否同时存在忙碌和请假的搬运工
     */
    private boolean hasBoth;

    public CheckResult(boolean hasBusy, boolean hasOnLeave, boolean hasBoth) {
        this.hasBusy = hasBusy;
        this.hasOnLeave = hasOnLeave;
        this.hasBoth = hasBoth;
    }
}

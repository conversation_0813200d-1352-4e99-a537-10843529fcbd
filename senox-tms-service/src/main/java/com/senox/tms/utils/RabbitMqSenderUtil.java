package com.senox.tms.utils;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.domain.BicycleManager;
import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.domain.BicycleRider;
import com.senox.tms.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/19 15:52
 */
@Slf4j
public class RabbitMqSenderUtil {

    private RabbitMqSenderUtil(){
    }

    /**
     * 发送骑手未揽货延时消息
     * @param duration
     * @param messageVo
     * @param rabbitTemplate
     */
    public static void sendUnpickedMessage(int duration, BicycleRiderUnpickedMessageVo messageVo, RabbitTemplate rabbitTemplate) {
        rabbitTemplate.convertAndSend(TmsConst.MQ.EX_TMS_DELAYED,TmsConst.MQ.KEY_TMS_UNPICKED_GOODS,
                messageVo,
                message -> {
                    message.getMessageProperties().setDelay(duration * 1000);
                    return message;
                }
        );
    }

    /**
     * 发送分配骑手消息
     * @param deliveryOrder
     * @param rider
     * @param rabbitTemplate
     */
    public static void sendDeliveryRiderMessage(BicycleDeliveryOrder deliveryOrder, BicycleRider rider, RabbitTemplate rabbitTemplate) {
        //发送分配骑手信息的模板通知
        BicycleRiderMessageVo messageVo = new BicycleRiderMessageVo();
        messageVo.setRiderId(rider.getId());
        messageVo.setDeliveryOrderSerialNo(deliveryOrder.getDeliveryOrderSerialNo());
        messageVo.setModifiedTime(deliveryOrder.getModifiedTime());
        messageVo.setRiderName(rider.getName());
        rabbitTemplate.convertAndSend(TmsConst.MQ.MQ_TMS_AC_RIDER, messageVo);
        log.info("发送分配骑手信息到消息队列 {}", JsonUtils.object2Json(messageVo));
    }

    /**
     * 发送取消骑手配送单消息
     * @param deliveryOrderSerialNo
     * @param riderId
     * @param rabbitTemplate
     */
    public static void sendCancelDeliveryOrderMessage(String deliveryOrderSerialNo, Long riderId, RabbitTemplate rabbitTemplate) {
        BicycleDeliveryOrderCancelMessageVo messageVo = new BicycleDeliveryOrderCancelMessageVo();
        messageVo.setRiderId(riderId);
        messageVo.setDeliveryOrderSerialNo(deliveryOrderSerialNo);
        messageVo.setCancelTime(LocalDateTime.now());
        rabbitTemplate.convertAndSend(TmsConst.MQ.MQ_TMS_AC_CANCEL_RIDER, messageVo);
        log.info("发送取消骑手配送单消息队列 {}", JsonUtils.object2Json(messageVo));
    }

    /**
     * 发送未分配的订单消息
     * @param manager
     * @param unAssignCount
     * @param rabbitTemplate
     */
    public static void sendUnAssignMessage(BicycleManager manager, Integer unAssignCount, RabbitTemplate rabbitTemplate) {
        BicycleManagerUnAssignMessageVo messageVo = new BicycleManagerUnAssignMessageVo();
        messageVo.setNoticeTime(LocalDateTime.now());
        messageVo.setAdminUserId(manager.getAdminUserId());
        messageVo.setUnAssignCount(unAssignCount);
        rabbitTemplate.convertAndSend(TmsConst.MQ.MQ_TMS_AC_UN_ASSIGN,messageVo);
        log.info("发送未分配的订单至消息队列 {}", JsonUtils.object2Json(messageVo));
    }

    /**
     * 三轮车订单取消
     * @param order
     * @param cancelReason
     * @param rabbitTemplate
     */
    public static void sendCancelOrderMessage(BicycleOrder order, String cancelReason, RabbitTemplate rabbitTemplate) {
        if (StringUtils.isBlank(order.getCreateOpenid())) {
            return;
        }
        BicycleOrderCancelMessageVo messageVo = new BicycleOrderCancelMessageVo();
        messageVo.setOpenid(order.getCreateOpenid());
        messageVo.setOrderSerialNo(order.getOrderSerialNo());
        messageVo.setCancelReason(cancelReason);
        rabbitTemplate.convertAndSend(TmsConst.MQ.MQ_TMS_AC_CANCEL_ORDER, messageVo);
        log.info("发送三轮车订单取消至消息队列 {}", JsonUtils.object2Json(messageVo));
    }
}

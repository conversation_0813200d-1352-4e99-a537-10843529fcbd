package com.senox.tms.utils;

import com.senox.common.domain.OperateEntity;
import com.senox.common.exception.UnAuthorizedException;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;

/**
 * <AUTHOR>
 * @date 2023-9-12
 */
public class ContextUtils {

    private ContextUtils() {
    }

    /**
     * 初始化实体创建人信息
     */
    public static void initEntityCreator(OperateEntity entity) {
        if (!WrapperClassUtils.biggerThanLong(entity.getCreatorId(), 0)) {
            AdminUserDto adminUser = getUserInContext();
            entity.setCreatorId(adminUser.getUserId());
            entity.setCreatorName(adminUser.getUsername());
        }
    }

    /**
     * 初始化实体修改人信息
     */
    public static void initEntityModifier(OperateEntity entity) {
        if (!WrapperClassUtils.biggerThanLong(entity.getModifierId(), 0)) {
            AdminUserDto adminUser = getUserInContext();
            entity.setModifierId(adminUser.getUserId());
            entity.setModifierName(adminUser.getUsername());
        }
    }

    /**
     * 获取上下文用户信息
     * @return
     */
    public static AdminUserDto getUserInContext() {
        return getUserInContext(AdminContext.isNeedMock());
    }

    /**
     * 获取上下文用户信息
     */
    public static AdminUserDto getUserInContext(boolean needMock) {
        if (!AdminContext.isUserValid()) {
            //xxl环境
            if (needMock) {
                AdminUserDto adminUser = new AdminUserDto();
                adminUser.setUserId(1L);
                adminUser.setUsername("admin");
                AdminContext.setUser(adminUser);
            } else {
                throw new UnAuthorizedException();
            }
        }
        return AdminContext.getUser();
    }
}

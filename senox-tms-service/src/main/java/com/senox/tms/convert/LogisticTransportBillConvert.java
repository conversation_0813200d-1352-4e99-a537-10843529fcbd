package com.senox.tms.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.tms.domain.LogisticTransportBill;
import com.senox.tms.vo.LogisticTransportBillVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025-05-26
 **/
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LogisticTransportBillConvert extends BaseConvert<LogisticTransportBill, LogisticTransportBillVo> {

}

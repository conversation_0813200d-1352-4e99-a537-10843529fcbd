package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingOrder;
import com.senox.tms.vo.UnloadingOrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingOrderConvert {

    UnloadingOrder toDo(UnloadingOrderVo vo);

    List<UnloadingOrder> toDo(List<UnloadingOrderVo> voList);

    UnloadingOrderVo toVo(UnloadingOrder domain);

    List<UnloadingOrderVo> toVo(List<UnloadingOrder> domainList);
}

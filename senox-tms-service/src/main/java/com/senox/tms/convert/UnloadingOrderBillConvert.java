package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingOrderBill;
import com.senox.tms.vo.UnloadingOrderBillVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingOrderBillConvert {

    UnloadingOrderBill toDo(UnloadingOrderBillVo vo);

    List<UnloadingOrderBill> toDo(List<UnloadingOrderBillVo> voList);

    UnloadingOrderBillVo toVo(UnloadingOrderBill domain);

    List<UnloadingOrderBillVo> toVo(List<UnloadingOrderBill> domainList);
}

package com.senox.tms.convert;

import com.senox.tms.domain.BicyclePayoffDayReport;
import com.senox.tms.domain.BicyclePayoffMonthReport;
import com.senox.tms.vo.BicyclePayoffReportVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicyclePayoffReportConvert {


    /**
     * do to vo
     *
     * @param domain do
     * @return vo
     */
    @Mapping(target = "date", expression = "java(domain.getYearMonthDay())")
    BicyclePayoffReportVo toV(BicyclePayoffDayReport domain);

    /**
     * do to vo
     *
     * @param domain do
     * @return vo
     */
    @Mapping(target = "date", expression = "java(domain.getYearMonth())")
    BicyclePayoffReportVo toV(BicyclePayoffMonthReport domain);
}

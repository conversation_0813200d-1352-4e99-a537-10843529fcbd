package com.senox.tms.convert;

import com.senox.tms.constant.BicycleStatus;
import com.senox.tms.domain.BicycleShares;
import com.senox.tms.vo.BicycleSharesVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 佣金 convert
 *
 * <AUTHOR>
 * @date 2023-9-12
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleSharesConvert {

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    @Mapping(target = "status", expression = "java(getStatusNumber(vo.getStatus()))")
    BicycleShares todo(BicycleSharesVo vo);

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    List<BicycleShares> todo(List<BicycleSharesVo> vo);

    /**
     * domain to vo
     *
     * @param domain do
     * @return vo vo
     */
    @Mapping(target = "status", expression = "java(com.senox.tms.constant.BicycleStatus.fromStatus(domain.getStatus()))")
    BicycleSharesVo toVo(BicycleShares domain);

    /**
     * domain to vo
     *
     * @param domain do
     * @return vo vo
     */
    List<BicycleSharesVo> toVo(List<BicycleShares> domain);

    /**
     * 获取状态值
     *
     * @param status 状态
     * @return 状态值
     */
    default Integer getStatusNumber(BicycleStatus status) {
        return null == status ? null : BicycleStatus.fromStatus(status.getNumber()).getNumber();
    }
}

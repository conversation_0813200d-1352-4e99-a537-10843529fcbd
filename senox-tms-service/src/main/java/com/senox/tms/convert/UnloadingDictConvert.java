package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingDict;
import com.senox.tms.vo.UnloadingDictVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingDictConvert {

    UnloadingDict toDo(UnloadingDictVo vo);

    List<UnloadingDict> toDo(List<UnloadingDictVo> voList);

    UnloadingDictVo toVo(UnloadingDict domain);

    List<UnloadingDictVo> toVo(List<UnloadingDict> domainList);
}

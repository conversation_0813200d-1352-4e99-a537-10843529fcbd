package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingShares;
import com.senox.tms.vo.UnloadingSharesVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingSharesConvert {

    UnloadingShares toDo(UnloadingSharesVo vo);

    List<UnloadingShares> toDo(List<UnloadingSharesVo> voList);

    UnloadingSharesVo toVo(UnloadingShares domain);

    List<UnloadingSharesVo> toVo(List<UnloadingShares> domainList);
}

package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingOrderGoods;
import com.senox.tms.vo.UnloadingOrderGoodsVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingOrderGoodsConvert {

    UnloadingOrderGoods toDo(UnloadingOrderGoodsVo vo);

    List<UnloadingOrderGoods> toDo(List<UnloadingOrderGoodsVo> voList);

    UnloadingOrderGoodsVo toVo(UnloadingOrderGoods domain);

    List<UnloadingOrderGoodsVo> toVo(List<UnloadingOrderGoods> domainList);
}

package com.senox.tms.convert;

import com.senox.tms.domain.BicycleDeliveryOrderJob;
import com.senox.tms.vo.BicycleDeliveryOrderJobVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/9/20 10:09
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleDeliveryOrderJobConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicycleDeliveryOrderJob toDo(BicycleDeliveryOrderJobVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicycleDeliveryOrderJobVo toVo(BicycleDeliveryOrderJob domain);
}

package com.senox.tms.convert;

import com.senox.tms.domain.BicycleDeliveryOrderDetail;
import com.senox.tms.vo.BicycleDeliveryOrderDetailVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20 9:10
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)

public interface BicycleDeliveryOrderDetailConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicycleDeliveryOrderDetail toDo(BicycleDeliveryOrderDetailVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicycleDeliveryOrderDetailVo toVo(BicycleDeliveryOrderDetail domain);

    /**
     * 视图集合 转domain 对象集合
     * @param voList
     * @return
     */
    List<BicycleDeliveryOrderDetail> toDo(List<BicycleDeliveryOrderDetailVo> voList);

    /**
     * domain集合 对象 转视图集合
     * @param domainList
     * @return
     */
    List<BicycleDeliveryOrderDetailVo> toVo(List<BicycleDeliveryOrderDetail> domainList);
}

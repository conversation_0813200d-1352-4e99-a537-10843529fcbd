package com.senox.tms.convert;

import com.senox.tms.constant.BicycleStatus;
import com.senox.tms.domain.BicycleCharges;
import com.senox.tms.vo.BicycleChargesVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 收费标准 convert
 *
 * <AUTHOR>
 * @date 2023-9-12
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleChargesConvert {

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    @Mapping(target = "status", expression = "java(getStatusNumber(vo.getStatus()))")
    BicycleCharges toDo(BicycleChargesVo vo);

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    List<BicycleCharges> toDo(List<BicycleChargesVo> vo);

    /**
     * domain to vo
     *
     * @param domain do
     * @return vo vo
     */
    @Mapping(target = "status", expression = "java(com.senox.tms.constant.BicycleStatus.fromStatus(domain.getStatus()))")
    BicycleChargesVo toVo(BicycleCharges domain);

    /**
     * domain to vo
     *
     * @param domain do
     * @return vo vo
     */
    List<BicycleChargesVo> toVo(List<BicycleCharges> domain);

    /**
     * 获取状态值
     *
     * @param status 状态
     * @return 状态值
     */
    default Integer getStatusNumber(BicycleStatus status) {
        return null == status ? null : BicycleStatus.fromStatus(status.getNumber()).getNumber();
    }
}

package com.senox.tms.convert;

import com.senox.tms.domain.BicycleRider;
import com.senox.tms.vo.BicycleRiderVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/13 16:04
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleRiderConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicycleRider toDo(BicycleRiderVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicycleRiderVo toVo(BicycleRider domain);

    /**
     * 视图集合 转domain 对象集合
     * @param voList
     * @return
     */
    List<BicycleRider> toDo(List<BicycleRiderVo> voList);

    /**
     * domain集合 对象 转视图集合
     * @param domainList
     * @return
     */
    List<BicycleRiderVo> toVo(List<BicycleRider> domainList);
}

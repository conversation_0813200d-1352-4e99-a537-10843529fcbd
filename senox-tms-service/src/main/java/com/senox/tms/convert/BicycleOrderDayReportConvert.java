package com.senox.tms.convert;

import com.senox.tms.domain.BicycleOrderDayReport;
import com.senox.tms.vo.BicycleOrderDayReportVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/16 9:34
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleOrderDayReportConvert {

    /**
     * 视图对象转实例对象
     * @param vo
     * @return
     */
    BicycleOrderDayReport toDo(BicycleOrderDayReportVo vo);

    /**
     * 视图对象列表转实例对象列表
     * @param voList
     * @return
     */
    List<BicycleOrderDayReport> toDo(List<BicycleOrderDayReportVo> voList);

    /**
     * 实例对象转视图对象
     * @param domain
     * @return
     */
    BicycleOrderDayReportVo toVo(BicycleOrderDayReport domain);

    /**
     * 实例对象列表转视图对象列表
     * @param list
     * @return
     */
    List<BicycleOrderDayReportVo> toVo(List<BicycleOrderDayReport> list);
}

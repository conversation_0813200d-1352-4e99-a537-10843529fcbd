package com.senox.tms.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.tms.domain.BicyclePayoffCharges;
import com.senox.tms.vo.BicyclePayoffChargesVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024-4-1
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicyclePayoffChargesConvert extends BaseConvert<BicyclePayoffCharges, BicyclePayoffChargesVo> {
}

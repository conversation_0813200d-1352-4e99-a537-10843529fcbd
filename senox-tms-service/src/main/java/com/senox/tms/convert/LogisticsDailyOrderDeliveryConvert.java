package com.senox.tms.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.tms.domain.LogisticsDailyOrderDelivery;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023-12=5
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LogisticsDailyOrderDeliveryConvert extends BaseConvert<LogisticsDailyOrderDelivery, LogisticsDailyOrderDeliveryVo> {

}

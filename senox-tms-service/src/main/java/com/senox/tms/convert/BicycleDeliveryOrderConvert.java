package com.senox.tms.convert;

import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.vo.BicycleDeliveryOrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/9/20 9:08
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleDeliveryOrderConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicycleDeliveryOrder toDo(BicycleDeliveryOrderVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicycleDeliveryOrderVo toVo(BicycleDeliveryOrder domain);
}

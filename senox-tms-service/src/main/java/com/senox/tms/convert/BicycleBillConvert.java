package com.senox.tms.convert;

import com.senox.tms.domain.BicycleBill;
import com.senox.tms.vo.BicycleBillVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023-11-15
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleBillConvert {

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    BicycleBill toDo(BicycleBillVo vo);
}

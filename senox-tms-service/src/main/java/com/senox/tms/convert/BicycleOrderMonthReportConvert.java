package com.senox.tms.convert;

import com.senox.tms.domain.BicycleOrderMonthReport;
import com.senox.tms.vo.BicycleOrderMonthReportVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/17 9:11
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleOrderMonthReportConvert {

    /**
     * 视图对象转实例对象
     * @param vo
     * @return
     */
    BicycleOrderMonthReport toDo(BicycleOrderMonthReportVo vo);

    /**
     * 视图对象列表转实例对象列表
     * @param voList
     * @return
     */
    List<BicycleOrderMonthReport> toDo(List<BicycleOrderMonthReportVo> voList);

    /**
     * 实例对象转视图对象
     * @param domain
     * @return
     */
    BicycleOrderMonthReportVo toVo(BicycleOrderMonthReport domain);

    /**
     * 实例对象列表转视图对象列表
     * @param list
     * @return
     */
    List<BicycleOrderMonthReportVo> toVo(List<BicycleOrderMonthReport> list);
}

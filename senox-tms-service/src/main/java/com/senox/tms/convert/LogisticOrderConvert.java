package com.senox.tms.convert;

import com.senox.tms.domain.LogisticOrderBill;
import com.senox.tms.domain.LogisticOrderProduct;
import com.senox.tms.domain.LogisticOrderShip;
import com.senox.tms.vo.LogisticOrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/11/30 11:19
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LogisticOrderConvert {

    /**
     * 物流订单视图 转 商品订单实体
     * @param vo
     * @return
     */
    @Mapping(source = "productOrderId", target = "id")
    @Mapping(source = "productTotalAmount", target = "totalAmount")
    LogisticOrderProduct toProductOrder(LogisticOrderVo vo);

    /**
     * 物流订单视图 转 配送订单实体
     * @param vo
     * @return
     */
    @Mapping(source = "shipId", target = "id")
    @Mapping(source = "productOrderId", target = "orderProductId")
    @Mapping(source = "shipTotalAmount", target = "totalAmount")
    LogisticOrderShip toShipOrder(LogisticOrderVo vo);

    /**
     * 物流订单视图 转 账单实体
     * @param vo
     * @return
     */
    @Mapping(source = "productOrderId", target = "orderProductId")
    @Mapping(source = "shipId", target = "shipId")
    @Mapping(source = "productTotalAmount", target = "orderAmount")
    @Mapping(source = "shipTotalAmount", target = "shipAmount")
    LogisticOrderBill toOrderBill(LogisticOrderVo vo);


}

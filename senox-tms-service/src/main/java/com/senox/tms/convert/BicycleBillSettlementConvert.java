package com.senox.tms.convert;

import com.senox.tms.domain.BicycleBillSettlement;
import com.senox.tms.vo.BicycleBillSettlementVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-13
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleBillSettlementConvert {

    /**
     * do to vo
     *
     * @param domain do
     * @return vo
     */
    BicycleBillSettlementVo toVo(BicycleBillSettlement domain);


    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    BicycleBillSettlement toDo(BicycleBillSettlementVo vo);

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    List<BicycleBillSettlement> toDo(List<BicycleBillSettlementVo> vo);
}

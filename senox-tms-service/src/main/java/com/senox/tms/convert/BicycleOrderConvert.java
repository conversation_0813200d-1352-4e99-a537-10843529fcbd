package com.senox.tms.convert;

import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.vo.BicycleOrderInfoVo;
import com.senox.tms.vo.BicycleOrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18 10:05
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleOrderConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicycleOrder toDo(BicycleOrderVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicycleOrderVo toVo(BicycleOrder domain);

    /**
     * 订单转详情
     * @param order
     * @return
     */
    BicycleOrderInfoVo toInfoVo(BicycleOrderVo order);

    /**
     * 转详情
     * @param list
     * @return
     */
    List<BicycleOrderInfoVo.BicycleDeliveryDetailInfoVo> toDetailInfo(List<BicycleOrderVo> list);
}

package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.vo.UnloadingWorkerVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingWorkerConvert {

    UnloadingWorker toDo(UnloadingWorkerVo vo);

    List<UnloadingWorker> toDo(List<UnloadingWorkerVo> voList);

    UnloadingWorkerVo toVo(UnloadingWorker domain);

    List<UnloadingWorkerVo> toVo(List<UnloadingWorker> domainList);
}

package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingOrderMonthPayoff;
import com.senox.tms.vo.UnloadingOrderMonthPayoffVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8 15:22
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingOrderMonthPayoffConvert {

    UnloadingOrderMonthPayoff toDo(UnloadingOrderMonthPayoffVo vo);

    List<UnloadingOrderMonthPayoff> toDo(List<UnloadingOrderMonthPayoffVo> voList);

    UnloadingOrderMonthPayoffVo toVo(UnloadingOrderMonthPayoff domain);

    List<UnloadingOrderMonthPayoffVo> toVo(List<UnloadingOrderMonthPayoff> domainList);
}

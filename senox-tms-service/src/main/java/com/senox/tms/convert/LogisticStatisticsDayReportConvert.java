package com.senox.tms.convert;

import com.senox.tms.domain.LogisticStatisticsDayReport;
import com.senox.tms.vo.LogisticStatisticsDayReportVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 11:23
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LogisticStatisticsDayReportConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    LogisticStatisticsDayReport toDo(LogisticStatisticsDayReportVo vo);

    /**
     * 视图 转domain 对象
     * @param voList
     * @return
     */
    List<LogisticStatisticsDayReport> toDo(List<LogisticStatisticsDayReportVo> voList);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    LogisticStatisticsDayReportVo toVo(LogisticStatisticsDayReport domain);
}

package com.senox.tms.convert;

import com.senox.tms.domain.BicycleOperateAnalysis;
import com.senox.tms.vo.BicycleOperateAnalysisVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/10/12 14:47
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleOperateAnalysisConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicycleOperateAnalysis toDo(BicycleOperateAnalysisVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicycleOperateAnalysisVo toVo(BicycleOperateAnalysis domain);
}

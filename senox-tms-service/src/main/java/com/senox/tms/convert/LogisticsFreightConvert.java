package com.senox.tms.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.tms.domain.LogisticsFreight;
import com.senox.tms.vo.LogisticsFreightVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023-12-6
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LogisticsFreightConvert extends BaseConvert<LogisticsFreight, LogisticsFreightVo> {
}

package com.senox.tms.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.tms.constant.LogisticTransportCategory;
import com.senox.tms.constant.LogisticTransportOrderPayer;
import com.senox.tms.domain.LogisticTransportOrder;
import com.senox.tms.vo.LogisticTransportOrderVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2025-05-20
 **/
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LogisticTransportOrderConvert extends BaseConvert<LogisticTransportOrder, LogisticTransportOrderVo> {


    @Override
    @Mapping(target = "category", expression = "java(getCategoryNumber(vo.getCategory()))")
    @Mapping(target = "payer", expression = "java(getPayerNumber(vo.getPayer()))")
    LogisticTransportOrder toDo(LogisticTransportOrderVo vo);

    @Override
    @Mapping(target = "category", expression = "java(getCategory(domain.getCategory()))")
    @Mapping(target = "payer", expression = "java(getPayer(domain.getPayer()))")
    LogisticTransportOrderVo toV(LogisticTransportOrder domain);


    default LogisticTransportCategory getCategory(Integer category) {
        if (null == category) {
            return null;
        }
        return LogisticTransportCategory.fromNumber(category);
    }

    default Integer getCategoryNumber(LogisticTransportCategory category) {
        if (null == category) {
            return null;
        }
        return category.getNumber();
    }

    default LogisticTransportOrderPayer getPayer(Integer payer) {
        if (null == payer) {
            return null;
        }
        return LogisticTransportOrderPayer.fromNumber(payer);
    }

    default Integer getPayerNumber(LogisticTransportOrderPayer payer) {
        if (null == payer) {
            return null;
        }
        return payer.getNumber();
    }
}

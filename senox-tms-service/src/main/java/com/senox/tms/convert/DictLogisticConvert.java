package com.senox.tms.convert;


import com.senox.tms.domain.DictLogistic;
import com.senox.tms.vo.DictLogisticVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface DictLogisticConvert{

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    @Mapping(target = "category", expression = "java(null!=vo.getCategory()?vo.getCategory().getNumber():null)")
    DictLogistic toDo(DictLogisticVo vo);

    /**
     * do to vo
     * @param domain do
     * @return vo
     */

    @Mapping(target = "category", expression = "java(com.senox.tms.constant.DictLogisticCategory.fromNumber(domain.getCategory()))")
    DictLogisticVo toV(DictLogistic domain);
}

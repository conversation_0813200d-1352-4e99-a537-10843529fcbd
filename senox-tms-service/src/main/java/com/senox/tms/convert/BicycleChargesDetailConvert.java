package com.senox.tms.convert;


import com.senox.tms.domain.BicycleChargesDetail;
import com.senox.tms.vo.BicycleChargesDetailVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * 收费标准明细 convert
 *
 * <AUTHOR>
 * @date 2023-9-12
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleChargesDetailConvert {

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    BicycleChargesDetail toDo(BicycleChargesDetailVo vo);

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    List<BicycleChargesDetail> toDo(List<BicycleChargesDetailVo> vo);


    /**
     * domain to vo
     *
     * @param domain do
     * @return vo vo
     */
    BicycleChargesDetailVo toVo(BicycleChargesDetail domain);


    /**
     * domain to vo
     *
     * @param domain do
     * @return vo vo
     */
    List<BicycleChargesDetailVo> toVo(List<BicycleChargesDetail> domain);

}

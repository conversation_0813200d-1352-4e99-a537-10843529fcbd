package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingOrderWorkers;
import com.senox.tms.vo.UnloadingOrderWorkersVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingOrderWorkersConvert {

    UnloadingOrderWorkers toDo(UnloadingOrderWorkersVo vo);

    List<UnloadingOrderWorkers> toDo(List<UnloadingOrderWorkersVo> voList);

    UnloadingOrderWorkersVo toVo(UnloadingOrderWorkers domain);

    List<UnloadingOrderWorkersVo> toVo(List<UnloadingOrderWorkers> domainList);
}

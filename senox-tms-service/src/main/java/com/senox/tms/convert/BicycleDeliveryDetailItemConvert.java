package com.senox.tms.convert;

import com.senox.tms.domain.BicycleDeliveryOrderDetailItem;
import com.senox.tms.domain.BicycleOrderGoodsDetail;
import com.senox.tms.vo.BicycleDeliveryOrderDetailItemVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/3 11:41
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleDeliveryDetailItemConvert {

    List<BicycleOrderGoodsDetail> toGoodsList(List<BicycleDeliveryOrderDetailItemVo> itemVos);

    List<BicycleDeliveryOrderDetailItemVo> toVoList(List<BicycleDeliveryOrderDetailItem> items);

    List<BicycleDeliveryOrderDetailItem> toDoList(List<BicycleDeliveryOrderDetailItemVo> itemVos);

    BicycleDeliveryOrderDetailItemVo toVo(BicycleDeliveryOrderDetailItem item);

    BicycleDeliveryOrderDetailItem toDo(BicycleDeliveryOrderDetailItemVo itemVo);
}

package com.senox.tms.convert;

import com.senox.tms.domain.BicycleOrderGoodsDetail;
import com.senox.tms.vo.BicycleOrderGoodsDetailVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18 10:09
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleOrderGoodsDetailConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicycleOrderGoodsDetail toDo(BicycleOrderGoodsDetailVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicycleOrderGoodsDetailVo toVo(BicycleOrderGoodsDetail domain);

    /**
     * 视图集合 转domain 对象集合
     * @param voList
     * @return
     */
    List<BicycleOrderGoodsDetail> toDo(List<BicycleOrderGoodsDetailVo> voList);

    /**
     * domain集合 对象 转视图集合
     * @param domainList
     * @return
     */
    List<BicycleOrderGoodsDetailVo> toVo(List<BicycleOrderGoodsDetail> domainList);
}

package com.senox.tms.convert;

import com.senox.tms.domain.LogisticLoaderSettlement;
import com.senox.tms.vo.LogisticLoaderSettlementFormVo;
import com.senox.tms.vo.LogisticLoaderSettlementVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LoaderSettlementConvert {

    /**
     * vo to do
     *
     * @param vo vo
     * @return domain
     */
    @Mapping(target = "freightType", expression = "java(null == vo.getFreightType() ? null : vo.getFreightType().getNumber())")
    @Mapping(target = "goodsType", expression = "java(null == vo.getGoodsType() ? null : vo.getGoodsType().getNumber())")
    LogisticLoaderSettlement toDo(LogisticLoaderSettlementVo vo);


    /**
     * vo to do
     *
     * @param vo vo
     * @return domain
     */
    @Mapping(target = "freightType", expression = "java(null == vo.getFreightType() ? null : vo.getFreightType().getNumber())")
    @Mapping(target = "goodsType", expression = "java(null == vo.getGoodsType() ? null : vo.getGoodsType().getNumber())")
    LogisticLoaderSettlement toDo(LogisticLoaderSettlementFormVo vo);


    /**
     * do to vo
     *
     * @param domain domain
     * @return vo
     */
    @Mapping(target = "freightType", expression = "java(com.senox.tms.constant.LogisticLoaderFreightType.fromNumber(domain.getFreightType()))")
    @Mapping(target = "goodsType", expression = "java(com.senox.tms.constant.LogisticLoaderGoodsType.fromNumber(domain.getGoodsType()))")
    LogisticLoaderSettlementVo toV(LogisticLoaderSettlement domain);

    /**
     * vo to do
     *
     * @param vo vo
     * @return do
     */
    List<LogisticLoaderSettlement> toDo(List<LogisticLoaderSettlementVo> vo);

    /**
     * do to vo
     *
     * @param domain do
     * @return vo
     */
    List<LogisticLoaderSettlementVo> toV(List<LogisticLoaderSettlement> domain);
}

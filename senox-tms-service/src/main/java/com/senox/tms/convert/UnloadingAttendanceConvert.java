package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingAttendance;
import com.senox.tms.vo.UnloadingAttendanceVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingAttendanceConvert {

    UnloadingAttendance toDo(UnloadingAttendanceVo vo);

    List<UnloadingAttendance> toDo(List<UnloadingAttendanceVo> voList);

    UnloadingAttendanceVo toVo(UnloadingAttendance domain);

    List<UnloadingAttendanceVo> toVo(List<UnloadingAttendance> domainList);
}

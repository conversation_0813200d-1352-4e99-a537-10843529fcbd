package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingNightSchedule;
import com.senox.tms.vo.UnloadingNightScheduleVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/10 16:10
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingNightScheduleConvert {

    UnloadingNightSchedule toDo(UnloadingNightScheduleVo vo);

    List<UnloadingNightSchedule> toDo(List<UnloadingNightScheduleVo> voList);

    UnloadingNightScheduleVo toVo(UnloadingNightSchedule domain);

    List<UnloadingNightScheduleVo> toVo(List<UnloadingNightSchedule> domainList);
}

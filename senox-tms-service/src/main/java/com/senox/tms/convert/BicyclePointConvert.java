package com.senox.tms.convert;

import com.senox.tms.domain.BicyclePoint;
import com.senox.tms.vo.BicyclePointVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/13 14:56
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicyclePointConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicyclePoint toDo(BicyclePointVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicyclePointVo toVo(BicyclePoint domain);

    /**
     * 视图集合 转domain 对象集合
     * @param voList
     * @return
     */
    List<BicyclePoint> toDo(List<BicyclePointVo> voList);

    /**
     * domain集合 对象 转视图集合
     * @param domainList
     * @return
     */
    List<BicyclePointVo> toVo(List<BicyclePoint> domainList);
}

package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingWorkerAccess;
import com.senox.tms.vo.UnloadingWorkerAccessVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingWorkerAccessConvert {

    UnloadingWorkerAccess toDo(UnloadingWorkerAccessVo vo);

    List<UnloadingWorkerAccess> toDo(List<UnloadingWorkerAccessVo> voList);

    UnloadingWorkerAccessVo toVo(UnloadingWorkerAccess domain);

    List<UnloadingWorkerAccessVo> toVo(List<UnloadingWorkerAccess> domainList);
}

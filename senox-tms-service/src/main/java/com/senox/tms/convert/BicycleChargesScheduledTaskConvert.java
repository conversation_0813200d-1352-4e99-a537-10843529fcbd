package com.senox.tms.convert;

import com.senox.common.convert.BaseConvert;
import com.senox.tms.domain.BicycleChargesScheduledTask;
import com.senox.tms.vo.BicycleChargesScheduledTaskVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleChargesScheduledTaskConvert extends BaseConvert<BicycleChargesScheduledTask, BicycleChargesScheduledTaskVo> {
}

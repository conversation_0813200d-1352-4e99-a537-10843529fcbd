package com.senox.tms.convert;

import com.senox.tms.domain.LogisticPayoff;
import com.senox.tms.vo.LogisticPayoffVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/15 16:02
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LogisticPayoffConvert {

    /**
     * 物流客户应付视图对象转应付实体
     * @param vo
     * @return
     */
    LogisticPayoff toDo(LogisticPayoffVo vo);

    /**
     * 物流客户应付实体转视图
     * @param domain
     * @return
     */
    LogisticPayoffVo toVo(LogisticPayoff domain);

    /**
     * 物流客户应付实体转视图列表
     * @param list
     * @return
     */
    List<LogisticPayoffVo> toVo(List<LogisticPayoff> list);
}

package com.senox.tms.convert;

import com.senox.tms.domain.BicycleRiderAttendance;
import com.senox.tms.vo.BicycleRiderAttendanceVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/14 16:54
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleRiderAttendanceConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicycleRiderAttendance toDo(BicycleRiderAttendanceVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicycleRiderAttendanceVo toVo(BicycleRiderAttendance domain);

    /**
     * 视图集合 转domain 对象集合
     * @param voList
     * @return
     */
    List<BicycleRiderAttendance> toDo(List<BicycleRiderAttendanceVo> voList);

    /**
     * domain集合 对象 转视图集合
     * @param domainList
     * @return
     */
    List<BicycleRiderAttendanceVo> toVo(List<BicycleRiderAttendance> domainList);
}

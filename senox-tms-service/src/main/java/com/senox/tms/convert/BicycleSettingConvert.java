package com.senox.tms.convert;

import com.senox.tms.domain.BicycleSetting;
import com.senox.tms.vo.BicycleSettingVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/25 9:36
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleSettingConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicycleSetting toDo(BicycleSettingVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicycleSettingVo toVo(BicycleSetting domain);

    /**
     * domain集合 对象 转视图集合
     * @param domainList
     * @return
     */
    List<BicycleSettingVo> toVo(List<BicycleSetting> domainList);
}

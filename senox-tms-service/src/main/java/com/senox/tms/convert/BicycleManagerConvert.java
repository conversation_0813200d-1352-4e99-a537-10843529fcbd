package com.senox.tms.convert;

import com.senox.tms.domain.BicycleManager;
import com.senox.tms.vo.BicycleManagerVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @date 2023/10/27 10:25
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BicycleManagerConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    BicycleManager toDo(BicycleManagerVo vo);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    BicycleManagerVo toVo(BicycleManager domain);
}

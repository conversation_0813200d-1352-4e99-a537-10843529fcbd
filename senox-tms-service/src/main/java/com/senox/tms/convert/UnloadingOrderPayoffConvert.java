package com.senox.tms.convert;

import com.senox.tms.domain.UnloadingOrderPayoff;
import com.senox.tms.vo.UnloadingOrderPayoffVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 15:06
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface UnloadingOrderPayoffConvert {

    UnloadingOrderPayoff toDo(UnloadingOrderPayoffVo vo);

    List<UnloadingOrderPayoff> toDo(List<UnloadingOrderPayoffVo> voList);

    UnloadingOrderPayoffVo toVo(UnloadingOrderPayoff domain);

    List<UnloadingOrderPayoffVo> toVo(List<UnloadingOrderPayoff> domainList);
}

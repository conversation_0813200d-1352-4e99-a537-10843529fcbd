package com.senox.tms.convert;

import com.senox.tms.domain.OutgoingTtl;
import com.senox.tms.vo.OutgoingTtlVo;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 11:23
 */
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface OutgoingTtlConvert {

    /**
     * 视图 转domain 对象
     * @param vo
     * @return
     */
    OutgoingTtl toDo(OutgoingTtlVo vo);

    /**
     * 视图 转domain 对象
     * @param voList
     * @return
     */
    List<OutgoingTtl> toDo(List<OutgoingTtlVo> voList);

    /**
     * domain 对象 转视图
     * @param domain
     * @return
     */
    OutgoingTtlVo toVo(OutgoingTtl domain);
}

package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.common.vo.BillPaidVo;
import com.senox.tms.domain.LogisticTransportBillSettlement;
import com.senox.tms.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-06-09
 **/
public interface LogisticTransportBillSettlementMapper extends BaseMapper<LogisticTransportBillSettlement> {

    /**
     * 批量添加
     * @param billSettlements 结算单集
     */
    void addBatch(List<LogisticTransportBillSettlement> billSettlements);

    /**
     * 批量更新
     * @param billSettlements 结算单集
     */
    void updateBatch(List<LogisticTransportBillSettlement> billSettlements);

    /**
     * 列表统计查询
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int countList(LogisticTransportBillSettlementSearchVo search);

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    List<LogisticTransportBillSettlementVo> list(LogisticTransportBillSettlementSearchVo search);

    /**
     * 根据账单id更新账单支付结果
     * @param billPaid 账单支付信息
     */
    void updateBillPaidById(BillPaidVo billPaid);

    /**
     * 根据订单id更新账单支付结果
     * @param billPaid 账单支付信息
     */
    void updateBillPaidByOrderId(BillPaidVo billPaid);

    /**
     * 账单状态更新
     * @param remoteOrderId  远程订单id
     * @param paid 支付状态
     */
    void updateBillStatusByOrderId(Long remoteOrderId, Boolean paid);

    /**
     * 结算单统计
     * @param search 查询参数
     * @return 返回结算单统计
     */
    LogisticTransportBillSettlementStatisticsVo statistics(LogisticTransportBillSettlementSearchVo search);

    /**
     * 下发
     * @param send 下发参数
     */
    void send(LogisticTransportBillSettlementSendVo send);

    /**
     * 结算单更新支付订单
     *
     * @param billPaid 账单支付信息
     */
    void updatePaidOrder(BillPaidVo billPaid);
}

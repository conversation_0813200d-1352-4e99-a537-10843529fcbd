package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingOrder;
import com.senox.tms.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/14 11:17
 */
@Mapper
public interface UnloadingOrderMapper extends BaseMapper<UnloadingOrder> {


    /**
     * 获取最大订单编号
     * @param prefix
     * @return
     */
    String findMaxOrderNo(String prefix);

    /**
     * 订单数量
     * @param searchVo
     * @return
     */
    int countOrder(UnloadOrderSearchVo searchVo);

    /**
     * 订单列表
     * @param searchVo
     * @return
     */
    List<UnloadingOrderVo> listOrder(UnloadOrderSearchVo searchVo);


    /**
     * 订单合计
     * @param searchVo
     * @return
     */
    UnloadingOrderVo sumOrder(UnloadOrderSearchVo searchVo);

    /**
     * 根据id查询订单详细
     * @param id
     * @return
     */
    UnloadingOrderVo findDetailById(@Param("id") Long id);

    /**
     * 运营分析
     * @param startTime
     * @param endTime
     * @return
     */
    UnloadingOperateAnalysisVo findOperateAnalysis(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 总数量
     * @param startTime
     * @param endTime
     * @return
     */
    int totalQuantity(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 下单数量列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<UnloadingOrderCountRankingVo> listOrderCountRanking(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 下单货物数量列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<UnloadingQuantityCountRankingVo> listQuantityCountRanking(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 下单金额列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<UnloadingAmountCountRankingVo> listAmountCountRanking(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 订单统计
     * @param searchVo
     * @return
     */
    List<UnloadingOrderCountVo> listCountList(UnloadingStatisticSearchVo searchVo);

    /**
     * 查询该时间后，搬运工关联的最新一个订单id
     * @param startTime
     * @param workerId
     * @return
     */
    Long lastOrderByWorkerId(@Param("startTime") LocalDateTime startTime, @Param("workerId") Long workerId);
}

package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicycleOrderDayReport;
import com.senox.tms.vo.BicycleOrderDayReportSearchVo;
import com.senox.tms.vo.BicycleOrderDayReportVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/16 9:36
 */
@Mapper
public interface BicycleOrderDayReportMapper extends BaseMapper<BicycleOrderDayReport> {

    /**
     * 日报表合计
     * @param searchVo
     * @return
     */
    BicycleOrderDayReport sumDayReport(BicycleOrderDayReportSearchVo searchVo);

    /**
     * 日报表数统计
     * @param searchVo
     * @return
     */
    int countDayReport(BicycleOrderDayReportSearchVo searchVo);

    /**
     * 日报表列表
     * @param searchVo
     * @return
     */
    List<BicycleOrderDayReportVo> listDayReport(BicycleOrderDayReportSearchVo searchVo);
}

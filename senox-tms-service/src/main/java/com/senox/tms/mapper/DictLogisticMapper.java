package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.DictLogistic;
import com.senox.tms.vo.DictLogisticSearchVo;
import com.senox.tms.vo.DictLogisticVo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
public interface DictLogisticMapper extends BaseMapper<DictLogistic> {

    /**
     * 批量添加
     *
     * @param dictLogistics 物流字典集
     * @return 影响条
     */
    int addBatch(Collection<DictLogistic> dictLogistics);

    /**
     * 批量更新
     *
     * @param dictLogistics 物流字典集
     * @return 影响条
     */
    int updateBatch(Collection<DictLogistic> dictLogistics);

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    List<DictLogisticVo> list(DictLogisticSearchVo searchVo);

    /**
     * 列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    int countList(DictLogisticSearchVo searchVo);
}

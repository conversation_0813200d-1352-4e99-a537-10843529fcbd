package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.OutgoingTtl;
import com.senox.tms.vo.OutgoingTtlSearchVo;
import com.senox.tms.vo.OutgoingTtlVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/8 11:11
 */
@Mapper
public interface OutgoingTtlMapper extends BaseMapper<OutgoingTtl> {

    /**
     * 太太乐外发数量
     * @param searchVo
     * @return
     */
    int countOutgoing(OutgoingTtlSearchVo searchVo);

    /**
     * 太太乐外列表
     * @param searchVo
     * @return
     */
    List<OutgoingTtlVo> listOutgoing(OutgoingTtlSearchVo searchVo);

    /**
     * 太太乐外发合计
     * @param searchVo
     * @return
     */
    OutgoingTtlVo sumOutgoing(OutgoingTtlSearchVo searchVo);

    /**
     * 根据物流单号查询太太乐外发
     * @param logisticsNoList
     * @return
     */
    List<OutgoingTtl> listOutgoingByLogisticsNoList(@Param("logisticsNoList") List<String> logisticsNoList);
}

package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingOrderBill;
import com.senox.tms.vo.UnloadingOrderBillSearchVo;
import com.senox.tms.vo.UnloadingOrderBillVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/31 15:53
 */
@Mapper
public interface UnloadingOrderBillMapper extends BaseMapper<UnloadingOrderBill> {


    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    int countBill(UnloadingOrderBillSearchVo searchVo);

    /**
     * 列表
     * @param searchVo
     * @return
     */
    List<UnloadingOrderBillVo> listBill(UnloadingOrderBillSearchVo searchVo);

    /**
     * 金额合计
     * @param searchVo
     * @return
     */
    UnloadingOrderBillVo sumBill(UnloadingOrderBillSearchVo searchVo);
}

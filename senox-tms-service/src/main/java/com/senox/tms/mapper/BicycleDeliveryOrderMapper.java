package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.vo.BicycleDeliveryInfoSearchVo;
import com.senox.tms.vo.BicycleDeliveryInfoVo;
import com.senox.tms.vo.BicycleDeliveryOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20 8:49
 */
@Mapper
public interface BicycleDeliveryOrderMapper extends BaseMapper<BicycleDeliveryOrder> {

    /**
     * 获取最大的配送单号
     * @param prefix
     * @return
     */
    String findMaxDeliveryOrderNo(String prefix);

    /**
     * 根据id查询配送单
     * @param id
     * @return
     */
    BicycleDeliveryOrderVo findDeliveryOrderById(@Param("id") Long id);

    /**
     * 配送单提货信息数量
     * @param searchVo
     * @return
     */
    int deliveryInfoCount(BicycleDeliveryInfoSearchVo searchVo);

    /**
     * 配送单提货信息列表
     * @param searchVo
     * @return
     */
    List<BicycleDeliveryInfoVo> deliveryInfoList(BicycleDeliveryInfoSearchVo searchVo);

}

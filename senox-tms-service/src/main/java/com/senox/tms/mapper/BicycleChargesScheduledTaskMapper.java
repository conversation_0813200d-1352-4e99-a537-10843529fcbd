package com.senox.tms.mapper;

import com.senox.tms.domain.BicycleChargesScheduledTask;
import com.senox.tms.vo.BicycleChargesScheduledTaskSearchVo;
import com.senox.tms.vo.BicycleChargesScheduledTaskVo;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-3-7
 */
public interface BicycleChargesScheduledTaskMapper {

    /**
     * 批量添加
     *
     * @param tasks 任务集
     */
    void addBatch(Collection<BicycleChargesScheduledTask> tasks);

    /**
     * 添加商户
     *
     * @param taskId      任务id
     * @param merchantIds 商户集
     */
    void addMerchant(Long taskId, Collection<Long> merchantIds);

    /**
     * 批量更新
     *
     * @param updateTasks 任务集
     */
    void updateBatch(Collection<BicycleChargesScheduledTask> updateTasks);

    /**
     * 根据任务id删除商户
     *
     * @param taskId 任务id
     */
    void deleteMerchantByTaskId(Long taskId);

    /**
     * 删除
     *
     * @param taskId 任务id
     */
    void delete(Long taskId);

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    List<BicycleChargesScheduledTaskVo> list(BicycleChargesScheduledTaskSearchVo searchVo);

    /**
     * 列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    int listCount(BicycleChargesScheduledTaskSearchVo searchVo);

    /**
     * 根据id查找计划任务
     * @param id id
     * @return 返回查找到的计划任务
     */
    BicycleChargesScheduledTaskVo findById(Long id);
}

package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicyclePayoffMonthReport;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 应付月账单报表 mapper
 * <AUTHOR>
 * @date 2023-10-16
 */
public interface BicyclePayoffYearMonthMapper extends BaseMapper<BicyclePayoffMonthReport> {

    /**
     * 列表金额合计
     * @return 返回合计
     */
    @Select("select sum(total_amount) from t_bicycle_payoff_month_report")
    BigDecimal listTotalAmount();

}

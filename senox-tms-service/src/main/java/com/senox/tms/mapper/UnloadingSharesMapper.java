package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingShares;
import com.senox.tms.vo.UnloadingSharesSearchVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/30 14:54
 */
@Mapper
public interface UnloadingSharesMapper extends BaseMapper<UnloadingShares> {

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    int countShares(UnloadingSharesSearchVo searchVo);

    /**
     * 列表
     * @param searchVo
     * @return
     */
    List<UnloadingShares> listShares(UnloadingSharesSearchVo searchVo);
}

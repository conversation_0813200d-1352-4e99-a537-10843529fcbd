package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingAttendance;
import com.senox.tms.vo.UnloadingAttendanceSearchVo;
import com.senox.tms.vo.UnloadingAttendanceVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/14 9:14
 */
@Mapper
public interface UnloadingAttendanceMapper extends BaseMapper<UnloadingAttendance> {

    /**
     * 搬运工考勤总数
     * @param searchVo
     * @return
     */
    int countAttendance(UnloadingAttendanceSearchVo searchVo);

    /**
     * 搬运工考勤列表
     * @param searchVo
     * @return
     */
    List<UnloadingAttendanceVo> listAttendance(UnloadingAttendanceSearchVo searchVo);
}

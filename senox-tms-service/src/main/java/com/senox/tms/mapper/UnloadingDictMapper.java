package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingDict;
import com.senox.tms.vo.UnloadingDictSearchVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 13:34
 */
@Mapper
public interface UnloadingDictMapper extends BaseMapper<UnloadingDict> {

    /**
     * 装卸字典数量
     * @param searchVo
     * @return
     */
    int countDict(UnloadingDictSearchVo searchVo);

    /**
     * 装卸字典列表
     * @param searchVo
     * @return
     */
    List<UnloadingDict> listDict(UnloadingDictSearchVo searchVo);
}

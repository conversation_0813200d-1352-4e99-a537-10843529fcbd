package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingNightSchedule;
import com.senox.tms.vo.UnloadingNightScheduleSearchVo;
import com.senox.tms.vo.UnloadingNightScheduleVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/10 11:10
 */
@Mapper
public interface UnloadingNightScheduleMapper extends BaseMapper<UnloadingNightSchedule> {


    /**
     * 搬运工排期计划数量
     * @param searchVo
     * @return
     */
    int countSchedule(UnloadingNightScheduleSearchVo searchVo);


    /**
     * 搬运工排期计划列表
     * @param searchVo
     * @return
     */
    List<UnloadingNightScheduleVo> listSchedule(UnloadingNightScheduleSearchVo searchVo);
}

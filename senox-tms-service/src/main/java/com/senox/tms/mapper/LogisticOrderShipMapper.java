package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.LogisticOrderShip;
import com.senox.tms.vo.ShipOrderDiscountSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/27 16:05
 */
@Mapper
@Repository
public interface LogisticOrderShipMapper extends BaseMapper<LogisticOrderShip> {

    /**
     * 根据商品订单更新物流订单
     * @param productOrderIds
     * @return
     */
    int updateShipSizeByProductOrder(@Param("productOrderIds") List<Long> productOrderIds);

    /**
     * 根据商品订单更新物流费用
     * @param productOrderIds
     * @return
     */
    int updateShipAmountByProductOrder(@Param("productOrderIds") List<Long> productOrderIds);

    /**
     * 更新合计物流费
     * @param productOrderIds
     * @return
     */
    int updateShipTotalAmountByProductOrder(@Param("productOrderIds") List<Long> productOrderIds);


    /**
     * 根据订单时间更新物流订单折扣率
     * 62开头的订单，总重量500kg以下的不打折，总重量500kg（含）以上，1000kg （不含）以下打95折，1000kg（含）以上打9折
     * @param shipDates
     * @return
     */
    int updateShipDiscountByShipDate(List<LocalDate> shipDates);

    /**
     * 计算物流订单金额
     * @param shipDates
     * @return
     */
    int updateShipTotalByShipDate(List<LocalDate> shipDates);

    /**
     * 查询发货订单折扣率
     * @param search
     * @return
     */
    BigDecimal findShipOrderWeight(ShipOrderDiscountSearchVo search);
}

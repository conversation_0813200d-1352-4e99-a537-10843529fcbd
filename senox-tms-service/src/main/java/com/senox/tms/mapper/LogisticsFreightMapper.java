package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.common.vo.BillPaidVo;
import com.senox.tms.domain.LogisticsFreight;
import com.senox.tms.vo.LogisticFreightRemarkVo;
import com.senox.tms.vo.LogisticsFreightSearchVo;
import com.senox.tms.vo.LogisticsFreightStatisticsVo;
import com.senox.tms.vo.LogisticsFreightVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-6
 */
public interface LogisticsFreightMapper extends BaseMapper<LogisticsFreight> {

    /**
     * 列表查询
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    List<LogisticsFreightVo> list(LogisticsFreightSearchVo searchVo);

    /**
     * 列表统计查询
     *
     * @param searchVo 查询参数
     * @return 返回统计数
     */
    int countList(LogisticsFreightSearchVo searchVo);

    /**
     * 列表金额合计
     * @param searchVo 查询
     * @return 返回合计
     */
    LogisticsFreightStatisticsVo listTotalAmount(LogisticsFreightSearchVo searchVo);

    /**
     * 根据id查找
     * @param id id
     * @return 返回查找到的
     */
    LogisticsFreightVo findById(Long id);

    /**
     * 通过账单id更新账单状态
     * @param billPaid
     * @return
     */
    int updateBillPaidById(BillPaidVo billPaid);

    /**
     * 通过支付订单id更新物业账单状态
     * @param billPaid
     * @return
     */
    int updateBillPaidByRemoteOrder(BillPaidVo billPaid);

    /**
     * 更新账单备注
     * @param remarkVo
     * @return
     */
    int updateBillRemark(LogisticFreightRemarkVo remarkVo);
}

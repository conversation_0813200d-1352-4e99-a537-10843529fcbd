package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.LogisticOrderProduct;
import com.senox.tms.domain.LogisticPayoff;
import com.senox.tms.vo.LogisticOrderEditBatchVo;
import com.senox.tms.vo.LogisticOrderSearchVo;
import com.senox.tms.vo.LogisticOrderVo;
import com.senox.tms.vo.LogisticPayoffGenerateVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/27 15:51
 */
@Mapper
@Repository
public interface LogisticOrderProductMapper extends BaseMapper<LogisticOrderProduct> {

    /**
     * 更新商品订单金额
     * @param ids
     */
    void updateProductOrderAmount(@Param("ids") List<Long> ids);

    /**
     * 批量更新订单明细
     * @param edit
     */
    void updateOrderBatch(LogisticOrderEditBatchVo edit);

    /**
     * 根据商品订单id查找订单明细
     * @param productOrderId
     * @return
     */
    LogisticOrderVo findOrderByProductOrderId(Long productOrderId);

    /**
     * 根据发货日期、订单编号、商品名查找商品订单
     * @param list
     * @return
     */
    List<LogisticOrderProduct> listProductOrderByProduct(@Param("list") List<LogisticOrderProduct> list);

    /**
     * 订单数合计
     * @param search
     * @return
     */
    int countOrder(LogisticOrderSearchVo search);

    /**
     * 订单统计
     * @param search
     * @return
     */
    LogisticOrderVo sumOrder(LogisticOrderSearchVo search);

    /**
     * 订单列表
     * @param search
     * @return
     */
    List<LogisticOrderVo> listOrder(LogisticOrderSearchVo search);

    /**
     * 物流客户应付
     * @param generate
     * @return
     */
    List<LogisticPayoff> listMerchantPayoff(LogisticPayoffGenerateVo generate);

}

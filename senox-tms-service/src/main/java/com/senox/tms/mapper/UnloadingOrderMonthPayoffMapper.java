package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingOrderMonthPayoff;
import com.senox.tms.vo.UnloadingOrderMonthPayoffSearchVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/8 10:29
 */
@Mapper
public interface UnloadingOrderMonthPayoffMapper extends BaseMapper<UnloadingOrderMonthPayoff> {

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    int countMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo);

    /**
     * 应付合计
     * @param searchVo
     * @return
     */
    UnloadingOrderMonthPayoff sumMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo);

    /**
     * 列表
     * @param searchVo
     * @return
     */
    List<UnloadingOrderMonthPayoff> listMonthPayoff(UnloadingOrderMonthPayoffSearchVo searchVo);

}

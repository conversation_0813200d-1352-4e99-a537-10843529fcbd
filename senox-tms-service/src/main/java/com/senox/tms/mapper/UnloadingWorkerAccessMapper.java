package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingWorkerAccess;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2024/10/29 9:36
 */
@Mapper
public interface UnloadingWorkerAccessMapper extends BaseMapper<UnloadingWorkerAccess> {
    /**
     * 检查搬运工是否存在且未删除
     * @param workerSign 搬运工标识
     * @return 是否存在
     */
    boolean checkWorkerExistsBySign(@Param("workerSign") String workerSign);

}

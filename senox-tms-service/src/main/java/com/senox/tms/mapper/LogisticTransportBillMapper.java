package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.common.vo.BillPaidVo;
import com.senox.tms.domain.LogisticTransportBill;
import com.senox.tms.vo.LogisticTransportBillSearchVo;
import com.senox.tms.vo.LogisticTransportBillSettlementDetailVo;
import com.senox.tms.vo.LogisticTransportBillVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-22
 **/
public interface LogisticTransportBillMapper extends BaseMapper<LogisticTransportBill> {

    /**
     * 批量新增
     * @param bills 账单集
     */
    void addBatch(List<LogisticTransportBill> bills);

    /**
     * 批量更新
     * @param bills 账单集
     */
    void updateBatch(List<LogisticTransportBill> bills);

    /**
     * 列表统计查询
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int countList(LogisticTransportBillSearchVo search);

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    List<LogisticTransportBillVo> list(LogisticTransportBillSearchVo search);

    /**
     * 账单状态更新
     * @param billPaid 账单支付信息
     */
    void updatePaidBySettlementId(BillPaidVo billPaid);

    /**
     * 根据订单id更新支付信息
     * @param billPaid 账单支付信息
     */
    void updatePaidByOrderId(BillPaidVo billPaid);

    /**
     * 根据结算单统计
     * @param settlementId 结算单id
     * @return 返回结算单统计
     */
    LogisticTransportBillSettlementDetailVo statisticBySettlement(Long settlementId);
}

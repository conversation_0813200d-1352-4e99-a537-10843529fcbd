package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.LogisticStatisticsDayReport;
import com.senox.tms.vo.LogisticStatisticsDayReportSearchVo;
import com.senox.tms.vo.LogisticStatisticsDayReportVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/19 10:27
 */
@Mapper
public interface LogisticStatisticsDayReportMapper extends BaseMapper<LogisticStatisticsDayReport> {

    /**
     * 货物统计报表总数
     * @param searchVo
     * @return
     */
    int countLogisticStatisticsDayReport(LogisticStatisticsDayReportSearchVo searchVo);

    /**
     * 货物统计报表列表
     * @param searchVo
     * @return
     */
    List<LogisticStatisticsDayReportVo> listLogisticStatisticsDayReport(LogisticStatisticsDayReportSearchVo searchVo);

    /**
     * 货物统计报表合计
     * @param searchVo
     * @return
     */
    LogisticStatisticsDayReportVo sumLogisticStatisticsDayReport(LogisticStatisticsDayReportSearchVo searchVo);

    /**
     * 根据物流单号查询货物统计
     * @param logisticsNoList
     * @return
     */
    List<LogisticStatisticsDayReport> listByLogisticStatisticsDayReport(@Param("logisticsNoList") List<String> logisticsNoList);
}

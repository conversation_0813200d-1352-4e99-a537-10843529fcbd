package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicycleDeliveryOrderDetail;
import com.senox.tms.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20 8:50
 */
@Mapper
public interface BicycleDeliveryOrderDetailMapper extends BaseMapper<BicycleDeliveryOrderDetail> {

    /**
     * 根据id获取配送详细单及任务信息
     * @param id
     * @return
     */
    BicycleDeliveryOrderDetailVo findDeliveryOrderDetailById(@Param("id") Long id);

    /**
     * 根据订单流水号获取配送详细单及任务信息
     * @param orderSerialNo
     * @return
     */
    List<BicycleDeliveryOrderDetailVo> findDeliveryOrderDetailByOrderSerialNo(@Param("orderSerialNo") String orderSerialNo);

    /**
     * 查询骑手当日当月统计
     * @param riderId
     * @return
     */
    BicycleRiderCountVo riderCountByRiderId(@Param("riderId") Long riderId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询骑手当日当月统计数量
     * @param searchVo
     * @return
     */
    int riderCount(BicycleRiderCountSearchVo searchVo);

    /**
     * 查询骑手当日当月统计列表
     * @param searchVo
     * @return
     */
    List<BicycleRiderCountVo> riderCountList(BicycleRiderCountSearchVo searchVo);

    /**
     * 骑手当日统计合计
     * @param searchVo
     * @return
     */
    BicycleRiderCountVo sumRiderCount(BicycleRiderCountSearchVo searchVo);

    /**
     * 骑手历史统计合计
     * @param searchVo
     * @return
     */
    int riderHistoryCount(BicycleRiderCountSearchVo searchVo);

    /**
     * 骑手配送历史统计列表
     * @param searchVo
     * @return
     */
    List<BicycleRiderCountVo> riderHistoryCountList(BicycleRiderCountSearchVo searchVo);

    /**
     * 骑手历史配送统计合计
     * @param searchVo
     * @return
     */
    BicycleRiderCountVo sumRiderHistoryCount(BicycleRiderCountSearchVo searchVo);

    /**
     * 当天最佳骑手
     * @param startTime
     * @param endTime
     * @return
     */
    List<BicycleDayBestRiderVo> listDayBestRider(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 处理中和处理完成订单统计
     * @param searchVo
     * @return
     */
    List<BicycleOrderCountVo> listOrderDoingAndDoneCount(BicycleStatisticsSearchVo searchVo);

    /**
     * 未处理订单统计
     * @param searchVo
     * @return
     */
    List<BicycleOrderCountVo> listOrderUnDoCount(BicycleStatisticsSearchVo searchVo);

    /**
     * 查询骑手未处理完的配送单
     * @param riderId
     * @return
     */
    Integer undoDeliveryOrderCount(@Param("riderId") Long riderId);

    /**
     * 获取最大的子配送单号
     * @param prefix
     * @return
     */
    String findMaxDeliveryOrderItemNo(String prefix);

    /**
     * 配送详细单总数
     * @param searchVo
     * @return
     */
    int countDeliveryDetail(BicycleDeliverySearchVo searchVo);

    /**
     * 配送详细单列表
     * @param searchVo
     * @return
     */
    List<BicycleDeliveryOrderDetailVo> listDeliveryDetail(BicycleDeliverySearchVo searchVo);

    /**
     * 根据订单编号查询配送订单详细信息
     * @param orderSerialNo
     * @return
     */
    List<BicycleDeliveryDetailInfoVo> deliveryDetailInfoByOrderSerialNo(@Param("orderSerialNo") String orderSerialNo);
}

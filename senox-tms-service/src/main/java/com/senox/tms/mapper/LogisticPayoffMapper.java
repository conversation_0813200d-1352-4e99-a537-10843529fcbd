package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.LogisticPayoff;
import com.senox.tms.vo.LogisticPayoffSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/15 11:29
 */
@Mapper
@Repository
public interface LogisticPayoffMapper extends BaseMapper<LogisticPayoff> {

    /**
     * 更新应付合计
     * @param ids
     * @return
     */
    int updatePayoffTotalAmount(@Param("ids") List<Long> ids);

    /**
     * 根据账单日和商户列表查询应付
     * @param list
     * @return
     */
    List<LogisticPayoff> listByBillDateAndMerchant(List<LogisticPayoff> list);

    /**
     * 应付记录数合计
     * @param search
     * @return
     */
    int countPayoff(LogisticPayoffSearchVo search);

    /**
     * 应付统计
     * @param search
     * @return
     */
    LogisticPayoff sumPayoff(LogisticPayoffSearchVo search);

    /**
     * 应付记录列表
     * @param search
     * @return
     */
    List<LogisticPayoff> listPayoff(LogisticPayoffSearchVo search);
}

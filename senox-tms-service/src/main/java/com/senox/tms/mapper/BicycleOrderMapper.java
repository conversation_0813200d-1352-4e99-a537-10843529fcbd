package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18 10:11
 */
@Mapper
public interface BicycleOrderMapper extends BaseMapper<BicycleOrder> {

    /**
     * 获取最大的订单号
     * @param prefix
     * @return
     */
    String findMaxOrderNo(String prefix);


    /**
     * 根据id查询配送单及货物明细
     * @param id
     * @param containStatus
     * @return
     */
    BicycleOrderVo findOrderVoById(@Param("id") Long id, @Param("containStatus") Boolean containStatus);

    /**
     * 根据id查询配送单及货物明细
     * @param orderSerialNo
     * @return
     */
    BicycleOrderVo findOrderVoByOrderSerialNo(@Param("orderSerialNo") String orderSerialNo);

    /**
     * 未分派订单合计
     * @param search
     * @return
     */
    BicycleOrderVo sumOrderUnassigned(BicycleOrderSearchVo search);

    /**
     * 未分派订单数合计
     * @param search
     * @return
     */
    int countOrderUnassigned(BicycleOrderSearchVo search);

    /**
     * 未分派订单列表
     * @param search
     * @return
     */
    List<BicycleOrderVo> listOrderUnassigned(BicycleOrderSearchVo search);

    /**
     * 已分派订单数合计
     * @param search
     * @return
     */
    int countOrderAssigned(BicycleOrderSearchVo search);

    /**
     * 已分派订单
     * @param search
     * @return
     */
    List<BicycleOrderVo> listOrderAssigned(BicycleOrderSearchVo search);

    /**
     * 订单合计数
     * @param searchVo
     * @return
     */
    int count(BicycleOrderSearchVo searchVo);

    /**
     * 订单列表
     * @param searchVo
     * @return
     */
    List<BicycleOrderVo> listOrder(BicycleOrderSearchVo searchVo);

    /**
     * 订单合计
     * @param searchVo
     * @return
     */
    BicycleOrderVo sumOrder(BicycleOrderSearchVo searchVo);

    /**
     * 订单合计
     * @param search
     * @return
     */
    BicycleOrderVo sumOrders(BicycleOrderSearchVo search);

    /**
     * 订单合计数
     * @param searchVo
     * @return
     */
    int assignCount(BicycleOrderSearchVo searchVo);

    /**
     * 运营分析
     * @param startTime
     * @param endTime
     * @return
     */
    BicycleOperateAnalysisVo findOperateAnalysis(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 平均每单用时
     * @param startTime
     * @param endTime
     * @return
     */
    Integer avgSecond(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 查询最佳骑手
     * @param startTime
     * @param endTime
     * @return
     */
    BicycleOperateAnalysisVo findBestRider(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 查询最佳客户
     * @param startTime
     * @param endTime
     * @return
     */
    BicycleOperateAnalysisVo findBestCustomer(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 配送地点使用统计
     * @param startTime
     * @param endTime
     * @param isStart 查看终点还是起点
     * @return
     */
    List<BicyclePointCountVo> listPointCount(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime, @Param("isStart") Boolean isStart);

    /**
     * 下单数量列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<BicycleOrderCountRankingVo> listOrderCountRanking(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 下单件数列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<BicyclePiecesCountRankingVo> listPiecesCountRanking(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 下单金额列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<BicycleAmountCountRankingVo> listAmountCountRanking(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);

    /**
     * 未处理的订单数量
     * @return
     */
    Integer undoOrderCount();

    /**
     * 客户下单统计数量
     * @param searchVo
     * @return
     */
    int customerCount(BicycleCustomerCountSearchVo searchVo);

    /**
     * 客户下单统计列表
     * @param searchVo
     * @return
     */
    List<BicycleCustomerCountVo> customerCountList(BicycleCustomerCountSearchVo searchVo);

    /**
     * 客户下单统计合计
     * @param searchVo
     * @return
     */
    BicycleCustomerCountVo sumCustomerCount(BicycleCustomerCountSearchVo searchVo);


    /**
     * 查询订单统计数量情况
     * @param searchVo
     * @return
     */
    BicycleOrderCountVo orderCount(BicycleOrderCountSearchVo searchVo);

    /**
     * 查询分配的配送单
     * @param searchVo
     * @return
     */
    BicycleOrderCountVo deliveryOrderCount(BicycleOrderCountSearchVo searchVo);

    /**
     * 分配的订单集合
     * @param searchVo
     * @return
     */
    List<String> listAssignOrder(BicycleOrderSearchVo searchVo);

    /**
     * 根据配送单号查询配送单详细信息
     * @param deliveryOrderSerialList
     * @return
     */
    List<BicycleOrderVo> listByDeliveryOrderSerialList(@Param("deliveryOrderSerialList") List<String> deliveryOrderSerialList);

    /**
     * 根据配送单好查询订单详情
     * @param deliverySerials
     * @return
     */
    List<BicycleOrderVo> listOrdersByDeliverySerials(@Param("deliverySerials") List<String> deliverySerials);

    /**
     * 分配的最大配送状态
     * @param deliveryOrderSerialList
     * @return
     */
    List<BicycleOrder> assignMaxStatus(@Param("deliveryOrderSerialList") List<String> deliveryOrderSerialList);


    /**
     * 分配的最大配送状态
     * @param deliveryOrderSerialList
     * @return
     */
    List<BicycleOrder> assignMinStatus(@Param("deliveryOrderSerialList") List<String> deliveryOrderSerialList);

    /**
     * 导出订单列表
     * @param searchVo
     * @return
     */
    List<BicycleOrderVo> exportOrderList(BicycleOrderSearchVo searchVo);

    /**
     * 客户每月下单统计
     * @return
     */
    List<BicycleCustomerMonthInfoVo> customerMonthInfoList(BicycleCustomerMonthInfoSearchVo searchVo);

    /**
     * 查询商户有效单数
     * @param merchantId
     * @return
     */
    int countOrder(@Param("merchantId") Long merchantId);


    /**
     * 三轮车配送订单数量V2
     * @param searchVo
     * @return
     */
    int countOrderV2(BicycleOrderV2SearchVo searchVo);

    /**
     * 三轮车配送订单列表V2
     * @param searchVo
     * @return
     */
    List<BicycleOrderV2Vo> listOrderV2(BicycleOrderV2SearchVo searchVo);

    /**
     * 根据订单id查询骑手信息V2
     * @param orderIdList
     * @return
     */
    List<BicycleDeliveryOrderInfoV2Vo> listDeliveryOrderInfoV2ByOrderIdList(@Param("orderIdList") List<Long> orderIdList);

    /**
     * 三轮车订单分页列表V2
     * @param searchVo
     * @return
     */
    List<BicycleOrderV2Vo> listPageOrderV2(BicycleOrderV2SearchVo searchVo);

    /**
     * 三轮车配送订单分页数量V2
     * @param searchVo
     * @return
     */
    int countPageDeliveryOrderV2(BicycleOrderV2SearchVo searchVo);

    /**
     * 三轮车配送订单分页列表V2
     * @param searchVo
     * @return
     */
    List<BicycleDeliveryOrderV2Vo> listPageDeliveryOrderV2(BicycleOrderV2SearchVo searchVo);

    /**
     * 查询今日订单统计数量情况V2
     * @param searchVo
     * @return
     */
    BicycleOrderCountVo todayOrderCountV2(BicycleOrderCountSearchVo searchVo);

    /**
     * 查询订单统计数量情况V2
     * @param searchVo
     * @return
     */
    BicycleOrderCountVo orderCountV2(BicycleOrderCountSearchVo searchVo);


    /**
     * 三轮车订单总计V2
     * @param searchVo
     * @return
     */
    BicycleOrderV2Vo sumOrderV2(BicycleOrderV2SearchVo searchVo);
}

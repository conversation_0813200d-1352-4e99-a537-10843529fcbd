package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.LogisticTransportOrder;
import com.senox.tms.vo.LogisticTransportOrderSearchVo;
import com.senox.tms.vo.LogisticTransportOrderStatisticsVo;
import com.senox.tms.vo.LogisticTransportOrderVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-05-20
 **/
public interface LogisticTransportOrderMapper extends BaseMapper<LogisticTransportOrder> {

    /**
     * 批量添加订单
     * @param orders 订单集
     */
    void addBatch(List<LogisticTransportOrder> orders);

    /**
     * 批量更新订单
     * @param orders 订单集
     */
    void updateBatch(List<LogisticTransportOrder> orders);

    /**
     * 列表统计查询
     *
     * @param search 查询参数
     * @return 返回统计数
     */
    int countList(LogisticTransportOrderSearchVo search);

    /**
     * 列表查询
     *
     * @param search 查询参数
     * @return 返回查询到的列表
     */
    List<LogisticTransportOrderVo> list(LogisticTransportOrderSearchVo search);

    /**
     * 订单统计
     * @param search 查询参数
     * @return 返回订单统计
     */
    LogisticTransportOrderStatisticsVo orderStatistics(LogisticTransportOrderSearchVo search);

}

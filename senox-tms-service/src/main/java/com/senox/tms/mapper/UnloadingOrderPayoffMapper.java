package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingOrderPayoff;
import com.senox.tms.vo.UnloadingDayBestWorkerVo;
import com.senox.tms.vo.UnloadingOrderPayoffSearchVo;
import com.senox.tms.vo.UnloadingOrderPayoffVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 10:20
 */
@Mapper
public interface UnloadingOrderPayoffMapper extends BaseMapper<UnloadingOrderPayoff> {

    /**
     * 统计数量
     * @param searchVo
     * @return
     */
    int countPayoff(UnloadingOrderPayoffSearchVo searchVo);

    /**
     * 应付合计
     * @param searchVo
     * @return
     */
    UnloadingOrderPayoff sumPayoff(UnloadingOrderPayoffSearchVo searchVo);

    /**
     * 列表
     * @param searchVo
     * @return
     */
    List<UnloadingOrderPayoffVo> listPayoff(UnloadingOrderPayoffSearchVo searchVo);


    /**
     * 搬运工应付日信息
     * @param searchVo
     * @return
     */
    List<UnloadingOrderPayoffVo> payoffDailyList(UnloadingOrderPayoffSearchVo searchVo);


    /**
     * 当天最佳搬运工
     * @param startTime
     * @param endTime
     * @return
     */
    List<UnloadingDayBestWorkerVo> listBestWorker(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}

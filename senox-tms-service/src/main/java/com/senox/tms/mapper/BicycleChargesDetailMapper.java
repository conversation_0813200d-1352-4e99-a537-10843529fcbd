package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicycleChargesDetail;
import com.senox.tms.vo.BicycleChargesDetailVo;

import java.util.List;

/**
 * 收费标准明细 mapper
 *
 * <AUTHOR>
 * @date 2023-9-13
 */
public interface BicycleChargesDetailMapper extends BaseMapper<BicycleChargesDetail> {

    /**
     * 根据收费标准查询明细列表
     *
     * @param chargesId 收费标准
     * @return 查询到的明细列表
     */
    List<BicycleChargesDetailVo> listByCharges(Long chargesId);
}

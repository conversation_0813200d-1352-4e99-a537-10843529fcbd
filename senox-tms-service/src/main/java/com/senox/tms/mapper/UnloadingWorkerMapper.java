package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.common.vo.DataDelDto;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.vo.UnloadingDayCountWorkerVo;
import com.senox.tms.vo.UnloadingWorkerSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/13 13:38
 */
@Mapper
public interface UnloadingWorkerMapper extends BaseMapper<UnloadingWorker> {

    /**
     * 获取最大的搬运工编号
     * @return
     */
    int getMaxOrderNum();

    /**
     * 根据序号筛选列表
     * @param orderNumStart
     * @param orderNumEnd
     * @return
     */
    List<UnloadingWorker> filterListWorker(@Param("orderNumStart") Integer orderNumStart, @Param("orderNumEnd") Integer orderNumEnd);

    /**
     * 获取最大搬运工标识
     * @param prefix
     * @return
     */
    String findMaxWorkerSign(String prefix);

    /**
     * 搬运工数量
     * @param searchVo
     * @return
     */
    int countWorker(UnloadingWorkerSearchVo searchVo);

    /**
     * 搬运工列表
     * @param searchVo
     * @return
     */
    List<UnloadingWorker> listWorker(UnloadingWorkerSearchVo searchVo);

    /**
     * 搬运工顺序列表
     * @param searchVo
     * @return
     */
    List<UnloadingWorker> listSequenceWorker(UnloadingWorkerSearchVo searchVo);

    /**
     * 查询当前轮次第一个搬运工
     * @param roundNum
     * @return
     */
    UnloadingWorker findFirstWorker(@Param("roundNum") int roundNum);

    /**
     * 查询当前轮次最后一个搬运工
     * @param roundNum
     * @return
     */
    UnloadingWorker findLastWorker(@Param("roundNum") int roundNum);

    /**
     * 所有被惩罚的搬运工
     * @return
     */
    List<UnloadingWorker> listPunishWorker();

    /**
     * 搬运工信息列表
     * @return
     */
    UnloadingDayCountWorkerVo dayCountWorker();

    /**
     * 根据订单id查询该单参与的搬运工
     * @param orderId
     * @return
     */
    List<UnloadingWorker> listWorkerByOrderId(@Param("orderId") Long orderId);

    /**
     * 逻辑删除搬运工
     * @param delData 删除数据DTO
     * @return 影响行数
     */
    int deleteWorker(DataDelDto delData);
}

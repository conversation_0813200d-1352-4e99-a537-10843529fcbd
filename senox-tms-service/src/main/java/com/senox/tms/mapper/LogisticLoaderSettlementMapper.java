package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.LogisticLoaderSettlement;
import com.senox.tms.vo.LogisticLoaderSettlementSearchVo;
import com.senox.tms.vo.LogisticLoaderSettlementVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
public interface LogisticLoaderSettlementMapper extends BaseMapper<LogisticLoaderSettlement> {

    /**
     * 批量添加
     *
     * @param settlements 结算集
     * @return 影响条
     */
    int addBatch(Collection<LogisticLoaderSettlement> settlements);

    /**
     * 批量更新
     *
     * @param settlements 结算集
     * @return 影响条
     */
    int updateBatch(Collection<LogisticLoaderSettlement> settlements);

    /**
     * 根据id获取结算
     *
     * @param id id
     * @return 返回获取到的结算
     */
    LogisticLoaderSettlementVo getById(Long id);

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    List<LogisticLoaderSettlementVo> list(LogisticLoaderSettlementSearchVo searchVo);

    /**
     * 列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    int countList(LogisticLoaderSettlementSearchVo searchVo);

    /**
     * 列表金额合计
     *
     * @param searchVo 查询
     * @return 返回合计
     */
    BigDecimal listTotalAmount(LogisticLoaderSettlementSearchVo searchVo);

}

package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicycleOperateAnalysis;
import com.senox.tms.vo.BicycleOperateAnalysisSearchVo;
import com.senox.tms.vo.BicycleOperateAnalysisVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/12 14:48
 */
@Mapper
public interface BicycleOperateAnalysisMapper extends BaseMapper<BicycleOperateAnalysis> {

    /**
     * 运营分析记录总数
     * @param searchVo
     * @return
     */
    int count(BicycleOperateAnalysisSearchVo searchVo);

    /**
     * 运营分析记录列表
     * @param searchVo
     * @return
     */
    List<BicycleOperateAnalysisVo> listOperateAnalysis(BicycleOperateAnalysisSearchVo searchVo);

    /**
     * 运营分析合计
     * @param searchVo
     * @return
     */
    BicycleOperateAnalysis sumOperateAnalysis(BicycleOperateAnalysisSearchVo searchVo);
}

package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.constant.DictLogisticCategory;
import com.senox.tms.domain.LogisticLoaderIncome;
import com.senox.tms.vo.LogisticLoaderIncomeSearchVo;
import com.senox.tms.vo.LogisticLoaderIncomeVo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-28
 */
public interface LogisticLoaderIncomeMapper extends BaseMapper<LogisticLoaderIncome> {

    /**
     * 批量添加
     *
     * @param incomes 收益集
     * @return 影响条
     */
    int addBatch(Collection<LogisticLoaderIncome> incomes);

    /**
     * 批量更改
     *
     * @param incomes 收益集
     * @return 影响条
     */
    int updateBatch(Collection<LogisticLoaderIncome> incomes);

    /**
     * 列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    List<LogisticLoaderIncomeVo> list(LogisticLoaderIncomeSearchVo searchVo);

    /**
     * 列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    int countList(LogisticLoaderIncomeSearchVo searchVo);

    /**
     * 日统计
     * @param category 类型
     * @param searchVo 查询参数
     * @return 返回统计结果列表
     */
    List<LogisticLoaderIncomeVo> statisticsOfDay(DictLogisticCategory category, LogisticLoaderIncomeSearchVo searchVo);

    /**
     * 批量保存收益统计
     * @param incomes 收益集
     */
    void incomeStatisticsAddBatch(Collection<LogisticLoaderIncomeVo> incomes);

    /**
     * 批量更新收益统计
     * @param incomes 收益集
     */
    void incomeStatisticsUpdateBatch(Collection<LogisticLoaderIncomeVo> incomes);

    /**
     * 批量删除收益统计
     * @param incomes 收益集
     */
    void incomeStatisticsRemoveBatch(Collection<LogisticLoaderIncomeVo> incomes);

    /**
     * 收益统计列表
     *
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    List<LogisticLoaderIncomeVo> listStatistics(LogisticLoaderIncomeSearchVo searchVo);

    /**
     * 收益统计列表总数
     *
     * @param searchVo 查询
     * @return 返回总数
     */
    int listCountStatistics(LogisticLoaderIncomeSearchVo searchVo);

    /**
     * 列表金额合计
     * @return 返回合计
     */
    BigDecimal listTotalAmount();
}

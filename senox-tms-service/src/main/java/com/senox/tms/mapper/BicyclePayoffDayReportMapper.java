package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicyclePayoffDayReport;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * 应付日账单报表 mapper
 * <AUTHOR>
 * @date 2023-10-13
 */
public interface BicyclePayoffDayReportMapper extends BaseMapper<BicyclePayoffDayReport> {

    /**
     * 列表金额合计
     * @return 返回合计
     */
    @Select("select sum(total_amount) from t_bicycle_payoff_day_report")
    BigDecimal listTotalAmount();

}

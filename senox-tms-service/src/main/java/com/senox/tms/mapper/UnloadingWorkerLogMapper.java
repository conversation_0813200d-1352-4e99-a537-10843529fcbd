package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.UnloadingWorkerLog;
import com.senox.tms.vo.UnloadingWorkerLogSearchVo;
import com.senox.tms.vo.UnloadingWorkerLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24 8:39
 */
@Mapper
public interface UnloadingWorkerLogMapper extends BaseMapper<UnloadingWorkerLog> {

    /**
     * 根据id查询搬运工异常日志
     * @param id
     * @return
     */
    UnloadingWorkerLogVo findWorkerLogById(@Param("id") Long id);

    /**
     * 搬运工异常日志统计
     * @param searchVo
     * @return
     */
    int countWorkerLog(UnloadingWorkerLogSearchVo searchVo);

    /**
     * 搬运工异常日志列表
     * @param searchVo
     * @return
     */
    List<UnloadingWorkerLogVo> listWorkerLog(UnloadingWorkerLogSearchVo searchVo);
}

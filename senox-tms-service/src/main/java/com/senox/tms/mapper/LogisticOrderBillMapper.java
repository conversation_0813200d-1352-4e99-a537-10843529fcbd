package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.LogisticOrderBill;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/27 16:13
 */
@Mapper
@Repository
public interface LogisticOrderBillMapper extends BaseMapper<LogisticOrderBill> {

    /**
     * 更新配送账单金额
     * @param idList
     * @return
     */
    int updateOrderBillAmount(List<Long> idList);

    /**
     * 更新账单金额
     * @param idList
     * @return
     */
    int updateBillAmount(List<Long> idList);

    /**
     * 根据配送日期跟新账单
     * @param shipDates
     * @return
     */
    int updateOrderBillAmountByShipDates(List<LocalDate> shipDates);

    /**
     * 根据配送日期更新账单金额
     * @param shipDates
     * @return
     */
    int updateBillAmountByShipDates(List<LocalDate> shipDates);

    /**
     * 根据商品订单及配送订单查找账单
     * @param list
     * @return
     */
    List<LogisticOrderBill> listOrderBillByProductAndShip(List<LogisticOrderBill> list);
}

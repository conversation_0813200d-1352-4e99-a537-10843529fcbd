package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicycleOrderMonthReport;
import com.senox.tms.vo.BicycleOrderMonthReportSearchVo;
import com.senox.tms.vo.BicycleOrderMonthReportVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/17 9:12
 */
@Mapper
public interface BicycleOrderMonthReportMapper extends BaseMapper<BicycleOrderMonthReport> {

    /**
     * 月报表合计
     * @param searchVo
     * @return
     */
    BicycleOrderMonthReport sumMonthReport(BicycleOrderMonthReportSearchVo searchVo);

    /**
     * 月报表数统计
     * @param searchVo
     * @return
     */
    int countMonthReport(BicycleOrderMonthReportSearchVo searchVo);

    /**
     * 月报表列表
     * @param searchVo
     * @return
     */
    List<BicycleOrderMonthReportVo> listMonthReport(BicycleOrderMonthReportSearchVo searchVo);
}

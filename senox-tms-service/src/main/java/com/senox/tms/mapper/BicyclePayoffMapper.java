package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicyclePayoff;
import com.senox.tms.vo.BicyclePayoffSearchVo;
import com.senox.tms.vo.BicyclePayoffVo;

import java.util.List;

/**
 * 应付账单 mapper
 * <AUTHOR>
 * @date 2023-9-22
 */
public interface BicyclePayoffMapper extends BaseMapper<BicyclePayoff> {

    /**
     * 应收账单合计
     * @param searchVo
     * @return
     */
    BicyclePayoffVo sumPayoff(BicyclePayoffSearchVo searchVo);

    /**
     * 应付账单合计
     * @param searchVo
     * @return
     */
    int countPayoff(BicyclePayoffSearchVo searchVo);

    /**
     * 应付账单列表
     * @param searchVo
     * @return
     */
    List<BicyclePayoffVo> listPayoff(BicyclePayoffSearchVo searchVo);
}

package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicycleBill;
import com.senox.tms.vo.BicycleBillSearchVo;
import com.senox.tms.vo.BicycleBillVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/22 10:11
 */
@Mapper
public interface BicycleBillMapper extends BaseMapper<BicycleBill> {

    /**
     * 根据id查找应收账单
     * @param ids
     * @return
     */
    List<BicycleBillVo> listByIds(List<Long> ids);

    /**
     * 应收账单合计
     * @param searchVo
     * @return
     */
    BicycleBillVo sumBill(BicycleBillSearchVo searchVo);

    /**
     * 应收账单统计
     * @param searchVo
     * @return
     */
    int countBill(BicycleBillSearchVo searchVo);

    /**
     * 应收账单列表
     * @param searchVo
     * @return
     */
    List<BicycleBillVo> listBill(BicycleBillSearchVo searchVo);

    /**
     * 订单应收账单列表
     * @param searchVo
     * @return
     */
    List<BicycleBillVo> listOrderBill(BicycleBillSearchVo searchVo);


    /**
     * 根据订单流水号集查询应收账单列表
     * @param orderSerialNos 订单流水号集
     * @return 返回查询到的应收账单
     */
    List<BicycleBillVo> findByOrderSerialNos(List<String> orderSerialNos);

    /**
     * 根据结算单id查询账单列表
     * @param settlementId 结算单id
     * @return 返回查询到的账单
     */
    List<BicycleBill> billListBySettlementId(Long settlementId);

    /**
     * 更新结算单
     * @param bill 账单
     * @param settlementId 结算单id
     * @return 返回影响行
     */
    int updateSettlement(BicycleBill bill, Long settlementId);
}

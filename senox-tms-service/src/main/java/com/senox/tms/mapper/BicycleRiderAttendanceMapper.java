package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicycleRiderAttendance;
import com.senox.tms.vo.BicycleRiderAttendanceSearchVo;
import com.senox.tms.vo.BicycleRiderAttendanceVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/14 16:55
 */
@Mapper
public interface BicycleRiderAttendanceMapper extends BaseMapper<BicycleRiderAttendance> {

    /**
     * 三轮车骑手考勤合计
     * @param searchVo
     * @return
     */
    int count(BicycleRiderAttendanceSearchVo searchVo);

    /**
     * 三轮车骑手考勤列表
     * @param searchVo
     * @return
     */
    List<BicycleRiderAttendanceVo> listRiderAttendance(BicycleRiderAttendanceSearchVo searchVo);
}

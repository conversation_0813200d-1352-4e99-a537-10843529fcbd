package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.BicycleRider;
import com.senox.tms.vo.BicycleDayCountRiderVo;
import com.senox.tms.vo.BicycleRiderInfoVo;
import com.senox.tms.vo.BicycleRiderSearchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023/9/13 15:45
 */
@Mapper
public interface BicycleRiderMapper extends BaseMapper<BicycleRider> {

    /**
     * 获取最大的骑手编号
     * @param prefix
     * @return
     */
    String findMaxRiderNo(String prefix);

    /**
     * 骑手统计数
     * @param searchVo
     * @return
     */
    int countRider(BicycleRiderSearchVo searchVo);

    /**
     * 骑手列表
     * @param searchVo
     * @return
     */
    List<BicycleRider> listRider(BicycleRiderSearchVo searchVo);

    /**
     * 骑手情况
     * @return
     */
    BicycleDayCountRiderVo dayCountRider();

    /**
     * 骑手信息列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<BicycleRiderInfoVo> listRiderInfo(@Param("startTime") LocalDateTime startTime, @Param(("endTime")) LocalDateTime endTime);
}

package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.tms.domain.LogisticsDailyOrderDelivery;
import com.senox.tms.vo.LogisticsDailyOrderDeliveryTotalAmountVo;
import com.senox.tms.vo.LogisticsDailyOrderDeliverySearchVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-12-5
 */
public interface LogisticsDailyOrderDeliveryMapper extends BaseMapper<LogisticsDailyOrderDelivery> {

    /**
     * 列表查询
     * @param searchVo 查询
     * @return 返回查询到的列表
     */
    List<LogisticsDailyOrderDelivery> list(LogisticsDailyOrderDeliverySearchVo searchVo);

    /**
     * 列表统计查询
     *
     * @param searchVo 查询参数
     * @return 返回统计数
     */
    int countList(LogisticsDailyOrderDeliverySearchVo searchVo);

    /**
     * 列表金额合计
     * @param searchVo 查询
     * @return 返回合计
     */
    LogisticsDailyOrderDeliveryTotalAmountVo totalListAmount(LogisticsDailyOrderDeliverySearchVo searchVo);

    /**
     * 根据id查找
     *
     * @param id id
     * @return 返回查找到的配送
     */
    LogisticsDailyOrderDelivery findById(Long id);
}

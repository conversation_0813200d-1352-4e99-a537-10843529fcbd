package com.senox.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.senox.common.vo.BillPaidVo;
import com.senox.tms.domain.BicycleBillSettlement;
import com.senox.tms.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-8
 */
public interface BicycleBillSettlementMapper extends BaseMapper<BicycleBillSettlement> {

    /**
     * 根据id集查询结算列表
     * @param ids id集
     * @return 返回查询到的结算列表
     */
    List<BicycleBillSettlementVo> listByIds(List<Long> ids);

    /**
     * 批量添加结算单
     * @param billSettlements 结算单集
     */
    int addBatch(List<BicycleBillSettlement> billSettlements);

    /**
     * 批量更新结算单
     * @param billSettlements 结算单集
     */
    void updateBatch(List<BicycleBillSettlement> billSettlements);

    /**
     * 列表查询
     *
     * @param searchVo 查询参数
     * @return 返回查询到的列表
     */
    List<BicycleBillSettlementVo> list(BicycleBillSettlementSearchVo searchVo);

    /**
     * 列表统计查询
     *
     * @param searchVo 查询参数
     * @return 返回统计数
     */
    int countList(BicycleBillSettlementSearchVo searchVo);

    /**
     * 桀算单支付更新
     * @param billPaidVo 账单支付信息
     */
    int updatePaid(BillPaidVo billPaidVo);

    /**
     * 根据结算单id查询详情
     * @param settlementId 结算单id
     * @return 返回查询到的详情
     */
    List<BicycleBillVo> detailBySettlement(Long settlementId);

    /**
     * 结算单下发
     *
     * @param sendVo 下发参数
     */
    void send(BicycleBillSettlementSendVo sendVo);

    /**
     * 列表统计
     * @param searchVo 查询参数
     * @return 返回统计
     */
    BicycleBillSettlementDetailVo listCount(BicycleBillSettlementSearchVo searchVo);

    /**
     * 根据id查询结算单
     *
     * @param id id
     * @return 返回结算单
     */
    BicycleBillSettlementVo findById(Long id);

    /**
     * 根据结算单id统计详情
     *
     * @param settlementId 结算单id
     * @return 返回统计详情
     */
    BicycleBillSettlementDetailVo countDetailByDetailBySettlement(Long settlementId);
}

package com.senox.tms.handler;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.lang.reflect.Method;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2023-11-29
 */
@Slf4j
public class GenericEnumTypeHandler<E extends Enum<E>> extends BaseTypeHandler<E> {

    private final Class<E> type;

    public GenericEnumTypeHandler(Class<E> type) {
        this.type = type;
    }

    @SneakyThrows
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, E parameter, JdbcType jdbcType) {
        Method getNumberMethod = type.getMethod("getNumber");
        int number = (int) getNumberMethod.invoke(parameter);
        ps.setInt(i, number);
    }

    @SneakyThrows
    @Override
    public E getNullableResult(ResultSet rs, String columnName) {
        return getEnumValue(rs.getInt(columnName));
    }

    @Override
    public E getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getEnumValue(rs.getInt(columnIndex));
    }

    @Override
    public E getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getEnumValue(cs.getInt(columnIndex));
    }

    @SneakyThrows
    private E getEnumValue(int number) {
        Method valuesMethod = type.getMethod("values");
        Enum<?>[] values = (Enum<?>[]) valuesMethod.invoke(type);

        for (Enum<?> value : values) {
            Method getNumberMethod = value.getClass().getMethod("getNumber");
            if (number == (int) getNumberMethod.invoke(value)) {
                return type.cast(value);
            }
        }
        return null;
    }
}

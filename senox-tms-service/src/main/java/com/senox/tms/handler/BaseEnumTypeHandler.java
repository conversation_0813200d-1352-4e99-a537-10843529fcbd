package com.senox.tms.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.lang.reflect.Method;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2023-11-14
 */
public class BaseEnumTypeHandler<T extends Enum<T>> extends BaseTypeHandler<T> {

    private static final String FROM_NUMBER = "fromNumber";
    private static final String GET_NUMBER = "getNumber";

    private final Class<T> type;

    public BaseEnumTypeHandler(Class<T> type) {
        this.type = type;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, getNumber());
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        int value = rs.getInt(columnName);
        return rs.wasNull() ? null : fromNumber(value);
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int value = rs.getInt(columnIndex);
        return rs.wasNull() ? null : fromNumber(value);
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int value = cs.getInt(columnIndex);
        return cs.wasNull() ? null : fromNumber(value);
    }

    private T fromNumber(int value) {
        try {
            Method fromNumberMethod = type.getMethod(FROM_NUMBER, int.class);
            Object result = fromNumberMethod.invoke(null, value);
            return type.cast(result);
        } catch (Exception e) {
            e.fillInStackTrace();
        }
        return null;
    }

    private int getNumber() {
        try {
            Method fromNumberMethod = type.getMethod(GET_NUMBER);
            return (int) fromNumberMethod.invoke(type);
        } catch (Exception e) {
            e.fillInStackTrace();
        }
        return -1;
    }
}

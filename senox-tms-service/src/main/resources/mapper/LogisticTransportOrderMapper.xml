<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticTransportOrderMapper">

    <resultMap id="resultMap" type="com.senox.tms.vo.LogisticTransportOrderVo">
        <id property="id" column="id"/>
        <result property="serialNo" column="serial_no"/>
        <result property="yearMonthDay" column="year_month_day"/>
        <result property="consignorId" column="consignor_id"/>
        <result property="consignorName" column="consignor_name"/>
        <result property="consignorCode" column="consignor_code"/>
        <result property="consignorPhone" column="consignor_phone"/>
        <result property="consigneeName" column="consignee_name"/>
        <result property="consigneePhone" column="consignee_phone"/>
        <result property="category" column="category" typeHandler="com.senox.tms.handler.GenericEnumTypeHandler"/>
        <result property="driverName" column="driver_name"/>
        <result property="driverPhone" column="driver_phone"/>
        <result property="licensePlateNumber" column="license_plate_number"/>
        <result property="charter" column="is_charter"/>
        <result property="departureStation" column="departure_station"/>
        <result property="destinationStation" column="destination_station"/>
        <result property="pieces" column="pieces"/>
        <result property="loadingWeight" column="loading_weight"/>
        <result property="freightCharge" column="freight_charge"/>
        <result property="otherCharge" column="other_charge"/>
        <result property="receivableFreightCharge" column="receivable_freight_charge"/>
        <result property="payer" column="payer" typeHandler="com.senox.tms.handler.GenericEnumTypeHandler"/>
        <result property="hasBill" column="has_bill"/>
        <result property="payStatus" column="pay_status"/>
        <result property="paidTime" column="paid_time"/>
        <result property="remark" column="remark"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="auditorId" column="auditor_id"/>
        <result property="auditorName" column="auditor_name"/>
        <result property="auditTime" column="audit_time"/>
        <result property="creatorName" column="creator_name"/>
        <result property="createTime" column="create_time"/>
        <result property="modifierName" column="modifier_name"/>
        <result property="modifiedTime" column="modified_time"/>
    </resultMap>

    <insert id="addBatch" useGeneratedKeys="true" keyProperty="id">
        insert into t_logistic_transport_order (serial_no, year_month_day, consignor_id, consignor_name, consignor_code,
        consignor_phone, consignee_name, consignee_phone, category, driver_name, driver_phone,
        license_plate_number, is_charter, departure_station,
        destination_station, pieces, loading_weight, freight_charge,other_charge,receivable_freight_charge, payer,
        remark, creator_id, creator_name, create_time, modifier_id,
        modifier_name, modified_time)
        values
        <foreach collection="orders" item="item" separator=",">
            (#{item.serialNo},#{item.yearMonthDay},#{item.consignorId},#{item.consignorName},#{item.consignorCode}
            ,#{item.consignorPhone},#{item.consigneeName},#{item.consigneePhone},#{item.category}
            ,#{item.driverName},#{item.driverPhone},#{item.licensePlateNumber},#{item.charter}
            ,#{item.departureStation},#{item.destinationStation},#{item.pieces}
            ,#{item.loadingWeight},#{item.freightCharge},#{item.otherCharge}
            ,#{item.receivableFreightCharge},#{item.payer},#{item.remark}
            ,#{item.creatorId},#{item.creatorName},#{item.createTime}
            ,#{item.modifierId}, #{item.modifierName},#{item.modifiedTime})
        </foreach>
    </insert>

    <update id="updateBatch">
        update t_logistic_transport_order
        <set>
            <trim prefix=", consignor_id = case" suffix="else consignor_id end">
                <foreach collection="orders" item="item">
                    <if test="null != item.consignorId">
                        when serial_no = #{item.serialNo} then #{item.consignorId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", consignor_name = case" suffix="else consignor_name end">
                <foreach collection="orders" item="item">
                    <if test="null != item.consignorName and item.consignorName != ''">
                        when serial_no = #{item.serialNo} then #{item.consignorName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", consignor_code = case" suffix="else consignor_code end">
                <foreach collection="orders" item="item">
                    <if test="null != item.consignorCode and item.consignorCode != ''">
                        when serial_no = #{item.serialNo} then #{item.consignorCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", consignor_phone = case" suffix="else consignor_phone end">
                <foreach collection="orders" item="item">
                    <if test="null != item.consignorPhone and item.consignorPhone != ''">
                        when serial_no = #{item.serialNo} then #{item.consignorPhone}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", consignee_name = case" suffix="else consignee_name end">
                <foreach collection="orders" item="item">
                    <if test="null != item.consigneeName and item.consigneeName != ''">
                        when serial_no = #{item.serialNo} then #{item.consigneeName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", consignee_phone = case" suffix="else consignee_phone end">
                <foreach collection="orders" item="item">
                    <if test="null != item.consigneePhone and item.consigneePhone != ''">
                        when serial_no = #{item.serialNo} then #{item.consigneePhone}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", category = case" suffix="else category end">
                <foreach collection="orders" item="item">
                    <if test="null != item.category">
                        when serial_no = #{item.serialNo} then #{item.category}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", driver_name = case" suffix="else driver_name end">
                <foreach collection="orders" item="item">
                    <if test="null != item.driverName and item.driverName != ''">
                        when serial_no = #{item.serialNo} then #{item.driverName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", driver_phone = case" suffix="else driver_phone end">
                <foreach collection="orders" item="item">
                    <if test="null != item.driverPhone and item.driverPhone != ''">
                        when serial_no = #{item.serialNo} then #{item.driverPhone}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", license_plate_number = case" suffix="else license_plate_number end">
                <foreach collection="orders" item="item">
                    <if test="null != item.licensePlateNumber and item.licensePlateNumber != ''">
                        when serial_no = #{item.serialNo} then #{item.licensePlateNumber}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", is_charter = case" suffix="else is_charter end">
                <foreach collection="orders" item="item">
                    <if test="null != item.charter">
                        when serial_no = #{item.serialNo} then #{item.charter}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", departure_station = case" suffix="else departure_station end">
                <foreach collection="orders" item="item">
                    <if test="null != item.departureStation and item.departureStation != ''">
                        when serial_no = #{item.serialNo} then #{item.departureStation}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", destination_station = case" suffix="else destination_station end">
                <foreach collection="orders" item="item">
                    <if test="null != item.destinationStation and item.destinationStation != ''">
                        when serial_no = #{item.serialNo} then #{item.destinationStation}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", pieces = case" suffix="else pieces end">
                <foreach collection="orders" item="item">
                    <if test="null != item.pieces">
                        when serial_no = #{item.serialNo} then #{item.pieces}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", loading_weight = case" suffix="else loading_weight end">
                <foreach collection="orders" item="item">
                    <if test="null != item.loadingWeight">
                        when serial_no = #{item.serialNo} then #{item.loadingWeight}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", freight_charge = case" suffix="else freight_charge end">
                <foreach collection="orders" item="item">
                    <if test="null != item.freightCharge">
                        when serial_no = #{item.serialNo} then #{item.freightCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", other_charge = case" suffix="else other_charge end">
                <foreach collection="orders" item="item">
                    <if test="null != item.otherCharge">
                        when serial_no = #{item.serialNo} then #{item.otherCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", receivable_freight_charge = case" suffix="else receivable_freight_charge end">
                <foreach collection="orders" item="item">
                    <if test="null != item.receivableFreightCharge">
                        when serial_no = #{item.serialNo} then #{item.receivableFreightCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", payer = case" suffix="else payer end">
                <foreach collection="orders" item="item">
                    <if test="null != item.payer">
                        when serial_no = #{item.serialNo} then #{item.payer}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", remark = case" suffix="else remark end">
                <foreach collection="orders" item="item">
                    <if test="null != item.remark and item.remark != ''">
                        when serial_no = #{item.serialNo} then #{item.remark}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", audit_status = case" suffix="else audit_status end">
                <foreach collection="orders" item="item">
                    <if test="null != item.auditStatus">
                        <choose>
                            <when test="item.auditStatus == false">
                                when serial_no = #{item.serialNo} then false
                            </when>
                            <otherwise>when serial_no = #{item.serialNo} then true</otherwise>
                        </choose>
                    </if>
                </foreach>
            </trim>
            <trim prefix=", auditor_id = case" suffix="else auditor_id end">
                <foreach collection="orders" item="item">
                    <if test="null != item.auditorId">
                        when serial_no = #{item.serialNo} then #{item.auditorId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", auditor_name = case" suffix="else auditor_name end">
                <foreach collection="orders" item="item">
                    <if test="null != item.auditorName">
                        when serial_no = #{item.serialNo} then #{item.auditorName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", audit_time = case" suffix="else audit_time end">
                <foreach collection="orders" item="item">
                    <if test="item.auditStatus = 0 or null != item.auditTime">
                        when serial_no = #{item.serialNo} then #{item.auditTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", modifier_id = case" suffix="else modifier_id end">
                <foreach collection="orders" item="item">
                    <if test="null != item.modifierId">
                        when serial_no = #{item.serialNo} then #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", modifier_name = case" suffix="else modifier_name end">
                <foreach collection="orders" item="item">
                    <if test="null != item.modifierName">
                        when serial_no = #{item.serialNo} then #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", modified_time = case" suffix="else modified_time end">
                <foreach collection="orders" item="item">
                    <if test="null != item.modifiedTime">
                        when serial_no = #{item.serialNo} then #{item.modifiedTime}
                    </if>
                </foreach>
            </trim>
        </set>
        <where>
            serial_no in
            <foreach collection="orders" item="item" open="(" close=")" separator=",">
                #{item.serialNo}
            </foreach>
        </where>
    </update>

    <select id="countList" resultType="int">
        select count(*)
        from t_logistic_transport_order lto
        left join t_logistic_transport_bill ltb on ltb.order_serial_no = lto.serial_no
        <where>
            <if test="null != ids and ids.size() > 0">
                and lto.id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != serialNos and serialNos.size() > 0">
                and lto.serial_no in
                <foreach collection="serialNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != startDate">
                and lto.year_month_day >= #{startDate}
            </if>
            <if test="null != endDate">
                and lto.year_month_day &lt;= #{endDate}
            </if>
            <if test="null != consignorName and consignorName != ''">
                and lto.consignor_name like concat('%', #{consignorName}, '%')
            </if>
            <if test="null != consignorCode and consignorCode != ''">
                and lto.consignor_code like concat('%', #{consignorCode}, '%')
            </if>
            <if test="null != consignorCodes and consignorCodes.size() > 0">
                and lto.consignor_code in
                <foreach collection="consignorCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != hasBill">
                <choose>
                    <when test="hasBill == true">
                        and if(ltb.id is not null, 1, 0) = 1
                    </when>
                    <otherwise>
                        and if(ltb.id is not null, 1, 0) = 0
                    </otherwise>
                </choose>
            </if>
            <if test="null != category">
                and lto.category = #{category.number}
            </if>
            <if test="null != departureStation and departureStation != ''">
                and lto.departure_station like concat('%', #{departureStation}, '%')
            </if>
            <if test="null != destinationStation and destinationStation != ''">
                and lto.destination_station like concat('%', #{destinationStation}, '%')
            </if>
            <if test="null != driverName and driverName != ''">
                and lto.driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="null != driverPhone and driverPhone != ''">
                and lto.driver_phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="null != licensePlateNumber and licensePlateNumber != ''">
                and lto.license_plate_number like concat('%', #{licensePlateNumber}, '%')
            </if>
            <if test="null != licensePlateNumbers and licensePlateNumbers.size() > 0">
                and lto.license_plate_number in
                <foreach collection="licensePlateNumbers" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != charter">
                and lto.is_charter = #{charter}
            </if>
            <if test="null != payer">
                and lto.payer = #{payer.number}
            </if>
            <if test="null != auditStatus">
                and lto.audit_status = #{auditStatus}
            </if>
            <if test="null != paidStatus">
                and ifnull(ltb.status, 0) = #{paidStatus}
            </if>
            <if test="null != freightCharges and freightCharges.size() > 0">
                and lto.freight_charge in
                <foreach collection="freightCharges" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="list" resultMap="resultMap">
        select lto.id,
               lto.serial_no,
               lto.year_month_day,
               lto.consignor_id,
               lto.consignor_name,
               lto.consignor_code,
               lto.consignor_phone,
               lto.consignee_name,
               lto.consignee_phone,
               lto.category,
               lto.driver_name,
               lto.license_plate_number,
               lto.is_charter,
               lto.departure_station,
               lto.destination_station,
               lto.pieces,
               lto.loading_weight,
               lto.freight_charge,
               lto.other_charge,
               lto.receivable_freight_charge,
               lto.payer,
               lto.remark,
               lto.audit_status,
               lto.auditor_id,
               lto.auditor_name,
               lto.audit_time,
               lto.creator_id,
               lto.creator_name,
               lto.create_time,
               lto.modifier_id,
               lto.modifier_name,
               lto.modified_time,
               if(ltb.id is not null, 1, 0) as has_bill,
               ifnull(ltb.status,0) as pay_status,
               ltb.paid_time as paid_time
        from t_logistic_transport_order lto
                left join t_logistic_transport_bill ltb on ltb.order_serial_no = lto.serial_no
        <where>
            <if test="null != ids and ids.size() > 0">
                and lto.id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != serialNos and serialNos.size() > 0">
                and lto.serial_no in
                <foreach collection="serialNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != startDate">
                and lto.year_month_day >= #{startDate}
            </if>
            <if test="null != endDate">
                and lto.year_month_day &lt;= #{endDate}
            </if>
            <if test="null != consignorName and consignorName != ''">
                and lto.consignor_name like concat('%', #{consignorName}, '%')
            </if>
            <if test="null != consignorCode and consignorCode != ''">
                and lto.consignor_code like concat('%', #{consignorCode}, '%')
            </if>
            <if test="null != consignorCodes and consignorCodes.size() > 0">
                and lto.consignor_code in
                <foreach collection="consignorCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != hasBill">
                <choose>
                    <when test="hasBill == true">
                        and if(ltb.id is not null, 1, 0) = 1
                    </when>
                    <otherwise>
                        and if(ltb.id is not null, 1, 0) = 0
                    </otherwise>
                </choose>
            </if>
            <if test="null != category">
                and lto.category = #{category.number}
            </if>
            <if test="null != departureStation and departureStation != ''">
                and lto.departure_station like concat('%', #{departureStation}, '%')
            </if>
            <if test="null != destinationStation and destinationStation != ''">
                and lto.destination_station like concat('%', #{destinationStation}, '%')
            </if>
            <if test="null != driverName and driverName != ''">
                and lto.driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="null != driverPhone and driverPhone != ''">
                and lto.driver_phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="null != licensePlateNumber and licensePlateNumber != ''">
                and lto.license_plate_number like concat('%', #{licensePlateNumber}, '%')
            </if>
            <if test="null != licensePlateNumbers and licensePlateNumbers.size() > 0">
                and lto.license_plate_number in
                <foreach collection="licensePlateNumbers" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != charter">
                and lto.is_charter = #{charter}
            </if>
            <if test="null != payer">
                and lto.payer = #{payer.number}
            </if>
            <if test="null != auditStatus">
                and lto.audit_status = #{auditStatus}
            </if>
            <if test="null != paidStatus">
                and ifnull(ltb.status, 0) = #{paidStatus}
            </if>
            <if test="null != freightCharges and freightCharges.size() > 0">
                and lto.freight_charge in
                <foreach collection="freightCharges" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                order by ${orderStr}
            </when>
            <otherwise>
                order by pay_status, lto.create_time desc
            </otherwise>
        </choose>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="orderStatistics" resultType="com.senox.tms.vo.LogisticTransportOrderStatisticsVo">
        select sum(lto.pieces)         as total_pieces,
               sum(lto.loading_weight) as total_loading_weight,
               sum(lto.freight_charge) as total_freight_charge,
               sum(lto.other_charge)   as total_other_charge,
               sum(lto.receivable_freight_charge) as total_receivable_freight_charge
        from t_logistic_transport_order lto
        left join t_logistic_transport_bill ltb on ltb.order_serial_no = lto.serial_no
        <where>
            <if test="null != ids and ids.size() > 0">
                and lto.id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != serialNos and serialNos.size() > 0">
                and lto.serial_no in
                <foreach collection="serialNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != startDate">
                and lto.year_month_day >= #{startDate}
            </if>
            <if test="null != endDate">
                and lto.year_month_day &lt;= #{endDate}
            </if>
            <if test="null != consignorName and consignorName != ''">
                and lto.consignor_name like concat('%', #{consignorName}, '%')
            </if>
            <if test="null != consignorCode and consignorCode != ''">
                and lto.consignor_code like concat('%', #{consignorCode}, '%')
            </if>
            <if test="null != consignorCodes and consignorCodes.size() > 0">
                and lto.consignor_code in
                <foreach collection="consignorCodes" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != hasBill">
                <choose>
                    <when test="hasBill == true">
                        and if(ltb.id is not null, 1, 0) = 1
                    </when>
                    <otherwise>
                        and if(ltb.id is not null, 1, 0) = 0
                    </otherwise>
                </choose>
            </if>
            <if test="null != category">
                and lto.category = #{category.number}
            </if>
            <if test="null != departureStation and departureStation != ''">
                and lto.departure_station like concat('%', #{departureStation}, '%')
            </if>
            <if test="null != destinationStation and destinationStation != ''">
                and lto.destination_station like concat('%', #{destinationStation}, '%')
            </if>
            <if test="null != driverName and driverName != ''">
                and lto.driver_name like concat('%', #{driverName}, '%')
            </if>
            <if test="null != driverPhone and driverPhone != ''">
                and lto.driver_phone like concat('%', #{driverPhone}, '%')
            </if>
            <if test="null != licensePlateNumber and licensePlateNumber != ''">
                and lto.license_plate_number like concat('%', #{licensePlateNumber}, '%')
            </if>
            <if test="null != licensePlateNumbers and licensePlateNumbers.size() > 0">
                and lto.license_plate_number
                <foreach collection="licensePlateNumbers" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != charter">
                and lto.is_charter = #{charter}
            </if>
            <if test="null != payer">
                and lto.payer = #{payer.number}
            </if>
            <if test="null != auditStatus">
                and lto.audit_status = #{auditStatus}
            </if>
            <if test="null != paidStatus">
                and ifnull(ltb.status, 0) = #{paidStatus}
            </if>
            <if test="null != freightCharges and freightCharges.size() > 0">
                and lto.freight_charge in
                <foreach collection="freightCharges" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticStatisticsDayReportMapper">

    <resultMap id="LogisticStatisticsDayReportVo_Result" type="com.senox.tms.vo.LogisticStatisticsDayReportVo">
        <result property="id" column="id"/>
        <result property="reportDate" column="report_date"/>
        <result property="operationsDepartment" column="operations_department"/>
        <result property="shipper" column="shipper"/>
        <result property="logisticsNo" column="logistics_no"/>
        <result property="incomeType" column="income_type"/>
        <result property="driverName" column="driver_name"/>
        <result property="carNo" column="car_no"/>
        <result property="charteredBus" column="chartered_bus"/>
        <result property="departureStation" column="departure_station"/>
        <result property="destinationStation" column="destination_station"/>
        <result property="pieces" column="pieces"/>
        <result property="loadingWeight" column="loading_weight"/>
        <result property="storageWeight" column="storage_weight"/>
        <result property="unStockedWeight" column="un_stocked_weight"/>
        <result property="volume" column="volume"/>
        <result property="freightIncomeAmount" column="freight_income_amount"/>
        <result property="paymentTime" column="payment_time"/>
        <result property="actualFreightAmount" column="actual_freight_amount"/>
        <result property="warehousingNo" column="warehousing_no"/>
        <result property="frozenGoodsDiscounts" column="frozen_goods_discounts"/>
        <result property="unpaidAmount" column="unpaid_amount"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createName" column="create_name"/>
    </resultMap>

    <select id="countLogisticStatisticsDayReport" parameterType="com.senox.tms.vo.LogisticStatisticsDayReportSearchVo" resultType="int">
        select count(1) from t_logistic_statistics_day_report
        <where>
            <if test="reportDateStart != null">
                AND report_date >= #{reportDateStart}
            </if>
            <if test="reportDateEnd != null">
                AND report_date <![CDATA[<=]]> #{reportDateEnd}
            </if>
            <if test="shipper != null and shipper != ''">
                AND shipper like CONCAT('%', #{shipper}, '%')
            </if>
            <if test="logisticsNo != null and logisticsNo != ''">
                AND logistics_no like CONCAT('%', #{logisticsNo}, '%')
            </if>
            <if test="incomeType != null">
                AND income_type = #{incomeType}
            </if>
            <if test="driverName != null and driverName != ''">
                AND driver_name like CONCAT('%', #{driverName}, '%')
            </if>
            <if test="carNo != null and carNo != ''">
                AND car_no = #{carNo}
            </if>
            <if test="departureStation != null and departureStation != ''">
                AND departure_station = #{departureStation}
            </if>
            <if test="destinationStation != null and destinationStation != ''">
                AND destination_station = #{destinationStation}
            </if>
            <if test="charteredBus != null">
                AND chartered_bus = #{charteredBus}
            </if>
            <if test="paymentTimeStart != null">
                AND payment_time >= #{paymentTimeStart}
            </if>
            <if test="paymentTimeEnd != null">
                AND payment_time <![CDATA[<=]]> #{paymentTimeEnd}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listLogisticStatisticsDayReport" parameterType="com.senox.tms.vo.LogisticStatisticsDayReportSearchVo" resultMap="LogisticStatisticsDayReportVo_Result">
        select tlsdr.id, tlsdr.report_date, tlsdr.operations_department, tlsdr.shipper, tlsdr.logistics_no, tlsdr.income_type, tlsdr.driver_name, tlsdr.car_no, tlsdr.chartered_bus
            , tlsdr.departure_station, tlsdr.destination_station, tlsdr.pieces, tlsdr.loading_weight, tlsdr.storage_weight, tlsdr.un_stocked_weight, tlsdr.volume, tlsdr.freight_income_amount
            , tlsdr.payment_time, tlsdr.actual_freight_amount, tlsdr.warehousing_no, tlsdr.frozen_goods_discounts, tlsdr.unpaid_amount, tlsdr.remark, tlsdr.create_time, u.real_name as create_name
            from t_logistic_statistics_day_report tlsdr
            inner join u_admin_user u on u.id = tlsdr.creator_id
        <where>
            <if test="reportDateStart != null">
                AND tlsdr.report_date >= #{reportDateStart}
            </if>
            <if test="reportDateEnd != null">
                AND tlsdr.report_date <![CDATA[<=]]> #{reportDateEnd}
            </if>
            <if test="shipper != null and shipper != ''">
                AND tlsdr.shipper like CONCAT('%', #{shipper}, '%')
            </if>
            <if test="logisticsNo != null and logisticsNo != ''">
                AND tlsdr.logistics_no like CONCAT('%', #{logisticsNo}, '%')
            </if>
            <if test="incomeType != null">
                AND tlsdr.income_type = #{incomeType}
            </if>
            <if test="driverName != null and driverName != ''">
                AND tlsdr.driver_name like CONCAT('%', #{driverName}, '%')
            </if>
            <if test="carNo != null and carNo != ''">
                AND tlsdr.car_no = #{carNo}
            </if>
            <if test="departureStation != null and departureStation != ''">
                AND tlsdr.departure_station = #{departureStation}
            </if>
            <if test="destinationStation != null and destinationStation != ''">
                AND tlsdr.destination_station = #{destinationStation}
            </if>
            <if test="charteredBus != null">
                AND tlsdr.chartered_bus = #{charteredBus}
            </if>
            <if test="paymentTimeStart != null">
                AND tlsdr.payment_time >= #{paymentTimeStart}
            </if>
            <if test="paymentTimeEnd != null">
                AND tlsdr.payment_time <![CDATA[<=]]> #{paymentTimeEnd}
            </if>
            AND tlsdr.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY tlsdr.report_date desc, tlsdr.id desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumLogisticStatisticsDayReport" parameterType="com.senox.tms.vo.LogisticStatisticsDayReportSearchVo" resultType="com.senox.tms.vo.LogisticStatisticsDayReportVo">
        select sum(pieces) as pieces, sum(loading_weight) as loading_weight, sum(storage_weight) as storage_weight, sum(un_stocked_weight) as un_stocked_weight
              , sum(volume) as volume, sum(freight_income_amount) as freight_income_amount, sum(actual_freight_amount) as actual_freight_amount
             , sum(frozen_goods_discounts) as frozen_goods_discounts, sum(unpaid_amount) as unpaid_amount
            from t_logistic_statistics_day_report
        <where>
            <if test="reportDateStart != null">
                AND report_date >= #{reportDateStart}
            </if>
            <if test="reportDateEnd != null">
                AND report_date <![CDATA[<=]]> #{reportDateEnd}
            </if>
            <if test="shipper != null and shipper != ''">
                AND shipper like CONCAT('%', #{shipper}, '%')
            </if>
            <if test="logisticsNo != null and logisticsNo != ''">
                AND logistics_no like CONCAT('%', #{logisticsNo}, '%')
            </if>
            <if test="incomeType != null">
                AND income_type = #{incomeType}
            </if>
            <if test="driverName != null and driverName != ''">
                AND driver_name like CONCAT('%', #{driverName}, '%')
            </if>
            <if test="carNo != null and carNo != ''">
                AND car_no = #{carNo}
            </if>
            <if test="departureStation != null and departureStation != ''">
                AND departure_station = #{departureStation}
            </if>
            <if test="destinationStation != null and destinationStation != ''">
                AND destination_station = #{destinationStation}
            </if>
            <if test="charteredBus != null">
                AND chartered_bus = #{charteredBus}
            </if>
            <if test="paymentTimeStart != null">
                AND payment_time >= #{paymentTimeStart}
            </if>
            <if test="paymentTimeEnd != null">
                AND payment_time <![CDATA[<=]]> #{paymentTimeEnd}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listByLogisticStatisticsDayReport" resultType="com.senox.tms.domain.LogisticStatisticsDayReport">
        select id, report_date, operations_department, shipper, logistics_no, income_type, driver_name, car_no, chartered_bus
            , departure_station, destination_station, pieces, loading_weight, storage_weight, un_stocked_weight, volume, freight_income_amount
            , payment_time, actual_freight_amount, warehousing_no, frozen_goods_discounts, unpaid_amount, remark
        from t_logistic_statistics_day_report
        WHERE logistics_no IN <foreach collection="logisticsNoList" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

</mapper>

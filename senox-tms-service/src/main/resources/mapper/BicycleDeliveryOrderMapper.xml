<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleDeliveryOrderMapper">

    <resultMap id="DeliveryOrderVo_Result" type="com.senox.tms.vo.BicycleDeliveryOrderVo">
        <result property="id" column="id"/>
        <result property="merged" column="merged"/>
        <result property="status" column="status"/>
        <result property="deliveryOrderSerialNo" column="delivery_order_serial_no"/>
        <result property="finishTime" column="finish_time"/>
        <collection property="detailVos" ofType="com.senox.tms.vo.BicycleDeliveryOrderDetailVo">
            <result property="id" column="detail_id"/>
            <result property="deliveryOrderSerialNo" column="delivery_order_serial_no"/>
            <result property="deliveryOrderSerialNoItem" column="delivery_order_serial_no_item"/>
            <result property="orderSerialNo" column="order_serial_no"/>
            <result property="riderId" column="rider_id"/>
            <result property="pickedPieces" column="picked_pieces"/>
            <result property="status" column="detail_status"/>
            <result property="startPointName" column="start_point_name"/>
            <result property="startPointDetailName" column="start_point_detail_name"/>
            <result property="endPointName" column="end_point_name"/>
            <result property="endPointDetailName" column="end_point_detail_name"/>
            <result property="sendTimeStart" column="send_time_start"/>
            <result property="sendTimeEnd" column="send_time_end"/>
            <result property="senderId" column="sender_id"/>
            <result property="sender" column="sender"/>
            <result property="senderContact" column="sender_contact"/>
            <result property="recipient" column="recipient"/>
            <result property="recipientContact" column="recipient_contact"/>
            <result property="remark" column="remark"/>
            <result property="pieces" column="pieces"/>
            <result property="orderStatus" column="order_status"/>
            <result property="statusRemark" column="status_remark"/>
        </collection>
    </resultMap>

    <!-- 获取最大的配送单号 -->
    <select id="findMaxDeliveryOrderNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(delivery_order_serial_no) FROM t_bicycle_delivery_order WHERE delivery_order_serial_no LIKE CONCAT(#{prefix}, '%');
    </select>

    <select id="findDeliveryOrderById" parameterType="java.lang.Long" resultMap="DeliveryOrderVo_Result">
        SELECT tbdo.id, tbdo.merged, tbdod.rider_id, tbdo.status, tbdo.delivery_order_serial_no, tbdo.finish_time, tbdod.id AS detail_id
            , tbdod.order_serial_no, tbdod.status AS detail_status, tbo.start_point_name, tbo.end_point_name, tbo.send_time_start
            , tbo.send_time_end, tbo.sender_id, tbo.sender, tbo.sender_contact, tbo.recipient, tbo.recipient_contact, tbo.remark, tbo.pieces
            , tbo.start_point_detail_name, tbo.end_point_detail_name, tbo.status AS order_status, tbo.status_remark
        FROM t_bicycle_delivery_order tbdo
            INNER JOIN t_bicycle_delivery_order_detail tbdod ON tbdo.delivery_order_serial_no = tbdod.delivery_order_serial_no
            INNER JOIN t_bicycle_order tbo ON tbo.order_serial_no = tbdod.order_serial_no
        WHERE tbdo.id = #{id}
    </select>

    <select id="deliveryInfoCount" parameterType="com.senox.tms.vo.BicycleDeliveryInfoSearchVo" resultType="int">
        SELECT count(DISTINCT bdo.id) from t_bicycle_delivery_order bdo
            INNER JOIN t_bicycle_delivery_order_detail bdod
                ON bdod.delivery_order_serial_no = bdo.delivery_order_serial_no
            INNER JOIN t_bicycle_order bo ON bo.order_serial_no = bdod.order_serial_no
        <where>
            <if test="startPointId != null">
                AND bo.start_point_id = #{startPointId}
            </if>
            <if test="statusList != null and statusList.size > 0">
                AND bdo.`status` IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </where>
    </select>

    <resultMap id="BicycleDeliveryInfo_Result" type="com.senox.tms.vo.BicycleDeliveryInfoVo">
        <result property="id" column="id"/>
        <result property="sender" column="sender"/>
        <result property="senderSerialNo" column="sender_serial_no"/>
        <result property="deliveryOrderSerial" column="delivery_order_serial_no"/>
        <result property="startPointName" column="start_point_name"/>
        <result property="endPointName" column="end_point_name"/>
        <result property="pieces" column="pieces"/>
        <result property="totalCharge" column="total_charge"/>
        <result property="deliveryTime" column="delivery_time"/>
        <result property="goodsCount" column="goods_count"/>
        <result property="status" column="status"/>
        <result property="arrivingPickupTime" column="arriving_pickup_time"/>
        <result property="useTime" column="use_time"/>
        <collection property="infoDetailVoList" column="delivery_order_serial_no" ofType="com.senox.tms.vo.BicycleDeliveryInfoDetailVo" select="deliveryInfoDetailList">
            <result property="riderName" column="rider_name"/>
            <result property="detailStatus" column="detail_status"/>
        </collection>
    </resultMap>

    <select id="deliveryInfoDetailList" resultType="com.senox.tms.vo.BicycleDeliveryInfoDetailVo">
        SELECT bdod.delivery_order_serial_no
             , br.name as rider_name
             , bdod.`status` as detail_status
        FROM t_bicycle_delivery_order_detail bdod
        INNER JOIN t_bicycle_rider br ON br.id = bdod.rider_id
        WHERE bdod.delivery_order_serial_no = #{delivery_order_serial_no}
    </select>

    <select id="deliveryInfoList" parameterType="com.senox.tms.vo.BicycleDeliveryInfoSearchVo" resultMap="BicycleDeliveryInfo_Result">
        SELECT bdo.id
            , bo.sender
            , bo.sender_serial_no
            , bdo.delivery_order_serial_no
            , bo.start_point_name
            , bo.end_point_name
            , bo.pieces
            , bdo.`status`
            , bdo.create_time as delivery_time
            , bdod.arriving_pickup_time
            , TIMESTAMPDIFF(MINUTE, bdod.arriving_pickup_time, NOW()) AS use_time
            , (SELECT COUNT(*) FROM t_bicycle_order_goods_detail bodg WHERE bodg.order_id = bo.id) AS goods_count
        from t_bicycle_delivery_order bdo
        INNER JOIN (
            SELECT bdod.order_serial_no, bdod.delivery_order_serial_no
                , Min(CASE WHEN bdoj.`status` = 2 THEN bdoj.modified_time ELSE NULL END) as arriving_pickup_time
            from t_bicycle_delivery_order_detail bdod
             LEFT JOIN t_bicycle_delivery_order_job bdoj on bdoj.delivery_order_detail_id = bdod.id
             GROUP BY  bdod.order_serial_no, bdod.delivery_order_serial_no
        ) as bdod on bdod.delivery_order_serial_no = bdo.delivery_order_serial_no
        INNER JOIN t_bicycle_order bo ON bo.order_serial_no = bdod.order_serial_no
        <where>
            <if test="startPointId != null">
                AND bo.start_point_id = #{startPointId}
            </if>
            <if test="statusList != null and statusList.size > 0">
                AND bdo.`status` IN <foreach collection="statusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY bdod.arriving_pickup_time is NULL, bdod.arriving_pickup_time asc, bdo.id DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>

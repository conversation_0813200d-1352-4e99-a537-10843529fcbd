<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingAttendanceMapper">


    <select id="countAttendance" parameterType="com.senox.tms.vo.UnloadingAttendanceSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_attendance
        <where>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
            <if test="operateTimeStart != null">
                AND operate_time >= #{operateTimeStart}
            </if>
            <if test="operateTimeEnd != null">
                AND operate_time <![CDATA[<=]]> #{operateTimeEnd}
            </if>
        </where>
    </select>

    <select id="listAttendance" parameterType="com.senox.tms.vo.UnloadingAttendanceSearchVo" resultType="com.senox.tms.vo.UnloadingAttendanceVo">
        SELECT
              id
            , worker_id
            , worker_no
            , worker_name
            , operate_time
            , remark
        FROM px_unloading_attendance
        <where>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
            <if test="operateTimeStart != null">
                AND operate_time >= #{operateTimeStart}
            </if>
            <if test="operateTimeEnd != null">
                AND operate_time <![CDATA[<=]]> #{operateTimeEnd}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>

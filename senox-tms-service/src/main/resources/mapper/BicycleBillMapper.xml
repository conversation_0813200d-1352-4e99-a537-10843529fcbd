<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleBillMapper">

    <!-- 根据id查找应收账单 -->
    <select id="listByIds" parameterType="java.util.List" resultType="com.senox.tms.vo.BicycleBillVo">
        SELECT b.id, b.order_serial_no, b.delivery_order_serial_no, b.merchant_id, b.amount
             , b.bill_date, b.bill_year, b.bill_month, d.amount as delivery_amount, o.amount as other_amount
             , h.amount as handling_amount, u.amount as upstairs_amount
        FROM t_bicycle_bill b
        LEFT JOIN t_bicycle_bill_detail d ON b.id = d.bill_id AND d.fee_id = 21
        LEFT JOIN t_bicycle_bill_detail o ON b.id = o.bill_id AND o.fee_id = 22
        LEFT JOIN t_bicycle_bill_detail h ON b.id = h.bill_id AND h.fee_id = 23
        LEFT JOIN t_bicycle_bill_detail u ON b.id = u.bill_id AND u.fee_id = 24
        WHERE b.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <select id="sumBill" parameterType="com.senox.tms.vo.BicycleBillSearchVo" resultType="com.senox.tms.vo.BicycleBillVo">
        SELECT SUM(b.amount) AS amount
             , SUM(d.amount) as delivery_amount
             , SUM(o.amount) as other_amount
             , SUM(h.amount) as handling_amount
             , SUM(u.amount) as upstairs_amount
        FROM t_bicycle_bill b
        inner join u_merchant m on m.id = b.merchant_id
        INNER JOIN t_bicycle_delivery_order do on do.delivery_order_serial_no = b.delivery_order_serial_no
        LEFT JOIN t_bicycle_bill_detail d ON b.id = d.bill_id AND d.fee_id = 21
        LEFT JOIN t_bicycle_bill_detail o ON b.id = o.bill_id AND o.fee_id = 22
        LEFT JOIN t_bicycle_bill_detail h ON b.id = h.bill_id AND h.fee_id = 23
        LEFT JOIN t_bicycle_bill_detail u ON b.id = u.bill_id AND u.fee_id = 24
        <where>
            <if test="null != billIds and billIds.size() > 0">
                b.id in
                <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="null != startDate">
                AND b.bill_date >= #{startDate}
            </if>
           <if test="null != endDate">
                AND b.bill_date &lt;= #{endDate}
            </if>
            <if test="null != settlePeriod">
                and m.settle_period = #{settlePeriod}
            </if>
            <if test="null != statusSet and statusSet.size() > 0">
                AND do.status in
                <foreach collection="statusSet" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND b.delivery_order_serial_no LIKE CONCAT('%', #{deliveryOrderSerialNo}, '%')
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND b.order_serial_no LIKE CONCAT('%', #{orderSerialNo}, '%')
            </if>
            <if test="merchantId != null">
                AND b.merchant_id >= #{merchantId}
            </if>
            <if test="null != merchantName and merchantName != ''">
                and m.name like concat('%',#{merchantName},'%')
            </if>
            <if test="null != containSettlement">
                <choose>
                    <when test="containSettlement">
                        and b.settlement_id > 0
                    </when>
                    <otherwise>
                        and b.settlement_id = 0
                    </otherwise>
                </choose>
            </if>
            <if test="null != settlementIds and settlementIds.size() > 0">
                and b.settlement_id in
                <foreach collection="settlementIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </where>
    </select>

    <select id="countBill" parameterType="com.senox.tms.vo.BicycleBillSearchVo" resultType="int">
        SELECT COUNT(b.id)
        FROM t_bicycle_bill b
        inner join u_merchant m on m.id = b.merchant_id
        INNER JOIN t_bicycle_delivery_order do on do.delivery_order_serial_no = b.delivery_order_serial_no
        <where>
            <if test="null != billIds and billIds.size() > 0">
                b.id in
                <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="null != startDate">
                AND b.bill_date >= #{startDate}
            </if>
           <if test="null != endDate">
                AND b.bill_date &lt;= #{endDate}
            </if>
            <if test="null != settlePeriod">
                and m.settle_period = #{settlePeriod}
            </if>
            <if test="null != statusSet and statusSet.size() > 0">
                AND do.status in
                <foreach collection="statusSet" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND b.delivery_order_serial_no LIKE CONCAT('%', #{deliveryOrderSerialNo}, '%')
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND b.order_serial_no LIKE CONCAT('%', #{orderSerialNo}, '%')
            </if>
            <if test="merchantId != null">
                AND b.merchant_id >= #{merchantId}
            </if>
            <if test="null != merchantName and merchantName != ''">
                and m.name like concat('%',#{merchantName},'%')
            </if>
            <if test="null != containSettlement">
                <choose>
                    <when test="containSettlement">
                        and b.settlement_id > 0
                    </when>
                    <otherwise>
                        and b.settlement_id = 0
                    </otherwise>
                </choose>
            </if>
            <if test="null != settlementIds and settlementIds.size() > 0">
                and b.settlement_id in
                <foreach collection="settlementIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </where>
    </select>

    <select id="listBill" parameterType="com.senox.tms.vo.BicycleBillSearchVo" resultType="com.senox.tms.vo.BicycleBillVo">
        SELECT b.id, b.order_serial_no, b.delivery_order_serial_no, b.merchant_id,m.name as merchant_name,m.settle_period,m.rc_serial,b.settlement_id,bo.create_openid, b.amount
        , b.bill_date, b.bill_year, b.bill_month, d.amount as delivery_amount, o.amount as other_amount, h.amount as handling_amount, u.amount as upstairs_amount
        FROM t_bicycle_bill b
        inner join u_merchant m on m.id = b.merchant_id
        inner join t_bicycle_order bo on bo.order_serial_no = b.order_serial_no
        INNER JOIN t_bicycle_delivery_order do on do.delivery_order_serial_no = b.delivery_order_serial_no
        LEFT JOIN t_bicycle_bill_detail d ON b.id = d.bill_id AND d.fee_id = 21
        LEFT JOIN t_bicycle_bill_detail o ON b.id = o.bill_id AND o.fee_id = 22
        LEFT JOIN t_bicycle_bill_detail h ON b.id = h.bill_id AND h.fee_id = 23
        LEFT JOIN t_bicycle_bill_detail u ON b.id = u.bill_id AND u.fee_id = 24
        <where>
            <if test="null != billIds and billIds.size() > 0">
                b.id in
                <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="null != startDate">
                AND b.bill_date >= #{startDate}
            </if>
           <if test="null != endDate">
                AND b.bill_date &lt;= #{endDate}
            </if>
            <if test="null != settlePeriod">
                and m.settle_period = #{settlePeriod}
            </if>
            <if test="null != statusSet and statusSet.size() > 0">
                AND do.status in
                <foreach collection="statusSet" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND b.delivery_order_serial_no LIKE CONCAT('%', #{deliveryOrderSerialNo}, '%')
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND b.order_serial_no LIKE CONCAT('%', #{orderSerialNo}, '%')
            </if>
            <if test="merchantId != null">
                AND b.merchant_id >= #{merchantId}
            </if>
            <if test="null != merchantName and merchantName != ''">
                and m.name like concat('%',#{merchantName},'%')
            </if>
            <if test="null != containSettlement">
                <choose>
                    <when test="containSettlement">
                        and b.settlement_id > 0
                    </when>
                    <otherwise>
                        and b.settlement_id = 0
                    </otherwise>
                </choose>
            </if>
            <if test="null != settlementIds and settlementIds.size() > 0">
                and b.settlement_id in
                <foreach collection="settlementIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY b.id DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="listOrderBill" parameterType="com.senox.tms.vo.BicycleBillSearchVo" resultType="com.senox.tms.vo.BicycleBillVo">
        SELECT b.id, b.order_serial_no, b.delivery_order_serial_no, b.merchant_id, b.amount
            , b.bill_date, b.bill_year, b.bill_month, d.amount as delivery_amount, o.amount as other_amount, tbo.pieces
            , h.amount as handling_amount, u.amount as upstairs_amount
        FROM t_bicycle_bill b
                 INNER JOIN t_bicycle_order tbo on tbo.order_serial_no = b.order_serial_no
                 LEFT JOIN t_bicycle_bill_detail d ON b.id = d.bill_id AND d.fee_id = 21
                 LEFT JOIN t_bicycle_bill_detail o ON b.id = o.bill_id AND o.fee_id = 22
                 LEFT JOIN t_bicycle_bill_detail h ON b.id = h.bill_id AND h.fee_id = 23
                 LEFT JOIN t_bicycle_bill_detail u ON b.id = u.bill_id AND u.fee_id = 24
        <where>
            <if test="null != startDate">
                AND b.bill_date >= #{startDate}
            </if>
           <if test="null != endDate">
                AND b.bill_date &lt;= #{endDate}
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND b.delivery_order_serial_no LIKE CONCAT('%', #{deliveryOrderSerialNo}, '%')
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND b.order_serial_no LIKE CONCAT('%', #{orderSerialNo}, '%')
            </if>
            <if test="merchantId != null">
                AND b.merchant_id >= #{merchantId}
            </if>
        </where>
    </select>

    <select id="findByOrderSerialNos" resultType="com.senox.tms.vo.BicycleBillVo">
        select b.id
             , b.bill_date
             , b.bill_year
             , b.bill_month
             , b.order_serial_no
             , b.delivery_order_serial_no
             , b.merchant_id
             , m.name as merchant_name
             , m.settle_period
             , o.create_openid
             , b.settlement_id
             , b.amount
             , b.creator_id
             , b.creator_name
             , b.create_time
             , b.modifier_id
             , b.modifier_name
             , b.modified_time
             , d1.amount as delivery_amount
             , d2.amount as other_amount
             , h.amount as handling_amount
             , u.amount as upstairs_amount
        from t_bicycle_bill b
                 inner join u_merchant m on m.id = b.merchant_id
                 inner join t_bicycle_order o on o.order_serial_no = b.order_serial_no
                 left join t_bicycle_bill_detail d1 on b.id = d1.bill_id and d1.fee_id = 21
                 left join t_bicycle_bill_detail d2 on b.id = d2.bill_id and d2.fee_id = 22
                 LEFT JOIN t_bicycle_bill_detail h ON b.id = h.bill_id AND h.fee_id = 23
                 LEFT JOIN t_bicycle_bill_detail u ON b.id = u.bill_id AND u.fee_id = 24
        <where>
             b.order_serial_no in <foreach collection="orderSerialNos" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </where>
    </select>

    <select id="billListBySettlementId" resultType="com.senox.tms.domain.BicycleBill">
        select b.id
        , b.bill_date
        , b.bill_year
        , b.bill_month
        , b.order_serial_no
        , b.delivery_order_serial_no
        , b.merchant_id
        , b.settlement_id
        , b.amount
        , b.creator_id
        , b.creator_name
        , b.create_time
        , b.modifier_id
        , b.modifier_name
        , b.modified_time
        from t_bicycle_bill b
        <where>
            b.settlement_id = #{settlementId}
        </where>
    </select>

    <update id="updateSettlement">
        update t_bicycle_bill
        <set>
            settlement_id = #{settlementId},
            modifier_id = #{bill.modifierId},
            modifier_name = #{bill.modifierName},
            modified_time = #{bill.modifiedTime}
        </set>
        <where>
            id = #{bill.id}
            and settlement_id = 0
        </where>
    </update>
</mapper>

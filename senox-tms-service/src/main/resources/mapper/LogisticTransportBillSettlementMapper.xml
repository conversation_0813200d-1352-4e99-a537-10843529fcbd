<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticTransportBillSettlementMapper">

    <resultMap id="resultMap" type="com.senox.tms.vo.LogisticTransportBillSettlementVo">
        <id property="id" column="id"/>
        <result property="billDate" column="bill_date"/>
        <result property="billYearMonth" column="bill_year_month"/>
        <result property="billYear" column="bill_year"/>
        <result property="billMonth" column="bill_month"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="merchantName" column="merchant_name"/>
        <result property="merchantSerial" column="merchant_serial"/>
        <result property="payer" column="payer" typeHandler="com.senox.tms.handler.GenericEnumTypeHandler"/>
        <result property="amount" column="amount"/>
        <result property="paidAmount" column="paid_amount"/>
        <result property="paidStillAmount" column="paid_still_amount"/>
        <result property="payWay" column="pay_way"/>
        <result property="paidTime" column="paid_time"/>
        <result property="remoteOrderId" column="remote_order_id"/>
        <result property="send" column="send"/>
        <result property="sendTime" column="send_time"/>
        <result property="tollManId" column="toll_man_id"/>
        <result property="tollManName" column="toll_man_name"/>
        <result property="status" column="status"/>
    </resultMap>

    <insert id="addBatch" useGeneratedKeys="true" keyProperty="id">
        insert into t_logistic_transport_bill_settlement (bill_date,bill_year_month,bill_year
        ,bill_month,merchant_id,payer,amount,paid_amount
        ,pay_way,paid_time,remote_order_id,send,send_time,toll_man_id,status
        ,creator_id,creator_name, create_time
        ,modifier_id, modifier_name,modified_time)
        values
        <foreach collection="billSettlements" item="item" separator=",">
            (#{item.billDate},#{item.billYearMonth},#{item.billYear},#{item.billMonth},#{item.merchantId},#{item.payer},#{item.amount}
            ,#{item.paidAmount},#{item.payWay},#{item.paidTime},#{item.remoteOrderId},#{item.send},#{item.sendTime},#{item.tollManId}
            ,#{item.status}
            ,#{item.creatorId},#{item.creatorName},#{item.createTime}
            ,#{item.modifierId}, #{item.modifierName},#{item.modifiedTime})
        </foreach>
    </insert>

    <update id="updateBatch">
        update t_logistic_transport_bill_settlement
        <set>
            <trim prefix=", bill_date = case" suffix="else bill_date end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.billDate">
                        when id = #{item.id} then #{item.billDate}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", bill_year_month = case" suffix="else bill_year_month end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.billYearMonth and item.billYearMonth != ''">
                        when id = #{item.id} then #{item.billYearMonth}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", bill_year = case" suffix="else bill_year end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.billYear">
                        when id = #{item.id} then #{item.billYear}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", bill_month = case" suffix="else bill_month end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.billMonth">
                        when id = #{item.id} then #{item.billMonth}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", merchant_id = case" suffix="else merchant_id end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.merchantId">
                        when id = #{item.id} then #{item.merchantId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", amount = case" suffix="else amount end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.amount">
                        when id = #{item.id} then #{item.amount}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", paid_amount = case" suffix="else paid_amount end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.paidAmount">
                        when id = #{item.id} then #{item.paidAmount}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", paid_still_amount = case" suffix="else paid_still_amount end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.paidStillAmount">
                        when id = #{item.id} then #{item.paidStillAmount}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", pay_way = case" suffix="else pay_way end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.payWay">
                        when id = #{item.id} then #{item.payWay}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", paid_time = case" suffix="else paid_time end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.paidTime">
                        when id = #{item.id} then #{item.paidTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", remote_order_id = case" suffix="else remote_order_id end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.remoteOrderId">
                        when id = #{item.id} then #{item.remoteOrderId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", send = case" suffix="else send end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.send">
                        when id = #{item.id} then #{item.send}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", send_time = case" suffix="else send_time end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.sendTime">
                        when id = #{item.id} then #{item.sendTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", toll_man_id = case" suffix="else toll_man_id end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.tollManId">
                        when id = #{item.id} then #{item.tollManId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", status = case" suffix="else status end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.status">
                        when id = #{item.id} then #{item.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", modifier_id = case" suffix="else modifier_id end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.modifierId">
                        when id = #{item.id} then #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", modifier_name = case" suffix="else modifier_name end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.modifierName">
                        when id = #{item.id} then #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", modified_time = case" suffix="else modified_time end">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.modifiedTime">
                        when id = #{item.id} then #{item.modifiedTime}
                    </if>
                </foreach>
            </trim>
        </set>
        <where>
            id in
            <foreach collection="billSettlements" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </where>
    </update>

    <select id="countList" resultType="int">
        select count(*)
        from t_logistic_transport_bill_settlement bs
        left join u_merchant um on um.id = bs.merchant_id
        left join u_admin_user au on au.id = bs.toll_man_id
        <where>
            <if test="null != ids and ids.size() > 0">
                bs.id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != keywords and keywords != ''">
                and (um.name like concat('%', #{keywords}, '%') or um.lt_serial like concat('%', #{keywords}, '%'))
            </if>
            <if test="null != settlementDateStart">
                and bs.bill_date >= #{settlementDateStart}
            </if>
            <if test="null!= settlementDateEnd">
                and bs.bill_date &lt;= #{settlementDateEnd}
            </if>
            <if test="null != settlementPaidDateStart">
                and bs.paid_time >= #{settlementPaidDateStart}
            </if>
            <if test="null != settlementPaidDateEnd">
                and bs.paid_time &lt;= #{settlementPaidDateEnd}
            </if>
            <if test="null != settlementPaidStatus">
                and bs.status = #{settlementPaidStatus}
            </if>
            <if test="null != payer">
                and bs.payer = #{payer.number}
            </if>
            <if test="null != paWays and paWays.size() > 0">
                and bs.pay_way in
                <foreach collection="paWays" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != merchantIds and merchantIds.size() > 0">
                and bs.merchant_id in
                <foreach collection="merchantIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != tollManIds and tollManIds.size() > 0">
                and au.id in
                <foreach collection="tollManIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != send">
                and bs.send = #{send}
            </if>
            <if test="null != orderSerialNos and orderSerialNos.size() > 0">
                and bs.id in (select b.settlement_id from t_logistic_transport_bill b where b.order_serial_no in
                <foreach collection="orderSerialNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="null != startDate and startDate != ''">
                and bs.bill_year_month >= #{startDate}
            </if>
            <if test="null != endDate and endDate != ''">
                and bs.bill_year_month &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="list" resultMap="resultMap">
        select bs.id,
        bs.bill_date,
        bs.bill_year_month,
        bs.bill_year,
        bs.bill_month,
        bs.amount,
        bs.paid_amount,
        bs.paid_still_amount,
        bs.pay_way,
        bs.paid_time,
        bs.remote_order_id,
        bs.send,
        bs.send_time,
        bs.toll_man_id,
        bs.status,
        bs.payer,
        um.id as merchant_id,
        um.name as merchant_name,
        um.lt_serial as merchant_serial,
        au.id as toll_man_id,
        au.real_name as toll_man_name
        from t_logistic_transport_bill_settlement bs
        left join u_merchant um on um.id = bs.merchant_id
        left join u_admin_user au on au.id = bs.toll_man_id
        <where>
            <if test="null != ids and ids.size() > 0">
                bs.id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != keywords and keywords != ''">
                and (um.name like concat('%', #{keywords}, '%') or um.lt_serial like concat('%', #{keywords}, '%'))
            </if>
            <if test="null != settlementDateStart">
                and bs.bill_date >= #{settlementDateStart}
            </if>
            <if test="null!= settlementDateEnd">
                and bs.bill_date &lt;= #{settlementDateEnd}
            </if>
            <if test="null != settlementPaidDateStart">
                and bs.paid_time >= #{settlementPaidDateStart}
            </if>
            <if test="null != settlementPaidDateEnd">
                and bs.paid_time &lt;= #{settlementPaidDateEnd}
            </if>
            <if test="null != settlementPaidStatus">
                and bs.status = #{settlementPaidStatus}
            </if>
            <if test="null != payer">
                and bs.payer = #{payer.number}
            </if>
            <if test="null != paWays and paWays.size() > 0">
                and bs.pay_way in
                <foreach collection="paWays" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != merchantIds and merchantIds.size() > 0">
                and bs.merchant_id in
                <foreach collection="merchantIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != tollManIds and tollManIds.size() > 0">
                and au.id in
                <foreach collection="tollManIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != send">
                and bs.send = #{send}
            </if>
            <if test="null != orderSerialNos and orderSerialNos.size() > 0">
                and bs.id in (select b.settlement_id from t_logistic_transport_bill b where b.order_serial_no in
                <foreach collection="orderSerialNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="null != startDate and startDate != ''">
                and bs.bill_year_month >= #{startDate}
            </if>
            <if test="null != endDate and endDate != ''">
                and bs.bill_year_month &lt;= #{endDate}
            </if>
        </where>
        order by bs.create_time desc
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>


    <update id="updateBillPaidById">
        update t_logistic_transport_bill_settlement bs
        inner join p_order_item oi on oi.order_id = #{orderId} and oi.product_id = bs.id
        <set>
            <if test="null!=orderId">
                bs.remote_order_id =#{orderId}
            </if>
            <choose>
                <when test="paid">
                    , bs.status = 1
                    , bs.paid_amount =#{amount}
                    , bs.paid_still_amount = bs.amount - bs.paid_amount
                    , bs.paid_time = #{paidTime}
                    , bs.pay_way = #{payWay}
                </when>
                <otherwise>
                    , bs.status = 0
                </otherwise>
            </choose>
            <if test="null != tollMan">
                , bs.toll_man_id = case when bs.toll_man_id is null or bs.toll_man_id = 0 then #{tollMan} else
                bs.toll_man_id end
            </if>
            , bs.modified_time = now()
        </set>
        <where>
            bs.id in
            <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            and bs.status = 0
            and bs.amount > 0
        </where>
    </update>

    <update id="updateBillPaidByOrderId">
        update t_logistic_transport_bill_settlement bs
        inner join p_order_item oi on oi.order_id = #{orderId} and oi.product_id = bs.id
        <set>
            <choose>
                <when test="paid">
                    , bs.status = 1
                    , bs.paid_amount =#{amount}
                    , bs.paid_still_amount = bs.amount - bs.paid_amount
                    , bs.paid_time = #{paidTime}
                    , bs.pay_way = #{payWay}
                </when>
                <otherwise>
                    , bs.status = 0
                </otherwise>
            </choose>
            <if test="null != tollMan">
                , bs.toll_man_id = case when bs.toll_man_id is null or bs.toll_man_id = 0 then #{tollMan} else
                bs.toll_man_id end
            </if>
            , bs.modified_time = now()
        </set>
        <where>
            bs.remote_order_id = #{orderId}
            and bs.status = 0
            and bs.amount > 0
        </where>
    </update>

    <update id="updateBillStatusByOrderId">
        update t_logistic_transport_bill_settlement
        set status        = #{paid},
            modified_time = now()
        where remote_order_id = #{orderId}
    </update>

    <select id="statistics" resultType="com.senox.tms.vo.LogisticTransportBillSettlementStatisticsVo">
        select sum(bs.amount)            as total_amount,
               sum(bs.paid_amount)       as total_paid_amount,
               sum(bs.paid_still_amount) as total_paid_stil_amount
        from t_logistic_transport_bill_settlement bs
                 left join u_merchant um on um.id = bs.merchant_id
                 left join u_admin_user au on au.id = bs.toll_man_id
        <where>
            <if test="null != ids and ids.size() > 0">
                bs.id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != keywords and keywords != ''">
                and (um.name like concat('%', #{keywords}, '%') or um.lt_serial like concat('%', #{keywords}, '%'))
            </if>
            <if test="null != settlementDateStart">
                and bs.bill_date >= #{settlementDateStart}
            </if>
            <if test="null!= settlementDateEnd">
                and bs.bill_date &lt;= #{settlementDateEnd}
            </if>
            <if test="null != settlementPaidDateStart">
                and bs.paid_time >= #{settlementPaidDateStart}
            </if>
            <if test="null != settlementPaidDateEnd">
                and bs.paid_time &lt;= #{settlementPaidDateEnd}
            </if>
            <if test="null != settlementPaidStatus">
                and bs.status = #{settlementPaidStatus}
            </if>
            <if test="null != payer">
                and bs.payer = #{payer.number}
            </if>
            <if test="null != paWays and paWays.size() > 0">
                and bs.pay_way in
                <foreach collection="paWays" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != merchantIds and merchantIds.size() > 0">
                and bs.merchant_id in
                <foreach collection="merchantIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != tollManIds and tollManIds.size() > 0">
                and au.id in
                <foreach collection="tollManIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != send">
                and bs.send = #{send}
            </if>
        </where>
    </select>

    <update id="send">
        update t_logistic_transport_bill_settlement
        <set>
            <choose>
                <when test="send">
                    , send = 1
                    , send_time = now()
                </when>
                <otherwise>
                    , send = 0
                    , send_time = null
                </otherwise>
            </choose>
            , modified_time = now()
        </set>
        <where>
            <if test="null != startDate and startDate != ''">
                bill_year_month >= #{startDate}
            </if>
            <if test="null != endDate and endDate != ''">
                and bill_year_month &lt;= #{endDate}
            </if>
            <if test="null != settlementIds and settlementIds.size() > 0">
                and id in
                <foreach collection="settlementIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="send">
                    and send = 0
                </when>
                <otherwise>
                    and send = 1
                </otherwise>
            </choose>
        </where>
    </update>

    <update id="updatePaidOrder">
        update t_logistic_transport_bill_settlement set remote_order_id = #{orderId}, modified_time = now()
        where id in
        <foreach collection="billIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and status = 0
    </update>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicyclePayoffMapper">

    <select id="sumPayoff" parameterType="com.senox.tms.vo.BicyclePayoffSearchVo" resultType="com.senox.tms.vo.BicyclePayoffVo">
        SELECT SUM(p.share_amount) AS share_amount,
               SUM(p.referral_amount) AS referral_amount,
               SUM(p.pieces) AS pieces
        FROM t_bicycle_payoff p
        inner join t_bicycle_order o on o.order_serial_no = p.order_serial_no
        <where>
            <if test="status != null">
                AND p.status = #{status}
            </if>
            <if test="paidTimeBegin != null">
                AND p.paid_time >= #{paidTimeBegin}
            </if>
            <if test="paidTimeEnd != null">
                AND p.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND p.delivery_order_serial_no LIKE CONCAT('%', #{deliveryOrderSerialNo}, '%')
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND p.order_serial_no LIKE CONCAT('%', #{orderSerialNo}, '%')
            </if>
            <if test="payeeName != null and payeeName != ''">
                AND p.payee_name LIKE CONCAT('%', #{payeeName}, '%')
            </if>
            <if test="startTime != null">
                AND p.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND p.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="referral != null">
                AND p.referral = #{referral}
            </if>
            <if test="excludeZero != null and excludeZero == true">
                AND (p.share_amount > 0 OR p.referral_amount > 0)
            </if>
            <if test="riderId != null">
                AND p.rider_id = #{riderId}
            </if>
        </where>
    </select>

    <select id="countPayoff" parameterType="com.senox.tms.vo.BicyclePayoffSearchVo" resultType="int">
        SELECT count(p.id)
        FROM t_bicycle_payoff p
        inner join t_bicycle_order o on o.order_serial_no = p.order_serial_no
        <where>
            <if test="status != null">
                AND p.status = #{status}
            </if>
            <if test="paidTimeBegin != null">
                AND p.paid_time >= #{paidTimeBegin}
            </if>
            <if test="paidTimeEnd != null">
                AND p.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND p.delivery_order_serial_no LIKE CONCAT('%', #{deliveryOrderSerialNo}, '%')
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND p.order_serial_no LIKE CONCAT('%', #{orderSerialNo}, '%')
            </if>
            <if test="payeeName != null and payeeName != ''">
                AND p.payee_name LIKE CONCAT('%', #{payeeName}, '%')
            </if>
            <if test="startTime != null">
                AND p.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND p.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="referral != null">
                AND p.referral = #{referral}
            </if>
            <if test="excludeZero != null and excludeZero == true">
                AND (p.share_amount > 0 OR p.referral_amount > 0)
            </if>
            <if test="riderId != null">
                AND p.rider_id = #{riderId}
            </if>
        </where>
    </select>

    <select id="listPayoff" parameterType="com.senox.tms.vo.BicyclePayoffSearchVo" resultType="com.senox.tms.vo.BicyclePayoffVo">
        SELECT p.id, p.bill_year, p.bill_month, p.order_serial_no, p.delivery_order_serial_no, p.payee_name
        , p.payee_type, p.payee_contact, p.rider_id, p.share_amount, p.referral_amount, p.referral, p.payway, p.status
        , p.paid_time,ceiling(p.pieces) as pieces,p.delivery_time, m.name as merchant_name, p.create_time
        , o.total_charge as order_total_charge, o.pieces as order_total_pieces
        FROM t_bicycle_payoff p
        inner join t_bicycle_order o on o.order_serial_no = p.order_serial_no
        inner join u_merchant m on o.sender_id = m.id
        <where>
            <if test="status != null">
                AND p.status = #{status}
            </if>
            <if test="paidTimeBegin != null">
                AND p.paid_time >= #{paidTimeBegin}
            </if>
            <if test="paidTimeEnd != null">
                AND p.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND p.delivery_order_serial_no LIKE CONCAT('%', #{deliveryOrderSerialNo}, '%')
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND p.order_serial_no LIKE CONCAT('%', #{orderSerialNo}, '%')
            </if>
            <if test="payeeName != null and payeeName != ''">
                AND p.payee_name LIKE CONCAT('%', #{payeeName}, '%')
            </if>
            <if test="startTime != null">
                AND p.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND p.create_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="referral != null">
                AND p.referral = #{referral}
            </if>
            <if test="excludeZero != null and excludeZero == true">
                AND (p.share_amount > 0 OR p.referral_amount > 0)
            </if>
            <if test="riderId != null">
                AND p.rider_id = #{riderId}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY p.status, p.paid_time DESC, p.id DESC
            </otherwise>
        </choose>
       <if test="page">
           LIMIT ${offset}, ${pageSize}
       </if>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingOrderMonthPayoffMapper">

    <select id="countMonthPayoff" parameterType="com.senox.tms.vo.UnloadingOrderMonthPayoffSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_order_month_payoff
        <where>
            <if test="year != null">
                AND payoff_year = #{year}
            </if>
            <if test="month != null">
                and payoff_month = #{month}
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
            <if test="keywords != null and keywords != ''">
                AND ( worker_no LIKE CONCAT('%', #{keywords}, '%') OR worker_name LIKE CONCAT('%', #{keywords}, '%') )
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="payoffTimeStart != null">
                AND payoff_time >= #{payoffTimeStart}
            </if>
            <if test="payoffTimeEnd != null">
                AND payoff_time <![CDATA[<=]]> #{payoffTimeEnd}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="sumMonthPayoff" parameterType="com.senox.tms.vo.UnloadingOrderMonthPayoffSearchVo" resultType="com.senox.tms.domain.UnloadingOrderMonthPayoff">
        SELECT
             IFNULL(SUM(payoff_amount), 0) as payoff_amount
           , IFNULL(SUM(share_amount), 0) as share_amount
        FROM px_unloading_order_month_payoff
        <where>
            <if test="year != null">
                AND payoff_year = #{year}
            </if>
            <if test="month != null">
                and payoff_month = #{month}
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
            <if test="keywords != null and keywords != ''">
                AND ( worker_no LIKE CONCAT('%', #{keywords}, '%') OR worker_name LIKE CONCAT('%', #{keywords}, '%') )
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="payoffTimeStart != null">
                AND payoff_time >= #{payoffTimeStart}
            </if>
            <if test="payoffTimeEnd != null">
                AND payoff_time <![CDATA[<=]]> #{payoffTimeEnd}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listMonthPayoff" parameterType="com.senox.tms.vo.UnloadingOrderMonthPayoffSearchVo" resultType="com.senox.tms.domain.UnloadingOrderMonthPayoff">
        SELECT
              id
            , payoff_year
            , payoff_month
            , worker_id
            , worker_no
            , worker_name
            , payoff_amount
            , share_amount
            , status
            , payoff_time
            , remark
        FROM px_unloading_order_month_payoff
        <where>
            <if test="year != null">
                AND payoff_year = #{year}
            </if>
            <if test="month != null">
                and payoff_month = #{month}
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
            <if test="keywords != null and keywords != ''">
                AND ( worker_no LIKE CONCAT('%', #{keywords}, '%') OR worker_name LIKE CONCAT('%', #{keywords}, '%') )
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="payoffTimeStart != null">
                AND payoff_time >= #{payoffTimeStart}
            </if>
            <if test="payoffTimeEnd != null">
                AND payoff_time <![CDATA[<=]]> #{payoffTimeEnd}
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>

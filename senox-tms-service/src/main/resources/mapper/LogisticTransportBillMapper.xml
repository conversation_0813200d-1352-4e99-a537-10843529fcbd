<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticTransportBillMapper">

    <resultMap id="resultMap" type="com.senox.tms.vo.LogisticTransportBillVo">
        <id property="id" column="id"/>
        <result property="settlementId" column="settlement_id"/>
        <result property="billDate" column="bill_date"/>
        <result property="billYear" column="bill_year"/>
        <result property="billMonth" column="bill_month"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="merchantName" column="merchant_name"/>
        <result property="settlementId" column="settlement_id"/>
        <result property="freightCharge" column="freight_charge"/>
        <result property="otherCharge" column="other_charge"/>
        <result property="receivableFreightCharge" column="receivable_freight_charge"/>
        <result property="status" column="status"/>
        <result property="paidTime" column="paid_time"/>
        <result property="payWay" column="pay_way"/>
        <result property="orderYearMonthDay" column="order_year_month_day"/>
        <result property="orderRemark" column="order_remark"/>
        <result property="payer" column="order_payer" typeHandler="com.senox.tms.handler.GenericEnumTypeHandler"/>
        <result property="category" column="order_category" typeHandler="com.senox.tms.handler.GenericEnumTypeHandler"/>
        <result property="driverName" column="order_driver_name"/>
        <result property="licensePlateNumber" column="order_license_plate_number"/>
        <result property="charter" column="order_charter"/>
        <result property="departureStation" column="order_departure_station"/>
        <result property="destinationStation" column="order_destination_station"/>
        <result property="pieces" column="order_pieces"/>
        <result property="loadingWeight" column="order_loading_weight"/>
    </resultMap>

    <insert id="addBatch" useGeneratedKeys="true" keyProperty="id">
        insert into t_logistic_transport_bill (bill_date,bill_year, bill_month, order_serial_no,
        merchant_id,freight_charge,other_charge,receivable_freight_charge,creator_id, creator_name, create_time, modifier_id, modifier_name,
        modified_time)
        values
        <foreach collection="bills" item="item" separator=",">
            (#{item.billDate},#{item.billYear},#{item.billMonth},#{item.orderSerialNo}
            ,#{item.merchantId},#{item.freightCharge},#{item.otherCharge},#{item.receivableFreightCharge}
            ,#{item.creatorId},#{item.creatorName},#{item.createTime}
            ,#{item.modifierId}, #{item.modifierName},#{item.modifiedTime})
        </foreach>
    </insert>

    <update id="updateBatch">
        update t_logistic_transport_bill
        <set>
            <trim prefix=", settlement_id = case" suffix="else settlement_id end">
                <foreach collection="bills" item="item">
                    <if test="null != item.settlementId">
                        when id = #{item.id} then #{item.settlementId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", bill_date = case" suffix="else bill_date end">
                <foreach collection="bills" item="item">
                    <if test="null != item.billDate">
                        when id = #{item.id} then #{item.billDate}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", bill_year = case" suffix="else bill_year end">
                <foreach collection="bills" item="item">
                    <if test="null != item.billYear">
                        when id = #{item.id} then #{item.billYear}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", bill_month = case" suffix="else bill_month end">
                <foreach collection="bills" item="item">
                    <if test="null != item.billMonth">
                        when id = #{item.id} then #{item.billMonth}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", merchant_id = case" suffix="else merchant_id end">
                <foreach collection="bills" item="item">
                    <if test="null != item.merchantId">
                        when id = #{item.id} then #{item.merchantId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", freight_charge = case" suffix="else freight_charge end">
                <foreach collection="bills" item="item">
                    <if test="null != item.freightCharge">
                        when id = #{item.id} then #{item.freightCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", other_charge = case" suffix="else other_charge end">
                <foreach collection="bills" item="item">
                    <if test="null != item.otherCharge">
                        when id = #{item.id} then #{item.otherCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", receivable_freight_charge = case" suffix="else receivable_freight_charge end">
                <foreach collection="bills" item="item">
                    <if test="null != item.receivableFreightCharge">
                        when id = #{item.id} then #{item.receivableFreightCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", modifier_id = case" suffix="else modifier_id end">
                <foreach collection="bills" item="item">
                    <if test="null != item.modifierId">
                        when id = #{item.id} then #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", modifier_name = case" suffix="else modifier_name end">
                <foreach collection="bills" item="item">
                    <if test="null != item.modifierName">
                        when id = #{item.id} then #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            <trim prefix=", modified_time = case" suffix="else modified_time end">
                <foreach collection="bills" item="item">
                    <if test="null != item.modifiedTime">
                        when id = #{item.id} then #{item.modifiedTime}
                    </if>
                </foreach>
            </trim>
        </set>
        <where>
            id in
            <foreach collection="bills" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </where>
    </update>

    <select id="countList" resultType="int">
        select count(*)
        from t_logistic_transport_bill ltb
        inner join u_merchant um on um.id = ltb.merchant_id
        inner join t_logistic_transport_order lto on lto.serial_no = ltb.order_serial_no
        left join t_logistic_transport_bill_settlement bs on bs.id = ltb.settlement_id
        <where>
            <if test="null != billDateStart">
                and ltb.bill_date >= #{billDateStart}
            </if>
            <if test="null != billDateEnd">
                and ltb.bill_date &lt;= #{billDateEnd}
            </if>
            <if test="null != paidDateStart">
                and ltb.paid_time >= #{paidDateStart}
            </if>
            <if test="null != paidDateEnd">
                and ltb.paid_time &lt;= #{paidDateEnd}
            </if>
            <if test="null != ids and ids.size() > 0">
                and ltb.id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != orderSerialNos and orderSerialNos.size() > 0">
                and ltb.order_serial_no in
                <foreach collection="orderSerialNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null!= settlementId">
                and ltb.settlement_id = #{settlementId}
            </if>
            <if test="null != merchantIds and merchantIds.size() > 0">
                and ltb.merchant_id in
                <foreach collection="merchantIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != tollManIds and tollManIds.size() > 0">
                and bs.toll_man_id in
                <foreach collection="tollManIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != category">
                and ltb.category = #{category.number}
            </if>
            <if test="null != payer">
                and lto.payer = #{payer.number}
            </if>
            <if test="null != status">
                and ltb.status = #{status}
            </if>
            <if test="null != keywords and keywords != ''">
                and (ltb.order_serial_no like concat('%', #{keywords}, '%') or um.lt_serial like concat('%', #{keywords}, '%'))
            </if>
            <if test="null != paWays and paWays.size() > 0">
                and ltb.pay_way in
                <foreach collection="paWays" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="list" resultMap="resultMap">
        select ltb.id,
        ltb.bill_date,
        ltb.bill_year,
        ltb.bill_month,
        ltb.order_serial_no,
        ltb.settlement_id,
        ltb.freight_charge,
        ltb.other_charge,
        ltb.receivable_freight_charge,
        ltb.status,
        ltb.paid_time,
        ltb.pay_way,
        lto.year_month_day as order_year_month_day,
        lto.payer as order_payer,
        lto.category as order_category,
        lto.driver_name as order_driver_name,
        lto.license_plate_number as order_license_plate_number,
        lto.is_charter as order_charter,
        lto.departure_station as order_departure_station,
        lto.destination_station as order_destination_station,
        lto.pieces as order_pieces,
        lto.loading_weight as order_loading_weight,
        lto.remark as order_remark,
        au.real_name as toll_man_name,
        um.id as merchant_id,
        um.name as merchant_name
        from t_logistic_transport_bill ltb
        inner join u_merchant um on um.id = ltb.merchant_id
        inner join t_logistic_transport_order lto on lto.serial_no = ltb.order_serial_no
        left join t_logistic_transport_bill_settlement bs on bs.id = ltb.settlement_id
        left join u_admin_user au on au.id = bs.toll_man_id
        <where>
            <if test="null != billDateStart">
                and ltb.bill_date >= #{billDateStart}
            </if>
            <if test="null != billDateEnd">
                and ltb.bill_date &lt;= #{billDateEnd}
            </if>
            <if test="null != paidDateStart">
                and ltb.paid_time >= #{paidDateStart}
            </if>
            <if test="null != paidDateEnd">
                and ltb.paid_time &lt;= #{paidDateEnd}
            </if>
            <if test="null != ids and ids.size() > 0">
                and ltb.id in
                <foreach collection="ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != orderSerialNos and orderSerialNos.size() > 0">
                and ltb.order_serial_no in
                <foreach collection="orderSerialNos" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null!= settlementId">
                and ltb.settlement_id = #{settlementId}
            </if>
            <if test="null != merchantIds and merchantIds.size() > 0">
                and ltb.merchant_id in
                <foreach collection="merchantIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != tollManIds and tollManIds.size() > 0">
                and bs.toll_man_id in
                <foreach collection="tollManIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != category">
                 and ltb.category = #{category.number}
            </if>
            <if test="null != payer">
                and lto.payer = #{payer.number}
            </if>
            <if test="null != status">
                and ltb.status = #{status}
            </if>
            <if test="null != keywords and keywords != ''">
                and (ltb.order_serial_no like concat('%', #{keywords}, '%') or um.lt_serial like concat('%', #{keywords}, '%'))
            </if>
            <if test="null != paWays and paWays.size() > 0">
                and ltb.pay_way in
                <foreach collection="paWays" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        order by ltb.bill_date
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <update id="updatePaidBySettlementId">
        update t_logistic_transport_bill
        <set>
            <choose>
                <when test="paid">
                    , status = 1
                    , paid_time = #{paidTime}
                    , pay_way = #{payWay}
                </when>
                <otherwise>
                    , status = 0
                </otherwise>
            </choose>
            , modified_time = now()
        </set>
        <where>
            and receivable_freight_charge > 0
            and settlement_id in
            <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </where>
    </update>

    <update id="updatePaidByOrderId">
        update t_logistic_transport_bill b inner join t_logistic_transport_bill_settlement bs on bs.id = b.settlement_id
        <set>
            <choose>
                <when test="paid">
                   , b.status = 1
                   , b.paid_time = #{paidTime}
                   , b.pay_way = #{payWay}
                </when>
                <otherwise>
                   , b.status = 0
                </otherwise>
            </choose>
        </set>
        <where>
            and b.receivable_freight_charge > 0
            and bs.remote_order_id = #{orderId}
        </where>
    </update>

    <select id="statisticBySettlement" resultType="com.senox.tms.vo.LogisticTransportBillSettlementDetailVo">
        select sum(tto.pieces)         as total_pieces,
               sum(tto.loading_weight) as total_loading_weight,
               sum(tto.freight_charge) as total_freight_charge,
               sum(tto.other_charge)   as total_other_charge,
               sum(tto.receivable_freight_charge) as total_receivable_freight_charge
        from t_logistic_transport_bill_settlement bs
                 inner join t_logistic_transport_bill b on b.settlement_id = bs.id
                 inner join t_logistic_transport_order tto on tto.serial_no = b.order_serial_no
        <where>
            bs.id = #{settlementId}
        </where>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleOperateAnalysisMapper">

    <select id="count" parameterType="com.senox.tms.vo.BicycleOperateAnalysisSearchVo" resultType="int">
        select count(id)
        from t_bicycle_operate_analysis
        <where>
            <if test="operateAnalysisDate != null">
                AND operate_analysis_date = #{operateAnalysisDate}
            </if>
            <if test="operateAnalysisDateStart != null">
                AND operate_analysis_date >= #{operateAnalysisDateStart}
            </if>
            <if test="operateAnalysisDateEnd != null">
                AND operate_analysis_date <![CDATA[<=]]> #{operateAnalysisDateEnd}
            </if>
        </where>
    </select>

    <select id="listOperateAnalysis" parameterType="com.senox.tms.vo.BicycleOperateAnalysisSearchVo" resultType="com.senox.tms.vo.BicycleOperateAnalysisVo" >
        select id, operate_analysis_date, total_amount, total_pieces, avg_unit_price, total_count
              , avg_second, best_sender_id, best_customer_name, best_rider_id, best_rider_name, modified_time
        from t_bicycle_operate_analysis
        <where>
            <if test="operateAnalysisDate != null">
                AND operate_analysis_date = #{operateAnalysisDate}
            </if>
            <if test="operateAnalysisDateStart != null">
                AND operate_analysis_date >= #{operateAnalysisDateStart}
            </if>
            <if test="operateAnalysisDateEnd != null">
                AND operate_analysis_date <![CDATA[<=]]> #{operateAnalysisDateEnd}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id desc
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="sumOperateAnalysis" parameterType="com.senox.tms.vo.BicycleOperateAnalysisSearchVo" resultType="com.senox.tms.domain.BicycleOperateAnalysis">
        SELECT SUM(total_amount) AS total_amount
             , SUM(total_pieces) AS total_pieces
             , SUM(total_count) AS total_count
        FROM t_bicycle_operate_analysis
        <where>
            <if test="operateAnalysisDate != null">
                AND operate_analysis_date = #{operateAnalysisDate}
            </if>
            <if test="operateAnalysisDateStart != null">
                AND operate_analysis_date >= #{operateAnalysisDateStart}
            </if>
            <if test="operateAnalysisDateEnd != null">
                AND operate_analysis_date <![CDATA[<=]]> #{operateAnalysisDateEnd}
            </if>
        </where>
    </select>

</mapper>
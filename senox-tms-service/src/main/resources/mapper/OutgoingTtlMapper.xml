<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.OutgoingTtlMapper">

    <select id="countOutgoing" parameterType="com.senox.tms.vo.OutgoingTtlSearchVo" resultType="int">
        select count(1) from t_outgoing_ttl
        <where>
            <if test="receivingDateStart != null">
                AND receiving_date >= #{receivingDateStart}
            </if>
            <if test="receivingDateEnd != null">
                AND receiving_date <![CDATA[<=]]> #{receivingDateEnd}
            </if>
            <if test="shipperName != null and shipperName != ''">
                AND shipper_name like CONCAT('%', #{shipperName}, '%')
            </if>
            <if test="recipientName != null and recipientName != ''">
                AND recipient_name like CONCAT('%', #{recipientName}, '%')
            </if>
            <if test="logisticsNo != null and logisticsNo != ''">
                AND logistics_no like CONCAT('%', #{logisticsNo}, '%')
            </if>
            <if test="logisticsCompany != null and logisticsCompany != ''">
                AND logistics_company like CONCAT('%', #{logisticsCompany}, '%')
            </if>
            <if test="settlementType != null">
                AND settlement_type = #{settlementType}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listOutgoing" parameterType="com.senox.tms.vo.OutgoingTtlSearchVo" resultType="com.senox.tms.vo.OutgoingTtlVo">
        select id
            , receiving_date
            , shipper_name
            , recipient_name
            , recipient_address
            , recipient_contact
            , logistics_company
            , logistics_no
            , shipper_pieces
            , actual_freight_amount
            , actual_shipping_amount
            , profit_amount
            , settlement_type
            from t_outgoing_ttl
        <where>
            <if test="receivingDateStart != null">
                AND receiving_date >= #{receivingDateStart}
            </if>
            <if test="receivingDateEnd != null">
                AND receiving_date <![CDATA[<=]]> #{receivingDateEnd}
            </if>
            <if test="shipperName != null and shipperName != ''">
                AND shipper_name like CONCAT('%', #{shipperName}, '%')
            </if>
            <if test="recipientName != null and recipientName != ''">
                AND recipient_name like CONCAT('%', #{recipientName}, '%')
            </if>
            <if test="logisticsNo != null and logisticsNo != ''">
                AND logistics_no like CONCAT('%', #{logisticsNo}, '%')
            </if>
            <if test="logisticsCompany != null and logisticsCompany != ''">
                AND logistics_company like CONCAT('%', #{logisticsCompany}, '%')
            </if>
            <if test="settlementType != null">
                AND settlement_type = #{settlementType}
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY receiving_date desc, id desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumOutgoing" parameterType="com.senox.tms.vo.OutgoingTtlSearchVo" resultType="com.senox.tms.vo.OutgoingTtlVo">
        select sum(shipper_pieces) as shipper_pieces
             , sum(actual_freight_amount) as actual_freight_amount
             , sum(actual_shipping_amount) as actual_shipping_amount
             , sum(profit_amount) as profit_amount
            from t_outgoing_ttl
        <where>
            <if test="receivingDateStart != null">
                AND receiving_date >= #{receivingDateStart}
            </if>
            <if test="receivingDateEnd != null">
                AND receiving_date <![CDATA[<=]]> #{receivingDateEnd}
            </if>
            <if test="shipperName != null and shipperName != ''">
                AND shipper_name like CONCAT('%', #{shipperName}, '%')
            </if>
            <if test="recipientName != null and recipientName != ''">
                AND recipient_name like CONCAT('%', #{recipientName}, '%')
            </if>
            <if test="logisticsNo != null and logisticsNo != ''">
                AND logistics_no like CONCAT('%', #{logisticsNo}, '%')
            </if>
            <if test="logisticsCompany != null and logisticsCompany != ''">
                AND logistics_company like CONCAT('%', #{logisticsCompany}, '%')
            </if>
            <if test="settlementType != null">
                AND settlement_type = #{settlementType}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listOutgoingByLogisticsNoList" resultType="com.senox.tms.domain.OutgoingTtl">
        select id
            , receiving_date
            , shipper_name
            , recipient_name
            , recipient_address
            , recipient_contact
            , logistics_company
            , logistics_no
            , shipper_pieces
            , actual_freight_amount
            , actual_shipping_amount
            , profit_amount
            , settlement_type
        from t_outgoing_ttl
        WHERE logistics_no IN <foreach collection="logisticsNoList" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleChargesMapper">

    <select id="getDefaultEffective" resultType="com.senox.tms.domain.BicycleCharges">
        select id,
               name,
               effective_time,
               ineffective_time,
               status,
               default_effective,
               flexible,
               min_amount,
               creator_id,
               creator_name,
               create_time,
               modifier_id,
               modifier_name,
               modified_time
        from t_bicycle_charges
        where status = 2
          and default_effective = true
          and is_disabled = false;
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleRiderMapper">

    <!-- 获取最大的订单号 -->
    <select id="findMaxRiderNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(rider_no) FROM t_bicycle_rider WHERE rider_no LIKE CONCAT(#{prefix}, '%');
    </select>

    <select id="countRider" parameterType="com.senox.tms.vo.BicycleRiderSearchVo" resultType="int">
        select count(1) from t_bicycle_rider
        <where>
            <if test="riderNo != null and riderNo != ''">
                AND rider_no like CONCAT('%', #{riderNo}, '%')
            </if>
            <if test="name != null and name != ''">
                AND name like CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listRider" parameterType="com.senox.tms.vo.BicycleRiderSearchVo" resultType="com.senox.tms.domain.BicycleRider">
        select id, rider_no, name, contact, status, modified_time
        from t_bicycle_rider
        <where>
            <if test="riderNo != null and riderNo != ''">
                AND rider_no like CONCAT('%', #{riderNo}, '%')
            </if>
            <if test="name != null and name != ''">
                AND name like CONCAT('%', #{name}, '%')
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY
                    CASE status
                        WHEN 2 THEN 1
                        WHEN 0 THEN 2
                        WHEN 1 THEN 3
                        END DESC
                    , id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="dayCountRider" resultType="com.senox.tms.vo.BicycleDayCountRiderVo">
        SELECT
            SUM( CASE WHEN r.STATUS = 1 THEN 1 ELSE 0 END ) AS online_count,
            SUM( CASE WHEN r.STATUS = 0 THEN 1 ELSE 0 END ) AS offline_count
        FROM
            t_bicycle_rider r where is_disabled = 0
    </select>

    <select id="listRiderInfo" resultType="com.senox.tms.vo.BicycleRiderInfoVo">
        SELECT tbr.id as rider_id, tbr.name as rider_name, sum( CASE WHEN tbdod.STATUS IN ( 6, 7 ) THEN 1 ELSE 0 END ) AS order_count,
            (CASE
                WHEN sum( CASE WHEN tbdod.STATUS IN ( 1, 2, 3, 4, 5 ) THEN 1 ELSE 0 END ) > 0
                THEN 1 ELSE 0 END
            ) AS rider_status
        FROM t_bicycle_rider tbr
        LEFT JOIN t_bicycle_delivery_order_detail tbdod ON tbdod.rider_id = tbr.id
            and tbdod.create_time >= #{startTime} and tbdod.create_time <![CDATA[<=]]> #{endTime}
        where  tbr.status = 1 and tbr.is_disabled = 0
        GROUP BY tbr.id
    </select>
</mapper>
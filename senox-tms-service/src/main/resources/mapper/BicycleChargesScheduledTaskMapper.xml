<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleChargesScheduledTaskMapper">

    <resultMap id="taskVoResultMap" type="com.senox.tms.vo.BicycleChargesScheduledTaskVo">
             <id property="id" column="id"/>
              <result property="taskName" column="task_name"/>
              <result property="taskCount" column="task_count"/>
              <result property="model" column="model"/>
              <result property="startTime" column="start_time"/>
              <result property="chargesId" column="charges_id"/>
              <result property="chargesName" column="charges_name"/>
              <collection property="merchantInfos" ofType="com.senox.tms.vo.MerchantInfoVo">
                    <id property="merchantId" column="merchant_id"/>
                    <result property="merchantName" column="merchant_name"/>
                    <result property="rcSerial" column="rc_serial"/>
              </collection>
    </resultMap>


    <insert id="addBatch" useGeneratedKeys="true" keyProperty="id">
          insert into t_bicycle_charges_scheduled_task
          (task_name, model, charges_id, start_time, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time)
          values
          <foreach collection="tasks" item="item" separator=",">
              <trim prefix="(" suffix=")" suffixOverrides=",">
                  #{item.taskName},
                  #{item.model},
                  #{item.chargesId},
                  #{item.startTime},
                  #{item.creatorId},
                  #{item.creatorName},
                  now(),
                  #{item.modifierId},
                  #{item.modifierName},
                  now()
              </trim>
          </foreach>

    </insert>

    <insert id="addMerchant">
        insert into t_bicycle_charges_scheduled_task_merchant
        (task_id, merchant_id)
        values
        <foreach collection="merchantIds" item="item" separator=",">
           (#{taskId},#{item})
        </foreach>
    </insert>

    <update id="updateBatch">
        update t_bicycle_charges_scheduled_task
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="task_name = case" suffix="end,">
                <foreach collection="updateTasks" item="item">
                    <if test="null != item.taskName and item.taskName != ''">
                        when id = #{item.id} then #{item.taskName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="task_count = case" suffix="end,">
                <foreach collection="updateTasks" item="item">
                    <if test="null != item.taskCount">
                        when id = #{item.id} then #{item.taskCount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="charges_id = case" suffix="end,">
                <foreach collection="updateTasks" item="item">
                    <if test="null != item.chargesId">
                        when id = #{item.id} then #{item.chargesId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="model = case" suffix="end,">
                <foreach collection="updateTasks" item="item">
                    <if test="null != item.model">
                        when id = #{item.id} then #{item.model}
                    </if>
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="updateTasks" item="item">
                    <if test="null != item.startTime">
                        when id = #{item.id} then #{item.startTime}
                    </if>
                </foreach>
            </trim>
            modified_time = now()
        </trim>
        <where>
            and id in
            <foreach collection="updateTasks" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </where>
    </update>

    <delete id="deleteMerchantByTaskId">
        delete from t_bicycle_charges_scheduled_task_merchant
        <where>
            and task_id = #{taskId}
        </where>
    </delete>

    <delete id="delete">
          delete from t_bicycle_charges_scheduled_task
        <where>
            and id = #{taskId}
        </where>
    </delete>

    <select id="list" resultMap="taskVoResultMap">
            select bcst.id,
                   task_name,
                   task_count,
                   model,
                   charges_id,
                   start_time,
                   m.id as merchant_id,
                   m.name as merchant_name,
                   m.rc_serial,
                   bc.name as charges_name
            from t_bicycle_charges_scheduled_task bcst
                     left join t_bicycle_charges_scheduled_task_merchant bcstm on bcstm.task_id = bcst.id
                     left join u_merchant m on m.id = bcstm.merchant_id
                     left join t_bicycle_charges bc on bcst.charges_id = bc.id
            <where>
                and bcst.is_disabled = false
                and bc.is_disabled = false
                <if test="null != startTime">
                 and bcst.start_time >= #{startTime}
                </if>
                <if test="null != endTime">
                 and bcst.start_time &lt;= #{endTime}
                </if>
                <if test="null != maxTaskCount">
                 and bcst.task_count &lt; #{maxTaskCount}
                </if>
            </where>
            <if test="page">
                limit ${offset}, ${pageSize}
            </if>
    </select>

    <select id="listCount" resultType="int">
            select count(bcst.id)
            from t_bicycle_charges_scheduled_task bcst
                     inner join t_bicycle_charges bc on bcst.charges_id = bc.id
            <where>
                and bcst.is_disabled = false
                and bc.is_disabled = false
                <if test="null != startTime">
                 and bcst.start_time >= #{startTime}
                </if>
                <if test="null != endTime">
                 and bcst.start_time &lt;= #{endTime}
                </if>
                <if test="null != maxTaskCount">
                 and bcst.task_count &lt; #{maxTaskCount}
                </if>
            </where>
    </select>

    <select id="findById" resultMap="taskVoResultMap">
            select bcst.id,
                   task_name,
                   task_count,
                   model,
                   charges_id,
                   start_time,
                   m.id as merchant_id,
                   m.name as merchant_name,
                   m.rc_serial,
                   bc.name as charges_name
            from t_bicycle_charges_scheduled_task bcst
                     left join t_bicycle_charges_scheduled_task_merchant bcstm on bcstm.task_id = bcst.id
                     left join u_merchant m on m.id = bcstm.merchant_id
                     left join t_bicycle_charges bc on bcst.charges_id = bc.id
            <where>
                and bcst.id = #{id}
            </where>
    </select>
</mapper>

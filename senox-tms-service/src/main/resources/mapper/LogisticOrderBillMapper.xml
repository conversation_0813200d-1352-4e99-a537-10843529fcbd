<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticOrderBillMapper">

    <!-- 更新配送账单金额 -->
    <update id="updateOrderBillAmount" parameterType="java.lang.Long">
        UPDATE t_logistic_order_bill b
            INNER JOIN t_logistic_order_product p ON p.id = b.order_product_id
            INNER JOIN t_logistic_order_ship s ON s.id = b.ship_id
        SET b.order_amount = p.total_amount
            , b.ship_amount = s.total_amount
	        , b.product_owe = CASE WHEN (INSTR(p.remark, '欠款') OR INSTR(p.remark, '不代收货款') OR INSTR(p.remark, '运费由供应商承担') OR INSTR(p.remark, '订单运费不足')) THEN p.product_amount ELSE 0 END
            , b.product_paid = CASE WHEN b.is_product_paid_manual THEN b.product_paid ELSE b.order_amount - b.product_owe END
            , b.product_diversity = CASE WHEN b.is_product_paid_manual THEN b.product_paid + b.product_owe - b.order_amount ELSE 0 END
	        , b.total_amount = b.order_amount - b.product_owe - b.ship_amount
            , b.modified_time = NOW()
        WHERE b.order_product_id IN <foreach collection="idList" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <update id="updateBillAmount" parameterType="java.lang.Long">
        UPDATE t_logistic_order_bill b
            INNER JOIN t_logistic_order_product p ON p.id = b.order_product_id
            INNER JOIN t_logistic_order_ship s ON s.id = b.ship_id
        SET b.product_paid = CASE WHEN b.is_product_paid_manual THEN b.product_paid ELSE b.order_amount - b.product_owe END
            , b.product_diversity = CASE WHEN b.is_product_paid_manual THEN b.product_paid + b.product_owe - b.order_amount ELSE 0 END
            , b.total_amount = b.order_amount - b.product_owe - b.ship_amount
            , b.modified_time = NOW()
        WHERE b.order_product_id IN <foreach collection="idList" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <!-- 更新订单金额 -->
    <update id="updateOrderBillAmountByShipDates" parameterType="java.time.LocalDate">
        UPDATE t_logistic_order_bill b
            INNER JOIN t_logistic_order_product p ON p.id = b.order_product_id
            INNER JOIN t_logistic_order_ship s ON s.id = b.ship_id
        SET b.order_amount = p.total_amount
            , b.ship_amount = s.total_amount
            , b.product_owe = CASE WHEN (INSTR(p.remark, '欠款') OR INSTR(p.remark, '不代收货款') OR INSTR(p.remark, '运费由供应商承担') OR INSTR(p.remark, '订单运费不足')) THEN p.product_amount ELSE 0 END
            , b.product_paid = CASE WHEN b.is_product_paid_manual THEN b.product_paid ELSE b.order_amount - b.product_owe END
            , b.product_diversity = CASE WHEN b.is_product_paid_manual THEN b.product_paid + b.product_owe - b.order_amount ELSE 0 END
            , b.total_amount = b.order_amount - b.product_owe - b.ship_amount
            , b.modified_time = NOW()
        WHERE s.ship_date IN <foreach collection="shipDates" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <update id="updateBillAmountByShipDates" parameterType="java.time.LocalDate">
        UPDATE t_logistic_order_bill b
            INNER JOIN t_logistic_order_product p ON p.id = b.order_product_id
            INNER JOIN t_logistic_order_ship s ON s.id = b.ship_id
        SET b.product_paid = CASE WHEN b.is_product_paid_manual THEN b.product_paid ELSE b.order_amount - b.product_owe END
            , b.product_diversity = CASE WHEN b.is_product_paid_manual THEN b.product_paid + b.product_owe - b.order_amount ELSE 0 END
            , b.total_amount = b.order_amount - b.product_owe - b.ship_amount
            , b.modified_time = NOW()
        WHERE s.ship_date IN <foreach collection="shipDates" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <!-- 根据商品订单及配送订单查找账单 -->
    <select id="listOrderBillByProductAndShip" parameterType="com.senox.tms.domain.LogisticOrderBill"
            resultType="com.senox.tms.domain.LogisticOrderBill">
        SELECT id, order_product_id, ship_id, order_amount, product_paid, product_paid_man, product_diversity
            , product_owe, ship_amount, total_amount
        FROM t_logistic_order_bill
        WHERE (order_product_id, ship_id) IN <foreach collection="list" item="item" open="(" close=")" separator=",">(#{item.orderProductId}, #{item.shipId})</foreach>
    </select>

</mapper>
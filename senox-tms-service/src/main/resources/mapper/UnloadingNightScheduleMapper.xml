<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingNightScheduleMapper">

    <select id="countSchedule" parameterType="com.senox.tms.vo.UnloadingNightScheduleSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_night_schedule
        <where>
            <if test="scheduleDate != null">
                AND schedule_date = #{scheduleDate}
            </if>
            <if test="scheduleDateStart != null">
                AND schedule_date >= #{scheduleDateStart}
            </if>
            <if test="scheduleDateEnd != null">
                AND schedule_date <![CDATA[<=]]> #{scheduleDateEnd}
            </if>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="workerName != null and workerName != ''">
                AND worker_name LIKE CONCAT('%', #{workerName}, '%')
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listSchedule" parameterType="com.senox.tms.vo.UnloadingNightScheduleSearchVo" resultType="com.senox.tms.vo.UnloadingNightScheduleVo">
        SELECT
              id
            , schedule_date
            , worker_id
            , worker_no
            , worker_name
        FROM px_unloading_night_schedule
        <where>
            <if test="scheduleDate != null">
                AND schedule_date = #{scheduleDate}
            </if>
            <if test="scheduleDateStart != null">
                AND schedule_date >= #{scheduleDateStart}
            </if>
            <if test="scheduleDateEnd != null">
                AND schedule_date <![CDATA[<=]]> #{scheduleDateEnd}
            </if>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="workerName != null and workerName != ''">
                AND worker_name LIKE CONCAT('%', #{workerName}, '%')
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>

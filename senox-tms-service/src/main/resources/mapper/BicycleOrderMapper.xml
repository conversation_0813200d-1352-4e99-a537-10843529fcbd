<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleOrderMapper">

    <resultMap id="OrderVo_Result" type="com.senox.tms.vo.BicycleOrderVo">
        <result property="id" column="id"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="startPointId" column="start_point_id"/>
        <result property="startPointName" column="start_point_name"/>
        <result property="startPointDetailName" column="start_point_detail_name"/>
        <result property="endPointId" column="end_point_id"/>
        <result property="endPointName" column="end_point_name"/>
        <result property="endPointDetailName" column="end_point_detail_name"/>
        <result property="sendTimeStart" column="send_time_start"/>
        <result property="sendTimeEnd" column="send_time_end"/>
        <result property="senderId" column="sender_id"/>
        <result property="sender" column="sender"/>
        <result property="senderSerialNo" column="sender_serial_no"/>
        <result property="senderContact" column="sender_contact"/>
        <result property="senderType" column="sender_type"/>
        <result property="recipient" column="recipient"/>
        <result property="recipientContact" column="recipient_contact"/>
        <result property="carNo" column="car_no"/>
        <result property="remark" column="remark"/>
        <result property="otherRemark" column="other_remark"/>
        <result property="pieces" column="pieces"/>
        <result property="deliveryCharge" column="delivery_charge"/>
        <result property="otherCharge" column="other_charge"/>
        <result property="handlingCharge" column="handling_charge"/>
        <result property="upstairsCharge" column="upstairs_charge"/>
        <result property="totalCharge" column="total_charge"/>
        <result property="chargesId" column="charges_id"/>
        <result property="state" column="state"/>
        <result property="origin" column="origin"/>
        <result property="orderStatus" column="order_status"/>
        <result property="statusRemark" column="status_remark"/>
        <result property="orderTime" column="order_time"/>
        <result property="createOpenid" column="create_openid"/>
        <result property="settlePeriod" column="settle_period"/>
        <result property="deliveryOrderSerialNo" column="delivery_order_serial_no"/>
        <result property="status" column="delivery_status"/>
        <result property="deliveryOrderId" column="delivery_order_id"/>
        <collection property="detailVos" ofType="com.senox.tms.vo.BicycleDeliveryOrderDetailVo" resultMap="DeliveryOrderDetailVo_Result"/>
        <collection property="orderGoodsDetailVos" ofType="com.senox.tms.vo.BicycleOrderGoodsDetailVo" resultMap="OrderGoodsDetailVo_Result"/>
    </resultMap>

    <resultMap id="DeliveryOrderDetailVo_Result" type="com.senox.tms.vo.BicycleDeliveryOrderDetailVo">
        <result property="id" column="delivery_order_detail_id"/>
        <result property="status" column="status"/>
        <result property="deliveryOrderSerialNoItem" column="delivery_order_serial_no_item"/>
        <result property="pickedPieces" column="picked_pieces"/>
        <result property="deliveryOrderId" column="delivery_order_id"/>
        <result property="deliveryOrderSerialNo" column="delivery_order_serial_no"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="pickingPoint" column="picking_point"/>
        <result property="deliveryPoint" column="delivery_point"/>
        <result property="pickingTime" column="picking_time"/>
        <result property="sendTime" column="send_time"/>
        <result property="receivingTime" column="receiving_time"/>
        <result property="finishTime" column="finish_time"/>
        <result property="riderId" column="rider_id"/>
        <result property="riderName" column="rider_name"/>
        <result property="riderContact" column="rider_contact"/>
        <collection property="detailItemVos" ofType="com.senox.tms.vo.BicycleDeliveryOrderDetailItemVo" resultMap="DeliveryOrderDetailVo_Item_Result"/>
    </resultMap>

    <resultMap id="OrderGoodsDetailVo_Result" type="com.senox.tms.vo.BicycleOrderGoodsDetailVo">
        <result property="id" column="detail_id"/>
        <result property="orderId" column="id"/>
        <result property="pieces" column="detail_pieces"/>
        <result property="goodsName" column="goods_name"/>
        <result property="goodsType" column="goods_type"/>
        <result property="weight" column="weight"/>
        <result property="size" column="size"/>
    </resultMap>

    <resultMap id="DeliveryOrderDetailVo_Item_Result" type="com.senox.tms.vo.BicycleDeliveryOrderDetailItemVo">
        <result property="id" column="item_id"/>
        <result property="deliveryOrderSerialNo" column="delivery_order_serial_no"/>
        <result property="deliveryOrderSerialNoItem" column="delivery_order_serial_no_item"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="goodsId" column="item_goods_id"/>
        <result property="goodsType" column="item_goods_type"/>
        <result property="goodsName" column="item_goods_name"/>
        <result property="pieces" column="item_pieces"/>
        <result property="weight" column="item_weight"/>
        <result property="size" column="item_size"/>
    </resultMap>

    <resultMap id="Assign_OrderVo_Result" type="com.senox.tms.vo.BicycleOrderVo">
        <result property="id" column="id"/>
        <result property="orderSerialNo" column="order_serial_no"/>
        <result property="startPointName" column="start_point_name"/>
        <result property="startPointDetailName" column="start_point_detail_name"/>
        <result property="endPointName" column="end_point_name"/>
        <result property="endPointDetailName" column="end_point_detail_name"/>
        <result property="sendTimeStart" column="send_time_start"/>
        <result property="sendTimeEnd" column="send_time_end"/>
        <result property="sender" column="sender"/>
        <result property="senderSerialNo" column="sender_serial_no"/>
        <result property="recipient" column="recipient"/>
        <result property="otherRemark" column="other_remark"/>
        <result property="pieces" column="pieces"/>
        <result property="deliveryCharge" column="delivery_charge"/>
        <result property="otherCharge" column="other_charge"/>
        <result property="handlingCharge" column="handling_charge"/>
        <result property="upstairsCharge" column="upstairs_charge"/>
        <result property="totalCharge" column="total_charge"/>
        <result property="state" column="state"/>
        <result property="origin" column="origin"/>
        <result property="orderStatus" column="order_status"/>
        <result property="statusRemark" column="status_remark"/>
        <result property="orderTime" column="order_time"/>
        <result property="settlePeriod" column="settle_period"/>
        <result property="deliveryOrderSerialNo" column="delivery_order_serial_no"/>
        <result property="deliveryOrderId" column="delivery_order_id"/>
        <result property="referralDelivery" column="referral_delivery"/>
        <result property="chargesId" column="charges_id"/>
        <collection property="detailVos" ofType="com.senox.tms.vo.BicycleDeliveryOrderDetailVo" resultMap="DeliveryOrderDetailVo_Result"/>
    </resultMap>

    <!-- 获取最大的订单号 -->
    <select id="findMaxOrderNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(order_serial_no) FROM t_bicycle_order WHERE order_serial_no LIKE CONCAT(#{prefix}, '%');
    </select>

    <select id="findOrderVoById" parameterType="java.lang.Long" resultMap="OrderVo_Result">
        select o.id, o.order_serial_no, o.start_point_id, o.start_point_name, o.end_point_id, o.end_point_name, o.send_time_start, o.send_time_end
            , o.start_point_detail_name, o.end_point_detail_name,o.sender_id, o.sender, o.sender_serial_no, o.sender_contact, o.sender_type, o.recipient
             , o.recipient_contact, o.car_no, o.remark, o.other_remark, o.pieces, o.delivery_charge, o.other_charge, o.handling_charge, o.upstairs_charge, o.total_charge, o.charges_id
             , o.state, o.order_time,o.origin, o.status as order_status, o.status_remark, d.id as detail_id, d.goods_name, d.pieces as detail_pieces, tbom.media_url
             , d.goods_type, d.weight, d.size
        <if test="containStatus != null and containStatus">
            , tbdod.delivery_order_serial_no, tbdod.status, tbdod.id as delivery_order_detail_id, tbdod.rider_id, tbr.name as rider_name
            , tbdod.picking_point, tbdod.delivery_point , tbdod.picking_time, tbdod.send_time, tbdod.receiving_time, tbdod.finish_time
            , tbdod.delivery_order_serial_no_item, tbdod.picked_pieces, tbdo.status as delivery_status
        </if>
        from t_bicycle_order o
        INNER JOIN t_bicycle_order_goods_detail d ON o.id = d.order_id
        LEFT JOIN t_bicycle_order_media tbom ON tbom.order_id = o.id
        <if test="containStatus != null and containStatus">
             LEFT JOIN t_bicycle_delivery_order_detail tbdod ON tbdod.order_serial_no = o.order_serial_no
             LEFT JOIN t_bicycle_delivery_order tbdo ON tbdo.delivery_order_serial_no = tbdod.delivery_order_serial_no
             LEFT JOIN t_bicycle_rider tbr ON tbdod.rider_id = tbr.id
        </if>
        where o.id = #{id}
    </select>

    <!-- 根据订单号查询订单 -->
    <select id="findOrderVoByOrderSerialNo" parameterType="java.lang.String" resultMap="OrderVo_Result">
        select o.id, o.order_serial_no, o.start_point_id, o.start_point_name, o.end_point_id, o.end_point_name, o.send_time_start, o.send_time_end
            , o.start_point_detail_name, o.end_point_detail_name,o.sender_id, o.sender, o.sender_serial_no, o.sender_contact, o.sender_type, o.recipient
            , o.recipient_contact, o.car_no, o.remark, o.other_remark, o.pieces, o.delivery_charge, o.other_charge, o.handling_charge, o.upstairs_charge, o.total_charge, o.charges_id
            , o.state, o.order_time,o.origin, o.status as order_status, o.status_remark, d.id as detail_id, d.goods_name, d.pieces as detail_pieces, tbom.media_url
            , tbdod.delivery_order_serial_no, tbdod.status, tbdod.id as delivery_order_detail_id, tbdod.rider_id, tbr.name as rider_name, tbr.contact as rider_contact
            , tbdod.picking_point, tbdod.delivery_point , tbdod.picking_time, tbdod.send_time, tbdod.receiving_time, tbdod.finish_time
            , tbdod.delivery_order_serial_no_item, tbdod.picked_pieces, tbdo.status as delivery_status, d.goods_type, d.weight, d.size
        from t_bicycle_order o
        INNER JOIN t_bicycle_order_goods_detail d ON o.id = d.order_id
        LEFT JOIN t_bicycle_order_media tbom ON tbom.order_id = o.id
        LEFT JOIN t_bicycle_delivery_order_detail tbdod ON tbdod.order_serial_no = o.order_serial_no
        LEFT JOIN t_bicycle_delivery_order tbdo ON tbdo.delivery_order_serial_no = tbdod.delivery_order_serial_no
        LEFT JOIN t_bicycle_rider tbr ON tbdod.rider_id = tbr.id
        where o.order_serial_no = #{orderSerialNo}
    </select>

    <!-- 未分派订单合计 -->
    <select id="sumOrderUnassigned" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultType="com.senox.tms.vo.BicycleOrderVo">
        SELECT SUM(o.pieces) AS pieces
            , SUM(o.delivery_charge) AS delivery_charge
            , SUM(o.other_charge) AS other_charge
            , SUM(o.handling_charge) AS handling_charge
            , SUM(o.upstairs_charge) AS upstairs_charge
            , SUM(o.total_charge) AS total_charge
        FROM t_bicycle_order o
            INNER JOIN u_merchant u ON u.id = o.sender_id
            LEFT JOIN t_bicycle_delivery_order_detail dod ON dod.order_serial_no = o.order_serial_no
        WHERE dod.delivery_order_serial_no IS NULL
        <if test="orderSerialNo != null and orderSerialNo != ''">
            AND o.order_serial_no = #{orderSerialNo}
        </if>
        <if test="startPointId != null">
            AND o.start_point_id = #{startPointId}
        </if>
        <if test="endPointId != null">
            AND o.end_point_id = #{endPointId}
        </if>
        <if test="state != null">
            AND o.state = #{state}
        </if>
        <if test="createOpenid != null and createOpenid != ''">
            AND o.create_openid = #{createOpenid}
        </if>
        <if test="orderTimeStart != null">
            AND o.order_time >= #{orderTimeStart}
        </if>
        <if test="orderTimeEnd != null">
            AND o.order_time <![CDATA[<=]]> #{orderTimeEnd}
        </if>
        <if test="origin != null">
            and o.origin = #{origin}
        </if>
        <if test="orderStatus != null">
            and o.status = #{orderStatus}
        </if>
        <if test="settlePeriodList != null and settlePeriodList.size > 0">
            AND u.settle_period IN <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="senderId != null">
            AND o.sender_id = #{senderId}
        </if>
        <if test="senderSerialNo != null and senderSerialNo != ''">
            AND u.rc_serial = #{senderSerialNo}
        </if>
        <if test="sender != null and sender != ''">
            AND o.sender LIKE CONCAT('%', #{sender}, '%')
        </if>
        <if test="recipient != null and recipient != ''">
            AND o.recipient LIKE CONCAT('%', #{recipient}, '%')
        </if>
    </select>

    <!-- 未分派订单合计数 -->
    <select id="countOrderUnassigned" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_bicycle_order o
            INNER JOIN u_merchant u ON u.id = o.sender_id
            LEFT JOIN t_bicycle_delivery_order_detail dod ON dod.order_serial_no = o.order_serial_no
        WHERE dod.delivery_order_serial_no IS NULL
        <if test="orderSerialNo != null and orderSerialNo != ''">
            AND o.order_serial_no = #{orderSerialNo}
        </if>
        <if test="startPointId != null">
            AND o.start_point_id = #{startPointId}
        </if>
        <if test="endPointId != null">
            AND o.end_point_id = #{endPointId}
        </if>
        <if test="state != null">
            AND o.state = #{state}
        </if>
        <if test="createOpenid != null and createOpenid != ''">
            AND o.create_openid = #{createOpenid}
        </if>
        <if test="orderTimeStart != null">
            AND o.order_time >= #{orderTimeStart}
        </if>
        <if test="orderTimeEnd != null">
            AND o.order_time <![CDATA[<=]]> #{orderTimeEnd}
        </if>
        <if test="origin != null">
            and o.origin = #{origin}
        </if>
        <if test="orderStatus != null">
            and o.status = #{orderStatus}
        </if>
        <if test="settlePeriodList != null and settlePeriodList.size > 0">
            AND u.settle_period IN <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="senderId != null">
            AND o.sender_id = #{senderId}
        </if>
        <if test="senderSerialNo != null and senderSerialNo != ''">
            AND u.rc_serial = #{senderSerialNo}
        </if>
        <if test="sender != null and sender != ''">
            AND o.sender LIKE CONCAT('%', #{sender}, '%')
        </if>
        <if test="recipient != null and recipient != ''">
            AND o.recipient LIKE CONCAT('%', #{recipient}, '%')
        </if>
    </select>

    <!-- 未分派订单列表 -->
    <select id="listOrderUnassigned" parameterType="com.senox.tms.vo.BicycleBillSearchVo" resultType="com.senox.tms.vo.BicycleOrderVo">
        SELECT o.id, o.order_serial_no, o.start_point_id, o.start_point_name, o.start_point_detail_name
            , o.end_point_id, o.end_point_name, o.end_point_detail_name, o.send_time_start, o.send_time_end
            , o.sender_id, o.sender, o.sender_serial_no, o.sender_contact, o.recipient, o.recipient_contact , o.car_no
            , o.pieces, o.charges_id, o.delivery_charge, o.other_charge, o.handling_charge, o.upstairs_charge, o.total_charge
            , o.order_time, o.state, o.status AS order_status, o.status_remark, o.remark, o.other_remark, o.origin
            , u.settle_period
        FROM t_bicycle_order o
            INNER JOIN u_merchant u ON u.id = o.sender_id
            LEFT JOIN t_bicycle_delivery_order_detail dod ON dod.order_serial_no = o.order_serial_no
        WHERE dod.delivery_order_serial_no IS NULL
        <if test="orderSerialNo != null and orderSerialNo != ''">
            AND o.order_serial_no = #{orderSerialNo}
        </if>
        <if test="startPointId != null">
            AND o.start_point_id = #{startPointId}
        </if>
        <if test="endPointId != null">
            AND o.end_point_id = #{endPointId}
        </if>
        <if test="state != null">
            AND o.state = #{state}
        </if>
        <if test="createOpenid != null and createOpenid != ''">
            AND o.create_openid = #{createOpenid}
        </if>
        <if test="orderTimeStart != null">
            AND o.order_time >= #{orderTimeStart}
        </if>
        <if test="orderTimeEnd != null">
            AND o.order_time <![CDATA[<=]]> #{orderTimeEnd}
        </if>
        <if test="origin != null">
            and o.origin = #{origin}
        </if>
        <if test="orderStatus != null">
            and o.status = #{orderStatus}
        </if>
        <if test="settlePeriodList != null and settlePeriodList.size > 0">
            AND u.settle_period IN <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="senderId != null">
            AND o.sender_id = #{senderId}
        </if>
        <if test="senderSerialNo != null and senderSerialNo != ''">
            AND u.rc_serial = #{senderSerialNo}
        </if>
        <if test="sender != null and sender != ''">
            AND o.sender LIKE CONCAT('%', #{sender}, '%')
        </if>
        <if test="recipient != null and recipient != ''">
            AND o.recipient LIKE CONCAT('%', #{recipient}, '%')
        </if>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY o.order_time DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="countOrderAssigned" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_bicycle_delivery_order do
        <where>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND do.delivery_order_serial_no = #{deliveryOrderSerialNo}
            </if>
            <if test="orderTimeStart != null">
                AND do.create_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND do.create_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="merged != null">
                AND do.merged = #{merged}
            </if>
            <if test="status != null and status.size > 0">
                AND do.status IN <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="modifiedTimeStart != null">
                AND do.modified_time >= #{modifiedTimeStart}
            </if>
            <if test="modifiedTimeEnd != null">
                AND do.modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
            </if>
            <if test="(orderSerialNo != null and orderSerialNo != '') or startPointId != null or endPointId != null or senderId != null or state != null or orderStatus != null or origin != null or (createOpenid != null and createOpenid != '') or riderId != null or assignRider != null or (senderSerialNo != null and senderSerialNo != '') or (settlePeriodList != null and settlePeriodList.size > 0) or (sender != null and sender != '') or (recipient != null and recipient != '')">
                AND EXISTS (
                    SELECT d.id
                    FROM t_bicycle_order o
                        INNER JOIN t_bicycle_delivery_order_detail d ON d.order_serial_no = o.order_serial_no
                        <if test="(senderSerialNo != null and senderSerialNo != '') or (settlePeriodList != null and settlePeriodList.size > 0)">
                            LEFT JOIN u_merchant u ON u.id = o.sender_id
                        </if>
                    WHERE d.delivery_order_serial_no = do.delivery_order_serial_no
                    <if test="orderSerialNo != null and orderSerialNo != ''">
                        AND o.order_serial_no = #{orderSerialNo}
                    </if>
                    <if test="startPointId != null">
                        AND o.start_point_id = #{startPointId}
                    </if>
                    <if test="endPointId != null">
                        AND o.end_point_id = #{endPointId}
                    </if>
                    <if test="senderId != null">
                        AND o.sender_id = #{senderId}
                    </if>
                    <if test="state != null">
                        AND o.state = #{state}
                    </if>
                    <if test="orderStatus != null">
                        and o.status = #{orderStatus}
                    </if>
                    <if test="origin != null">
                        AND o.origin = #{origin}
                    </if>
                    <if test="createOpenid != null and createOpenid != ''">
                        AND o.create_openid = #{createOpenid}
                    </if>
                    <if test="riderId != null">
                        AND d.rider_id = #{riderId}
                    </if>
                    <if test="assignRider != null">
                        <choose>
                            <when test="assignRider">AND d.rider_id > 0</when>
                            <otherwise>AND d.rider_id = 0</otherwise>
                        </choose>
                    </if>
                    <if test="senderSerialNo != null and senderSerialNo != ''">
                        AND u.rc_serial = #{senderSerialNo}
                    </if>
                    <if test="settlePeriodList != null and settlePeriodList.size > 0">
                        AND u.settle_period IN <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    </if>
                    <if test="sender != null and sender != ''">
                        AND o.sender LIKE CONCAT('%', #{sender}, '%')
                    </if>
                    <if test="recipient != null and recipient != ''">
                        AND o.recipient LIKE CONCAT('%', #{recipient}, '%')
                    </if>
                )
            </if>
        </where>
    </select>

    <select id="listOrderAssigned" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultType="com.senox.tms.vo.BicycleOrderVo">
        SELECT do.delivery_order_serial_no
            , SUM(o.pieces) AS pieces
            , SUM(o.delivery_charge) AS delivery_charge
            , SUM(o.other_charge) AS other_charge
            , SUM(o.upstairs_charge) AS upstairs_charge
            , SUM(o.handling_charge) AS handling_charge
            , SUM(o.total_charge) AS total_charge
            , do.status AS delivery_status
            , GROUP_CONCAT(dt.rider_name) AS rider_name
        FROM t_bicycle_delivery_order do
            INNER JOIN (
                SELECT dd.delivery_order_serial_no, dd.order_serial_no, GROUP_CONCAT(r.name) AS rider_name
                FROM t_bicycle_delivery_order_detail dd
                    LEFT JOIN t_bicycle_rider r ON r.id = dd.rider_id
                GROUP BY dd.delivery_order_serial_no, dd.order_serial_no
            ) dt ON dt.delivery_order_serial_no = do.delivery_order_serial_no
            INNER JOIN t_bicycle_order o ON o.order_serial_no = dt.order_serial_no
            LEFT JOIN u_merchant u ON u.id = o.sender_id
        <where>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND o.order_serial_no = #{orderSerialNo}
            </if>
            <if test="startPointId != null">
                AND o.start_point_id = #{startPointId}
            </if>
            <if test="endPointId != null">
                AND o.end_point_id = #{endPointId}
            </if>
            <if test="senderId != null">
                AND o.sender_id = #{senderId}
            </if>
            <if test="state != null">
                AND o.state = #{state}
            </if>
            <if test="origin != null">
                AND o.origin = #{origin}
            </if>
            <if test="orderStatus != null">
                and o.status = #{orderStatus}
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND o.create_openid = #{createOpenid}
            </if>
            <if test="senderSerialNo != null and senderSerialNo != ''">
                AND u.rc_serial = #{senderSerialNo}
            </if>
            <if test="settlePeriodList != null and settlePeriodList.size > 0">
                AND u.settle_period IN <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND do.delivery_order_serial_no = #{deliveryOrderSerialNo}
            </if>
            <if test="orderTimeStart != null">
                AND do.create_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND do.create_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="status != null and status.size > 0">
                AND do.status IN <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="merged != null">
                AND do.merged = #{merged}
            </if>
            <if test="assignRider != null">
                <choose>
                    <when test="assignRider">AND LENGTH(dt.rider_name) > 0</when>
                    <otherwise>AND LENGTH(dt.rider_name) = 0</otherwise>
                </choose>
            </if>
            <if test="riderId != null">
                AND EXISTS (SELECT dd.id FROM t_bicycle_delivery_order_detail dd where dd.delivery_order_serial_no = do.delivery_order_serial_no and dd.rider_id = 50)
            </if>
            <if test="modifiedTimeStart != null">
                AND do.modified_time >= #{modifiedTimeStart}
            </if>
            <if test="modifiedTimeEnd != null">
                AND do.modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
            </if>
            <if test="sender != null and sender != ''">
                AND o.sender LIKE CONCAT('%', #{sender}, '%')
            </if>
            <if test="recipient != null and recipient != ''">
                AND o.recipient LIKE CONCAT('%', #{recipient}, '%')
            </if>
        </where>
        GROUP BY do.delivery_order_serial_no, do.status
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY do.status, do.delivery_order_serial_no DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="count" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultType="int">
        select count(*)
        from t_bicycle_order tbo
            inner join u_merchant u on u.id = tbo.sender_id
            left join (
                    select tbdod.order_serial_no, tbdod.delivery_order_serial_no, GROUP_CONCAT(tbr.name) as rider_name
                         , GROUP_CONCAT( tbdod.rider_id ) AS rider_id, GROUP_CONCAT( tbdod.status ) as status
                         , tbdo.modified_time, tbdo.merged, min(tbdod.status) as delivery_status
                    from t_bicycle_delivery_order_detail tbdod
                    left join t_bicycle_delivery_order tbdo  on tbdo.delivery_order_serial_no = tbdod.delivery_order_serial_no
                    left join t_bicycle_rider tbr on tbr.id = tbdod.rider_id
                    group by order_serial_no, delivery_order_serial_no
            )  as od on od.order_serial_no = tbo.order_serial_no
        <where>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND tbo.order_serial_no = #{orderSerialNo}
            </if>
            <if test="startPointId != null">
                AND tbo.start_point_id = #{startPointId}
            </if>
            <if test="endPointId != null">
                AND tbo.end_point_id = #{endPointId}
            </if>
            <if test="state != null">
                AND tbo.state = #{state}
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND tbo.create_openid = #{createOpenid}
            </if>
            <if test="orderTimeStart != null">
                AND tbo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND tbo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="null != origin">
                and tbo.origin = #{origin}
            </if>
            <if test="orderStatus != null">
                and tbo.status = #{orderStatus}
            </if>
            <if test="recipient != null and recipient != ''">
                AND tbo.recipient like CONCAT('%', #{recipient}, '%')
            </if>
            <if test="senderId != null">
                AND tbo.sender_id = #{senderId}
            </if>
            <if test="sender != null and sender != ''">
                AND tbo.sender like CONCAT('%', #{sender}, '%')
            </if>
            <if test="settlePeriodList != null and settlePeriodList.size > 0">
                AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="senderSerialNo != null and senderSerialNo != ''">
                AND u.rc_serial = #{senderSerialNo}
            </if>
            <if test="delivery != null and delivery == true">
                AND od.delivery_order_serial_no is not null
            </if>
            <if test="delivery != null and delivery == false">
                AND od.delivery_order_serial_no is null
            </if>
            <if test="assignRider != null and assignRider == true">
                AND od.rider_name is NOT NULL
            </if>
            <if test="assignRider != null and assignRider == false">
                AND od.rider_name is NULL
            </if>
            <if test="riderId != null">
                AND FIND_IN_SET(#{riderId}, od.rider_id) > 0
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND od.delivery_order_serial_no = #{deliveryOrderSerialNo}
            </if>
            <if test="status != null and status.size > 0">
                AND od.delivery_status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="modifiedTimeStart != null">
                AND od.modified_time >= #{modifiedTimeStart}
            </if>
            <if test="modifiedTimeEnd != null">
                AND od.modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
            </if>
            <if test="merged != null and merged == true">
                AND od.merged = 1
            </if>
            <if test="merged != null and merged == false">
                AND od.merged = 0
            </if>
        </where>
    </select>

    <select id="listOrder" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultMap="OrderVo_Result">
        select tbo.id, tbo.order_serial_no, tbo.start_point_id, tbo.start_point_name, tbo.end_point_id, tbo.end_point_name, tbo.send_time_start
            , tbo.send_time_end, tbo.sender_id, tbo.sender, u.rc_serial as sender_serial_no, tbo.sender_contact, tbo.sender_type, tbo.recipient
            , tbo.recipient_contact, tbo.car_no, tbo.remark, tbo.other_remark, tbo.pieces, tbo.delivery_charge, tbo.other_charge
            , tbo.handling_charge, tbo.upstairs_charge, tbo.total_charge, tbo.charges_id, tbo.state,tbo.origin
            , tbo.status as order_status, tbo.status_remark, tbo.order_time, tbo.start_point_detail_name, tbo.end_point_detail_name, u.settle_period
            , od.delivery_order_serial_no, od.delivery_order_id, od.rider_name, od.delivery_status
        from t_bicycle_order tbo
            inner join u_merchant u on u.id = tbo.sender_id
            left join (
                select tbdo.id as delivery_order_id, tbdod.order_serial_no, tbdod.delivery_order_serial_no
                     , GROUP_CONCAT(tbr.name) as rider_name, GROUP_CONCAT( tbdod.rider_id ) AS rider_id
                     , GROUP_CONCAT( tbdod.status ) as status, tbdo.modified_time, tbdo.merged, min(tbdod.status) as delivery_status
                from t_bicycle_delivery_order_detail tbdod
                left join t_bicycle_delivery_order tbdo  on tbdo.delivery_order_serial_no = tbdod.delivery_order_serial_no
                left join t_bicycle_rider tbr on tbr.id = tbdod.rider_id
                group by order_serial_no, delivery_order_serial_no
            )  as od on od.order_serial_no = tbo.order_serial_no
        <where>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND tbo.order_serial_no = #{orderSerialNo}
            </if>
            <if test="startPointId != null">
                AND tbo.start_point_id = #{startPointId}
            </if>
            <if test="endPointId != null">
                AND tbo.end_point_id = #{endPointId}
            </if>
            <if test="state != null">
                AND tbo.state = #{state}
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND tbo.create_openid = #{createOpenid}
            </if>
            <if test="orderTimeStart != null">
                AND tbo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND tbo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="null != origin">
                and tbo.origin = #{origin}
            </if>
            <if test="orderStatus != null">
                and tbo.status = #{orderStatus}
            </if>
            <if test="settlePeriodList != null and settlePeriodList.size > 0">
                AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="recipient != null and recipient != ''">
                AND tbo.recipient like CONCAT('%', #{recipient}, '%')
            </if>
            <if test="senderId != null">
                AND tbo.sender_id = #{senderId}
            </if>
            <if test="sender != null and sender != ''">
                AND tbo.sender like CONCAT('%', #{sender}, '%')
            </if>
            <if test="senderSerialNo != null and senderSerialNo != ''">
                AND u.rc_serial = #{senderSerialNo}
            </if>
            <if test="delivery != null and delivery == true">
                AND od.delivery_order_serial_no is not null
            </if>
            <if test="delivery != null and delivery == false">
                AND od.delivery_order_serial_no is null
            </if>
            <if test="assignRider != null and assignRider == true">
                AND od.rider_name is NOT NULL
            </if>
            <if test="assignRider != null and assignRider == false">
                AND od.rider_name is NULL
            </if>
            <if test="riderId != null">
                AND FIND_IN_SET(#{riderId}, od.rider_id) > 0
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND od.delivery_order_serial_no = #{deliveryOrderSerialNo}
            </if>
            <if test="status != null and status.size > 0">
                AND od.delivery_status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="modifiedTimeStart != null">
                AND od.modified_time >= #{modifiedTimeStart}
            </if>
            <if test="modifiedTimeEnd != null">
                AND od.modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
            </if>
            <if test="merged != null and merged == true">
                AND od.merged = 1
            </if>
            <if test="merged != null and merged == false">
                AND od.merged = 0
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY od.status, tbo.order_time DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumOrder" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultType="com.senox.tms.vo.BicycleOrderVo">
        select SUM(tbo.pieces) as pieces
             , SUM(tbo.delivery_charge) as delivery_charge
             , SUM(tbo.other_charge) as other_charge
             , SUM(tbo.handling_charge) as handling_charge
             , SUM(tbo.upstairs_charge) as upstairs_charge
             , SUM(tbo.total_charge) as total_charge
        from t_bicycle_order tbo
            inner join u_merchant u on u.id = tbo.sender_id
            left join (
                select tbdod.order_serial_no, tbdod.delivery_order_serial_no
                    , GROUP_CONCAT(tbr.name) as rider_name, GROUP_CONCAT( tbdod.rider_id ) AS rider_id
                    , GROUP_CONCAT( tbdod.status ) as status, tbdo.modified_time, tbdo.merged, min(tbdod.status) as delivery_status
                from t_bicycle_delivery_order_detail tbdod
                left join t_bicycle_delivery_order tbdo  on tbdo.delivery_order_serial_no = tbdod.delivery_order_serial_no
                left join t_bicycle_rider tbr on tbr.id = tbdod.rider_id
                group by order_serial_no, delivery_order_serial_no
            )  as od on od.order_serial_no = tbo.order_serial_no
        <where>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND tbo.order_serial_no = #{orderSerialNo}
            </if>
            <if test="startPointId != null">
                AND tbo.start_point_id = #{startPointId}
            </if>
            <if test="endPointId != null">
                AND tbo.end_point_id = #{endPointId}
            </if>
            <if test="state != null">
                AND tbo.state = #{state}
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND tbo.create_openid = #{createOpenid}
            </if>
            <if test="orderTimeStart != null">
                AND tbo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND tbo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="null != origin">
                and tbo.origin = #{origin}
            </if>
            <if test="orderStatus != null">
                and tbo.status = #{orderStatus}
            </if>
            <if test="recipient != null and recipient != ''">
                AND tbo.recipient like CONCAT('%', #{recipient}, '%')
            </if>
            <if test="senderId != null">
                AND tbo.sender_id = #{senderId}
            </if>
            <if test="sender != null and sender != ''">
                AND tbo.sender like CONCAT('%', #{sender}, '%')
            </if>
            <if test="settlePeriodList != null and settlePeriodList.size > 0">
                AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="senderSerialNo != null and senderSerialNo != ''">
                AND u.rc_serial = #{senderSerialNo}
            </if>
            <if test="delivery != null and delivery == true">
                AND od.delivery_order_serial_no is not null
            </if>
            <if test="delivery != null and delivery == false">
                AND od.delivery_order_serial_no is null
            </if>
            <if test="assignRider != null and assignRider == true">
                AND od.rider_name is NOT NULL
            </if>
            <if test="assignRider != null and assignRider == false">
                AND od.rider_name is NULL
            </if>
            <if test="riderId != null">
                AND FIND_IN_SET(#{riderId}, od.rider_id) > 0
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND od.delivery_order_serial_no = #{deliveryOrderSerialNo}
            </if>
            <if test="status != null and status.size > 0">
                AND od.delivery_status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="modifiedTimeStart != null">
                AND od.modified_time >= #{modifiedTimeStart}
            </if>
            <if test="modifiedTimeEnd != null">
                AND od.modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
            </if>
            <if test="merged != null and merged == true">
                AND od.merged = 1
            </if>
            <if test="merged != null and merged == false">
                AND od.merged = 0
            </if>
        </where>
    </select>

    <select id="sumOrders" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultType="com.senox.tms.vo.BicycleOrderVo">
        SELECT SUM(o.pieces) AS pieces
            , SUM(o.delivery_charge) AS delivery_charge
            , SUM(o.other_charge) AS other_charge
            , SUM(o.handling_charge) AS handling_charge
            , SUM(o.upstairs_charge) AS upstairs_charge
            , SUM(o.total_charge) AS total_charge
        FROM t_bicycle_order o
        <if test="settlePeriodList != null and settlePeriodList.size() > 0 OR (senderSerialNo != null and senderSerialNo != '')">
            INNER JOIN u_merchant u ON u.id = tbo.sender_id
        </if>
        <where>
            <if test="orderTimeStart != null">
                AND o.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND o.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND o.order_serial_no = #{orderSerialNo}
            </if>
            <if test="startPointId != null">
                AND o.start_point_id = #{startPointId}
            </if>
            <if test="endPointId != null">
                AND o.end_point_id = #{endPointId}
            </if>
            <if test="createOpenid != null and createOpenid != ''">
                AND o.create_openid = #{createOpenid}
            </if>
            <if test="origin != null">
                AND o.origin = #{origin}
            </if>
            <if test="state != null">
                AND o.state = #{state}
            </if>
            <if test="orderStatus != null">
                AND o.status = #{orderStatus}
            </if>
            <if test="senderId != null">
                AND o.sender_id = #{senderId}
            </if>
            <if test="settlePeriodList != null and settlePeriodList.size() > 0">
                AND u.settle_period IN <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="senderSerialNo != null and senderSerialNo != ''">
                AND u.rc_serial = #{senderSerialNo}
            </if>
            <if test="sender != null and sender != ''">
                AND o.sender LIKE CONCAT('%', #{sender}, '%')
            </if>
            <if test="recipient != null and recipient != ''">
                AND o.recipient LIKE CONCAT('%', #{recipient}, '%')
            </if>
            <if test="delivery != null or assignRider != null or (deliveryOrderSerialNo != null and deliveryOrderSerialNo != '') or (status != null and status.size() > 0) or modifiedTimeStart != null or modifiedTimeEnd != null or riderId != null or assignRider != null">
                AND
                <choose>
                    <when test="(delivery != null and !delivery) or (assignRider != null and !assignRider)"> NOT EXISTS </when>
                    <otherwise> EXISTS </otherwise>
                </choose>
                (
                    SELECT od.id
                    FROM t_bicycle_delivery_order_detail od
                    WHERE od.order_serial_no = o.order_serial_no
                    <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                        AND od.delivery_order_serial_no = #{deliveryOrderSerialNo}
                    </if>
                    <if test="status != null and status.size > 0">
                        AND od.delivery_status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    </if>
                    <if test="modifiedTimeStart != null">
                        AND od.modified_time >= #{modifiedTimeStart}
                    </if>
                    <if test="modifiedTimeEnd != null">
                        AND od.modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
                    </if>
                    <if test="riderId != null">
                        AND od.rider_id = #{riderId}
                    </if>
                    <if test="merged != null">
                        AMD od.merged = #{merged}
                    </if>
                    <if test="assignRider != null">
                        AND od.rider_id > 0
                    </if>
                )
            </if>
            AND o.is_disabled = 0
        </where>
    </select>

    <select id="assignCount" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultType="int">
        select count(t.delivery_order_serial_no)
        from t_bicycle_delivery_order tbdo
        inner join (
            select tbdod.delivery_order_serial_no, GROUP_CONCAT( tbdod.status ) as status
            from t_bicycle_order tbo
            inner join u_merchant u on u.id = tbo.sender_id
            INNER join t_bicycle_delivery_order_detail tbdod on tbo.order_serial_no = tbdod.order_serial_no
            left join t_bicycle_rider tbr on tbr.id = tbdod.rider_id
            <where>
                <if test="riderId != null">
                    AND tbdod.rider_id = #{riderId}
                </if>
                <if test="orderSerialNo != null and orderSerialNo != ''">
                    AND tbo.order_serial_no = #{orderSerialNo}
                </if>
                <if test="startPointId != null">
                    AND tbo.start_point_id = #{startPointId}
                </if>
                <if test="endPointId != null">
                    AND tbo.end_point_id = #{endPointId}
                </if>
                <if test="state != null">
                    AND tbo.state = #{state}
                </if>
                <if test="createOpenid != null and createOpenid != ''">
                    AND tbo.create_openid = #{createOpenid}
                </if>
                <if test="orderTimeStart != null">
                    AND tbo.order_time >= #{orderTimeStart}
                </if>
                <if test="orderTimeEnd != null">
                    AND tbo.order_time <![CDATA[<=]]> #{orderTimeEnd}
                </if>
                <if test="null != origin">
                    and tbo.origin = #{origin}
                </if>
                <if test="orderStatus != null">
                    and tbo.status = #{orderStatus}
                </if>
                <if test="recipient != null and recipient != ''">
                    AND tbo.recipient like CONCAT('%', #{recipient}, '%')
                </if>
                <if test="senderId != null">
                    AND tbo.sender_id = #{senderId}
                </if>
                <if test="sender != null and sender != ''">
                    AND tbo.sender like CONCAT('%', #{sender}, '%')
                </if>
                <if test="settlePeriodList != null and settlePeriodList.size > 0">
                    AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
                </if>
                <if test="senderSerialNo != null and senderSerialNo != ''">
                    AND u.rc_serial = #{senderSerialNo}
                </if>
                <if test="delivery != null and delivery == true">
                    AND tbdod.delivery_order_serial_no is not null
                </if>
                <if test="delivery != null and delivery == false">
                    AND tbdod.delivery_order_serial_no is null
                </if>
                <if test="assignRider != null and assignRider == true">
                    AND tbr.name is NOT NULL
                </if>
                <if test="assignRider != null and assignRider == false">
                    AND tbr.name is NULL
                </if>
            </where>
            GROUP BY tbdod.delivery_order_serial_no
        ) as t on t.delivery_order_serial_no = tbdo.delivery_order_serial_no
        <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
            AND tbdo.delivery_order_serial_no = #{deliveryOrderSerialNo}
        </if>
        <if test="status != null and status.size > 0">
            AND tbdo.status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="modifiedTimeStart != null">
            AND tbdo.modified_time >= #{modifiedTimeStart}
        </if>
        <if test="modifiedTimeEnd != null">
            AND tbdo.modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
        </if>
        <if test="merged != null and merged == true">
            AND tbdo.merged = 1
        </if>
        <if test="merged != null and merged == false">
            AND tbdo.merged = 0
        </if>
    </select>

    <select id="findOperateAnalysis" resultType="com.senox.tms.vo.BicycleOperateAnalysisVo">
        select SUM(tbo.pieces) as total_pieces, SUM(tbo.total_charge) as total_amount, COUNT(tbo.id) as total_count
        from t_bicycle_order tbo
        where tbo.order_time >= #{startTime} and tbo.order_time <![CDATA[<=]]> #{endTime} and tbo.state = 1 and tbo.status = 0
    </select>

    <select id="avgSecond" resultType="java.lang.Integer">
        SELECT
            ROUND( AVG( tbdod.picking_time - tbdod. create_time ), 0 ) as time
        FROM
            t_bicycle_delivery_order_detail tbdod
            INNER JOIN t_bicycle_order tbo ON tbo.order_serial_no = tbdod.order_serial_no
        WHERE
            tbdod.STATUS IN ( 6, 7 ) and tbo.state = 1 and tbo.origin = 0 and tbo.status = 0
          AND tbo.order_time >= #{startTime} AND tbo.order_time <![CDATA[<=]]> #{endTime}
    </select>

    <select id="findBestRider" resultType="com.senox.tms.vo.BicycleOperateAnalysisVo">
        SELECT tbr.id as best_rider_id, tbr.name as best_rider_name FROM t_bicycle_delivery_order_detail tbdod
            INNER JOIN t_bicycle_order tbo ON tbo.order_serial_no = tbdod.order_serial_no
            INNER JOIN t_bicycle_rider tbr ON tbr.id = tbdod.rider_id
        WHERE tbdod.STATUS IN ( 6, 7 ) and tbo.state = 1 and tbo.status = 0 AND tbdod.receiving_time >= #{startTime} AND tbdod.receiving_time <![CDATA[<=]]> #{endTime}
        GROUP BY tbdod.id
        ORDER BY count(tbdod.id) desc, SUM(tbo.pieces) desc
        LIMIT 1
    </select>

    <select id="findBestCustomer" resultType="com.senox.tms.vo.BicycleOperateAnalysisVo">
        SELECT tbo.sender_id as best_sender_id, um.name as best_customer_name FROM t_bicycle_order tbo
        INNER JOIN u_merchant um ON um.id = tbo.sender_id
        WHERE tbo.order_time >= #{startTime} AND tbo.order_time <![CDATA[<=]]> #{endTime} and tbo.state = 1 and tbo.status = 0
        GROUP BY tbo.sender_id
        ORDER BY count(tbo.id) desc, SUM(tbo.pieces) desc
        LIMIT 1
    </select>

    <select id="listPointCount" resultType="com.senox.tms.vo.BicyclePointCountVo">
        SELECT dbp.name point_name, count(tbo.id) as use_count from dict_bicycle_point dbp
        <choose>
            <when test="isStart != null and isStart == true">
                INNER JOIN t_bicycle_order tbo ON dbp.id = tbo.start_point_id
                    and tbo.order_time >= #{startTime} and tbo.order_time <![CDATA[<=]]> #{endTime} and tbo.state = 1 and tbo.status = 0
            </when>
            <when test="isStart != null and isStart == false">
                INNER JOIN t_bicycle_order tbo ON dbp.id = tbo.end_point_id
                    and tbo.order_time >= #{startTime} and tbo.order_time <![CDATA[<=]]> #{endTime} and tbo.state = 1 and tbo.status = 0
            </when>
        </choose>
        GROUP BY dbp.id
    </select>

    <select id="listOrderCountRanking" resultType="com.senox.tms.vo.BicycleOrderCountRankingVo">
        select um.name as sender, count(tbo.id) as total_count from t_bicycle_order tbo
        INNER JOIN u_merchant um ON um.id = tbo.sender_id
        where tbo.state = 1 and tbo.status = 0 and tbo.order_time >= #{startTime} and tbo.order_time <![CDATA[<=]]> #{endTime}
        GROUP BY tbo.sender_id
        order by count(tbo.id) desc
        LIMIT 5
    </select>

    <select id="listPiecesCountRanking" resultType="com.senox.tms.vo.BicyclePiecesCountRankingVo">
        select um.name as sender, sum(tbo.pieces) as total_pieces from t_bicycle_order tbo
        INNER JOIN u_merchant um ON um.id = tbo.sender_id
        where tbo.state = 1 and tbo.status = 0 and tbo.order_time >= #{startTime} and tbo.order_time <![CDATA[<=]]> #{endTime}
        GROUP BY tbo.sender_id
        order by sum(tbo.pieces) desc
        LIMIT 5
    </select>

    <select id="listAmountCountRanking" resultType="com.senox.tms.vo.BicycleAmountCountRankingVo">
        select um.name as sender, sum(tbo.total_charge) as total_amount from t_bicycle_order tbo
        INNER JOIN u_merchant um ON um.id = tbo.sender_id
        where tbo.state = 1  and tbo.status = 0 and tbo.order_time >= #{startTime} and tbo.order_time <![CDATA[<=]]> #{endTime}
        GROUP BY tbo.sender_id
        order by sum(tbo.total_charge) desc
        LIMIT 5
    </select>

    <select id="undoOrderCount" resultType="java.lang.Integer">
        SELECT(
                (
                    SELECT count(t1.undo_count) as undo_count
                    from (
                             select tbdod.delivery_order_serial_no as undo_count FROM t_bicycle_delivery_order_detail tbdod
                             INNER JOIN t_bicycle_order tbo ON tbo.order_serial_no = tbdod.order_serial_no
                             WHERE tbo.state = 1 AND tbo.status = 0 AND tbdod.STATUS in (0, 1)  AND tbdod.rider_id = 0 AND tbdod.delivery_order_serial_no IS NOT NULL
                             GROUP BY tbdod.delivery_order_serial_no
                    ) as t1
                )
                +
                (
                    SELECT count( 1 ) AS undo_count
                    FROM t_bicycle_order tbo
                    LEFT JOIN t_bicycle_delivery_order_detail tbdod ON tbo.order_serial_no = tbdod.order_serial_no
                    WHERE tbo.state = 1 AND tbo.status = 0 AND tbdod.delivery_order_serial_no IS NULL
                )
        ) AS undo_count
    </select>

    <select id="customerCount" parameterType="com.senox.tms.vo.BicycleCustomerCountSearchVo" resultType="int">
        SELECT COUNT(1) FROM (
            SELECT count(1) , SUM(CASE WHEN DATE(tbo.order_time) = CURDATE() THEN 1 ELSE 0 END) AS today_count
        FROM t_bicycle_order tbo
        LEFT JOIN u_merchant um ON um.id = tbo.sender_id
            <where>
                    tbo.state = 1 and tbo.status = 0
                <if test="merchantName != null and merchantName != ''">
                    AND um.name LIKE CONCAT('%', #{merchantName}, '%')
                </if>
                <if test="merchantId != null">
                    AND tbo.sender_id = #{merchantId}
                </if>
            </where>
            GROUP BY tbo.sender_id
            HAVING today_count > 0
        ) t
    </select>

    <select id="customerCountList" parameterType="com.senox.tms.vo.BicycleCustomerCountSearchVo" resultType="com.senox.tms.vo.BicycleCustomerCountVo">
        SELECT CURDATE() as today_date,
                um.name as merchant_name,
                tbo.sender_id as merchant_id,
               SUM(CASE WHEN DATE(tbo.order_time) = CURDATE() THEN 1 ELSE 0 END) AS today_count,
               SUM(CASE WHEN DATE(tbo.order_time) = CURDATE() THEN tbo.pieces ELSE 0 END) AS today_pieces,
               SUM(CASE WHEN DATE(tbo.order_time) = CURDATE() THEN tbo.total_charge ELSE 0 END) AS today_cost
        FROM t_bicycle_order tbo
            LEFT JOIN u_merchant um ON um.id = tbo.sender_id
        <where>
                tbo.state = 1 and tbo.status = 0
            <if test="merchantName != null and merchantName != ''">
                AND um.name LIKE CONCAT('%', #{merchantName}, '%')
            </if>
            <if test="merchantId != null">
                AND tbo.sender_id = #{merchantId}
            </if>
        </where>
        GROUP BY tbo.sender_id
        HAVING today_count > 0
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY today_count desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumCustomerCount" parameterType="com.senox.tms.vo.BicycleCustomerCountSearchVo" resultType="com.senox.tms.vo.BicycleCustomerCountVo">
        SELECT
            SUM(CASE WHEN DATE(tbo.order_time) = CURDATE() THEN 1 ELSE 0 END) AS today_count,
            SUM(CASE WHEN DATE(tbo.order_time) = CURDATE() THEN tbo.pieces ELSE 0 END) AS today_pieces,
            SUM(CASE WHEN DATE(tbo.order_time) = CURDATE() THEN tbo.total_charge ELSE 0 END) AS today_cost
        FROM t_bicycle_order tbo
            LEFT JOIN u_merchant um ON um.id = tbo.sender_id
        <where>
                tbo.state = 1 and tbo.status = 0
            <if test="merchantName != null and merchantName != ''">
                AND um.name LIKE CONCAT('%', #{merchantName}, '%')
            </if>
            <if test="merchantId != null">
                AND tbo.sender_id = #{merchantId}
            </if>
        </where>
    </select>

    <select id="orderCount" parameterType="com.senox.tms.vo.BicycleOrderCountSearchVo" resultType="com.senox.tms.vo.BicycleOrderCountVo">
        SELECT
            sum( CASE WHEN od.delivery_order_serial_no IS NULL THEN 1 ELSE 0 END ) AS total_count,
            sum( CASE WHEN od.id IS NULL THEN 1 ELSE 0 END ) AS undo_count
        FROM
            t_bicycle_order tbo
            LEFT JOIN (
                SELECT tbdo.id, tbdod.delivery_order_serial_no, tbdod.order_serial_no FROM t_bicycle_delivery_order_detail tbdod
                LEFT JOIN t_bicycle_delivery_order tbdo ON tbdo.delivery_order_serial_no = tbdod.delivery_order_serial_no
                GROUP BY tbdod.delivery_order_serial_no, tbdod.order_serial_no
            ) as od on od.order_serial_no = tbo.order_serial_no
        WHERE
            tbo.state = 1 AND tbo.status = 0
        <if test="startDate != null">
            AND tbo.order_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND tbo.order_time <![CDATA[<=]]> #{endDate}
        </if>
    </select>

    <select id="deliveryOrderCount" parameterType="com.senox.tms.vo.BicycleOrderCountSearchVo" resultType="com.senox.tms.vo.BicycleOrderCountVo">
        SELECT
            SUM( CASE WHEN t.total_count > 0 THEN 1 ELSE 0 END ) AS total_count,
            SUM( CASE WHEN t.undo_count > 0 THEN 1 ELSE 0 END ) AS undo_count,
            SUM( CASE WHEN t.doing_count > 0 THEN 1 ELSE 0 END ) AS doing_count
        FROM
        (
            SELECT
                SUM( CASE WHEN tbdo.delivery_order_serial_no IS NOT NULL THEN 1 ELSE 0 END ) AS total_count,
                SUM( CASE WHEN tbdo.status = 0 THEN 1 ELSE 0 END ) AS undo_count,
                SUM( CASE WHEN tbdo.`status` IN ( 1, 2, 3, 4, 5 ) THEN 1 ELSE 0 END ) AS doing_count
            FROM
                t_bicycle_order tbo
                    LEFT JOIN t_bicycle_delivery_order_detail tbdod ON tbdod.order_serial_no = tbo.order_serial_no
                    LEFT JOIN t_bicycle_delivery_order tbdo ON tbdo.delivery_order_serial_no = tbdod.delivery_order_serial_no
            WHERE
                tbo.state = 1 AND tbo.status = 0
            <if test="startDate != null">
                AND tbo.order_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND tbo.order_time <![CDATA[<=]]> #{endDate}
            </if>
            GROUP BY
                tbdo.id
        ) AS t
    </select>

    <select id="listAssignOrder" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultType="java.lang.String">
        select tbdo.delivery_order_serial_no
        from t_bicycle_delivery_order tbdo inner join (
            select tbdod.delivery_order_serial_no, GROUP_CONCAT( tbdod.status ) as status
            from t_bicycle_order tbo
                inner join u_merchant u on u.id = tbo.sender_id
                INNER join t_bicycle_delivery_order_detail tbdod on tbo.order_serial_no = tbdod.order_serial_no
                left join t_bicycle_rider tbr on tbr.id = tbdod.rider_id
                <where>
                    <if test="riderId != null">
                        AND tbdod.rider_id = #{riderId}
                    </if>
                    <if test="orderSerialNo != null and orderSerialNo != ''">
                        AND tbo.order_serial_no = #{orderSerialNo}
                    </if>
                    <if test="startPointId != null">
                        AND tbo.start_point_id = #{startPointId}
                    </if>
                    <if test="endPointId != null">
                        AND tbo.end_point_id = #{endPointId}
                    </if>
                    <if test="state != null">
                        AND tbo.state = #{state}
                    </if>
                    <if test="createOpenid != null and createOpenid != ''">
                        AND tbo.create_openid = #{createOpenid}
                    </if>
                    <if test="orderTimeStart != null">
                        AND tbo.order_time >= #{orderTimeStart}
                    </if>
                    <if test="orderTimeEnd != null">
                        AND tbo.order_time <![CDATA[<=]]> #{orderTimeEnd}
                    </if>
                    <if test="null != origin">
                        and tbo.origin = #{origin}
                    </if>
                    <if test="orderStatus != null">
                        and tbo.status = #{orderStatus}
                    </if>
                    <if test="recipient != null and recipient != ''">
                        AND tbo.recipient like CONCAT('%', #{recipient}, '%')
                    </if>
                    <if test="senderId != null">
                        AND tbo.sender_id = #{senderId}
                    </if>
                    <if test="sender != null and sender != ''">
                        AND tbo.sender like CONCAT('%', #{sender}, '%')
                    </if>
                    <if test="settlePeriodList != null and settlePeriodList.size > 0">
                        AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    </if>
                    <if test="senderSerialNo != null and senderSerialNo != ''">
                        AND u.rc_serial = #{senderSerialNo}
                    </if>
                    <if test="delivery != null and delivery == true">
                        AND tbdod.delivery_order_serial_no is not null
                    </if>
                    <if test="delivery != null and delivery == false">
                        AND tbdod.delivery_order_serial_no is null
                    </if>
                    <if test="assignRider != null and assignRider == true">
                        AND tbr.name is NOT NULL
                    </if>
                    <if test="assignRider != null and assignRider == false">
                        AND tbr.name is NULL
                    </if>
                </where>
            GROUP BY tbdod.delivery_order_serial_no
        ) as t on t.delivery_order_serial_no = tbdo.delivery_order_serial_no
        <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
            AND tbdo.delivery_order_serial_no = #{deliveryOrderSerialNo}
        </if>
        <if test="status != null and status.size > 0">
            AND tbdo.status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="modifiedTimeStart != null">
            AND tbdo.modified_time >= #{modifiedTimeStart}
        </if>
        <if test="modifiedTimeEnd != null">
            AND tbdo.modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
        </if>
        <if test="merged != null and merged == true">
            AND tbdo.merged = 1
        </if>
        <if test="merged != null and merged == false">
            AND tbdo.merged = 0
        </if>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY tbdo.status, tbdo.create_time DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="listByDeliveryOrderSerialList" resultMap="Assign_OrderVo_Result">
        SELECT tbo.id, tbo.order_serial_no, tbo.start_point_name, tbo.end_point_name, tbo.send_time_start, tbo.send_time_end
            , tbo.sender, u.rc_serial AS sender_serial_no, tbo.recipient, tbo.other_remark, tbo.pieces, tbo.delivery_charge
            , tbo.other_charge, tbo.handling_charge, tbo.upstairs_charge, tbo.total_charge, tbo.status AS order_status
            , tbo.status_remark, tbo.order_time, tbdod.status, tbdod.delivery_order_serial_no, tbdod.rider_id, tbo.state, tbo.origin
            , tbr.name AS rider_name, tbdod.id AS delivery_order_detail_id, tbdo.id AS delivery_order_id, tbdo.referral_delivery
            , tbo.start_point_detail_name, tbo.end_point_detail_name, tbdod.picked_pieces, u.settle_period
            , tbdod.picking_time, tbdod.send_time, tbdod.receiving_time, tbdod.finish_time, tbo.charges_id
            , tbdodi.goods_id AS item_goods_id, tbdodi.goods_name AS item_goods_name, tbdodi.goods_type AS item_goods_type
            , tbdodi.pieces AS item_pieces, tbdodi.weight AS item_weight, tbdodi.size AS item_size, tbdodi.id AS item_id
        FROM t_bicycle_order tbo
            INNER JOIN u_merchant u ON u.id = tbo.sender_id
            INNER JOIN t_bicycle_delivery_order_detail tbdod ON tbo.order_serial_no = tbdod.order_serial_no
            INNER JOIN t_bicycle_delivery_order tbdo ON tbdo.delivery_order_serial_no = tbdod.delivery_order_serial_no
            LEFT JOIN t_bicycle_delivery_order_detail_item tbdodi ON tbdodi.delivery_order_serial_no_item = tbdod.delivery_order_serial_no_item
            LEFT JOIN t_bicycle_rider tbr ON tbr.id = tbdod.rider_id
        WHERE tbdod.delivery_order_serial_no IN <foreach collection="deliveryOrderSerialList" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </select>

    <!-- 根据配送单好查询订单详情 -->
    <select id="listOrdersByDeliverySerials" parameterType="java.util.List" resultType="com.senox.tms.vo.BicycleOrderVo">
        SELECT o.id
            , o.order_serial_no
            , d.delivery_order_serial_no
            , o.sender_serial_no
            , o.sender
            , u.settle_period
            , o.charges_id
            , o.recipient
            , o.start_point_name
            , o.start_point_detail_name
            , o.end_point_name
            , o.end_point_detail_name
            , o.send_time_start
            , o.send_time_end
            , o.order_time
            , o.other_remark
            , o.pieces
            , o.delivery_charge
            , o.other_charge
            , o.handling_charge
            , o.upstairs_charge
            , o.total_charge
            , o.status AS order_status
            , d.delivery_order_serial_no
            , d.rider_id
            , r.name AS rider_name
            , d.picked_pieces
            , d.picking_time
            , d.send_time
            , d.receiving_time
            , d.finish_time
        FROM t_bicycle_order o
            INNER JOIN t_bicycle_delivery_order_detail d ON d.order_serial_no = o.order_serial_no
            INNER JOIN u_merchant u ON u.id = o.sender_id
            LEFT JOIN t_bicycle_rider r ON r.id = d.rider_id
        WHERE d.delivery_order_serial_no IN <foreach collection="deliverySerials" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND o.is_disabled = 0
    </select>

    <select id="assignMaxStatus" resultType="com.senox.tms.domain.BicycleOrder">
        SELECT order_serial_no, max(`status`) as status from t_bicycle_delivery_order_detail
            where delivery_order_serial_no in <foreach collection="deliveryOrderSerialList" item="item" open="(" close=")" separator=",">#{item}</foreach>
        GROUP BY order_serial_no
    </select>

    <select id="assignMinStatus" resultType="com.senox.tms.domain.BicycleOrder">
        SELECT order_serial_no, min(`status`) as status from t_bicycle_delivery_order_detail
        where delivery_order_serial_no in <foreach collection="deliveryOrderSerialList" item="item" open="(" close=")" separator=",">#{item}</foreach>
        GROUP BY order_serial_no
    </select>

    <select id="exportOrderList" parameterType="com.senox.tms.vo.BicycleOrderSearchVo" resultType="com.senox.tms.vo.BicycleOrderVo">
        select tbo.id, tbo.order_serial_no, tbo.start_point_id, tbo.start_point_name, tbo.end_point_id, tbo.end_point_name, tbo.send_time_start
             , tbo.send_time_end, tbo.sender_id, tbo.sender, u.rc_serial as sender_serial_no, tbo.sender_contact, tbo.sender_type, tbo.recipient
             , tbo.recipient_contact, tbo.car_no, tbo.remark, tbo.other_remark, tbo.pieces, tbo.delivery_charge, tbo.other_charge
             , tbo.handling_charge, tbo.upstairs_charge, tbo.total_charge, tbo.charges_id, tbo.state,tbo.origin
             , tbo.status as order_status, tbo.status_remark, tbo.order_time, tbo.start_point_detail_name, tbo.end_point_detail_name, u.settle_period
             , od.delivery_order_serial_no, od.rider_name, od.status
        from t_bicycle_order tbo
            inner join u_merchant u on u.id = tbo.sender_id
            inner join(
            select tbdod.order_serial_no, tbdod.delivery_order_serial_no, GROUP_CONCAT(tbr.name) as rider_name, tbdo.`status`
              from t_bicycle_delivery_order_detail tbdod
              left join t_bicycle_delivery_order tbdo  on tbdo.delivery_order_serial_no = tbdod.delivery_order_serial_no
              left join t_bicycle_rider tbr on tbr.id = tbdod.rider_id
                <where>
                    <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                        AND tbdod.delivery_order_serial_no = #{deliveryOrderSerialNo}
                    </if>
                    <if test="status != null and status.size > 0">
                        AND tbdo.status in <foreach collection="status" item="item" open="(" close=")" separator=",">#{item}</foreach>
                    </if>
                    <if test="riderId != null">
                        AND tbdod.rider_id = #{riderId}
                    </if>
                    <if test="modifiedTimeStart != null">
                        AND tbdo.modified_time >= #{modifiedTimeStart}
                    </if>
                    <if test="modifiedTimeEnd != null">
                        AND tbdo.modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
                    </if>
                    <if test="merged != null and merged == true">
                        AND tbdo.merged = 1
                    </if>
                    <if test="merged != null and merged == false">
                        AND tbdo.merged = 0
                    </if>
                </where>
              group by order_serial_no, delivery_order_serial_no
            ) as od on od.order_serial_no = tbo.order_serial_no
            <where>
                <if test="orderSerialNo != null and orderSerialNo != ''">
                    AND tbo.order_serial_no = #{orderSerialNo}
                </if>
                <if test="startPointId != null">
                    AND tbo.start_point_id = #{startPointId}
                </if>
                <if test="endPointId != null">
                    AND tbo.end_point_id = #{endPointId}
                </if>
                <if test="state != null">
                    AND tbo.state = #{state}
                </if>
                <if test="createOpenid != null and createOpenid != ''">
                    AND tbo.create_openid = #{createOpenid}
                </if>
                <if test="orderTimeStart != null">
                    AND tbo.order_time >= #{orderTimeStart}
                </if>
                <if test="orderTimeEnd != null">
                    AND tbo.order_time <![CDATA[<=]]> #{orderTimeEnd}
                </if>
                <if test="null != origin">
                    and tbo.origin = #{origin}
                </if>
                <if test="orderStatus != null">
                    and tbo.status = #{orderStatus}
                </if>
                <if test="settlePeriodList != null and settlePeriodList.size > 0">
                    AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
                </if>
                <if test="recipient != null and recipient != ''">
                    AND tbo.recipient like CONCAT('%', #{recipient}, '%')
                </if>
                <if test="senderId != null">
                    AND tbo.sender_id = #{senderId}
                </if>
                <if test="sender != null and sender != ''">
                    AND tbo.sender like CONCAT('%', #{sender}, '%')
                </if>
                <if test="senderSerialNo != null and senderSerialNo != ''">
                    AND u.rc_serial = #{senderSerialNo}
                </if>
                <if test="delivery != null and delivery == true">
                    AND od.delivery_order_serial_no is not null
                </if>
                <if test="delivery != null and delivery == false">
                    AND od.delivery_order_serial_no is null
                </if>
                <if test="assignRider != null and assignRider == true">
                    AND od.rider_name is NOT NULL
                </if>
                <if test="assignRider != null and assignRider == false">
                    AND od.rider_name is NULL
                </if>
            </where>
            <choose>
                <when test="orderStr != null and orderStr != ''">
                    ORDER BY ${orderStr}
                </when>
                <otherwise>
                    ORDER BY od.status, tbo.order_time DESC
                </otherwise>
            </choose>
            <if test="page">
                LIMIT ${offset}, ${pageSize}
            </if>
    </select>

    <resultMap id="customerMonthInfoResult" type="com.senox.tms.vo.BicycleCustomerMonthInfoVo">
        <result property="senderId" column="sender_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="contact" column="contact"/>
        <result property="chargesName" column="charges_name"/>
        <collection property="countVoList" ofType="com.senox.tms.vo.BicycleCustomerMonthOrderCountVo" resultMap="customerMonthOrderCountResult"/>
    </resultMap>

    <resultMap id="customerMonthOrderCountResult" type="com.senox.tms.vo.BicycleCustomerMonthOrderCountVo">
        <result property="yearMonth" column="year_month"/>
        <result property="pieces" column="pieces"/>
        <result property="count" column="count"/>
    </resultMap>

    <select id="customerMonthInfoList" parameterType="com.senox.tms.vo.BicycleCustomerMonthInfoSearchVo" resultMap="customerMonthInfoResult">
        SELECT
            o.sender_id,
            u.name as customer_name,
            u.contact as contact,
            c.name as charges_name,
            DATE_FORMAT( o.order_time, '%Y-%m-01' ) as `year_month`,
            sum(o.pieces) as pieces,
            count( 1 ) as count
        FROM
            t_bicycle_order o
            INNER JOIN u_merchant u on u.id = o.sender_id
            INNER JOIN t_bicycle_charges c on c.id = u.bicycle_charges_id
        WHERE
            o.state = 1 AND o.`status` = 0
            <if test="startTime != null">
                AND o.order_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND o.order_time <![CDATA[<=]]> #{endTime}
            </if>
        GROUP BY
            DATE_FORMAT( o.order_time, '%Y-%m-01' ),
            o.sender_id
        ORDER BY
            u.bicycle_charges_id
    </select>

    <select id="countOrder" parameterType="java.lang.Long" resultType="int">
        select count(o.id) from t_bicycle_order o
        inner join u_merchant u on o.sender_id = u.id
            where o.state = 1 and u.id = #{merchantId}
    </select>


    <!-- 三轮车配送订单数量V2 -->
    <select id="countOrderV2" parameterType="com.senox.tms.vo.BicycleOrderV2SearchVo" resultType="int">
        SELECT count(*)
        from t_bicycle_order bo
        left join u_merchant u on u.id = bo.sender_id
        <if test="delivery == null || delivery">
            INNER JOIN t_bicycle_delivery_order bdo on bdo.delivery_order_serial_no = bo.delivery_order_serial_no
        </if>
        <where>
            <if test="state != null">
                AND bo.state = #{state}
            </if>
            <if test="orderStatus != null">
                and bo.status = #{orderStatus}
            </if>
            <if test="senderId != null">
                and bo.sender_id = #{senderId}
            </if>
            <if test="orderTimeStart != null">
                AND bo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND bo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND bo.order_serial_no = #{orderSerialNo}
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND bo.delivery_order_serial_no = #{deliveryOrderSerialNo}
            </if>
            <if test="startPointId != null">
                AND bo.start_point_id = #{startPointId}
            </if>
            <if test="endPointId != null">
                AND bo.end_point_id = #{endPointId}
            </if>
            <if test="sender != null and sender != ''">
                AND bo.sender like CONCAT('%', #{sender}, '%')
            </if>
            <if test="senderSerialNo != null and senderSerialNo != ''">
                AND u.rc_serial = #{senderSerialNo}
            </if>
            <if test="settlePeriodList != null and settlePeriodList.size > 0">
                AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="delivery == null || delivery">
                <if test="deliveryStatusList != null and deliveryStatusList.size > 0">
                    AND bdo.status in <foreach collection="deliveryStatusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
                </if>
            </if>
            <if test="!delivery">
                and LENGTH(bo.delivery_order_serial_no) = 0
            </if>
        </where>
    </select>


    <!-- 三轮车配送订单列表V2 -->
    <select id="listOrderV2" parameterType="com.senox.tms.vo.BicycleOrderV2SearchVo" resultType="com.senox.tms.vo.BicycleOrderV2Vo">
        SELECT bo.id
             , bo.order_serial_no
             , bo.sender_serial_no
             , bo.sender
             , bo.recipient
             , bo.start_point_name
             , bo.start_point_detail_name
             , bo.end_point_name
             , bo.end_point_detail_name
             , bo.charges_id
             , bo.other_remark
             , bo.pieces
             , bo.total_charge
             , bo.delivery_charge
             , bo.other_charge
             , bo.handling_charge
             , bo.upstairs_charge
             , bo.state
             , bo.origin
             , bo.order_time
             , bo.status as order_status
             , bo.delivery_order_serial_no
             , u.settle_period
            <if test="delivery == null || delivery">
                , bdo.`status` as delivery_status
                , (SELECT GROUP_CONCAT(br.name) as compose_rider_name
                    FROM t_bicycle_delivery_order_detail bdod
                    INNER JOIN t_bicycle_rider br on br.id = bdod.rider_id
                    WHERE bdod.order_serial_no = bo.order_serial_no
                    GROUP BY bdod.order_serial_no
                ) AS compose_rider_name
            </if>
        from t_bicycle_order bo
        left join u_merchant u on u.id = bo.sender_id
        <if test="delivery == null || delivery">
            INNER JOIN t_bicycle_delivery_order bdo on bdo.delivery_order_serial_no = bo.delivery_order_serial_no
        </if>
        <where>
            <if test="state != null">
                AND bo.state = #{state}
            </if>
            <if test="orderStatus != null">
                and bo.status = #{orderStatus}
            </if>
            <if test="senderId != null">
                and bo.sender_id = #{senderId}
            </if>
            <if test="orderTimeStart != null">
                AND bo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND bo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND bo.order_serial_no = #{orderSerialNo}
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND bo.delivery_order_serial_no = #{deliveryOrderSerialNo}
            </if>
            <if test="startPointId != null">
                AND bo.start_point_id = #{startPointId}
            </if>
            <if test="endPointId != null">
                AND bo.end_point_id = #{endPointId}
            </if>
            <if test="sender != null and sender != ''">
                AND bo.sender like CONCAT('%', #{sender}, '%')
            </if>
            <if test="senderSerialNo != null and senderSerialNo != ''">
                AND u.rc_serial = #{senderSerialNo}
            </if>
            <if test="settlePeriodList != null and settlePeriodList.size > 0">
                AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="delivery == null || delivery">
                <if test="deliveryStatusList != null and deliveryStatusList.size > 0">
                    AND bdo.status in <foreach collection="deliveryStatusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
                </if>
            </if>
            <if test="!delivery">
                and LENGTH(bo.delivery_order_serial_no) = 0
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY
                <if test="delivery == null || delivery">
                    bdo.status,
                </if>
                bo.order_time DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>


    <!-- 根据订单id查询骑手信息V2 -->
    <select id="listDeliveryOrderInfoV2ByOrderIdList" parameterType="java.util.List" resultType="com.senox.tms.vo.BicycleDeliveryOrderInfoV2Vo">
        SELECT bo.id as order_id
             , bdod.`status` as delivery_detail_status
             , bdod.rider_id
             , bdod.picked_pieces
             , br.name as rider_name
        from t_bicycle_order bo
        INNER JOIN t_bicycle_delivery_order_detail bdod on bdod.order_serial_no = bo.order_serial_no
        INNER JOIN t_bicycle_rider br on br.id = bdod.rider_id
        <where>
            <if test="orderIdList != null and orderIdList.size > 0">
                AND bo.id in <foreach collection="orderIdList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </where>
    </select>

    <select id="listPageOrderV2" parameterType="com.senox.tms.vo.BicycleOrderV2SearchVo" resultType="com.senox.tms.vo.BicycleOrderV2Vo">
        SELECT bo.id
            , bo.order_serial_no
            , bo.sender_serial_no
            , bo.sender
            , bo.recipient
            , bo.start_point_name
            , bo.start_point_detail_name
            , bo.end_point_name
            , bo.end_point_detail_name
            , bo.charges_id
            , bo.other_remark
            , bo.pieces
            , bo.total_charge
            , bo.delivery_charge
            , bo.other_charge
            , bo.handling_charge
            , bo.upstairs_charge
            , bo.state
            , bo.origin
            , bo.order_time
            , bo.status as order_status
            , bo.delivery_order_serial_no
            , u.settle_period
        from t_bicycle_order bo
        left join u_merchant u on u.id = bo.sender_id
        <where>
            <if test="deliveryOrderSerialNoList != null and deliveryOrderSerialNoList.size > 0">
                AND bo.delivery_order_serial_no in <foreach collection="deliveryOrderSerialNoList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
        </where>
    </select>

    <!-- 三轮车配送订单分页数量V2 -->
    <select id="countPageDeliveryOrderV2" parameterType="com.senox.tms.vo.BicycleOrderV2SearchVo" resultType="int">
        select count(*)
        from t_bicycle_delivery_order bdo
        INNER JOIN (
            SELECT bdo.delivery_order_serial_no from t_bicycle_order bo
            left join u_merchant u on u.id = bo.sender_id
            INNER JOIN t_bicycle_delivery_order bdo on bdo.delivery_order_serial_no = bo.delivery_order_serial_no
            <if test="state != null">
                AND bo.state = #{state}
            </if>
            <if test="orderStatus != null">
                and bo.status = #{orderStatus}
            </if>
            <if test="senderId != null">
                and bo.sender_id = #{senderId}
            </if>
            <if test="orderTimeStart != null">
                AND bo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND bo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND bo.order_serial_no = #{orderSerialNo}
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND bo.delivery_order_serial_no = #{deliveryOrderSerialNo}
            </if>
            <if test="startPointId != null">
                AND bo.start_point_id = #{startPointId}
            </if>
            <if test="endPointId != null">
                AND bo.end_point_id = #{endPointId}
            </if>
            <if test="sender != null and sender != ''">
                AND bo.sender like CONCAT('%', #{sender}, '%')
            </if>
            <if test="senderSerialNo != null and senderSerialNo != ''">
                AND u.rc_serial = #{senderSerialNo}
            </if>
            <if test="settlePeriodList != null and settlePeriodList.size > 0">
                AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="deliveryStatusList != null and deliveryStatusList.size > 0">
                AND bdo.status in <foreach collection="deliveryStatusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            GROUP BY bdo.delivery_order_serial_no
        ) as b on b.delivery_order_serial_no = bdo.delivery_order_serial_no
        <where>
            <if test="assignRider != null">
                <choose>
                    <when test="assignRider">AND bdo.status > 0</when>
                    <otherwise>AND bdo.status = 0</otherwise>
                </choose>
            </if>
        </where>
    </select>

    <!-- 三轮车配送订单分页列表V2 -->
    <select id="listPageDeliveryOrderV2" parameterType="com.senox.tms.vo.BicycleOrderV2SearchVo" resultType="com.senox.tms.vo.BicycleDeliveryOrderV2Vo">
        select bdo.id
             , bdo.delivery_order_serial_no
             , bdo.merged
             , bdo.status
        from t_bicycle_delivery_order bdo
        INNER JOIN (
            SELECT bdo.delivery_order_serial_no from t_bicycle_order bo
            left join u_merchant u on u.id = bo.sender_id
            INNER JOIN t_bicycle_delivery_order bdo on bdo.delivery_order_serial_no = bo.delivery_order_serial_no
            <if test="state != null">
                AND bo.state = #{state}
            </if>
            <if test="orderStatus != null">
                and bo.status = #{orderStatus}
            </if>
            <if test="senderId != null">
                and bo.sender_id = #{senderId}
            </if>
            <if test="orderTimeStart != null">
                AND bo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND bo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND bo.order_serial_no = #{orderSerialNo}
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND bo.delivery_order_serial_no = #{deliveryOrderSerialNo}
            </if>
            <if test="startPointId != null">
                AND bo.start_point_id = #{startPointId}
            </if>
            <if test="endPointId != null">
                AND bo.end_point_id = #{endPointId}
            </if>
            <if test="sender != null and sender != ''">
                AND bo.sender like CONCAT('%', #{sender}, '%')
            </if>
            <if test="senderSerialNo != null and senderSerialNo != ''">
                AND u.rc_serial = #{senderSerialNo}
            </if>
            <if test="settlePeriodList != null and settlePeriodList.size > 0">
                AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="deliveryStatusList != null and deliveryStatusList.size > 0">
                AND bdo.status in <foreach collection="deliveryStatusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            GROUP BY bdo.delivery_order_serial_no
        ) as b on b.delivery_order_serial_no = bdo.delivery_order_serial_no
        <where>
            <if test="assignRider != null">
                <choose>
                    <when test="assignRider">AND bdo.status > 0</when>
                    <otherwise>AND bdo.status = 0</otherwise>
                </choose>
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY bdo.status, bdo.create_time DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 查询今日订单统计数量情况V2 -->
    <select id="todayOrderCountV2" parameterType="com.senox.tms.vo.BicycleOrderCountSearchVo" resultType="com.senox.tms.vo.BicycleOrderCountVo">
        SELECT (
            SELECT count(*) from t_bicycle_order bo
            WHERE bo.state = 1 AND bo.status = 0 and LENGTH(bo.delivery_order_serial_no) = 0
            <if test="startDate != null">
                AND bo.order_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND bo.order_time <![CDATA[<=]]> #{endDate}
            </if>
        )
        +
        (
            SELECT count(*)  from t_bicycle_delivery_order bdo
            INNER JOIN (
                SELECT bdo.delivery_order_serial_no from t_bicycle_delivery_order bdo
                INNER JOIN t_bicycle_order bo on bdo.delivery_order_serial_no = bo.delivery_order_serial_no
                WHERE bo.state = 1 AND bo.status = 0
                <if test="startDate != null">
                    AND bo.order_time >= #{startDate}
                </if>
                <if test="endDate != null">
                    AND bo.order_time <![CDATA[<=]]> #{endDate}
                </if>
                GROUP BY bdo.delivery_order_serial_no
            ) as t on t.delivery_order_serial_no = bdo.delivery_order_serial_no
        ) as total_count
    </select>

    <!-- 查询订单统计数量情况V2 -->
    <select id="orderCountV2" parameterType="com.senox.tms.vo.BicycleOrderCountSearchVo" resultType="com.senox.tms.vo.BicycleOrderCountVo">
        SELECT
            (
                IFNULL(SUM(CASE WHEN tbdo.`status` = 0 THEN 1 ELSE 0 END), 0)
                +
                (
                    SELECT count(*) AS undo_count FROM t_bicycle_order bo
                    WHERE bo.state = 1 AND bo.STATUS = 0 AND LENGTH(bo.delivery_order_serial_no) = 0
                )
            ) AS undo_count,
            IFNULL(SUM(CASE WHEN tbdo.`status` IN (1, 2, 3, 4, 5 ) THEN 1 ELSE 0 END), 0) AS doing_count
        FROM t_bicycle_delivery_order tbdo
    </select>

    <!-- 三轮车订单总计V2 -->
    <select id="sumOrderV2" parameterType="com.senox.tms.vo.BicycleOrderV2SearchVo" resultType="com.senox.tms.vo.BicycleOrderV2Vo">
        SELECT IFNULL(SUM(bo.pieces), 0) AS pieces
             , IFNULL(SUM(bo.delivery_charge), 0) AS delivery_charge
             , IFNULL(SUM(bo.other_charge), 0) AS other_charge
             , IFNULL(SUM(bo.handling_charge), 0) AS handling_charge
             , IFNULL(SUM(bo.upstairs_charge), 0) AS upstairs_charge
             , IFNULL(SUM(bo.total_charge), 0) AS total_charge
        FROM t_bicycle_order bo
        left join u_merchant u on u.id = bo.sender_id
        INNER JOIN t_bicycle_delivery_order bdo on bdo.delivery_order_serial_no = bo.delivery_order_serial_no
        <where>
            <if test="state != null">
                AND bo.state = #{state}
            </if>
            <if test="orderStatus != null">
                and bo.status = #{orderStatus}
            </if>
            <if test="senderId != null">
                and bo.sender_id = #{senderId}
            </if>
            <if test="orderTimeStart != null">
                AND bo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND bo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="orderSerialNo != null and orderSerialNo != ''">
                AND bo.order_serial_no = #{orderSerialNo}
            </if>
            <if test="deliveryOrderSerialNo != null and deliveryOrderSerialNo != ''">
                AND bo.delivery_order_serial_no = #{deliveryOrderSerialNo}
            </if>
            <if test="startPointId != null">
                AND bo.start_point_id = #{startPointId}
            </if>
            <if test="endPointId != null">
                AND bo.end_point_id = #{endPointId}
            </if>
            <if test="sender != null and sender != ''">
                AND bo.sender like CONCAT('%', #{sender}, '%')
            </if>
            <if test="senderSerialNo != null and senderSerialNo != ''">
                AND u.rc_serial = #{senderSerialNo}
            </if>
            <if test="settlePeriodList != null and settlePeriodList.size > 0">
                AND u.settle_period in <foreach collection="settlePeriodList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="deliveryStatusList != null and deliveryStatusList.size > 0">
                AND bdo.status in <foreach collection="deliveryStatusList" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="delivery != null and delivery">
                AND bdo.status > 0
            </if>
        </where>
    </select>
</mapper>

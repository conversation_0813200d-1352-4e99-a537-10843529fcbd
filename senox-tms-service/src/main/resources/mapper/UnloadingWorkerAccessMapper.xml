<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingWorkerAccessMapper">

    <select id="checkWorkerExistsBySign" resultType="boolean">
        SELECT COUNT(*) > 0 FROM px_unloading_worker
        WHERE worker_sign = #{workerSign}
        AND is_disabled = 0
    </select>

</mapper>

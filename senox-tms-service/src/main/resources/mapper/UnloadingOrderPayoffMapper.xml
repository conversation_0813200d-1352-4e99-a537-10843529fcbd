<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingOrderPayoffMapper">

    <select id="countPayoff" parameterType="com.senox.tms.vo.UnloadingOrderPayoffSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_order_payoff
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="workerName != null and workerName != ''">
                AND worker_name LIKE CONCAT('%', #{workerName}, '%')
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
            <if test="payoffDateStart != null">
                AND payoff_date >= #{payoffDateStart}
            </if>
            <if test="payoffDateEnd != null">
                AND payoff_date <![CDATA[<=]]> #{payoffDateEnd}
            </if>
            <if test="payoffTimeStart != null">
                AND payoff_time >= #{payoffTimeStart}
            </if>
            <if test="payoffTimeEnd != null">
                AND payoff_time <![CDATA[<=]]> #{payoffTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="monthPayoffId != null">
                AND month_payoff_id = #{monthPayoffId}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="sumPayoff" parameterType="com.senox.tms.vo.UnloadingOrderPayoffSearchVo" resultType="com.senox.tms.domain.UnloadingOrderPayoff">
        SELECT
             IFNULL(SUM(payoff_amount), 0) as payoff_amount
        FROM px_unloading_order_payoff
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="workerName != null and workerName != ''">
                AND worker_name LIKE CONCAT('%', #{workerName}, '%')
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
            <if test="payoffDateStart != null">
                AND payoff_date >= #{payoffDateStart}
            </if>
            <if test="payoffDateEnd != null">
                AND payoff_date <![CDATA[<=]]> #{payoffDateEnd}
            </if>
            <if test="payoffTimeStart != null">
                AND payoff_time >= #{payoffTimeStart}
            </if>
            <if test="payoffTimeEnd != null">
                AND payoff_time <![CDATA[<=]]> #{payoffTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="monthPayoffId != null">
                AND month_payoff_id = #{monthPayoffId}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listPayoff" parameterType="com.senox.tms.vo.UnloadingOrderPayoffSearchVo" resultType="com.senox.tms.vo.UnloadingOrderPayoffVo">
        SELECT
              p.id
            , p.payoff_date
            , p.payoff_year
            , p.payoff_month
            , p.order_id
            , p.order_no
            , p.worker_id
            , p.worker_no
            , p.worker_name
            , p.order_num
            , p.payoff_amount
            , p.month_payoff_id
            , p.status
            , p.payoff_time
            , o.customer_name
            , o.merchant_id
            , o.address
            , o.license_plate
            , o.contact
        FROM px_unloading_order_payoff p
        INNER JOIN px_unloading_order o on p.order_no = o.order_no
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND p.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and p.order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="workerNo != null and workerNo != ''">
                AND p.worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="workerName != null and workerName != ''">
                AND p.worker_name LIKE CONCAT('%', #{workerName}, '%')
            </if>
            <if test="workerId != null">
                AND p.worker_id = #{workerId}
            </if>
            <if test="payoffDateStart != null">
                AND p.payoff_date >= #{payoffDateStart}
            </if>
            <if test="payoffDateEnd != null">
                AND p.payoff_date <![CDATA[<=]]> #{payoffDateEnd}
            </if>
            <if test="payoffTimeStart != null">
                AND p.payoff_time >= #{payoffTimeStart}
            </if>
            <if test="payoffTimeEnd != null">
                AND p.payoff_time <![CDATA[<=]]> #{payoffTimeEnd}
            </if>
            <if test="status != null">
                AND p.status = #{status}
            </if>
            <if test="monthPayoffId != null">
                AND p.month_payoff_id = #{monthPayoffId}
            </if>
            AND p.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY p.order_num, p.worker_no
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>


    <select id="payoffDailyList" parameterType="com.senox.tms.vo.UnloadingOrderPayoffSearchVo" resultType="com.senox.tms.vo.UnloadingOrderPayoffVo">
        SELECT p.payoff_date
             , p.worker_name
             , p.worker_no
             , p.order_num
             , sum(payoff_amount) as payoff_amount
        from px_unloading_order_payoff p
        <where>
            <if test="payoffDateStart != null">
                AND p.payoff_date >= #{payoffDateStart}
            </if>
            <if test="payoffDateEnd != null">
                AND p.payoff_date <![CDATA[<=]]> #{payoffDateEnd}
            </if>
        </where>
        GROUP BY p.payoff_date, p.worker_name, p.worker_no, p.order_num
        ORDER BY p.order_num, p.worker_no
    </select>

    <select id="listBestWorker" resultType="com.senox.tms.vo.UnloadingDayBestWorkerVo">
        SELECT uop.worker_id
             , uop.worker_name
             , ifnull(sum( uop.payoff_amount ), 0) as amount
             , ifnull(count(uop.id), 0) order_count
        FROM px_unloading_order_payoff uop
          INNER JOIN px_unloading_order uo on uop.order_no = uo.order_no
        WHERE
            uo.state = 1 and uo.worker_status = 2
          AND uo.finish_time >= #{startTime}
          AND uo.finish_time <![CDATA[<=]]> #{endTime}
        GROUP BY uop.worker_id, uop.worker_name
        ORDER BY count(uop.id) DESC
            LIMIT 3
    </select>

</mapper>

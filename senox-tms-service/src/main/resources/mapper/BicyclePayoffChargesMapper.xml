<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicyclePayoffChargesMapper">
    <update id="update">
        update t_bicycle_payoff_charges set
            <if test="null != basicAmount">
                basic_amount = #{basicAmount},
            </if>
            <if test="null != basicPieces">
                basic_pieces = #{basicPieces},
            </if>
            <if test="null != excessPrice">
                excess_price = #{excessPrice},
            </if>
                modifier_id = #{modifierId},
                modifier_name = #{modifierName},
                modified_time = now()
            <where>
                and id = #{id}
            </where>
    </update>

    <select id="findById" resultType="com.senox.tms.domain.BicyclePayoffCharges">
        select id,
               basic_amount,
               basic_pieces,
               excess_price,
               creator_id,
               creator_name,
               create_time,
               modifier_id,
               modifier_name,
               modified_time
        from t_bicycle_payoff_charges
        <where>
            <if test="null != id">
               and id = #{id}
            </if>
        </where>
        order by create_time desc
        <if test="null == id">
            limit 1
        </if>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleRiderAttendanceMapper">

    <resultMap id="RiderAttendanceVo_Result" type="com.senox.tms.vo.BicycleRiderAttendanceVo">
        <result property="id" column="id"/>
        <result property="riderId" column="rider_id"/>
        <result property="onlineTime" column="online_time"/>
        <result property="offlineTime" column="offline_time"/>
        <result property="duration" column="duration"/>
        <result property="completeCount" column="complete_count"/>
        <result property="completePieces" column="complete_pieces"/>
        <result property="shareAmount" column="share_amount"/>
        <result property="riderNo" column="rider_no"/>
        <result property="riderName" column="rider_name"/>
    </resultMap>

    <select id="count" parameterType="com.senox.tms.vo.BicycleRiderAttendanceSearchVo" resultType="int">
        select count(a.id)
        from t_bicycle_rider_attendance a
            inner join t_bicycle_rider r on a.rider_id = r.id
        <where>
            <if test="riderId != null">
                AND a.rider_id = #{riderId}
            </if>
            <if test="riderNo != null and riderNo != ''">
                AND r.rider_no = #{riderNo}
            </if>
            <if test="riderName != null and riderName != ''">
                AND r.name like CONCAT('%', #{riderName}, '%')
            </if>
            <if test="onlineTimeStart != null">
                AND a.online_time >= #{onlineTimeStart}
            </if>
            <if test="onlineTimeEnd != null">
                AND a.online_time <![CDATA[<=]]> #{onlineTimeEnd}
            </if>
            <if test="offlineTimeStart != null">
                AND a.offline_time >= #{offlineTimeStart}
            </if>
            <if test="offlineTimeEnd != null">
                AND a.offline_time <![CDATA[<=]]> #{offlineTimeEnd}
            </if>
        </where>
    </select>

    <select id="listRiderAttendance" parameterType="com.senox.tms.vo.BicycleRiderAttendanceSearchVo" resultMap="RiderAttendanceVo_Result">
        select a.id, a.rider_id, a.online_time, a.offline_time, a.duration, a.complete_count
             , a.complete_pieces, a.share_amount, r.rider_no, r.name as rider_name
        from t_bicycle_rider_attendance a
            inner join t_bicycle_rider r on a.rider_id = r.id
        <where>
            <if test="riderId != null">
                AND a.rider_id = #{riderId}
            </if>
            <if test="riderNo != null and riderNo != ''">
                AND r.rider_no = #{riderNo}
            </if>
            <if test="riderName != null and riderName != ''">
                AND r.name like CONCAT('%', #{riderName}, '%')
            </if>
            <if test="onlineTimeStart != null">
                AND a.online_time >= #{onlineTimeStart}
            </if>
            <if test="onlineTimeEnd != null">
                AND a.online_time <![CDATA[<=]]> #{onlineTimeEnd}
            </if>
            <if test="offlineTimeStart != null">
                AND a.offline_time >= #{offlineTimeStart}
            </if>
            <if test="offlineTimeEnd != null">
                AND a.offline_time <![CDATA[<=]]> #{offlineTimeEnd}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY a.id desc
            </otherwise>
        </choose>
        LIMIT ${offset}, ${pageSize}
    </select>

</mapper>
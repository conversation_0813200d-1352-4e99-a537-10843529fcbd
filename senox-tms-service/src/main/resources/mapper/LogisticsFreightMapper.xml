<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticsFreightMapper">

    <select id="list" resultType="com.senox.tms.vo.LogisticsFreightVo">
        select lf.id,
               operations_department,
               sender_customer_name,
               sender_customer_contact,
               sender_pieces,
               sender_freight_charge,
               sender_settlement_type,
               receiving_date,
               receiving_no,
               receiving_customer_name,
               receiving_customer_address,
               receiving_customer_contact,
               transfer_logistics_company,
               transfer_logistics_no,
               transfer_charge,
               profit_amount,
               lf.remark,
               au.real_name as creator_name,
               lf.create_time,
               lf.paid_amount,
               lf.paid_time,
               lf.status,
               o.pay_way,
               lf.paid_remark,
               tau.real_name as toll_man
        from t_logistics_freight lf
        inner join u_admin_user au on au.id = lf.creator_id
        left join u_admin_user tau on tau.id = lf.toll_man_id
        left join p_order o on o.id = lf.remote_order_id
        <where>
            <if test="null != senderCustomerName and senderCustomerName != ''">
                and sender_customer_name like concat('%',#{senderCustomerName},'%')
            </if>
            <if test="null != receivingCustomerName and receivingCustomerName != ''">
                and receiving_customer_name like concat('%',#{receivingCustomerName},'%')
            </if>
            <if test="null != receivingNo and receivingNo != ''">
                and receiving_no like concat('%',#{receivingNo},'%')
            </if>
            <if test="null != transferLogisticsCompany and transferLogisticsCompany != ''">
                and transfer_logistics_company like concat('%',#{transferLogisticsCompany},'%')
            </if>
            <if test="null != receivingStartDate">
                and receiving_date >= #{receivingStartDate}
            </if>
            <if test="null != receivingEndDate">
                and receiving_date &lt;= #{receivingEndDate}
            </if>
            <if test="null != senderSettlementType">
                and sender_settlement_type = #{senderSettlementType}
            </if>
            <if test="status != null">
                and lf.status = #{status}
            </if>
            <if test="payWay != null">
                and o.pay_way = #{payWay}
            </if>
            <if test="paidTimeStart != null">
                AND lf.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND lf.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
        </where>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="countList" resultType="int">
        select count(lf.id)
        from t_logistics_freight lf
        left join p_order o on o.id = lf.remote_order_id
        <where>
            <if test="null != senderCustomerName and senderCustomerName != ''">
                and lf.sender_customer_name like concat('%',#{senderCustomerName},'%')
            </if>
            <if test="null != receivingCustomerName and receivingCustomerName != ''">
                and lf.receiving_customer_name like concat('%',#{receivingCustomerName},'%')
            </if>
            <if test="null != receivingNo and receivingNo != ''">
                and lf.receiving_no like concat('%',#{receivingNo},'%')
            </if>
            <if test="null != transferLogisticsCompany and transferLogisticsCompany != ''">
                and lf.transfer_logistics_company like concat('%',#{transferLogisticsCompany},'%')
            </if>
            <if test="null != receivingStartDate">
                and lf.receiving_date >= #{receivingStartDate}
            </if>
            <if test="null != receivingEndDate">
                and lf.receiving_date &lt;= #{receivingEndDate}
            </if>
            <if test="null != senderSettlementType">
                and lf.sender_settlement_type = #{senderSettlementType}
            </if>
            <if test="status != null">
                and lf.status = #{status}
            </if>
            <if test="payWay != null">
                and o.pay_way = #{payWay}
            </if>
            <if test="paidTimeStart != null">
                AND lf.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND lf.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
        </where>
    </select>

    <select id="listTotalAmount" resultType="com.senox.tms.vo.LogisticsFreightStatisticsVo">
        select sum(lf.sender_pieces) as total_sender_pieces,
               sum(lf.sender_freight_charge) as total_sender_freight_charge,
               sum(lf.transfer_charge) as total_transfer_charge,
               sum(lf.profit_amount) as total_profit_amount,
               sum(lf.paid_amount) as total_paid_amount
        from t_logistics_freight lf
        left join p_order o on o.id = lf.remote_order_id
        <where>
            <if test="null != senderCustomerName and senderCustomerName != ''">
                and lf.sender_customer_name like concat('%',#{senderCustomerName},'%')
            </if>
            <if test="null != receivingCustomerName and receivingCustomerName != ''">
                and lf.receiving_customer_name like concat('%',#{receivingCustomerName},'%')
            </if>
            <if test="null != receivingNo and receivingNo != ''">
                and lf.receiving_no like concat('%',#{receivingNo},'%')
            </if>
            <if test="null != transferLogisticsCompany and transferLogisticsCompany != ''">
                and lf.transfer_logistics_company like concat('%',#{transferLogisticsCompany},'%')
            </if>
            <if test="null != receivingStartDate">
                and lf.receiving_date >= #{receivingStartDate}
            </if>
            <if test="null != receivingEndDate">
                and lf.receiving_date &lt;= #{receivingEndDate}
            </if>
            <if test="null != senderSettlementType">
                and lf.sender_settlement_type = #{senderSettlementType}
            </if>
            <if test="status != null">
                and lf.status = #{status}
            </if>
            <if test="payWay != null">
                and o.pay_way = #{payWay}
            </if>
            <if test="paidTimeStart != null">
                AND lf.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND lf.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
        </where>
    </select>

    <select id="findById" resultType="com.senox.tms.vo.LogisticsFreightVo">
        select lf.id,
        operations_department,
        sender_customer_name,
        sender_customer_contact,
        sender_pieces,
        sender_freight_charge,
        sender_settlement_type,
        receiving_date,
        receiving_no,
        receiving_customer_name,
        receiving_customer_address,
        receiving_customer_contact,
        transfer_logistics_company,
        transfer_logistics_no,
        transfer_charge,
        profit_amount,
        lf.remark,
        au.real_name as creator_name,
        lf.create_time,
        lf.paid_amount,
        lf.paid_time,
        lf.status,
        o.pay_way,
        lf.paid_remark,
        tau.real_name as toll_man
        from t_logistics_freight lf
        inner join u_admin_user au on au.id = lf.creator_id
        left join u_admin_user tau on tau.id = lf.toll_man_id
        left join p_order o on o.id = lf.remote_order_id
        <where>
            and lf.is_disabled = false
            and lf.id = #{id}
        </where>
    </select>


    <!-- 通过账单id更新账单状态 -->
    <update id="updateBillPaidById" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE t_logistics_freight lf
        INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id  = lf.id
        <set>
            <if test="orderId != null">
                , lf.remote_order_id = #{orderId}
            </if>
            <if test="paid">
                , lf.status = 1
                , lf.paid_amount = #{amount}
                , lf.paid_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , lf.toll_man_id = #{tollMan}
            </if>
            , lf.modified_time = NOW()
        </set>
        <where>
            AND lf.id IN <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND lf.status = 0
            AND lf.is_disabled = 0
        </where>
    </update>

    <!-- 通过支付订单id更新物业账单状态 -->
    <update id="updateBillPaidByRemoteOrder" parameterType="com.senox.common.vo.BillPaidVo">
        UPDATE t_logistics_freight lf
        INNER JOIN p_order_item oi ON oi.order_id = #{orderId} AND oi.product_id  = lf.id
        <set>
            <if test="paid">
                , lf.status = 1
                , lf.paid_amount = #{amount}
                , lf.paid_time = #{paidTime}
            </if>
            <if test="tollMan != null">
                , lf.toll_man_id = #{tollMan}
            </if>
            , lf.modified_time = NOW()
        </set>
        <where>
            AND lf.remote_order_id = #{orderId}
            AND lf.status = 0
            AND lf.is_disabled = 0
        </where>
    </update>

    <update id="updateBillRemark" parameterType="com.senox.tms.vo.LogisticFreightRemarkVo">
        UPDATE t_logistics_freight lf
        <set>
            lf.paid_remark = #{remark},
            lf.modifier_id = #{operatorId},
            lf.modifier_name = #{operatorName},
            lf.modified_time = NOW()
        </set>
        <where>
            AND lf.id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
            AND lf.status = 1
            AND lf.is_disabled = 0
        </where>
    </update>

</mapper>

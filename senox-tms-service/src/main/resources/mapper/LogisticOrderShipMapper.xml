<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticOrderShipMapper">

    <sql id="sql_ship_weight">
        SELECT dp.order_no, ds.ship_date, SUM(ds.ship_weight) AS total_weight
        FROM t_logistic_order_ship ds
            INNER JOIN t_logistic_order_product dp ON dp.id = ds.order_product_id
        WHERE ds.ship_date IN <foreach collection="shipDates" item="shipDate" open="(" close=")" separator=",">#{shipDate}</foreach>
            AND dp.order_no LIKE ('62%')
        GROUP BY ds.ship_date, dp.order_no
    </sql>

    <update id="updateShipSizeByProductOrder" parameterType="java.lang.Long">
        UPDATE t_logistic_order_ship s
            INNER JOIN t_logistic_order_product p on p.id = s.order_product_id
        SET s.ship_count = ABS(s.ship_count)
            , s.ship_diversity = ABS(p.product_count) - ABS(s.ship_count)
            , s.piece_weight = ABS(ROUND(p.product_weight / p.product_count, 2))
            , s.ship_weight = ABS(CASE WHEN s.ship_count = p.product_count THEN p.product_weight ELSE s.ship_count * ABS(ROUND(p.product_weight / p.product_count, 2)) END)
            , s.piece_size = ABS(ROUND(p.product_size / p.product_count, 2))
            , s.ship_size =  ABS(CASE WHEN s.ship_count = p.product_count THEN p.product_size ELSE s.ship_count * ABS(ROUND(p.product_size / p.product_count, 2)) END)
            , s.modified_time = NOW()
        WHERE s.order_product_id IN <foreach collection="productOrderIds" item="orderId" open="(" close=")" separator=",">#{orderId}</foreach>
    </update>

    <update id="updateShipAmountByProductOrder" parameterType="java.lang.Long">
        UPDATE t_logistic_order_ship s
            INNER JOIN t_logistic_order_product p on p.id = s.order_product_id
        SET s.ship_amount = ABS(CASE
                WHEN p.product IN ('采购商运费', '供应商运费') THEN ROUND(p.product_amount * s.ship_multiplying, 2)
                WHEN p.remark = '不代收货款（运费由采购商承担）' THEN 0
                WHEN s.ship_price >= 60 THEN ROUND(s.ship_size * s.ship_price * s.ship_multiplying, 2)
                WHEN s.ship_price > 0.3 THEN ROUND(s.ship_count * s.ship_price * s.ship_multiplying, 2)
                ELSE ROUND(s.ship_weight * s.ship_price * s.ship_multiplying, 2)
            END)
            , s.sort_amount = CASE WHEN s.is_sort_charge THEN ROUND(s.ship_weight / 100, 2) ELSE 0 END
            , s.total_amount = ROUND(s.ship_amount * s.ship_discount + s.sort_amount, 2)
            , s.modified_time = NOW()
        WHERE s.order_product_id IN <foreach collection="productOrderIds" item="orderId" open="(" close=")" separator=",">#{orderId}</foreach>
    </update>

    <update id="updateShipTotalAmountByProductOrder" parameterType="java.lang.Long">
        UPDATE t_logistic_order_ship s
        SET s.total_amount = ROUND(s.ship_amount * s.ship_discount + s.sort_amount, 2)
            , s.modified_time = NOW()
        WHERE s.order_product_id IN <foreach collection="productOrderIds" item="orderId" open="(" close=")" separator=",">#{orderId}</foreach>
    </update>

    <!-- 根据订单时间更新物流订单折扣率 -->
    <!-- 623开头的订单，总重量(按订单号合计)500kg以下的不打折，总重量500kg（含）以上，1000kg （不含）以下打95折，1000kg（含）以上打9折  -->
    <update id="updateShipDiscountByShipDate" parameterType="java.time.LocalDate">
        UPDATE t_logistic_order_ship s
            INNER JOIN t_logistic_order_product p ON p.id = s.order_product_id
            INNER JOIN (<include refid="sql_ship_weight" />) ds ON ds.ship_date = s.ship_date AND ds.order_no = p.order_no
        SET s.ship_discount = CASE
                WHEN ds.total_weight >= 1000 THEN 0.9
                WHEN ds.total_weight >= 500 THEN 0.95
                ELSE 1
            END
            , s.modified_time = NOW()
        WHERE s.ship_date IN <foreach collection="shipDates" item="shipDate" open="(" close=")" separator=",">#{shipDate}</foreach>
            AND p.order_no LIKE ('62%')
    </update>

    <update id="updateShipTotalByShipDate" parameterType="java.time.LocalDate">
        UPDATE t_logistic_order_ship s
            INNER JOIN t_logistic_order_product p ON p.id = s.order_product_id
        SET s.total_amount = ROUND(s.ship_amount * s.ship_discount + s.sort_amount, 2)
            , s.modified_time = NOW()
        WHERE s.ship_date IN <foreach collection="shipDates" item="shipDate" open="(" close=")" separator=",">#{shipDate}</foreach>
            AND p.order_no LIKE ('62%')
    </update>

    <select id="findShipOrderWeight" parameterType="com.senox.tms.vo.ShipOrderDiscountSearchVo" resultType="java.math.BigDecimal">
        SELECT SUM(ds.ship_weight) AS total_weight
        FROM t_logistic_order_ship ds
            INNER JOIN t_logistic_order_product dp ON dp.id = ds.order_product_id
        WHERE ds.ship_date = #{shipDate}
            AND dp.order_no = #{orderNo}
            AND dp.order_no LIKE ('62%')
            <if test="excludeId != null">
                AND ds.id not in (#{excludeId})
            </if>
        GROUP BY ds.ship_date, dp.order_no
    </select>

</mapper>
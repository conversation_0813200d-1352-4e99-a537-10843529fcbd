<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingWorkerLogMapper">

    <select id="findWorkerLogById" parameterType="java.lang.Long" resultType="com.senox.tms.vo.UnloadingWorkerLogVo">
        SELECT
            id
             , worker_id
             , worker_name
             , worker_no
             , remark
             , modified_time
        FROM px_unloading_worker_log
        where id = #{id}
    </select>

    <select id="countWorkerLog" parameterType="com.senox.tms.vo.UnloadingWorkerLogSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_worker_log
        <where>
            <if test="workerName != null and workerName != ''">
                AND worker_name LIKE CONCAT('%', #{workerName}, '%')
            </if>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="modifiedTimeStart != null">
                AND modified_time >= #{modifiedTimeStart}
            </if>
            <if test="modifiedTimeEnd != null">
                AND modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
        </where>
    </select>

    <select id="listWorkerLog" parameterType="com.senox.tms.vo.UnloadingWorkerLogSearchVo" resultType="com.senox.tms.vo.UnloadingWorkerLogVo">
        SELECT
              id
            , worker_id
            , worker_name
            , worker_no
            , remark
            , modified_time
        FROM px_unloading_worker_log
        <where>
            <if test="workerName != null and workerName != ''">
                AND worker_name LIKE CONCAT('%', #{workerName}, '%')
            </if>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="modifiedTimeStart != null">
                AND modified_time >= #{modifiedTimeStart}
            </if>
            <if test="modifiedTimeEnd != null">
                AND modified_time <![CDATA[<=]]> #{modifiedTimeEnd}
            </if>
            <if test="workerId != null">
                AND worker_id = #{workerId}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>

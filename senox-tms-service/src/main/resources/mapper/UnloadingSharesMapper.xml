<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingSharesMapper">

    <select id="countShares" parameterType="com.senox.tms.vo.UnloadingSharesSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_shares
        <where>
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="effectiveTimeStart != null">
                AND effective_time >= #{effectiveTimeStart}
            </if>
            <if test="effectiveTimeEnd != null">
                AND effective_time <![CDATA[<=]]> #{effectiveTimeEnd}
            </if>
            <if test="ineffectiveTimeStart != null">
                AND ineffective_time >= #{ineffectiveTimeStart}
            </if>
            <if test="ineffectiveTimeEnd != null">
                AND ineffective_time <![CDATA[<=]]> #{ineffectiveTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listShares" parameterType="com.senox.tms.vo.UnloadingSharesSearchVo" resultType="com.senox.tms.domain.UnloadingShares">
        SELECT
              id
            , name
            , shares_rate
            , effective_time
            , ineffective_time
            , status
        FROM px_unloading_shares
        <where>
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="effectiveTimeStart != null">
                AND effective_time >= #{effectiveTimeStart}
            </if>
            <if test="effectiveTimeEnd != null">
                AND effective_time <![CDATA[<=]]> #{effectiveTimeEnd}
            </if>
            <if test="ineffectiveTimeStart != null">
                AND ineffective_time >= #{ineffectiveTimeStart}
            </if>
            <if test="ineffectiveTimeEnd != null">
                AND ineffective_time <![CDATA[<=]]> #{ineffectiveTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticsDailyOrderDeliveryMapper">

    <select id="list" resultType="com.senox.tms.domain.LogisticsDailyOrderDelivery">
        select doe.id,
        doe.order_delivery_no,
        doe.order_delivery_car_no,
        doe.order_pieces,
        doe.order_total_kilograms,
        doe.order_time,
        doe.order_person,
        doe.send_time,
        doe.type,
        doe.pieces,
        doe.kilograms,
        doe.freight_unit_price,
        doe.receivable_freight_charge,
        doe.received_freight_charge,
        doe.discrepancy_amount,
        doe.remark,
        au.real_name as creator_name,
        doe.create_time
        from t_logistics_daily_order_delivery doe
        inner join u_admin_user au on au.id = doe.creator_id
        <where>
            <if test="null != orderDeliveryNo and orderDeliveryNo != ''">
                and doe.order_delivery_no like CONCAT('%', #{orderDeliveryNo}, '%')
            </if>
            <if test="null != orderDeliveryCarNo and orderDeliveryCarNo != ''">
                and doe.order_delivery_car_no like CONCAT('%', #{orderDeliveryCarNo}, '%')
            </if>
            <if test="null != startDate">
                and doe.order_time >= #{startDate}
            </if>
            <if test="null != endDate">
                and doe.order_time &lt;= #{endDate}
            </if>
            <if test="null != sendStartDate">
                and doe.send_time >= #{sendStartDate}
            </if>
            <if test="null != sendEndDate">
                and doe.send_time &lt;= #{sendEndDate}
            </if>
            <if test="null != type">
                and doe.type = #{type}
            </if>
        </where>
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="countList" resultType="int">
        select count(id)
        from t_logistics_daily_order_delivery
        <where>
            <if test="null != orderDeliveryNo and orderDeliveryNo != ''">
                and order_delivery_no like CONCAT('%', #{orderDeliveryNo}, '%')
            </if>
            <if test="null != orderDeliveryCarNo and orderDeliveryCarNo != ''">
                and order_delivery_car_no like CONCAT('%', #{orderDeliveryCarNo}, '%')
            </if>
            <if test="null != startDate">
                and order_time >= #{startDate}
            </if>
            <if test="null != endDate">
                and order_time &lt;= #{endDate}
            </if>
            <if test="null != sendStartDate">
                and send_time >= #{sendStartDate}
            </if>
            <if test="null != sendEndDate">
                and send_time &lt;= #{sendEndDate}
            </if>
            <if test="null != type">
                and type = #{type}
            </if>
        </where>
    </select>

    <select id="totalListAmount" resultType="com.senox.tms.vo.LogisticsDailyOrderDeliveryTotalAmountVo">
        select
        ifnull(sum(doe.pieces),0) as total_pieces,
        ifnull(sum(doe.kilograms),0) as total_kilograms,
        ifnull(sum(doe.receivable_freight_charge),0) as total_receivable_freight_charge,
        ifnull( sum(doe.received_freight_charge),0) as total_received_freight_charge
        from t_logistics_daily_order_delivery doe
        <where>
            <if test="null != orderDeliveryNo and orderDeliveryNo != ''">
                and doe.order_delivery_no like CONCAT('%', #{orderDeliveryNo}, '%')
            </if>
            <if test="null != orderDeliveryCarNo and orderDeliveryCarNo != ''">
                and doe.order_delivery_car_no like CONCAT('%', #{orderDeliveryCarNo}, '%')
            </if>
            <if test="null != startDate">
                and doe.order_time >= #{startDate}
            </if>
            <if test="null != endDate">
                and doe.order_time &lt;= #{endDate}
            </if>
            <if test="null != sendStartDate">
                and doe.send_time >= #{sendStartDate}
            </if>
            <if test="null != sendEndDate">
                and doe.send_time &lt;= #{sendEndDate}
            </if>
            <if test="null != type">
                and doe.type = #{type}
            </if>
        </where>
    </select>

    <select id="findById" resultType="com.senox.tms.domain.LogisticsDailyOrderDelivery">
        select doe.id,
        doe.order_delivery_no,
        doe.order_delivery_car_no,
        doe.order_pieces,
        doe.order_total_kilograms,
        doe.order_time,
        doe.order_person,
        doe.send_time,
        doe.type,
        doe.pieces,
        doe.kilograms,
        doe.freight_unit_price,
        doe.receivable_freight_charge,
        doe.received_freight_charge,
        doe.discrepancy_amount,
        doe.remark,
        au.real_name as creator_name,
        doe.create_time
        from t_logistics_daily_order_delivery doe
        inner join u_admin_user au on au.id = doe.creator_id
        <where>
            and doe.is_disabled = false
            and doe.id = #{id}
        </where>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticPayoffMapper">

    <!-- 更新应付合计 -->
    <update id="updatePayoffTotalAmount" parameterType="java.lang.Long">
        UPDATE t_logistic_payoff
        SET total_amount = product_amount - product_owe - product_deduction - ship_amount
            , modified_time = NOW()
        WHERE id IN <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <!-- 根据账单日和商户列表查询应付 -->
    <select id="listByBillDateAndMerchant" parameterType="com.senox.tms.domain.LogisticPayoff"
            resultType="com.senox.tms.domain.LogisticPayoff">
        SELECT id, bill_date, merchant, product_count, product_amount, product_full_reduction, product_to_paid, product_paid
            , product_owe, product_deduction, product_diversity, ship_amount, total_amount
        FROM t_logistic_payoff
        WHERE (bill_date, merchant) IN <foreach collection="list" item="item" open="(" close=")" separator=",">(#{item.billDate}, #{item.merchant})</foreach>
    </select>

    <!-- 应付记录数合计 -->
    <select id="countPayoff" parameterType="com.senox.tms.vo.LogisticPayoffSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_logistic_payoff p
        <where>
            <if test="shipDateStart != null">
                AND p.bill_date >= #{shipDateStart}
            </if>
            <if test="shipDateEnd != null">
                AND p.bill_date <![CDATA[<=]]> #{shipDateEnd}
            </if>
            <if test="productOweStart != null">
                AND p.product_owe >= #{productOweStart}
            </if>
            <if test="productOweEnd != null">
                AND p.product_owe <![CDATA[<=]]> #{productOweEnd}
            </if>
            <if test="totalAmountStart != null">
                AND p.total_amount >= #{totalAmountStart}
            </if>
            <if test="totalAmountEnd != null">
                AND p.total_amount <![CDATA[<=]]> #{totalAmountEnd}
            </if>
            <if test="merchant != null and merchant != ''">
                AND p.merchant LIKE CONCAT('%', #{merchant}, '%')
            </if>
        </where>
    </select>

    <!-- 应付统计 -->
    <select id="sumPayoff" parameterType="com.senox.tms.vo.LogisticPayoffSearchVo"
            resultType="com.senox.tms.domain.LogisticPayoff">
        SELECT SUM(p.product_count) AS product_count
            , SUM(p.product_amount) AS product_amount
            , SUM(p.product_full_reduction) AS product_full_reduction
            , SUM(p.product_to_paid) AS product_to_paid
            , SUM(p.product_paid) AS product_paid
            , SUM(p.product_owe) AS product_owe
            , SUM(p.product_deduction) AS product_deduction
            , SUM(p.product_diversity) AS product_diversity
            , SUM(p.ship_amount) AS ship_amount
            , SUM(p.total_amount) AS total_amount
        FROM t_logistic_payoff p
        <where>
            <if test="shipDateStart != null">
                AND p.bill_date >= #{shipDateStart}
            </if>
            <if test="shipDateEnd != null">
                AND p.bill_date <![CDATA[<=]]> #{shipDateEnd}
            </if>
            <if test="productOweStart != null">
                AND p.product_owe >= #{productOweStart}
            </if>
            <if test="productOweEnd != null">
                AND p.product_owe <![CDATA[<=]]> #{productOweEnd}
            </if>
            <if test="totalAmountStart != null">
                AND p.total_amount >= #{totalAmountStart}
            </if>
            <if test="totalAmountEnd != null">
                AND p.total_amount <![CDATA[<=]]> #{totalAmountEnd}
            </if>
            <if test="merchant != null and merchant != ''">
                AND p.merchant LIKE CONCAT('%', #{merchant}, '%')
            </if>
        </where>
    </select>

    <!-- 应付记录列表 -->
    <select id="listPayoff" parameterType="com.senox.tms.vo.LogisticPayoffSearchVo"
            resultType="com.senox.tms.domain.LogisticPayoff">
        SELECT p.id, p.bill_date, p.merchant, p.product_count, p.product_amount, p.product_full_reduction, p.product_to_paid
            , p.product_paid, p.product_owe, p.product_deduction, p.product_diversity, p.ship_amount, p.total_amount, p.remark
        FROM t_logistic_payoff p
        <where>
            <if test="shipDateStart != null">
                AND p.bill_date >= #{shipDateStart}
            </if>
            <if test="shipDateEnd != null">
                AND p.bill_date <![CDATA[<=]]> #{shipDateEnd}
            </if>
            <if test="productOweStart != null">
                AND p.product_owe >= #{productOweStart}
            </if>
            <if test="productOweEnd != null">
                AND p.product_owe <![CDATA[<=]]> #{productOweEnd}
            </if>
            <if test="totalAmountStart != null">
                AND p.total_amount >= #{totalAmountStart}
            </if>
            <if test="totalAmountEnd != null">
                AND p.total_amount <![CDATA[<=]]> #{totalAmountEnd}
            </if>
            <if test="merchant != null and merchant != ''">
                AND p.merchant LIKE CONCAT('%', #{merchant}, '%')
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY p.bill_date DESC, p.id DESC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>
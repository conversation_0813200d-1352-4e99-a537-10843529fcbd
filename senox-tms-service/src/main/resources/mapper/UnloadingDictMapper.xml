<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingDictMapper">

    <select id="countDict" parameterType="com.senox.tms.vo.UnloadingDictSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_dict
        <where>
            <if test="category != null">
                AND category = #{category}
            </if>
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listDict" parameterType="com.senox.tms.vo.UnloadingDictSearchVo" resultType="com.senox.tms.domain.UnloadingDict">
        SELECT
              id
            , category
            , name
            , unit
            , attr1
            , attr2
            , attr3
            , attr4
        FROM px_unloading_dict
        <where>
            <if test="category != null">
                AND category = #{category}
            </if>
            <if test="name != null and name != ''">
                AND `name` LIKE CONCAT('%', #{name}, '%')
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>

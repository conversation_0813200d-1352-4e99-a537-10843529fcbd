<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticLoaderIncomeMapper">

    <insert id="addBatch" useGeneratedKeys="true" keyProperty="id">
        insert into t_logistic_loader_income(date, loader_number, loader_name, settlement_id, amount,creator_id, creator_name,
        create_time,
        modifier_id, modifier_name, modified_time)
        values
        <foreach collection="incomes" item="item"  separator=",">
            (#{item.date},#{item.loaderNumber},#{item.loaderName}
            ,#{item.settlementId},#{item.amount},#{item.creatorId},#{item.creatorName}
            ,now(),#{item.modifierId},#{item.modifierName},now())
        </foreach>
    </insert>

    <update id="updateBatch">
        update t_logistic_loader_income
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="date = case" suffix="end,">
                <foreach collection="incomes" item="item">
                    <if test="null != item.date">
                        when id = #{item.id} then #{item.date}
                    </if>
                </foreach>
            </trim>
            <trim prefix="loader_number = case" suffix="end,">
                <foreach collection="incomes" item="item">
                    <if test="null != item.loaderNumber">
                        when id = #{item.id} then #{item.loaderNumber}
                    </if>
                </foreach>
            </trim>
            <trim prefix="loader_name = case" suffix="end,">
                <foreach collection="incomes" item="item">
                    <if test="null != item.loaderName">
                        when id = #{item.id} then #{item.loaderName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="amount = case" suffix="end,">
                <foreach collection="incomes" item="item">
                    <if test="null != item.amount">
                        when id = #{item.id} then #{item.amount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = case" suffix="end,">
                <foreach collection="incomes" item="item">
                    <if test="null != item.modifierId">
                        when id = #{item.id} then #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = case" suffix="end,">
                <foreach collection="incomes" item="item">
                    <if test="null != item.modifierName">
                        when id = #{item.id} then #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = now()
        </trim>
        <where>
            and id in
            <foreach collection="incomes" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </where>
    </update>

    <select id="list" resultType="com.senox.tms.vo.LogisticLoaderIncomeVo">
        select id, date, loader_number, loader_name, amount, creator_id, creator_name, create_time,
        modifier_id, modifier_name, modified_time
        from t_logistic_loader_income
        <where>
            <if test="null != date">
                and date = #{date}
            </if>
            <if test="null != settlementId and settlementId > 0">
                and settlement_id = #{settlementId}
            </if>
            <if test="null != loaderNumber and loaderNumber != ''">
                and loader_number like concat('%',#{loaderNumber},'%')
            </if>
            <if test="null != loaderName and loaderName != ''">
                and loader_name like concat('%',#{loaderName},'%')
            </if>
            and is_disabled = false
        </where>
        order by loader_number desc
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="countList" resultType="int">
        select count(*)
        from t_logistic_loader_income
        <where>
            <if test="null != date">
                and date = #{date}
            </if>
            <if test="null != settlementId and settlementId > 0">
                and settlement_id = #{settlementId}
            </if>
            <if test="null != loaderNumber and loaderNumber != ''">
                and loader_number like concat('%',#{loaderNumber},'%')
            </if>
            <if test="null != loaderName and loaderName != ''">
                and loader_name like concat('%',#{loaderName},'%')
            </if>
            and is_disabled = false
        </where>
    </select>

    <select id="statisticsOfDay" resultType="com.senox.tms.vo.LogisticLoaderIncomeVo">
        select li.date, dl.key as loader_number, dl.name as loader_name, ifnull(sum(li.amount), 0) as amount
        from t_dict_logistic dl
                 left join t_logistic_loader_income li
                           on li.loader_number = dl.key
        <where>
            and dl.category = #{category.number}
            and li.date >=  #{searchVo.startDate}
            and li.date &lt;= #{searchVo.endDate}
        </where>
        group by li.date, dl.key, dl.name
    </select>

    <insert id="incomeStatisticsAddBatch">
        insert into t_logistic_loader_income_day_report(date, loader_number, loader_name, amount)
        values
        <foreach collection="incomes" item="item" separator=",">
            (#{item.date},#{item.loaderNumber},#{item.loaderName},#{item.amount})
        </foreach>
    </insert>

    <insert id="incomeStatisticsUpdateBatch">
        update t_logistic_loader_income_day_report
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="amount = case" suffix="end,">
                <foreach collection="incomes" item="item">
                    <if test="null != item.amount">
                        when date = #{item.date} and loader_number = #{item.loaderNumber} then #{item.amount}
                    </if>
                </foreach>
            </trim>
        </trim>
        <where>
            and date in
            <foreach collection="incomes" item="item" open="(" close=")" separator=",">
                #{item.date}
            </foreach>
            and loader_number in
            <foreach collection="incomes" item="item" open="(" close=")" separator=",">
                #{item.loaderNumber}
            </foreach>
        </where>
    </insert>

    <delete id="incomeStatisticsRemoveBatch">
        delete from t_logistic_loader_income_day_report
        <where>
            and date in
            <foreach collection="incomes" item="item" open="(" close=")" separator=",">
                #{item.date}
            </foreach>
            and loader_number in
            <foreach collection="incomes" item="item" open="(" close=")" separator=",">
                #{item.loaderNumber}
            </foreach>
        </where>
    </delete>

    <select id="listStatistics" resultType="com.senox.tms.vo.LogisticLoaderIncomeVo">
        select date, loader_number, loader_name, amount
        from t_logistic_loader_income_day_report
        <where>
            <if test="null != loaderName and loaderName != ''">
                and loader_name like concat('%',#{loaderName},'%')
            </if>
            <if test="null != startDate">
                and date >= #{startDate}
            </if>
            <if test="null != endDate">
                and date &lt;= #{endDate}
            </if>
        </where>
        order by date desc
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="listCountStatistics" resultType="int">
        select count(*)
        from t_logistic_loader_income_day_report
        <where>
            <if test="null != loaderName and loaderName != ''">
                and loader_name like concat('%',#{loaderName},'%')
            </if>
            <if test="null != startDate">
                and date >= #{startDate}
            </if>
            <if test="null != endDate">
                and date &lt;= #{endDate}
            </if>
        </where>
    </select>

    <select id="listTotalAmount" resultType="BigDecimal">
        select sum(amount)
        from t_logistic_loader_income_day_report
    </select>
</mapper>

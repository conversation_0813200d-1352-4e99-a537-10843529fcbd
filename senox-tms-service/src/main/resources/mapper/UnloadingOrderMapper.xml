<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingOrderMapper">

    <resultMap id="Order_Result" type="com.senox.tms.vo.UnloadingOrderVo">
        <result property="id" column="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="customerName" column="customer_name"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="address" column="address"/>
        <result property="licensePlate" column="license_plate"/>
        <result property="contact" column="contact"/>
        <result property="remark" column="remark"/>
        <result property="state" column="state"/>
        <result property="workerStatus" column="worker_status"/>
        <result property="carpool" column="carpool"/>
        <result property="reservationOrder" column="reservation_order"/>
        <result property="reservationTime" column="reservation_time"/>
        <result property="carCategory" column="car_category"/>
        <result property="carCategoryName" column="car_category_name"/>
        <result property="carNum" column="car_num"/>
        <result property="workerNum" column="worker_num"/>
        <result property="orderTime" column="order_time"/>
        <result property="workTime" column="work_time"/>
        <result property="finishTime" column="finish_time"/>
        <result property="urgentOrder" column="urgent_order"/>
        <result property="urgentAmount" column="urgent_amount"/>
        <result property="amount" column="amount"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="shareAmount" column="share_amount"/>
        <result property="payoffAmount" column="payoff_amount"/>
        <result property="supplement" column="supplement"/>
        <result property="billStatus" column="bill_status"/>
        <result property="billPaidTime" column="bill_paid_time"/>
        <result property="payoffStatus" column="payoff_status"/>
        <collection property="goodsVoList" ofType="com.senox.tms.vo.UnloadingOrderGoodsVo" resultMap="OrderGoods_Result"/>
        <collection property="workersVoList" ofType="com.senox.tms.vo.UnloadingOrderWorkersVo" resultMap="OrderWorkers_Result"/>
    </resultMap>

    <resultMap id="OrderGoods_Result" type="com.senox.tms.vo.UnloadingOrderGoodsVo">
        <result property="id" column="goods_id"/>
        <result property="orderId" column="order_id"/>
        <result property="category" column="goods_category"/>
        <result property="goodsName" column="goods_name"/>
        <result property="unit" column="unit"/>
        <result property="quantity" column="quantity"/>
    </resultMap>

    <resultMap id="OrderWorkers_Result" type="com.senox.tms.vo.UnloadingOrderWorkersVo">
        <result property="id" column="workers_id"/>
        <result property="orderId" column="order_id"/>
        <result property="workerId" column="worker_id"/>
        <result property="workerNo" column="worker_no"/>
        <result property="workerName" column="worker_name"/>
    </resultMap>

    <!-- 获取最大的订单号 -->
    <select id="findMaxOrderNo" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(order_no) FROM px_unloading_order WHERE order_no LIKE CONCAT(#{prefix}, '%');
    </select>

    <select id="countOrder" parameterType="com.senox.tms.vo.UnloadOrderSearchVo" resultType="int">
        SELECT COUNT(*)
        FROM px_unloading_order uo
        LEFT JOIN px_unloading_order_bill uob on uob.order_no = uo.order_no
        LEFT JOIN (
            SELECT order_no
                , MIN(`status`) as status
            from px_unloading_order_payoff
            GROUP BY order_no
        ) as uof on uof.order_no = uo.order_no
        <where>
            <if test="merchantId != null">
                AND uo.merchant_id = #{merchantId}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND uo.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and uo.order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="openid != null and openid != ''">
                AND uo.openid = #{openid}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (uo.addresss LIKE CONCAT('%', #{keywords}, '%') OR uo.license_plate LIKE CONCAT('%', #{keywords}, '%') OR uo.contact LIKE CONCAT('%', #{keywords}, '%'))
            </if>
            <if test="state != null">
                AND uo.`state` = #{state}
            </if>
            <if test="workerStatus != null">
                and worker_status = #{workerStatus}
            </if>
            <if test="workerStatusList != null and workerStatusList.size() > 0">
                and uo.worker_status in
                <foreach collection="workerStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="orderTimeStart != null">
                AND uo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND uo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="workTimeStart != null">
                AND uo.work_time >= #{workTimeStart}
            </if>
            <if test="workTimeEnd != null">
                AND uo.work_time <![CDATA[<=]]> #{workTimeEnd}
            </if>
            <if test="finishTimeStart != null">
                AND uo.finish_time >= #{finishTimeStart}
            </if>
            <if test="finishTimeEnd != null">
                AND uo.finish_time <![CDATA[<=]]> #{finishTimeEnd}
            </if>
            <if test="carpool != null">
                and uo.carpool = #{carpool}
            </if>
            <if test="reservationOrder != null">
                and uo.reservation_order = #{reservationOrder}
            </if>
            <if test="reservationTimeStart != null">
                AND uo.reservation_time >= #{reservationTimeStart}
            </if>
            <if test="reservationTimeEnd != null">
                AND uo.reservation_time <![CDATA[<=]]> #{reservationTimeEnd}
            </if>
            <if test="urgentOrder != null">
                and uo.urgent_order = #{urgentOrder}
            </if>
            <if test="bill != null">
                <choose>
                    <when test="bill">
                        and uob.status is not null
                    </when>
                    <otherwise>
                        and uob.status is null
                    </otherwise>
                </choose>
            </if>
            <if test="payoff != null">
                <choose>
                    <when test="payoff">
                        and uof.status is not null
                    </when>
                    <otherwise>
                        and uof.status is null
                    </otherwise>
                </choose>
            </if>
            <if test="isZero != null">
                <choose>
                    <when test="isZero">
                        and uo.amount = 0
                    </when>
                    <otherwise>
                        and uo.amount > 0
                    </otherwise>
                </choose>
            </if>
            AND uo.is_disabled = 0
        </where>
    </select>

    <select id="listOrder" parameterType="com.senox.tms.vo.UnloadOrderSearchVo" resultType="com.senox.tms.vo.UnloadingOrderVo">
        SELECT uo.id
             , uo.order_no
             , uo.customer_name
             , uo.merchant_id
             , uo.address
             , uo.license_plate
             , uo.contact
             , uo.remark
             , uo.state
             , uo.worker_status
             , uo.carpool
             , uo.car_category
             , uo.car_category_name
             , uo.car_num
             , uo.worker_num
             , uo.order_time
             , uo.work_time
             , uo.finish_time
             , uo.urgent_order
             , uo.reservation_time
             , uo.urgent_amount
             , uo.amount
             , uo.total_amount
             , uo.amount - uo.payoff_amount as share_amount
             , uo.payoff_amount
             , uo.supplement
             , uob.status as bill_status
             , uob.paid_time as bill_paid_time
             , uof.status as payoff_status
             , au.real_name as toll_man
        FROM px_unloading_order uo
        LEFT JOIN px_unloading_order_bill uob on uob.order_no = uo.order_no
        left join u_admin_user au on au.id = uob.toll_man_id
        LEFT JOIN (
            SELECT order_no
                 , MIN(`status`) as status
            from px_unloading_order_payoff
            GROUP BY order_no
        ) as uof on uof.order_no = uo.order_no
        <where>
            <if test="merchantId != null">
                AND uo.merchant_id = #{merchantId}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND uo.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and uo.order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="openid != null and openid != ''">
                AND uo.openid = #{openid}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (uo.address LIKE CONCAT('%', #{keywords}, '%') OR uo.license_plate LIKE CONCAT('%', #{keywords}, '%') OR uo.contact LIKE CONCAT('%', #{keywords}, '%'))
            </if>
            <if test="state != null">
                AND uo.`state` = #{state}
            </if>
            <if test="workerStatus != null">
                and worker_status = #{workerStatus}
            </if>
            <if test="workerStatusList != null and workerStatusList.size() > 0">
                and uo.worker_status in
                <foreach collection="workerStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="orderTimeStart != null">
                AND uo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND uo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="workTimeStart != null">
                AND uo.work_time >= #{workTimeStart}
            </if>
            <if test="workTimeEnd != null">
                AND uo.work_time <![CDATA[<=]]> #{workTimeEnd}
            </if>
            <if test="finishTimeStart != null">
                AND uo.finish_time >= #{finishTimeStart}
            </if>
            <if test="finishTimeEnd != null">
                AND uo.finish_time <![CDATA[<=]]> #{finishTimeEnd}
            </if>
            <if test="carpool != null">
                and uo.carpool = #{carpool}
            </if>
            <if test="reservationOrder != null">
                and uo.reservation_order = #{reservationOrder}
            </if>
            <if test="reservationTimeStart != null">
                AND uo.reservation_time >= #{reservationTimeStart}
            </if>
            <if test="reservationTimeEnd != null">
                AND uo.reservation_time <![CDATA[<=]]> #{reservationTimeEnd}
            </if>
            <if test="urgentOrder != null">
                and uo.urgent_order = #{urgentOrder}
            </if>
            <if test="bill != null">
                <choose>
                    <when test="bill">
                        and uob.status is not null
                    </when>
                    <otherwise>
                        and uob.status is null
                    </otherwise>
                </choose>
            </if>
            <if test="payoff != null">
                <choose>
                    <when test="payoff">
                        and uof.status is not null
                    </when>
                    <otherwise>
                        and uof.status is null
                    </otherwise>
                </choose>
            </if>
            <if test="isZero != null">
                <choose>
                    <when test="isZero">
                        and uo.amount = 0
                    </when>
                    <otherwise>
                        and uo.amount > 0
                    </otherwise>
                </choose>
            </if>
            AND uo.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY uo.worker_status asc, uo.order_time desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumOrder" parameterType="com.senox.tms.vo.UnloadOrderSearchVo" resultType="com.senox.tms.vo.UnloadingOrderVo">
        SELECT IFNULL(SUM(uo.urgent_amount), 0) as urgent_amount
             , IFNULL(SUM(uo.amount), 0) as amount
             , IFNULL(SUM(uo.total_amount), 0) as total_amount
             , IFNULL(SUM(uo.amount - uo.payoff_amount), 0) as share_amount
             , IFNULL(SUM(uo.payoff_amount), 0) as payoff_amount
        FROM px_unloading_order uo
                 LEFT JOIN px_unloading_order_bill uob on uob.order_no = uo.order_no
                 LEFT JOIN (
            SELECT order_no
                 , MIN(`status`) as status
            from px_unloading_order_payoff
            GROUP BY order_no
        ) as uof on uof.order_no = uo.order_no
        <where>
            <if test="merchantId != null">
                AND uo.merchant_id = #{merchantId}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND uo.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and uo.order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="openid != null and openid != ''">
                AND uo.openid = #{openid}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (uo.address LIKE CONCAT('%', #{keywords}, '%') OR uo.license_plate LIKE CONCAT('%', #{keywords}, '%') OR uo.contact LIKE CONCAT('%', #{keywords}, '%'))
            </if>
            <if test="state != null">
                AND uo.`state` = #{state}
            </if>
            <if test="workerStatus != null">
                and worker_status = #{workerStatus}
            </if>
            <if test="workerStatusList != null and workerStatusList.size() > 0">
                and uo.worker_status in
                <foreach collection="workerStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="orderTimeStart != null">
                AND uo.order_time >= #{orderTimeStart}
            </if>
            <if test="orderTimeEnd != null">
                AND uo.order_time <![CDATA[<=]]> #{orderTimeEnd}
            </if>
            <if test="workTimeStart != null">
                AND uo.work_time >= #{workTimeStart}
            </if>
            <if test="workTimeEnd != null">
                AND uo.work_time <![CDATA[<=]]> #{workTimeEnd}
            </if>
            <if test="finishTimeStart != null">
                AND uo.finish_time >= #{finishTimeStart}
            </if>
            <if test="finishTimeEnd != null">
                AND uo.finish_time <![CDATA[<=]]> #{finishTimeEnd}
            </if>
            <if test="carpool != null">
                and uo.carpool = #{carpool}
            </if>
            <if test="reservationOrder != null">
                and uo.reservation_order = #{reservationOrder}
            </if>
            <if test="reservationTimeStart != null">
                AND uo.reservation_time >= #{reservationTimeStart}
            </if>
            <if test="reservationTimeEnd != null">
                AND uo.reservation_time <![CDATA[<=]]> #{reservationTimeEnd}
            </if>
            <if test="urgentOrder != null">
                and uo.urgent_order = #{urgentOrder}
            </if>
            <if test="bill != null">
                <choose>
                    <when test="bill">
                        and uob.status is not null
                    </when>
                    <otherwise>
                        and uob.status is null
                    </otherwise>
                </choose>
            </if>
            <if test="payoff != null">
                <choose>
                    <when test="payoff">
                        and uof.status is not null
                    </when>
                    <otherwise>
                        and uof.status is null
                    </otherwise>
                </choose>
            </if>
            <if test="isZero != null">
                <choose>
                    <when test="isZero">
                        and uo.amount = 0
                    </when>
                    <otherwise>
                        and uo.amount > 0
                    </otherwise>
                </choose>
            </if>
            AND uo.is_disabled = 0
        </where>
    </select>

    <select id="findDetailById" parameterType="java.lang.Long" resultMap="Order_Result">
        SELECT uo.id
             , uo.order_no
             , uo.customer_name
             , uo.merchant_id
             , uo.address
             , uo.license_plate
             , uo.contact
             , uo.remark
             , uo.state
             , uo.worker_status
             , uo.carpool
             , uo.reservation_order
             , uo.reservation_time
             , uo.car_category
             , uo.car_category_name
             , uo.car_num
             , uo.worker_num
             , uo.order_time
             , uo.work_time
             , uo.finish_time
             , uo.urgent_order
             , uo.reservation_time
             , uo.urgent_amount
             , uo.amount
             , uo.total_amount
             , uo.amount - uo.payoff_amount as share_amount
             , uo.payoff_amount
             , uo.supplement
             , uog.id as goods_id
             , uog.category as goods_category
             , uog.goods_name
             , uog.unit
             , uog.quantity
             , uow.id as workers_id
             , uow.worker_id
             , uow.worker_no
             , uow.worker_name
             , uo.id as order_id
             , uob.status as bill_status
             , uob.paid_time as bill_paid_time
        FROM px_unloading_order uo
        LEFT JOIN px_unloading_order_goods uog ON uo.id = uog.order_id
        LEFT JOIN px_unloading_order_workers uow ON uo.id = uow.order_id
        LEFT JOIN px_unloading_order_bill uob on uob.order_no = uo.order_no
        WHERE uo.id = #{id}
    </select>

    <select id="findOperateAnalysis" resultType="com.senox.tms.vo.UnloadingOperateAnalysisVo">
        select ifnull(SUM(uo.total_amount), 0) as total_amount
             , ifnull(COUNT(uo.id), 0) as total_count
             , ifnull(ROUND( AVG( uo.finish_time - uo.work_time ), 0 ), 0) as avg_second
        from px_unloading_order uo
        where uo.order_time >= #{startTime} and uo.order_time <![CDATA[<=]]> #{endTime} and uo.state = 1
    </select>

    <select id="totalQuantity" resultType="int">
        select
            ifnull(sum(og.quantity), 0) as total_quantity
        from px_unloading_order uo
            INNER JOIN px_unloading_order_goods og on og.order_id = uo.id
        where uo.order_time >= #{startTime} and uo.order_time <![CDATA[<=]]> #{endTime} and uo.state = 1
    </select>


    <select id="listOrderCountRanking" resultType="com.senox.tms.vo.UnloadingOrderCountRankingVo">
        select m.name as customer_name
             , count(uo.id) as total_count
        from px_unloading_order uo
            inner join u_merchant m on m.id = uo.merchant_id
        where uo.state = 1 and uo.order_time >= #{startTime} and uo.order_time <![CDATA[<=]]> #{endTime}
        GROUP BY uo.merchant_id
        order by count(uo.id) desc
        LIMIT 5
    </select>

    <select id="listQuantityCountRanking" resultType="com.senox.tms.vo.UnloadingQuantityCountRankingVo">
        select m.name as customer_name
             , sum(uog.quantity) as total_quantity
        from px_unloading_order uo
            inner join u_merchant m on m.id = uo.merchant_id
            INNER JOIN px_unloading_order_goods uog on uog.order_id = uo.id
        where uo.state = 1 and uo.order_time >= #{startTime} and uo.order_time <![CDATA[<=]]> #{endTime}
        GROUP BY uo.merchant_id
        order by sum(uog.quantity) desc
        LIMIT 5
    </select>

    <select id="listAmountCountRanking" resultType="com.senox.tms.vo.UnloadingAmountCountRankingVo">
        select m.name as customer_name
             , ifnull(sum(uo.total_amount), 0) as total_amount
        from px_unloading_order uo
            inner join u_merchant m on m.id = uo.merchant_id
        where uo.state = 1 and uo.order_time >= #{startTime} and uo.order_time <![CDATA[<=]]> #{endTime}
        GROUP BY uo.merchant_id
        order by sum(uo.total_amount) desc
        LIMIT 5
    </select>

    <select id="listCountList" resultType="com.senox.tms.vo.UnloadingOrderCountVo">
        SELECT 	t2.hours
              , ifnull(t3.undo_count, 0) as undo_count
              , ifnull(t3.doing_count, 0) as doing_count
              , ifnull(t3.done_count, 0) as done_count
              , ifnull(t3.quantity, 0) as quantity
        FROM
            (
                SELECT  DATE_FORMAT(DATE_SUB( #{endTime}, INTERVAL ( number * 1 ) ${searchType} ), #{searchTypeFormat}) AS hours
                FROM (${searchResultFormat}) as t1
            ) AS t2
        LEFT JOIN (
                SELECT DATE_FORMAT ( uo.order_time, #{searchTypeFormat} ) AS hour_of_order,
                       SUM( CASE WHEN uo.worker_status = 0 THEN 1 ELSE 0 END ) AS undo_count,
                       SUM( CASE WHEN uo.worker_status = 1 THEN 1 ELSE 0 END ) AS doing_count,
                       SUM( CASE WHEN uo.worker_status = 2 THEN 1 ELSE 0 END ) AS done_count,
                       (
                            SELECT sum( uog.quantity )
                            FROM px_unloading_order_goods uog
                            WHERE uog.order_id IN (
                                SELECT id FROM px_unloading_order WHERE DATE_FORMAT (order_time, #{searchTypeFormat} )  = hour_of_order
                            )
                        ) AS quantity
                FROM px_unloading_order uo
                WHERE uo.state = 1 and uo.order_time >= #{startTime} AND uo.order_time <![CDATA[<=]]> #{endTime}
                GROUP BY hour_of_order
            ) AS t3 ON t3.hour_of_order = t2.hours
    </select>

    <select id="lastOrderByWorkerId" resultType="java.lang.Long">
        SELECT puo.id FROM px_unloading_order puo
        INNER JOIN px_unloading_order_workers puow on puo.id = puow.order_id
        WHERE puo.finish_time >= #{startTime} and puow.worker_id = #{workerId}
        ORDER BY puo.finish_time desc
        LIMIT 1
    </select>
</mapper>

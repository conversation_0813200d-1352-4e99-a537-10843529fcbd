<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.LogisticOrderProductMapper">

    <!-- 更新商品订单金额 -->
    <update id="updateProductOrderAmount" parameterType="java.lang.Long">
        UPDATE t_logistic_order_product
        SET total_amount = product_amount - product_deduction - product_full_reduction
            , modified_time = NOW()
        WHERE id IN <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </update>

    <!-- 批量更新订单明细 -->
    <update id="updateOrderBatch" parameterType="com.senox.tms.vo.LogisticOrderEditBatchVo">
        UPDATE t_logistic_order_product p
            INNER JOIN t_logistic_order_ship s ON s.order_product_id = p.id
            INNER JOIN t_logistic_order_bill b ON b.order_product_id = p.id
        SET p.modifier_id = #{operatorId}
            , p.modifier_name = #{operatorName}
            , p.modified_time = NOW()
            , s.modifier_id = #{operatorId}
            , s.modifier_name = #{operatorName}
            , s.modified_time = NOW()
            , b.modifier_id = #{operatorId}
            , b.modifier_name = #{operatorName}
            , b.modified_time = NOW()
        <if test="orderPrePaidMan != null and orderPrePaidMan != ''">
            , b.product_paid_man = #{orderPrePaidMan}
        </if>
        <if test="driver != null and driver != ''">
            , s.driver = #{driver}
        </if>
        <if test="remark != null and remark != ''">
            , p.remark = #{remark}
        </if>
        WHERE p.id IN <foreach collection="productOrderIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
    </update>

    <!-- 根据商品订单id查找订单明细 -->
    <select id="findOrderByProductOrderId" parameterType="java.lang.Long" resultType="com.senox.tms.vo.LogisticOrderVo">
        SELECT p.id AS product_order_id, p.order_no, p.merchant, p.member, p.product, p.product_type1, p.product_type2, p.product_price, p.product_count
            , p.product_amount, p.product_weight, p.product_size, p.product_deduction, p.product_full_reduction, p.total_amount AS product_total_amount
            , IFNULL(p.remark, '') AS remark, s.id AS ship_id, s.ship_date, s.receiver, s.receiver_contact, s.destination, s.market, s.area, s.vehicle_no
            , s.driver, s.ship_count, s.ship_diversity, s.piece_weight, s.ship_weight, s.piece_size, s.ship_size, s.ship_price, s.ship_multiplying
            , s.ship_amount, s.ship_discount, s.sort_amount, s.is_sort_charge AS sort_charge, s.total_amount AS ship_total_amount, b.product_paid, b.product_paid_man
            , b.product_diversity, b.product_owe, b.total_amount, IFNULL(u.real_name, u.username) AS creator, p.create_time
        FROM t_logistic_order_product p
            LEFT JOIN t_logistic_order_ship s ON s.order_product_id = p.id
            LEFT JOIN t_logistic_order_bill b ON b.order_product_id = p.id AND b.ship_id = s.id
            LEFT JOIN u_admin_user u ON u.id = p.creator_id
        WHERE p.id = #{productOrderId}
    </select>


    <!-- 根据发货日期、订单编号、商品名查找商品订单 -->
    <select id="listProductOrderByProduct" parameterType="com.senox.tms.domain.LogisticOrderProduct"
            resultType="com.senox.tms.domain.LogisticOrderProduct">
        SELECT id, ship_date, order_no, merchant, member, product, product_type1, product_type2, product_price, product_count
            , product_amount, product_weight, product_size, product_deduction, product_full_reduction, total_amount, remark
        FROM t_logistic_order_product
        WHERE (ship_date, order_no, merchant, product, product_count) IN <foreach collection="list" item="item" open="(" close=")" separator=",">(#{item.shipDate}, #{item.orderNo}, #{item.merchant}, #{item.product}, #{item.productCount})</foreach>
    </select>

    <!-- 订单数合计 -->
    <select id="countOrder" parameterType="com.senox.tms.vo.LogisticOrderSearchVo" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_logistic_order_product p
            LEFT JOIN t_logistic_order_ship s ON s.order_product_id = p.id
            LEFT JOIN t_logistic_order_bill b ON b.order_product_id = p.id AND b.ship_id = s.id
        <where>
            <if test="shipDateStart != null">
                AND s.ship_date >= #{shipDateStart}
            </if>
            <if test="shipDateEnd != null">
                AND s.ship_date <![CDATA[<=]]> #{shipDateEnd}
            </if>
            <if test="shipPrice != null">
                AND s.ship_price = #{shipPrice}
            </if>
            <if test="shipPriceStart != null">
                AND s.ship_price >= #{shipPriceStart}
            </if>
            <if test="shipPriceEnd != null">
                AND s.ship_price <![CDATA[<=]]> #{shipPriceEnd}
            </if>
            <if test="shipDiscountLt != null">
                AND s.ship_discount <![CDATA[<]]> #{shipDiscountLt}
            </if>
            <if test="shipDiscountStart != null">
                AND s.ship_discount >= #{shipDiscountStart}
            </if>
            <if test="shipDiscountEnd != null">
                AND s.ship_discount <![CDATA[<=]]> #{shipDiscountEnd}
            </if>
            <if test="productOweStart != null">
                AND b.product_owe >= #{productOweStart}
            </if>
            <if test="productOweEnd != null">
                AND b.product_owe <![CDATA[<=]]> #{productOweEnd}
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
                AND s.vehicle_no LIKE CONCAT('%', #{vehicleNo}, '%')
            </if>
            <if test="area != null and area != ''">
                AND s.area LIKE CONCAT(#{area}, '%')
            </if>
            <if test="productPaidMan != null and productPaidMan != ''">
                AND b.product_paid_man LIKE CONCAT('%', #{productPaidMan}, '%')
            </if>
            <if test="market != null and market != ''">
                AND s.market LIKE CONCAT('%', #{market}, '%')
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND p.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="merchant != null and merchant != ''">
                AND p.merchant LIKE CONCAT('%', #{merchant}, '%')
            </if>
            <if test="product != null and product !=''">
                AND p.product LIKE CONCAT('%', #{product}, '%')
            </if>
            <if test="remark != null and remark != ''">
                AND p.remark LIKE CONCAT('%', #{remark}, '%')
            </if>
        </where>
    </select>

    <!-- 订单统计 -->
    <select id="sumOrder" parameterType="com.senox.tms.vo.LogisticOrderSearchVo" resultType="com.senox.tms.vo.LogisticOrderVo">
        SELECT IFNUll(SUM(p.product_count), 0) AS product_count
            , IFNUll(SUM(p.product_amount), 0) AS product_amount
            , IFNULL(SUM(p.product_weight), 0) AS product_weight
            , IFNULL(SUM(p.product_size), 0) AS product_size
            , SUM(p.product_deduction) AS product_deduction
            , SUM(p.product_full_reduction) AS product_full_reduction
            , SUM(p.total_amount) AS product_total_amount
            , SUM(s.ship_count) AS ship_count
            , SUM(s.ship_diversity) AS ship_diversity
            , SUM(s.ship_weight) AS ship_weight
            , SUM(s.ship_size) AS ship_size
            , SUM(s.ship_amount) AS ship_amount
            , SUM(s.sort_amount) AS sort_amount
            , SUM(s.total_amount) AS ship_total_amount
            , SUM(b.product_paid) AS product_paid
            , SUM(b.product_diversity) AS product_diversity
            , SUM(b.product_owe) AS product_owe
            , SUM(b.total_amount) AS total_amount
        FROM t_logistic_order_product p
            LEFT JOIN t_logistic_order_ship s ON s.order_product_id = p.id
            LEFT JOIN t_logistic_order_bill b ON b.order_product_id = p.id AND b.ship_id = s.id
        <where>
            <if test="shipDateStart != null">
                AND s.ship_date >= #{shipDateStart}
            </if>
            <if test="shipDateEnd != null">
                AND s.ship_date <![CDATA[<=]]> #{shipDateEnd}
            </if>
            <if test="shipPrice != null">
                AND s.ship_price = #{shipPrice}
            </if>
            <if test="shipPriceStart != null">
                AND s.ship_price >= #{shipPriceStart}
            </if>
            <if test="shipPriceEnd != null">
                AND s.ship_price <![CDATA[<=]]> #{shipPriceEnd}
            </if>
            <if test="shipDiscountLt != null">
                AND s.ship_discount <![CDATA[<]]> #{shipDiscountLt}
            </if>
            <if test="shipDiscountStart != null">
                AND s.ship_discount >= #{shipDiscountStart}
            </if>
            <if test="shipDiscountEnd != null">
                AND s.ship_discount <![CDATA[<=]]> #{shipDiscountEnd}
            </if>
            <if test="productOweStart != null">
                AND b.product_owe >= #{productOweStart}
            </if>
            <if test="productOweEnd != null">
                AND b.product_owe <![CDATA[<=]]> #{productOweEnd}
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
                AND s.vehicle_no LIKE CONCAT('%', #{vehicleNo}, '%')
            </if>
            <if test="area != null and area != ''">
                AND s.area LIKE CONCAT(#{area}, '%')
            </if>
            <if test="productPaidMan != null and productPaidMan != ''">
                AND b.product_paid_man LIKE CONCAT('%', #{productPaidMan}, '%')
            </if>
            <if test="market != null and market != ''">
                AND s.market LIKE CONCAT('%', #{market}, '%')
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND p.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="merchant != null and merchant != ''">
                AND p.merchant LIKE CONCAT('%', #{merchant}, '%')
            </if>
            <if test="product != null and product !=''">
                AND p.product LIKE CONCAT('%', #{product}, '%')
            </if>
            <if test="remark != null and remark != ''">
                AND p.remark LIKE CONCAT('%', #{remark}, '%')
            </if>
        </where>
    </select>

    <!-- 订单列表 -->
    <select id="listOrder" parameterType="com.senox.tms.vo.LogisticOrderSearchVo" resultType="com.senox.tms.vo.LogisticOrderVo">
        SELECT p.id AS product_order_id, p.order_no, p.merchant, p.member, p.product, p.product_type1, p.product_type2, p.product_price, p.product_count
            , p.product_amount, p.product_weight, p.product_size, p.product_deduction, p.product_full_reduction, p.total_amount AS product_total_amount
            , p.remark, s.id AS ship_id, s.ship_date, s.receiver_contact, s.destination, s.market, s.area, s.vehicle_no, s.driver
            , s.ship_count, s.ship_diversity, s.piece_weight, s.ship_weight, s.piece_size, s.ship_size, s.ship_price, s.ship_multiplying, s.ship_amount
            , s.ship_discount, s.sort_amount, s.is_sort_charge AS sort_charge, s.total_amount AS ship_total_amount, b.product_paid, b.product_paid_man
            , b.product_diversity, b.product_owe, b.total_amount, IFNULL(u.real_name, u.username) AS creator, p.create_time
        FROM t_logistic_order_product p
            LEFT JOIN t_logistic_order_ship s ON s.order_product_id = p.id
            LEFT JOIN t_logistic_order_bill b ON b.order_product_id = p.id AND b.ship_id = s.id
            LEFT JOIN u_admin_user u ON u.id = p.creator_id
        <where>
            <if test="shipDateStart != null">
                AND s.ship_date >= #{shipDateStart}
            </if>
            <if test="shipDateEnd != null">
                AND s.ship_date <![CDATA[<=]]> #{shipDateEnd}
            </if>
            <if test="shipPrice != null">
                AND s.ship_price = #{shipPrice}
            </if>
            <if test="shipPriceStart != null">
                AND s.ship_price >= #{shipPriceStart}
            </if>
            <if test="shipPriceEnd != null">
                AND s.ship_price <![CDATA[<=]]> #{shipPriceEnd}
            </if>
            <if test="shipDiscountLt != null">
                AND s.ship_discount <![CDATA[<]]> #{shipDiscountLt}
            </if>
            <if test="shipDiscountStart != null">
                AND s.ship_discount >= #{shipDiscountStart}
            </if>
            <if test="shipDiscountEnd != null">
                AND s.ship_discount <![CDATA[<=]]> #{shipDiscountEnd}
            </if>
            <if test="productOweStart != null">
                AND b.product_owe >= #{productOweStart}
            </if>
            <if test="productOweEnd != null">
                AND b.product_owe <![CDATA[<=]]> #{productOweEnd}
            </if>
            <if test="vehicleNo != null and vehicleNo != ''">
                AND s.vehicle_no LIKE CONCAT('%', #{vehicleNo}, '%')
            </if>
            <if test="area != null and area != ''">
                AND s.area LIKE CONCAT(#{area}, '%')
            </if>
            <if test="productPaidMan != null and productPaidMan != ''">
                AND b.product_paid_man LIKE CONCAT('%', #{productPaidMan}, '%')
            </if>
            <if test="market != null and market != ''">
                AND s.market LIKE CONCAT('%', #{market}, '%')
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND p.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="merchant != null and merchant != ''">
                AND p.merchant LIKE CONCAT('%', #{merchant}, '%')
            </if>
            <if test="product != null and product !=''">
                AND p.product LIKE CONCAT('%', #{product}, '%')
            </if>
            <if test="remark != null and remark != ''">
                AND p.remark LIKE CONCAT('%', #{remark}, '%')
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY p.ship_date DESC, p.id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <!-- 商户应付 -->
    <select id="listMerchantPayoff" parameterType="com.senox.tms.vo.LogisticPayoffGenerateVo" resultType="com.senox.tms.domain.LogisticPayoff">
        SELECT s.ship_date AS bill_date
            , p.merchant
            , SUM(s.ship_count) AS product_count
            , SUM(p.product_amount) AS product_amount
            , SUM(p.product_full_reduction) AS product_full_reduction
            , SUM(b.order_amount) AS product_to_paid
            , SUM(b.product_paid) AS product_paid
            , SUM(b.product_owe) AS product_owe
            , SUM(p.product_deduction) AS product_deduction
            , SUM(b.product_diversity) AS product_diversity
            , SUM(b.ship_amount) AS ship_amount
        FROM t_logistic_order_product p
            INNER JOIN t_logistic_order_ship s ON s.order_product_id = p.id
            INNER JOIN t_logistic_order_bill b ON b.order_product_id = p.id
        <where>
            <if test="billDate != null">
                AND s.ship_date = #{billDate}
            </if>
            <if test="merchant != null and merchant != ''">
                AND p.merchant = #{merchant}
            </if>
        </where>
        GROUP BY s.ship_date, p.merchant
    </select>
</mapper>
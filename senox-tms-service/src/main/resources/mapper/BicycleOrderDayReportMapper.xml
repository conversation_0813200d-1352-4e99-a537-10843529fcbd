<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleOrderDayReportMapper">

    <!--日报表合计-->
    <select id="sumDayReport" parameterType="com.senox.tms.vo.BicycleOrderDayReportSearchVo" resultType="com.senox.tms.domain.BicycleOrderDayReport">
        SELECT SUM(tbodr.delivery_charge) AS delivery_charge
             , SUM(tbodr.other_charge) AS other_charge
            , SUM(tbodr.handling_charge) AS handling_charge
            , SUM(tbodr.upstairs_charge) AS upstairs_charge
             , SUM(tbodr.total_charge) AS total_charge
            , SUM(tbodr.total_count) as total_count
            , SUM(tbodr.total_pieces) as total_pieces
        FROM t_bicycle_order_day_report tbodr
        LEFT JOIN u_merchant um ON tbodr.merchant_id = um.id
        <where>
            <if test="merchantId != null">
                AND tbodr.merchant_id = #{merchantId}
            </if>
            <if test="merchantName != null and merchantName != ''">
                AND um.name like CONCAT('%', #{merchantName}, '%')
            </if>
            <if test="reportDateStart != null">
                AND tbodr.report_date >= #{reportDateStart}
            </if>
            <if test="reportDateEnd != null">
                AND tbodr.report_date <![CDATA[<=]]> #{reportDateEnd}
            </if>
        </where>
    </select>

    <!--日报表统计-->
    <select id="countDayReport" parameterType="com.senox.tms.vo.BicycleOrderDayReportSearchVo" resultType="int">
        SELECT count(tbodr.id) FROM t_bicycle_order_day_report tbodr
        LEFT JOIN u_merchant um ON tbodr.merchant_id = um.id
        <where>
            <if test="merchantId != null">
                AND tbodr.merchant_id = #{merchantId}
            </if>
            <if test="merchantName != null and merchantName != ''">
                AND um.name like CONCAT('%', #{merchantName}, '%')
            </if>
            <if test="reportDateStart != null">
                AND tbodr.report_date >= #{reportDateStart}
            </if>
            <if test="reportDateEnd != null">
                AND tbodr.report_date <![CDATA[<=]]> #{reportDateEnd}
            </if>
        </where>
    </select>

    <!--日报表列表-->
    <select id="listDayReport" parameterType="com.senox.tms.vo.BicycleOrderDayReportSearchVo" resultType="com.senox.tms.vo.BicycleOrderDayReportVo">
        select tbodr.id, tbodr.report_date, tbodr.merchant_id, um.name as merchant_name, tbodr.total_pieces, tbodr.total_count
             , tbodr.delivery_charge, tbodr.other_charge, tbodr.handling_charge, tbodr.upstairs_charge, tbodr.total_charge
        from t_bicycle_order_day_report tbodr
        LEFT JOIN u_merchant um ON tbodr.merchant_id = um.id
        <where>
            <if test="merchantId != null">
                AND tbodr.merchant_id = #{merchantId}
            </if>
            <if test="merchantName != null and merchantName != ''">
                AND um.name like CONCAT('%', #{merchantName}, '%')
            </if>
            <if test="reportDateStart != null">
                AND tbodr.report_date >= #{reportDateStart}
            </if>
            <if test="reportDateEnd != null">
                AND tbodr.report_date <![CDATA[<=]]> #{reportDateEnd}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY tbodr.report_date desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>


</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleOrderMonthReportMapper">

    <!--月报表合计-->
    <select id="sumMonthReport" parameterType="com.senox.tms.vo.BicycleOrderMonthReportSearchVo" resultType="com.senox.tms.domain.BicycleOrderMonthReport">
        SELECT SUM(tbomr.delivery_charge) AS delivery_charge
        , SUM(tbomr.other_charge) AS other_charge
        , SUM(tbomr.handling_charge) AS handling_charge
        , SUM(tbomr.upstairs_charge) AS upstairs_charge
        , SUM(tbomr.total_charge) AS total_charge
        , SUM(tbomr.total_count) as total_count
        , SUM(tbomr.total_pieces) as total_pieces
        FROM t_bicycle_order_month_report tbomr
        LEFT JOIN u_merchant um ON tbomr.merchant_id = um.id
        <where>
            <if test="merchantId != null">
                AND tbomr.merchant_id = #{merchantId}
            </if>
            <if test="merchantName != null and merchantName != ''">
                AND um.name like CONCAT('%', #{merchantName}, '%')
            </if>
            <if test="reportYear != null">
                AND tbomr.report_year = #{reportYear}
            </if>
            <if test="reportMonth != null">
                AND tbomr.report_month = #{reportMonth}
            </if>
        </where>
    </select>

    <!--月报表统计-->
    <select id="countMonthReport" parameterType="com.senox.tms.vo.BicycleOrderMonthReportSearchVo" resultType="int">
        SELECT count(tbomr.id) FROM t_bicycle_order_month_report tbomr
        LEFT JOIN u_merchant um ON tbomr.merchant_id = um.id
        <where>
            <if test="merchantId != null">
                AND tbomr.merchant_id = #{merchantId}
            </if>
            <if test="merchantName != null and merchantName != ''">
                AND um.name like CONCAT('%', #{merchantName}, '%')
            </if>
            <if test="reportYear != null">
                AND tbomr.report_year = #{reportYear}
            </if>
            <if test="reportMonth != null">
                AND tbomr.report_month = #{reportMonth}
            </if>
        </where>
    </select>

    <!--月报表列表-->
    <select id="listMonthReport" parameterType="com.senox.tms.vo.BicycleOrderMonthReportSearchVo" resultType="com.senox.tms.vo.BicycleOrderMonthReportVo">
        select tbomr.id, tbomr.report_year_month, tbomr.report_year, tbomr.report_month, tbomr.merchant_id, um.name as merchant_name
             , tbomr.total_pieces, tbomr.total_count, tbomr.delivery_charge, tbomr.other_charge, tbomr.handling_charge, tbomr.upstairs_charge, tbomr.total_charge
        from t_bicycle_order_month_report tbomr
        LEFT JOIN u_merchant um ON tbomr.merchant_id = um.id
        <where>
            <if test="merchantId != null">
                AND tbomr.merchant_id = #{merchantId}
            </if>
            <if test="merchantName != null and merchantName != ''">
                AND um.name like CONCAT('%', #{merchantName}, '%')
            </if>
            <if test="reportYear != null">
                AND tbomr.report_year = #{reportYear}
            </if>
            <if test="reportMonth != null">
                AND tbomr.report_month = #{reportMonth}
            </if>
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY tbomr.id desc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>
</mapper>
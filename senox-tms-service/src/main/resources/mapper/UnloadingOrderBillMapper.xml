<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingOrderBillMapper">

    <select id="countBill" parameterType="com.senox.tms.vo.UnloadingOrderBillSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_order_bill
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="billDateStart != null">
                AND bill_date >= #{billDateStart}
            </if>
            <if test="billDateEnd != null">
                AND bill_date <![CDATA[<=]]> #{billDateEnd}
            </if>
            <if test="paidTimeStart != null">
                AND paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listBill" parameterType="com.senox.tms.vo.UnloadingOrderBillSearchVo" resultType="com.senox.tms.vo.UnloadingOrderBillVo">
        SELECT
              b.id
            , b.bill_date
            , b.bill_year
            , b.bill_month
            , b.order_no
            , b.amount
            , b.status
            , b.paid_time
            , o.customer_name
            , o.merchant_id
            , o.address
            , o.license_plate
            , o.contact
            , au.real_name as toll_man
            , o.id as order_id
            , (select sum(og.quantity) from px_unloading_order_goods og where og.order_id = o.id) as quantity
        FROM px_unloading_order_bill b
        INNER JOIN px_unloading_order o on b.order_no = o.order_no
        left join u_admin_user au on au.id = b.toll_man_id
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND b.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and b.order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="billDateStart != null">
                AND b.bill_date >= #{billDateStart}
            </if>
            <if test="billDateEnd != null">
                AND b.bill_date <![CDATA[<=]]> #{billDateEnd}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            AND b.is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY b.id
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="sumBill" parameterType="com.senox.tms.vo.UnloadingOrderBillSearchVo" resultType="com.senox.tms.vo.UnloadingOrderBillVo">
        SELECT
            SUM(b.amount) as amount
          , SUM(og_sum.quantity) as quantity
        FROM px_unloading_order_bill b
        INNER JOIN px_unloading_order o on b.order_no = o.order_no
        LEFT JOIN (
            SELECT order_id
                 , SUM(quantity) as quantity
            FROM px_unloading_order_goods
            GROUP BY order_id
        ) og_sum ON og_sum.order_id = o.id
        <where>
            <if test="orderNo != null and orderNo != ''">
                AND b.order_no LIKE CONCAT('%', #{orderNo}, '%')
            </if>
            <if test="orderNoList != null and orderNoList.size() > 0">
                and b.order_no in
                <foreach collection="orderNoList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="billDateStart != null">
                AND b.bill_date >= #{billDateStart}
            </if>
            <if test="billDateEnd != null">
                AND b.bill_date <![CDATA[<=]]> #{billDateEnd}
            </if>
            <if test="paidTimeStart != null">
                AND b.paid_time >= #{paidTimeStart}
            </if>
            <if test="paidTimeEnd != null">
                AND b.paid_time <![CDATA[<=]]> #{paidTimeEnd}
            </if>
            <if test="status != null">
                AND b.status = #{status}
            </if>
            AND b.is_disabled = 0
        </where>
    </select>

</mapper>

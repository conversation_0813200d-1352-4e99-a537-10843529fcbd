<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingWorkerMapper">

    <select id="getMaxOrderNum" resultType="int">
        SELECT MAX(order_num) FROM px_unloading_worker WHERE is_disabled = 0
    </select>


    <select id="filterListWorker" resultType="com.senox.tms.domain.UnloadingWorker">
        SELECT id, worker_no, name, status, order_num FROM px_unloading_worker
        <where>
            AND is_disabled = 0
            <if test="orderNumStart != null">
                AND order_num >= #{orderNumStart}
            </if>
            <if test="orderNumEnd != null">
                AND order_num <![CDATA[<=]]> #{orderNumEnd}
            </if>
        </where>
    </select>


    <select id="findMaxWorkerSign" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(worker_sign) FROM px_unloading_worker WHERE worker_sign LIKE CONCAT(#{prefix}, '%') AND is_disabled = 0;
    </select>

    <select id="countWorker" parameterType="com.senox.tms.vo.UnloadingWorkerSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_worker
        <where>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (name like concat('%', #{keyword}, '%') OR contact like concat('%', #{keyword}, '%'))
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="classes != null">
                and classes = #{classes}
            </if>
            <if test="roundNum != null">
                and round_num = #{roundNum}
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listWorker" parameterType="com.senox.tms.vo.UnloadingWorkerSearchVo" resultType="com.senox.tms.domain.UnloadingWorker">
        SELECT
              id
            , worker_no
            , worker_sign
            , name
            , born_date
            , contact
            , face_url
            , status
            , classes
            , order_num
            , round_num
            , punish_round_num
            <if test="rows">
                , ROW_NUMBER() OVER () AS row_num
            </if>
        FROM px_unloading_worker
        <where>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (name like concat('%', #{keyword}, '%') OR contact like concat('%', #{keyword}, '%'))
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="classes != null">
                and classes = #{classes}
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY
                    CASE status
                        WHEN 1 THEN 1
                        WHEN 2 THEN 2
                        WHEN 0 THEN 3
                        WHEN 3 THEN 4
                        END ASC
                    , CASE order_num
                        WHEN order_num = 0 THEN 9999
                        ELSE order_num END ASC
                    , id asc
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

    <select id="listSequenceWorker" parameterType="com.senox.tms.vo.UnloadingWorkerSearchVo" resultType="com.senox.tms.domain.UnloadingWorker">
        SELECT
            id
            , worker_no
            , worker_sign
            , name
            , born_date
            , contact
            , face_url
            , status
            , order_num
            , round_num
            , punish_round_num
        <if test="rows">
            , ROW_NUMBER() OVER () AS row_num
        </if>
        FROM px_unloading_worker
        <where>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (name like concat('%', #{keyword}, '%') OR contact like concat('%', #{keyword}, '%'))
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="roundNum != null">
                and round_num = #{roundNum}
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY round_num, worker_no
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>


    <select id="findFirstWorker" parameterType="int" resultType="com.senox.tms.domain.UnloadingWorker">
        SELECT
            id
             , worker_no
             , worker_sign
             , name
             , born_date
             , contact
             , face_url
             , status
             , order_num
             , round_num
             , punish_round_num
        FROM px_unloading_worker
        where round_num = #{roundNum} and status in (0, 1, 2) and is_disabled = 0
        ORDER BY round_num, worker_no
        limit 1
        </select>

    <select id="findLastWorker" parameterType="int" resultType="com.senox.tms.domain.UnloadingWorker">
        SELECT
            id
             , worker_no
             , worker_sign
             , name
             , born_date
             , contact
             , face_url
             , status
             , order_num
             , round_num
             , punish_round_num
        FROM px_unloading_worker
        where round_num = #{roundNum} and status in (0, 1, 2) and is_disabled = 0
        ORDER BY round_num, worker_no desc
        limit 1
    </select>

    <select id="listPunishWorker" resultType="com.senox.tms.domain.UnloadingWorker">
        SELECT
            id
             , worker_no
             , worker_sign
             , name
             , born_date
             , contact
             , face_url
             , status
             , order_num
             , round_num
             , punish_round_num
        FROM px_unloading_worker
        where punish_round_num > 0 and is_disabled = 0
    </select>

    <select id="dayCountWorker" resultType="com.senox.tms.vo.UnloadingDayCountWorkerVo">
        SELECT
            ifnull(SUM( CASE WHEN STATUS = 0 THEN 1 ELSE 0 END ), 0) AS not_listed_count,
            ifnull(SUM( CASE WHEN STATUS = 1 THEN 1 ELSE 0 END ), 0) AS already_listed_count,
            ifnull(SUM( CASE WHEN STATUS = 2 THEN 1 ELSE 0 END ), 0) AS during_transportation_count,
            ifnull(SUM( CASE WHEN STATUS = 3 THEN 1 ELSE 0 END ), 0) AS worker_leave_count
        FROM
            px_unloading_worker where is_disabled = 0
    </select>

    <select id="listWorkerByOrderId" resultType="com.senox.tms.domain.UnloadingWorker" parameterType="java.lang.Long">
        SELECT puw.id
             , puw.worker_no
             , puw.worker_sign
             , puw.name
             , puw.born_date
             , puw.contact
             , puw.face_url
             , puw.status
             , puw.listing
             , puw.order_num
             , puw.round_num
             , puw.punish_round_num
        from px_unloading_order_workers puow
        INNER JOIN px_unloading_worker puw on puow.worker_id = puw.id
        WHERE puow.order_id = #{orderId} and puw.is_disabled = 0
    </select>
</mapper>

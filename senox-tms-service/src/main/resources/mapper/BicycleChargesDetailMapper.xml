<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleChargesDetailMapper">

    <resultMap id="detail_point_result" type="com.senox.tms.vo.BicycleChargesDetailVo">
        <id property="id" column="d_id"/>
        <result property="chargesId" column="d_charges_id"/>
        <result property="minCount" column="d_min_count"/>
        <result property="maxCount" column="d_max_count"/>
        <result property="unitPrice" column="d_unit_price"/>
        <result property="calAnyway" column="d_is_cal_anyway"/>
        <result property="goodsType" column="goods_type"/>
        <result property="minUnit" column="min_unit"/>
        <result property="maxUnit" column="max_unit"/>
        <result property="defaultCharges" column="default_charges"/>
    </resultMap>

    <select id="listByCharges" resultMap="detail_point_result">
        select d.id          as d_id,
               d.charges_id  as d_charges_id,
               d.min_count   as d_min_count,
               d.max_count   as d_max_count,
               d.unit_price  as d_unit_price,
               d.is_cal_anyway as d_is_cal_anyway,
               d.goods_type,
               d.min_unit,
               d.max_unit,
               d.default_charges
        from t_bicycle_charges_detail d
        <where>
            and d.charges_id = #{chargesId}
        </where>
    </select>
</mapper>
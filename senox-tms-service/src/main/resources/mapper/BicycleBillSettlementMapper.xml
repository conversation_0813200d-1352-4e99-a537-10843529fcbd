<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.BicycleBillSettlementMapper">

    <select id="listByIds" resultType="com.senox.tms.vo.BicycleBillSettlementVo">
        select bs.id,
        bs.bill_year_month,
        bs.bill_year,
        bs.bill_month,
        bs.merchant_id,
        um.name as merchant_name,
        um.rc_serial,
        um.settle_period,
        bs.amount,
        bs.pay_way,
        bs.paid_amount,
        bs.paid_time,
        bs.remote_order_id,
        bs.send,
        bs.send_time,
        au.real_name as toll_man_name,
        bs.status,
        bs.creator_id,
        bs.creator_name,
        bs.create_time,
        bs.modifier_id,
        bs.modifier_name,
        bs.modified_time
        from t_bicycle_bill_settlement bs
        inner join u_merchant um on um.id = bs.merchant_id
        left join u_admin_user au on au.id = bs.toll_man_id
        <where>
            and bs.id in
            <foreach collection="ids" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </where>
    </select>

    <insert id="addBatch" useGeneratedKeys="true" keyProperty="id">
        insert into t_bicycle_bill_settlement(bill_date,bill_year_month, bill_year, bill_month, merchant_id,merchant_name,settle_period,amount,
        creator_id, creator_name, create_time, modifier_id,
        modifier_name, modified_time)
        values
        <foreach collection="billSettlements" item="item" separator=",">
            (#{item.billDate},#{item.billYearMonth},#{item.billYear},#{item.billMonth},#{item.merchantId},#{item.merchantName},#{item.settlePeriod},#{item.amount}
            ,#{item.creatorId},#{item.creatorName},now(),#{item.modifierId},#{item.modifierName},now()
            )
        </foreach>
    </insert>

    <update id="updateBatch">
        update t_bicycle_bill_settlement
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="bill_year_month = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.billYearMonth">
                        when id = #{item.id} then #{item.billYearMonth}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bill_year = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.billYear and item.billYear > 0">
                        when id = #{item.id} then #{item.billYear}
                    </if>
                </foreach>
            </trim>
            <trim prefix="bill_month = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.billMonth and item.billMonth > 0">
                        when id = #{item.id} then #{item.billMonth}
                    </if>
                </foreach>
            </trim>
            <trim prefix="merchant_id = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.merchantId and item.merchantId > 0">
                        when id = #{item.id} then #{item.merchantId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="amount = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.amount">
                        when id = #{item.id} then #{item.amount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="paid_amount = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.paidAmount">
                        when id = #{item.id} then #{item.paidAmount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pay_way = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.payWay and item.payWay > 0">
                        when id = #{item.id} then #{item.payWay}
                    </if>
                </foreach>
            </trim>
            <trim prefix="paid_time = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.paidTime">
                        when id = #{item.id} then #{item.paidTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remote_order_id = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.remoteOrderId and item.remoteOrderId > 0">
                        when id = #{item.id} then #{item.remoteOrderId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="send = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.send">
                        when id = #{item.id} then #{item.send}
                    </if>
                </foreach>
            </trim>
            <trim prefix="send_time = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.sendTime">
                        when id = #{item.id} then #{item.sendTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="toll_man_id = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.tollManId">
                        when id = #{item.id} then #{item.tollManId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.status">
                        when id = #{item.id} then #{item.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_id = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.modifierId">
                        when id = #{item.id} then #{item.modifierId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="modifier_name = case" suffix="end,">
                <foreach collection="billSettlements" item="item">
                    <if test="null != item.modifierName">
                        when id = #{item.id} then #{item.modifierName}
                    </if>
                </foreach>
            </trim>
            modified_time = now()
        </trim>
        <where>
            and id in
            <foreach collection="billSettlements" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </where>
    </update>

    <select id="list" resultType="com.senox.tms.vo.BicycleBillSettlementVo">
        select bs.id,
        bs.bill_year_month,
        bs.bill_year,
        bs.bill_month,
        bs.merchant_id,
        um.name as merchant_name,
        um.rc_serial,
        bs.settle_period,
        bs.amount,
        bs.pay_way,
        bs.paid_amount,
        bs.paid_time,
        bs.remote_order_id,
        bs.send,
        bs.send_time,
        au.real_name as toll_man_name,
        bs.status,
        bs.creator_id,
        bs.creator_name,
        bs.create_time,
        bs.modifier_id,
        bs.modifier_name,
        bs.modified_time
        from t_bicycle_bill_settlement bs
        inner join u_merchant um on um.id = bs.merchant_id
        left join u_admin_user au on au.id = bs.toll_man_id
        <where>
            <if test="null != merchantIds and merchantIds.size() > 0">
                and bs.merchant_id in
                <foreach collection="merchantIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="null != send">
                and bs.send = #{send}
            </if>
            <if test="null != keyword and keyword != ''">
                and (um.name like concat('%',#{keyword},'%') or um.rc_serial like concat('%',#{keyword},'%'))
            </if>
            <if test="null != tollManId and tollManId > 0">
                and bs.toll_man_id = #{tollManId}
            </if>
            <if test="null != settlePeriod">
                and um.settle_period = #{settlePeriod}
            </if>
            <if test="null != payWay and payWay != ''">
                and bs.pay_way = #{payWay}
            </if>
            <if test="null != status">
                and bs.status = #{status}
            </if>
            <if test="null != startDate and startDate != ''">
                and bs.bill_year_month >= #{startDate}
            </if>
            <if test="null != endDate and endDate != ''">
                and bs.bill_year_month &lt;= #{endDate}
            </if>
            <if test="null != paidStartDate">
                and bs.paid_time >= #{paidStartDate}
            </if>
            <if test="null != paidEndDate">
                and bs.paid_time &lt;= #{paidEndDate}
            </if>
        </where>
        order by bs.modified_time desc
        <if test="page">
            limit ${offset}, ${pageSize}
        </if>
    </select>

    <select id="countList" resultType="int">
        select count(*)
        from t_bicycle_bill_settlement bs
        inner join u_merchant um on um.id = bs.merchant_id
        <where>
            <if test="null != merchantIds and merchantIds.size() > 0">
                and bs.merchant_id in
                <foreach collection="merchantIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="null != send">
                and bs.send = #{send}
            </if>
            <if test="null != keyword and keyword != ''">
                and (um.name like concat('%',#{keyword},'%') or um.rc_serial like concat('%',#{keyword},'%'))
            </if>
            <if test="null != tollManId and tollManId > 0">
                and bs.toll_man_id = #{tollManId}
            </if>
            <if test="null != settlePeriod">
                and um.settle_period = #{settlePeriod}
            </if>
            <if test="null != payWay and payWay != ''">
                and bs.pay_way = #{payWay}
            </if>
            <if test="null != status">
                and bs.status = #{status}
            </if>
            <if test="null != startDate and startDate != ''">
                and bs.bill_year_month >= #{startDate}
            </if>
            <if test="null != endDate and endDate != ''">
                and bs.bill_year_month &lt;= #{endDate}
            </if>
            <if test="null != paidStartDate">
                and bs.paid_time >= #{paidStartDate}
            </if>
            <if test="null != paidEndDate">
                and bs.paid_time &lt;= #{paidEndDate}
            </if>
        </where>
    </select>

    <update id="updatePaid">
        update t_bicycle_bill_settlement bs
        inner join p_order_item oi on oi.order_id = #{orderId} and oi.product_id = bs.id
        <set>
            bs.status = #{paid},
            bs.paid_amount = bs.paid_amount + oi.total_amount,
            bs.pay_way = #{payWay},
            bs.paid_time = #{paidTime},
            bs.toll_man_id = #{tollMan},
            bs.modified_time = now()
        </set>
        <where>
            and bs.status = 0
            and bs.is_disabled = 0
            <choose>
                <when test="null != billIds and billIds.size() > 0">
                    and bs.id in
                    <foreach collection="billIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
                </when>
                <otherwise>
                    and bs.remote_order_id = #{orderId}
                </otherwise>
            </choose>
        </where>
    </update>

    <select id="detailBySettlement" resultType="com.senox.tms.vo.BicycleBillVo">
        select b.bill_date,
        b.bill_year,
        b.bill_month,
        m.name                                                as merchant_name,
        b.amount                                                 total_amount,
        o.order_serial_no,
        o.delivery_charge                                     as delivery_amount,
        o.other_charge                                        as other_amount,
        o.handling_charge                                        as handling_amount,
        o.upstairs_charge                                        as upstairs_amount,
        o.pieces,
        concat(o.start_point_name, o.start_point_detail_name) as start_point,
        concat(o.end_point_name, o.end_point_detail_name)            as end_point
        from t_bicycle_bill b
        inner join t_bicycle_order o on o.order_serial_no = b.order_serial_no
        inner join u_merchant m on m.id = b.merchant_id
        <where>
            and b.settlement_id = #{settlementId}
        </where>
        order by b.bill_date
    </select>

    <select id="countDetailByDetailBySettlement" resultType="com.senox.tms.vo.BicycleBillSettlementDetailVo">
        select sum(o.pieces) as pieces,
        sum(o.delivery_charge) as delivery_charge,
        sum(o.other_charge) as other_charge,
        sum(o.total_charge) as total_charge,
        sum(o.handling_charge) as handling_charge,
        sum(o.upstairs_charge) as upstairs_charge
        from t_bicycle_bill b
        inner join t_bicycle_order o on o.order_serial_no = b.order_serial_no
        inner join u_merchant m on m.id = b.merchant_id
        <where>
            and b.settlement_id = #{settlementId}
        </where>
    </select>

    <update id="send">
        update t_bicycle_bill_settlement set send = true,send_time = now(),modified_time = now()
        <where>
            <if test="null != startDate">
                and bill_year_month >= #{startDate}
            </if>
            <if test="null != endDate">
                and bill_year_month &lt;= #{endDate}
            </if>
            <if test="null != settlementIds and settlementIds.size() > 0">
                and id in
                <foreach collection="settlementIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and send = false
            and is_disabled = false
        </where>

    </update>

    <select id="listCount" resultType="com.senox.tms.vo.BicycleBillSettlementDetailVo">
        select ifnull(sum(bs.amount),0) as total_amount,ifnull(sum(bs.paid_amount),0) as total_paid_amount
        from t_bicycle_bill_settlement bs
        inner join u_merchant um on um.id = bs.merchant_id
        <if test="null != merchantIds and merchantIds.size() > 0">
            and bs.merchant_id in
            <foreach collection="merchantIds" item="item" open="(" close=")" separator=",">#{item}</foreach>
        </if>
        <if test="null != send">
            and bs.send = #{send}
        </if>
        <if test="null != keyword and keyword != ''">
            and (um.name like concat('%',#{keyword},'%') or um.rc_serial like concat('%',#{keyword},'%'))
        </if>
        <if test="null != tollManId and tollManId > 0">
            and bs.toll_man_id = #{tollManId}
        </if>
        <if test="null != settlePeriod">
            and um.settle_period = #{settlePeriod}
        </if>
        <if test="null != payWay and payWay != ''">
            and bs.pay_way = #{payWay}
        </if>
        <if test="null != status">
            and bs.status = #{status}
        </if>
        <if test="null != startDate and startDate != ''">
            and bs.bill_year_month >= #{startDate}
        </if>
        <if test="null != endDate and endDate != ''">
            and bs.bill_year_month &lt;= #{endDate}
        </if>
        <if test="null != paidStartDate">
            and bs.paid_time >= #{paidStartDate}
        </if>
        <if test="null != paidEndDate">
            and bs.paid_time &lt;= #{paidEndDate}
        </if>
    </select>

    <select id="findById" resultType="com.senox.tms.vo.BicycleBillSettlementVo">
        select bs.id,
        bs.bill_year_month,
        bs.bill_year,
        bs.bill_month,
        bs.merchant_id,
        um.name as merchant_name,
        um.rc_serial,
        um.settle_period,
        bs.amount,
        bs.pay_way,
        bs.paid_amount,
        bs.paid_time,
        bs.remote_order_id,
        bs.send,
        bs.send_time,
        au.real_name as toll_man_name,
        bs.status,
        bs.creator_id,
        bs.creator_name,
        bs.create_time,
        bs.modifier_id,
        bs.modifier_name,
        bs.modified_time
        from t_bicycle_bill_settlement bs
                 inner join u_merchant um on um.id = bs.merchant_id
                 left join u_admin_user au on au.id = bs.toll_man_id
        <where>
            and bs.id = #{id}
        </where>
    </select>
</mapper>

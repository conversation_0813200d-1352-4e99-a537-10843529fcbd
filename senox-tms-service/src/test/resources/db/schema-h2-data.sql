-- 三轮车接送点
INSERT INTO dict_bicycle_point(id, name, creator_id, creator_name, create_time, modifier_id, modifier_name, modified_time)
values
(1, '1号停车场', 1, 'admin', now(), 1, 'admin', now()),
(2, '1期冷库', 1, 'admin', now(), 1, 'admin', now()),
(3, '2期冷库', 1, 'admin', now(), 1, 'admin', now()),
(4, '3期冷库', 1, 'admin', now(), 1, 'admin', now()),
(5, '物流园', 1, 'admin', now(), 1, 'admin', now());

-- 收费标准
INSERT INTO `t_bicycle_charges`(`id`, `name`, `effective_time`, `ineffective_time`, `status`, `is_disabled`, `creator_id`, `creator_name`, `create_time`, `modifier_id`, `modifier_name`, `modified_time`)
VALUES (1, '测试2', '2023-09-19 12:00:00', '2023-11-21 12:00:00', 0, 0, 1, 'admin', now(), 1, 'admin', now());

-- 收费标准详细
INSERT INTO `t_bicycle_charges_detail`(`id`, `charges_id`, `min_count`, `max_count`, `unit_price`, `is_disabled`)
VALUES (1, 1, 1, 10, 1.00, 0);

-- 分佣标准
INSERT INTO `t_bicycle_shares`(`id`, `name`, `rider_shares`, `effective_time`, `ineffective_time`, `status`, `is_disabled`, `creator_id`, `creator_name`, `create_time`, `modifier_id`, `modifier_name`, `modified_time`)
VALUES (1, '测试2', 12.00, '2023-09-20 05:20:58', '2023-11-22 12:00:00', 2, 0, 1, 'admin', now(), 1, 'admin', now());

-- 骑手
INSERT INTO `t_bicycle_rider`(`id`, `rider_no`, `password`, `salt`, `name`, `contact`, `avatar`, `birthday`, `status`, `is_disabled`, `creator_id`, `creator_name`, `create_time`, `modifier_id`, `modifier_name`, `modified_time`)
VALUES (1, 'BDR00000001', 'caf473eb7b114a6beafa4e78e92dd3b2', 'wo6rLA', '测试1', '13226790691', '1', '2023-09-17', 0, 0, 1, 'admin', now(), 1, 'admin', now()),
(2, 'BDR00000002', 'caf473eb7b114a6beafa4e78e92dd3b2', 'wo6rLA', '测试2', '13226790692', '1', '2023-09-17', 0, 0, 1, 'admin', now(), 1, 'admin', now()),
(3, 'BDR00000003', 'caf473eb7b114a6beafa4e78e92dd3b2', 'wo6rLA', '测试3', '13226790693', '1', '2023-09-17', 0, 0, 1, 'admin', now(), 1, 'admin', now());


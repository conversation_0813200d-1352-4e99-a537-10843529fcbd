drop table if exists t_bicycle_charges;
create table t_bicycle_charges
(
    id               bigint unsigned auto_increment
        primary key comment 'id',
    name             varchar(100)                not null comment '名称',
    effective_time   datetime                    not null comment '生效时间',
    ineffective_time datetime                    not null comment '失效时间',
    status           tinyint unsigned default 0  not null comment '状态',
    default_effective tinyint unsigned NOT NULL DEFAULT '0' COMMENT '默认生效',
    flexible tinyint(1) NOT NULL DEFAULT '0' COMMENT '灵活收费计算',
    min_amount decimal(10, 2) default 0.00 not null comment '低消',
    is_disabled      tinyint(1)       default 0  not null comment '禁用',
    creator_id       bigint unsigned  default 0  not null comment '创建人id',
    creator_name     varchar(30)      default '' not null comment '创建人姓名',
    create_time      datetime                    not null comment '创建时间',
    modifier_id      bigint unsigned  default 0  not null comment '修改人id',
    modifier_name    varchar(30)      default '' not null comment '修改人姓名',
    modified_time    datetime                    not null comment '修改时间',
    constraint uk_name_etime_itime unique (name, effective_time, ineffective_time)
);
-- alter table t_bicycle_charges add default_effective tinyint unsigned default 0 not null comment '默认生效' after status;
-- ALTER TABLE t_bicycle_charges ADD COLUMN flexible tinyint(1) NOT NULL DEFAULT '0' COMMENT '灵活收费计算' AFTER default_effective;
-- ALTER TABLE t_bicycle_charges ADD COLUMN min_amount decimal(10, 2) default 0.00 not null comment '低消' AFTER flexible;

DROP TABLE IF EXISTS dict_bicycle_point;
CREATE TABLE dict_bicycle_point (
    id bigint not null auto_increment,
    name varchar(32) not null comment '名称',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id),
    unique key uk_dict_bicycle_point_name(name)
);

drop table if exists t_bicycle_charges_detail;
create table t_bicycle_charges_detail
(
    id          bigint unsigned auto_increment primary key comment 'id',
    charges_id  bigint unsigned             not null comment '收费标准id',
    goods_type   bigint unsigned   not null default 0 comment '货物类型 0其他，1重货，2抛货',
    min_count   int unsigned                    null comment '最少件数',
    max_count   int unsigned                    null comment '最多件数',
    min_unit  decimal(6, 2)    not null    default 0.00  comment '最少单位',
    max_unit  decimal(6, 2)   not null    default 0.00  comment '最多单位',
    unit_price  decimal(10, 2) default 0.00 not null comment '单价',
    default_charges    tinyint(1)       default 0  not null comment '默认标准',
    is_cal_anyway    tinyint(1)       default 0  not null comment '跨区间计算',
    is_disabled      tinyint(1)       default 0  not null comment '禁用'
);
-- ALTER TABLE t_bicycle_charges_detail DROP COLUMN point_id;
-- ALTER TABLE t_bicycle_charges_detail DROP COLUMN amount_base;
-- ALTER TABLE t_bicycle_charges_detail ADD COLUMN goods_type   bigint unsigned   not null default 0 comment '货物类型 0其他，1重货，2抛货' AFTER charges_id;
-- ALTER TABLE t_bicycle_charges_detail ADD COLUMN min_unit  decimal(6, 2)    not null    default 0.00  comment '最少单位' AFTER max_count;
-- ALTER TABLE t_bicycle_charges_detail ADD COLUMN max_unit  decimal(6, 2)    not null    default 0.00  comment '最多单位' AFTER min_unit;
-- ALTER TABLE t_bicycle_charges_detail ADD COLUMN default_charges    tinyint(1)       default 0  not null comment '默认标准' AFTER max_unit;
-- ALTER TABLE t_bicycle_charges_detail MODIFY COLUMN min_unit decimal(6, 2)    not null DEFAULT 0.00 COMMENT '最少单位';
-- ALTER TABLE t_bicycle_charges_detail MODIFY COLUMN max_unit decimal(6, 2)    not null DEFAULT 0.00 COMMENT '最多单位';



DROP TABLE IF EXISTS t_bicycle_rider;
CREATE TABLE t_bicycle_rider(
    id bigint not null auto_increment,
    rider_no      varchar(20)       not null   default '' comment '骑手编号',
    password        varchar(60)       not null    default '' comment '密码',
    salt            varchar(20)       not null    default '' comment '盐',
    name   varchar(60)   not null    default '' comment '姓名',
    referral_code varchar(20)  comment '推荐码',
    contact  varchar(20)  not null    default '' comment '联系方式',
    avatar  varchar(255)   comment '头像',
    birthday  date     comment '出生日期',
    status  tinyint unsigned default 0  not null comment '状态 0 离线; 1 在线; 2 离职;',
    last_modify_password_time datetime    comment '上一次修改密码时间',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id),
    unique key uk_t_bicycle_rider_no(rider_no),
    unique key uk_t_bicycle_rider_contact(contact)
);
-- ALTER TABLE t_bicycle_rider ADD COLUMN referral_code varchar(20)  comment '推荐码' AFTER name;
-- alter table t_bicycle_rider add index idx_bicycle_rider_referral_code(`referral_code`);

DROP TABLE IF EXISTS t_bicycle_rider_attendance;
CREATE TABLE t_bicycle_rider_attendance(
    id bigint not null auto_increment,
    rider_id  bigint unsigned   not null comment '骑手id',
    online_time  datetime      not null    comment '上线时间',
    offline_time  datetime    default null   comment '下线时间',
    duration     int   not null   default 0  comment '在线时长',
    complete_count  int   not null default 0  comment '完成单数',
    complete_pieces decimal(6, 1)    not null    default 0.0  comment '完成件数',
    share_amount    decimal(10, 2) default 0.00 not null comment '收益金额',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS t_bicycle_order;
CREATE TABLE t_bicycle_order(
    id          bigint      not null        auto_increment,
    order_serial_no varchar(20)       not null   comment '订单流水号',
    delivery_order_serial_no varchar(64) NOT NULL DEFAULT ''   comment '配送单流水号',
    start_point_id  bigint unsigned   not null comment '起点id',
    start_point_name varchar(32) not null comment '起点',
    start_point_detail_name varchar(200) comment '详细起点地址',
    end_point_id    bigint unsigned   not null comment '终点id',
    end_point_name varchar(32) not null comment '终点',
    end_point_detail_name varchar(200) comment '详细终点地址',
    send_time_start datetime      not null    comment '预约配送时间起',
    send_time_end datetime      not null    comment '预约配送时间止',
    sender_id bigint unsigned not null comment '寄件人id',
    sender        varchar(60)   not null   comment '寄件人',
    sender_serial_no      varchar(20)       not null   default '' comment '发货人冷藏编号',
    sender_contact        varchar(20)   not null   comment '寄件人联系方式',
    sender_type   bigint unsigned   not null comment '寄件人类别',
    recipient   varchar(60)   not null   comment '收件人',
    recipient_contact        varchar(20)   not null   comment '收件人联系方式',
    car_no    varchar(100)         default '' comment '车牌',
    remark    varchar(200)      default '' comment '备注',
    other_remark varchar(200)      default '' comment '其他备注',
    pieces  decimal(6, 1)    not null    default 0  comment '件数',
    delivery_charge  decimal(6, 2)    not null    default 0  comment '配送费用',
    other_charge    decimal(6, 2)    not null    default 0  comment '其他费用',
    handling_charge  decimal(6, 2)    not null    default 0 comment '装卸费',
    upstairs_charge  decimal(6, 2)    not null    default 0 comment '上楼费',
    total_charge    decimal(6, 2)    not null    default 0  comment '总费用',
    charges_id  bigint unsigned             not null comment '收费标准id',
    state tinyint(1)        not null   default 0  comment '状态 0：草稿 1：正常',
    order_time     datetime         comment '下单时间',
    origin tinyint(1) not null default 0 comment '来源(0:系统生成;1:导入)',
    status        tinyint(1)        not null   default 0  comment '状态 0：正常 1：取消',
    status_remark  varchar(200)      default '' comment '订单状态备注',
    create_openid   varchar(64)       not null    default '' comment '微信创建用户',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id),
    unique key uk_t_bicycle_order_serial_no(order_serial_no)
);
-- ALTER TABLE t_bicycle_order ADD COLUMN status        tinyint(1)        not null   default 0  comment '状态 0：正常 1：取消' AFTER origin;
-- ALTER TABLE t_bicycle_order ADD COLUMN status_remark  varchar(200)      default '' comment '订单状态备注' AFTER status;
-- ALTER TABLE t_bicycle_order ADD COLUMN handling_charge  decimal(6, 2)    not null    default 0 comment '装卸费' AFTER other_charge;
-- ALTER TABLE t_bicycle_order ADD COLUMN upstairs_charge  decimal(6, 2)    not null    default 0 comment '上楼费' AFTER handling_charge;
-- ALTER TABLE t_bicycle_order DROP COLUMN shares_id;

-- alter table t_bicycle_order add index idx_bicycle_order_start_point_id(start_point_id);
-- ALTER TABLE t_bicycle_order ADD COLUMN delivery_order_serial_no varchar(64) NOT NULL DEFAULT ''   comment '配送单流水号' AFTER order_serial_no;


DROP TABLE IF EXISTS t_bicycle_order_media;
CREATE TABLE t_bicycle_order_media(
    id              bigint unsigned   not null    auto_increment,
    order_id        bigint unsigned   not null    default 0 comment '订单id',
    media_url       varchar(100)      comment '多媒体资料访问连接',
    modified_time  datetime           not null   comment '修改时间',
    primary key (id)
);

DROP TABLE IF EXISTS t_bicycle_order_goods_detail;
CREATE TABLE t_bicycle_order_goods_detail(
    id          bigint      not null        auto_increment,
    order_id    bigint      not null   comment '订单id',
    goods_name    varchar(20)       not null   comment '货物名',
    goods_type   bigint unsigned   not null default 0 comment '货物类型 0其他，1重货，2抛货',
    pieces  decimal(6, 1)    not null    default 0  comment '件数',
    weight  decimal(6, 1)    not null    default 0  comment '重量',
    size  decimal(6, 1)    not null    default 0  comment '体积',
    PRIMARY KEY (id)
);
-- ALTER TABLE t_bicycle_order_goods_detail ADD COLUMN goods_type   bigint unsigned   not null default 0 comment '货物类型 0其他，1重货，2抛货' AFTER goods_name;
-- ALTER TABLE t_bicycle_order_goods_detail ADD COLUMN weight  decimal(6, 1)    not null    default 0  comment '重量' AFTER pieces;
-- ALTER TABLE t_bicycle_order_goods_detail ADD COLUMN size  decimal(6, 1)    not null    default 0  comment '体积' AFTER weight;


drop table if exists t_bicycle_shares;
create table t_bicycle_shares
(
    id               bigint unsigned auto_increment
        primary key comment 'id',
    name             varchar(100)    not null comment '名称',
    rider_shares            decimal(10, 2)           default 0.00 not null comment '分佣比例',
    rider_default_shares            decimal(10, 2)           default 0.00 not null comment '默认比例',
    referral_amount      decimal(10, 2)           default 0.00 not null comment '推荐金额',
    effective_time   datetime        not null comment '生效时间',
    ineffective_time datetime        not null comment '失效时间',
    status           tinyint unsigned         default 0 not null comment '状态',
    is_disabled      tinyint(1)       default 0  not null comment '禁用',
    creator_id       bigint unsigned not null default 0 comment '创建人id',
    creator_name     varchar(30)     not null default '' comment '创建人姓名',
    create_time      datetime        not null comment '创建时间',
    modifier_id      bigint unsigned not null default 0 comment '修改人id',
    modifier_name    varchar(30)     not null default '' comment '修改人姓名',
    modified_time    datetime        not null comment '修改时间',
    constraint uk_name_rates_etime_itime unique (name, rider_shares, effective_time, ineffective_time)
);
-- ALTER TABLE t_bicycle_shares ADD COLUMN rider_default_shares            decimal(10, 2)           default 0.00 not null comment '默认比例' AFTER rider_shares;
-- ALTER TABLE t_bicycle_shares ADD COLUMN referral_amount      decimal(10, 2)           default 0.00 not null comment '推荐金额' AFTER rider_default_shares;


DROP TABLE IF EXISTS t_bicycle_delivery_order;
CREATE TABLE t_bicycle_delivery_order(
    id          bigint      not null        auto_increment,
    delivery_order_serial_no varchar(20)       not null   comment '配送单流水号',
    merged      tinyint(1)       default 0  not null comment '是否合并',
    referral_delivery      tinyint(1)       default 0  not null comment '推荐分配',
    status          smallint   not null    default 1  comment '状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成',
    finish_time     datetime                      comment '完成时间',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id),
    unique key uk_t_bicycle_delivery_order_serial_no(delivery_order_serial_no)
);
-- ALTER TABLE t_bicycle_delivery_order ADD COLUMN referral_delivery      tinyint(1)       default 0  not null comment '推荐分配' AFTER merged;


DROP TABLE IF EXISTS t_bicycle_delivery_order_detail;
CREATE TABLE t_bicycle_delivery_order_detail(
    id          bigint      not null        auto_increment,
    delivery_order_serial_no varchar(20)       not null   comment '配送单流水号',
    delivery_order_serial_no_item varchar(30)  not null default '' comment '子配送单流水号',
    order_serial_no varchar(20)       not null   comment '订单流水号',
    rider_id  bigint unsigned   default 0 comment '骑手id',
    picked_pieces  decimal(6, 1)    not null    default 0  comment '揽货件数',
    status          smallint   not null    default 1  comment '状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成',
    picking_point varchar(40)   comment '揽货点经纬度',
    delivery_point   varchar(40)   comment '送货点经纬度',
    picking_time    datetime                      comment '拣货时间',
    send_time        datetime                      comment '配送时间',
    receiving_time    datetime                      comment '收货时间',
    finish_time     datetime                      comment '完成时间',
    rating  smallint   not null    default 5  comment '配送星级',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id),
    unique key uk_t_bicycle_delivery_order_detail(delivery_order_serial_no, delivery_order_serial_no_item, order_serial_no)
);

-- alter table t_bicycle_delivery_order_detail add index idx_bicycle_delivery_order_detail_orderSerial(order_serial_no);
-- ALTER TABLE t_bicycle_delivery_order_detail ADD COLUMN picking_point varchar(40)   comment '揽货点经纬度' AFTER status;
-- ALTER TABLE t_bicycle_delivery_order_detail ADD COLUMN delivery_point   varchar(40)   comment '送货点经纬度' AFTER picking_point;
-- ALTER TABLE t_bicycle_delivery_order_detail ADD COLUMN delivery_order_serial_no_item varchar(30)  not null default '' comment '子配送单流水号' AFTER delivery_order_serial_no;
-- ALTER TABLE t_bicycle_delivery_order_detail ADD COLUMN rider_id  bigint unsigned   default 0 comment '骑手id' AFTER order_serial_no;
-- ALTER TABLE t_bicycle_delivery_order_detail ADD COLUMN picked_pieces  decimal(6, 1)    not null    default 0  comment '揽货件数' AFTER rider_id;
-- ALTER TABLE t_bicycle_delivery_order_detail DROP INDEX uk_t_bicycle_delivery_order_detail;
-- ALTER TABLE t_bicycle_delivery_order_detail ADD UNIQUE uk_t_bicycle_delivery_order_detail(delivery_order_serial_no, delivery_order_serial_no_item, order_serial_no);

DROP TABLE IF EXISTS t_bicycle_delivery_order_detail_item;
CREATE TABLE t_bicycle_delivery_order_detail_item(
    id          bigint      not null        auto_increment,
    delivery_order_serial_no varchar(20)       not null   comment '配送单流水号',
    delivery_order_serial_no_item varchar(30)  not null default '' comment '子配送单流水号',
    order_serial_no varchar(64)       not null   comment '订单流水号',
    goods_id bigint unsigned   not null    comment '货物id',
    goods_name    varchar(20)       not null   comment '货物名',
    goods_type   bigint unsigned   not null default 0 comment '货物类型 0其他，1重货，2抛货',
    pieces  decimal(6, 1)    not null    default 0  comment '件数',
    weight  decimal(6, 1)    not null    default 0  comment '重量',
    size  decimal(6, 1)    not null    default 0  comment '体积',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);
-- alter table t_bicycle_delivery_order_detail_item add index idx_bicycle_delivery_order_detail_delivery_order_serial_no_item(delivery_order_serial_no_item);
-- alter table t_bicycle_delivery_order_detail_item add index idx_bicycle_delivery_order_detail_delivery_order_serial_no(delivery_order_serial_no);
-- alter table t_bicycle_delivery_order_detail_item add index idx_bicycle_delivery_order_detail_order_serial_no(order_serial_no);
-- ALTER TABLE t_bicycle_delivery_order_detail_item ADD INDEX idx_bicycle_delivery_order_detail_goods_id(goods_id);


DROP TABLE IF EXISTS t_bicycle_delivery_order_job;
CREATE TABLE t_bicycle_delivery_order_job(
    id              bigint unsigned   not null    auto_increment,
    delivery_order_detail_id bigint unsigned   not null    default 0 comment '配送单详细id',
    status          smallint   not null    default 1  comment '状态 1 待揽货；2 到达揽货点；3 已揽货；4 配送中；5 到达送货点； 6 配送完成； 7 确认完成',
    remark          varchar(200)      default '' comment '骑手备注',
    modified_time  datetime           not null   comment '修改时间',
    primary key(id)
);

DROP TABLE IF EXISTS t_bicycle_delivery_order_job_media;
CREATE TABLE t_bicycle_delivery_order_job_media(
    id              bigint unsigned   not null    auto_increment,
    job_id        bigint unsigned   not null    default 0 comment '配送单任务id',
    media_url       varchar(100)      comment '多媒体资料访问连接',
    modified_time  datetime           not null   comment '修改时间',
    primary key (id)
);

drop table if exists t_bicycle_order_income_detail;
create table t_bicycle_order_income_detail (
    id                       bigint unsigned auto_increment   primary key comment 'id',
    order_serial_no          varchar(20)    unique       not null comment '订单流水号',
    delivery_order_serial_no varchar(20)    not null comment '配送单流水号',
    total_amount             decimal(10, 2) default 0.00 not null comment '总金额',
    share_amount             decimal(10, 2) default 0.00 not null comment '分润金额',
    profit_amount            decimal(10, 2) default 0.00 not null comment '利润金额',
    modified_time  datetime           not null   comment '修改时间',
    finish_time     datetime                      comment '完成时间'
);

DROP TABLE IF EXISTS t_bicycle_bill;
CREATE TABLE t_bicycle_bill(
    id              bigint unsigned    not null   auto_increment,
    bill_date      date              not null   comment '账单日',
    bill_year       smallint unsigned  not null   comment '年份',
    bill_month      tinyint unsigned   not null   comment '月份',
    order_serial_no          varchar(20)         not null comment '订单流水号',
    delivery_order_serial_no varchar(20)         not null comment '配送单流水号',
    merchant_id bigint unsigned NOT NULL DEFAULT '0' COMMENT '商户id',
    amount          decimal(10, 2)     not null   default 0  comment '金额',
    is_disabled     tinyint(1)         not null   default 0  comment '禁用',
    creator_id      bigint unsigned    not null   default 0  comment '创建人id',
    creator_name    varchar(30)        not null   default '' comment '创建人姓名',
    create_time     datetime           not null   comment '创建时间',
    modifier_id     bigint unsigned    not null   default 0  comment '修改人id',
    modifier_name   varchar(30)        not null   default '' comment '修改人姓名',
    modified_time   datetime           not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE t_bicycle_bill ADD INDEX idx_t_bicycle_bill_year_month(bill_year, bill_month);
-- ALTER TABLE t_bicycle_bill ADD INDEX idx_t_bicycle_bill_delivery_order_serial_no(delivery_order_serial_no);
-- ALTER TABLE t_bicycle_bill ADD INDEX idx_t_bicycle_bill_order_serial_no(order_serial_no);
-- ALTER TABLE t_bicycle_bill ADD INDEX idx_t_bicycle_bill_merchant(merchant_id);
alter table t_bicycle_bill add settlement_id bigint unsigned not null default 0 comment '结算单id' after merchant_id;


drop table if exists t_bicycle_bill_detail;
create table t_bicycle_bill_detail(
    id              bigint unsigned    not null   auto_increment,
    bill_id bigint unsigned default 0                  not null comment '账单id',
    fee_id    bigint unsigned default 0                  not null comment '费项id',
    fee_name       varchar(50)       not null   default '' comment '费项名',
    amount    decimal(10, 2)  default 0.00               not null comment '金额',
    modified_time   datetime           not null   comment '修改时间',
    primary key (id)
);

/* 三轮车客户日报表 */
DROP TABLE IF EXISTS t_bicycle_order_day_report;
CREATE TABLE t_bicycle_order_day_report(
    id             bigint unsigned   not null   auto_increment,
    report_date      date              not null   comment '报表日期',
    merchant_id bigint unsigned NOT NULL DEFAULT '0' COMMENT '商户id',
    total_pieces    decimal(6, 1)    not null    default 0  comment '总件数',
    total_count     int  default 0  not null comment '总单数',
    delivery_charge    decimal(10, 2)   not null   default 0  comment '配送费用',
    other_charge    decimal(10, 2)   not null   default 0  comment '其他费用',
    handling_charge  decimal(6, 2)    not null    default 0 comment '装卸费',
    upstairs_charge  decimal(6, 2)    not null    default 0 comment '上楼费',
    total_charge    decimal(10, 2)   not null   default 0  comment '合计',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE t_bicycle_day_report ADD INDEX idx_t_bicycle_day_report_merchant(`merchant_id`);
-- ALTER TABLE t_bicycle_day_report ADD INDEX idx_t_bicycle_day_report_date(`report_date`);
-- ALTER TABLE t_bicycle_order_day_report ADD COLUMN handling_charge  decimal(6, 2)    not null    default 0 comment '装卸费' AFTER other_charge;
-- ALTER TABLE t_bicycle_order_day_report ADD COLUMN upstairs_charge  decimal(6, 2)    not null    default 0 comment '上楼费' AFTER handling_charge;

/* 三轮车客户月报表 */
DROP TABLE IF EXISTS t_bicycle_order_month_report;
CREATE TABLE t_bicycle_order_month_report(
    id             bigint unsigned   not null   auto_increment,
    report_year_month varchar(20) NOT NULL COMMENT '月报年月',
    report_year smallint unsigned NOT NULL COMMENT '月报年份',
    report_month tinyint unsigned NOT NULL COMMENT '月报月份',
    merchant_id bigint unsigned NOT NULL DEFAULT '0' COMMENT '商户id',
    total_pieces    decimal(6, 1)    not null    default 0  comment '总件数',
    total_count     int  default 0  not null comment '总单数',
    delivery_charge    decimal(10, 2)   not null   default 0  comment '配送费用',
    other_charge    decimal(10, 2)   not null   default 0  comment '其他费用',
    handling_charge  decimal(6, 2)    not null    default 0 comment '装卸费',
    upstairs_charge  decimal(6, 2)    not null    default 0 comment '上楼费',
    total_charge    decimal(10, 2)   not null   default 0  comment '合计',
    is_disabled    tinyint(1)        not null   default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);
-- ALTER TABLE t_bicycle_month_report ADD INDEX idx_t_bicycle_month_report_year_month(`report_year`, `report_month`);
-- ALTER TABLE t_bicycle_month_report ADD INDEX idx_t_bicycle_month_report_merchant(`merchant_id`);
-- ALTER TABLE t_bicycle_order_month_report ADD COLUMN handling_charge  decimal(6, 2)    not null    default 0 comment '装卸费' AFTER other_charge;
-- ALTER TABLE t_bicycle_order_month_report ADD COLUMN upstairs_charge  decimal(6, 2)    not null    default 0 comment '上楼费' AFTER handling_charge;

drop table if exists t_bicycle_payoff;
create table t_bicycle_payoff(
     id                       bigint unsigned auto_increment primary key comment 'id',
     bill_year                smallint unsigned                          not null comment '账单年份',
     bill_month               tinyint unsigned                           not null comment '账单月份',
     order_serial_no          varchar(20)                                null comment '订单流水号',
     delivery_order_serial_no varchar(20)                                not null comment '配送单流水号',
     payee_name               varchar(60)      default ''                not null comment '收款人姓名',
     payee_type               tinyint unsigned default 0                 not null comment '收款人类型',
     payee_contact            varchar(20)      default ''                not null comment '收款人联系方式',
     rider_id  bigint unsigned   not null default 0 comment '骑手id',
     pieces         decimal(10, 2) default 0.00    not null comment '件数',
     share_amount             decimal(10, 2)   default 0.00              not null comment '佣金',
     referral_amount             decimal(10, 2)   default 0.00              not null comment '推荐费',
     referral     tinyint(1)       default 0  not null comment '是否推荐费',
     delivery_time   bigint unsigned  default 0  not null comment '交货时间',
     status                   tinyint unsigned default 0                 not null comment '状态',
     payway          smallint unsigned  not null   default 0  comment '支付方式',
     paid_time                datetime                                   null comment '支付时间',
     is_disabled              tinyint(1)                                 not null default 0 comment '禁用',
     creator_id               bigint unsigned  default 0                 not null comment '创建人id',
     creator_name             varchar(30)      default ''                not null comment '创建人姓名',
     create_time              datetime                                   not null comment '创建时间',
     modifier_id              bigint unsigned  default 0                 not null comment '修改人id',
     modifier_name            varchar(30)      default ''                not null comment '修改人姓名',
     modified_time            datetime                                   not null comment '修改时间'
);
-- alter table t_bicycle_payoff add index index_bill_y_m (bill_year, bill_month);
-- ALTER TABLE t_bicycle_payoff ADD COLUMN pieces         decimal(10, 2) default 0.00    not null comment '件数' AFTER rider_id;
-- ALTER TABLE t_bicycle_payoff ADD COLUMN share_amount             decimal(10, 2)   default 0.00              not null comment '佣金' AFTER amount;
-- ALTER TABLE t_bicycle_payoff DROP COLUMN amount;
-- ALTER TABLE t_bicycle_payoff ADD COLUMN referral_amount             decimal(10, 2)   default 0.00              not null comment '推荐费' AFTER share_amount;
-- ALTER TABLE t_bicycle_payoff ADD COLUMN referral     tinyint(1)       default 0  not null comment '是否推荐费' AFTER referral_amount;


drop table if exists t_bicycle_payoff_detail;
create table t_bicycle_payoff_detail(
    id        bigint unsigned auto_increment primary key comment 'id',
    payoff_id bigint unsigned default 0                  not null comment '应付账单id',
    fee_id    bigint unsigned default 0                  not null comment '费项id',
    fee_name       varchar(50)       not null   default '' comment '费项名',
    modified_time   datetime           not null   comment '修改时间'
);
-- ALTER TABLE t_bicycle_payoff_detail DROP COLUMN amount;

drop table if exists t_bicycle_payoff_day_report;
create table t_bicycle_payoff_day_report
(
    id             bigint unsigned auto_increment primary key comment 'id',
    year_month_day varchar(10)                  not null comment '年月日',
    year           smallint unsigned            not null comment '年',
    month          tinyint unsigned             not null comment '月',
    day            tinyint unsigned             null comment '日',
    payee_id       bigint unsigned              null comment '收款人id',
    payee_name     varchar(30)                  null comment '收款人姓名',
    order_number   bigint unsigned default 0    not null comment '订单数',
    pieces         bigint unsigned default 0    not null comment '件数',
    referral_amount             decimal(10, 2)   default 0.00              not null comment '推荐费',
    referral_count             bigint unsigned default 0    not null comment '推荐数量',
    total_amount   decimal(10, 2)  default 0.00 not null comment '总金额',
    avg_delivery_time   bigint unsigned  default 0  not null comment '平均交货时间',
    creator_id     bigint unsigned default 0    not null comment '创建人id',
    creator_name   varchar(30)     default ''   not null comment '创建人姓名',
    create_time    datetime                     not null comment '创建时间',
    modifier_id    bigint unsigned default 0    not null comment '修改人id',
    modifier_name  varchar(30)     default ''   not null comment '修改人姓名',
    modified_time  datetime                     not null comment '修改时间'
);
-- ALTER TABLE t_bicycle_payoff_day_report ADD COLUMN referral_amount             decimal(10, 2)   default 0.00              not null comment '推荐费' AFTER pieces;
-- ALTER TABLE t_bicycle_payoff_day_report ADD COLUMN referral_count             bigint unsigned default 0    not null comment '推荐数量' AFTER referral_amount;

drop table if exists t_bicycle_payoff_month_report;
create table t_bicycle_payoff_month_report
(
    id            bigint unsigned auto_increment primary key comment 'id',
    `year_month`  varchar(10)                  not null comment '年月',
    year          smallint unsigned            not null comment '年',
    month         tinyint unsigned             not null comment '月',
    day           tinyint unsigned             null comment '日',
    payee_id      bigint unsigned              null comment '收款人id',
    payee_name    varchar(30)                  null comment '收款人姓名',
    order_number   bigint unsigned default 0    not null comment '订单数',
    pieces         bigint unsigned default 0    not null comment '件数',
    total_amount  decimal(10, 2)  default 0.00 not null comment '总金额',
    creator_id    bigint unsigned default 0    not null comment '创建人id',
    creator_name  varchar(30)     default ''   not null comment '创建人姓名',
    create_time   datetime                     not null comment '创建时间',
    modifier_id   bigint unsigned default 0    not null comment '修改人id',
    modifier_name varchar(30)     default ''   not null comment '修改人姓名',
    modified_time datetime                     not null comment '修改时间'
);

DROP TABLE IF EXISTS t_bicycle_operate_analysis;
CREATE TABLE t_bicycle_operate_analysis(
    id          bigint      not null        auto_increment,
    operate_analysis_date      date       not null   comment '日期',
    total_amount   decimal(10, 2)  default 0.00  not null comment '总金额',
    total_pieces   decimal(8, 1)  default 0.0  not null comment '总件数',
    avg_unit_price   decimal(8, 2)  default 0.00  not null comment '平均单价',
    total_count   int  default 0  not null comment '总单数',
    avg_second   int  default 0  not null comment '平均每单用时',
    best_sender_id bigint unsigned not null comment '寄件人id',
    best_customer_name    varchar(60)  not null default ''  comment '客户名',
    best_rider_id  bigint unsigned   default 0 comment '最佳骑手id',
    best_rider_name    varchar(60) not null default ''   comment '最佳骑手',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS t_bicycle_setting;
CREATE TABLE t_bicycle_setting(
    id          bigint      not null        auto_increment,
    name             varchar(100)    not null comment '配置名',
    alias          varchar(30)       not null   default '' comment '别名',
    enable        tinyint(1)       not null default 0 comment '是否启用',
    interval_minute  int unsigned    not null   default 1  comment '单位间隔，单位：分钟',
    is_disabled    tinyint(1)   not null default 0 comment '禁用',
    creator_id    bigint unsigned default 0    not null comment '创建人id',
    creator_name  varchar(30)     default ''   not null comment '创建人姓名',
    create_time   datetime                     not null comment '创建时间',
    modifier_id   bigint unsigned default 0    not null comment '修改人id',
    modifier_name varchar(30)     default ''   not null comment '修改人姓名',
    modified_time datetime                     not null comment '修改时间',
    unique key uk_t_bicycle_setting(alias),
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS t_bicycle_manager;
CREATE TABLE t_bicycle_manager(
    id bigint not null auto_increment,
    admin_user_id  bigint unsigned   not null comment '管理员id',
    status  tinyint unsigned default 1  not null comment '状态 0 离线; 1 在线;',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);


/* 三轮车客户账单结算 */
drop table if exists t_bicycle_bill_settlement;
create table t_bicycle_bill_settlement
(
    id              bigint unsigned     not null auto_increment,
    bill_year_month varchar(10)         not null comment '账单年月',
    bill_year       smallint unsigned   not null comment '年份',
    bill_month      tinyint unsigned    not null comment '月份',
    merchant_id     bigint unsigned     not null default 0 comment '商户id',
    merchant_name   varchar(30)         not null comment '商户名',
    amount          decimal(10, 2)      not null default 0 comment '金额',
    settle_period   tinyint(1)                   default 0 not null comment '结算周期',
    paid_amount     decimal(10, 2)               default 0.00 not null comment '支付金额',
    paid_time       datetime comment '支付时间',
    remote_order_id bigint unsigned     not null default 0 comment '订单id',
    send            tinyint(1) unsigned not null default 0 comment '下发状态：0初始化；1已下发',
    send_time       datetime comment '下发时间',
    toll_man_id     bigint unsigned              default '0' not null comment '收费员',
    status          tinyint unsigned    not null default 0 comment '账单状态：0初始化；1已支付',
    is_disabled     tinyint(1)          not null default 0 comment '禁用',
    creator_id      bigint unsigned     not null default 0 comment '创建人id',
    creator_name    varchar(30)         not null default '' comment '创建人姓名',
    create_time     datetime            not null comment '创建时间',
    modifier_id     bigint unsigned     not null default 0 comment '修改人id',
    modifier_name   varchar(30)         not null default '' comment '修改人姓名',
    modified_time   datetime            not null comment '修改时间',
    primary key (id)
);
-- alter table t_bicycle_bill_settlement add index idx_bicycle_bill_settlement_bill(bill_id);
-- alter table t_bicycle_bill_settlement add index idx_bicycle_bill_settlement_year_mont_date(bill_year,bill_month,bill_date);
-- alter table t_bicycle_bill_settlement add index idx_bicycle_bill_settlement_year_month(bill_year,bill_month);

alter table t_bicycle_bill_settlement
    add pay_way smallint unsigned not null default 0 comment '支付方式' after paid_amount;


drop table if exists t_logistic_order_product;
create table t_logistic_order_product
(
    id              bigint unsigned     not null auto_increment,
    ship_date       date                not null comment '发货日期',
    order_no        varchar(30)         not null comment '订单编号',
    merchant        varchar(30)         not null comment '商户',
    `member`          varchar(30)         comment '会员',
    product         varchar(100)        not null comment '货品',
    product_type1   varchar(20)         comment '商品总类',
    product_type2   varchar(20)         comment '商品分类',
    product_price   decimal(8, 2)       not null default 0 comment '商品单价',
    product_count   decimal(8, 2)       not null default 0 comment '商品件数',
    product_amount  decimal(8, 2)       not null default 0 comment '商品总价',
    product_weight  decimal(8, 2)       not null default 0 comment '商品总重量',
    product_size    decimal(10, 4)       not null default 0 comment '商品总体积',
    product_deduction decimal(8, 2)      not null default 0 comment '商品优惠金额',
    product_full_reduction decimal(8, 2) not null default 0 comment '商品满减额',
    total_amount    decimal(8, 2)       not null default 0 comment '合计金额',
    remark          varchar(200)        comment '备注',
    is_disabled     tinyint(1)          not null default 0 comment '禁用',
    creator_id      bigint unsigned     not null default 0 comment '创建人id',
    creator_name    varchar(30)         not null default '' comment '创建人姓名',
    create_time     datetime            not null comment '创建时间',
    modifier_id     bigint unsigned     not null default 0 comment '修改人id',
    modifier_name   varchar(30)         not null default '' comment '修改人姓名',
    modified_time   datetime            not null comment '修改时间',
    primary key(id)
);
-- alter table t_logistic_order_product add index idx_logistic_order_product_orderNo(order_no);
-- alter table t_logistic_order_product add index idx_logistic_order_product_shipDate(ship_date);

drop table if exists t_logistic_order_ship;
create table t_logistic_order_ship
(
    id              bigint unsigned     not null auto_increment,
    ship_date       date                not null comment '发货日期',
    order_product_id   bigint unsigned     not null comment '物流配送单id',
    receiver        varchar(50)         comment '收货人',
    receiver_contact varchar(50)        comment '收货人联系方式',
    destination     varchar(100)        comment '送货地址',
    market          varchar(30)         not null comment '市场',
    area            varchar(30)         not null comment '区域',
    vehicle_no      varchar(10)         not null comment '车牌',
    driver          varchar(10)         comment '司机',
    ship_count      decimal(8, 2)       not null default 0 comment '实发件数',
    ship_diversity  decimal(8, 2)       not null default 0 comment '实发差异',
    piece_weight    decimal(8, 2)       not null default 0 comment '件重',
    ship_weight     decimal(8, 2)       not null default 0 comment '实发重量',
    piece_size      decimal(10, 4)       not null default 0 comment '件体积',
    ship_size       decimal(10, 4)       not null default 0 comment '实发体积',
    ship_price      decimal(8, 2)       not null default 0 comment '运费单价',
    ship_multiplying decimal(8, 2)      not null default 1 comment '运费倍率',
    ship_amount     decimal(8, 2)       not null default 0 comment '物流费',
    ship_discount   decimal(8, 2)       not null default 0 comment '折扣率',
    sort_amount     decimal(8, 2)       not null default 0 comment '分拣费',
    is_sort_charge  tinyint(1)          not null default 0 comment '是否收取分拣费',
    total_amount    decimal(8, 2)       not null default 0 comment '实际运费',
    is_disabled     tinyint(1)          not null default 0 comment '禁用',
    creator_id      bigint unsigned     not null default 0 comment '创建人id',
    creator_name    varchar(30)         not null default '' comment '创建人姓名',
    create_time     datetime            not null comment '创建时间',
    modifier_id     bigint unsigned     not null default 0 comment '修改人id',
    modifier_name   varchar(30)         not null default '' comment '修改人姓名',
    modified_time   datetime            not null comment '修改时间',
    primary key(id),
    unique key uk_logistic_order_ship(order_product_id)
);
-- alter table t_logistic_order_ship add index idx_logistic_order_ship_date(ship_date);
-- alter table t_logistic_order_ship add index idx_logistic_order_vehicle_no(vehicle_no);

drop table if exists t_logistic_order_bill;
create table t_logistic_order_bill(
    id              bigint unsigned     not null auto_increment,
    order_product_id   bigint unsigned     not null default 0 comment '物流配送单id',
    ship_id         bigint unsigned     not null default 0 comment '物流单id',
    order_amount    decimal(8, 2)       not null default 0 comment '订单金额',
    product_paid    decimal(8, 2)    not null default 0 comment '已收货款金额',
    is_product_paid_manual tinyint(1) unsigned not null default 0 comment '手工调整已收货款金额',
    product_paid_man    varchar(20)      comment '预付款人',
    product_diversity   decimal(8, 2)    not null default 0 comment '已收货款差异金额',
    product_owe     decimal(8, 2)    not null default 0 comment '已收货款欠款金额',
    ship_amount     decimal(8, 2)       not null default 0 comment '运费',
    total_amount    decimal(8, 2)       not null default 0 comment '应付合计',
    is_disabled     tinyint(1)          not null default 0 comment '禁用',
    creator_id      bigint unsigned     not null default 0 comment '创建人id',
    creator_name    varchar(30)         not null default '' comment '创建人姓名',
    create_time     datetime            not null comment '创建时间',
    modifier_id     bigint unsigned     not null default 0 comment '修改人id',
    modifier_name   varchar(30)         not null default '' comment '修改人姓名',
    modified_time   datetime            not null comment '修改时间',
    primary key(id),
    unique key uk_logistic_order_bill(order_product_id, ship_id)
);

drop table if exists t_logistic_payoff;
create table t_logistic_payoff(
    id              bigint unsigned     not null auto_increment,
    bill_date       date                not null comment '账单日期',
    merchant        varchar(30)         not null comment '商户',
    product_count   decimal(8, 2)       not null default 0 comment '商品件数',
    product_amount  decimal(8, 2)       not null default 0 comment '订单总价',
    product_full_reduction decimal(8, 2) not null default 0 comment '满减额',
    product_to_paid decimal(8, 2)       not null default 0 comment '应收金额',
    product_paid    decimal(8, 2)       not null default 0 comment '已收金额',
    product_owe     decimal(8, 2)       not null default 0 comment '欠款金额',
    product_deduction decimal(8, 2)     not null default 0 comment '其他减款',
    product_diversity decimal(8, 2)     not null default 0 comment '差异金额',
    ship_amount     decimal(8, 2)       not null default 0 comment '物流费',
    total_amount    decimal(8, 2)       not null default 0 comment '物流费',
    remark          varchar(100)        comment '备注',
    is_disabled     tinyint(1)          not null default 0 comment '禁用',
    creator_id      bigint unsigned     not null default 0 comment '创建人id',
    creator_name    varchar(30)         not null default '' comment '创建人姓名',
    create_time     datetime            not null comment '创建时间',
    modifier_id     bigint unsigned     not null default 0 comment '修改人id',
    modifier_name   varchar(30)         not null default '' comment '修改人姓名',
    modified_time   datetime            not null comment '修改时间',
    primary key(id),
    unique key uk_logistic_payoff(bill_date, merchant)
);



DROP TABLE IF EXISTS t_logistic_statistics_day_report;
CREATE TABLE t_logistic_statistics_day_report(
    id bigint not null auto_increment,
    report_date  date NOT NULL COMMENT '日期',
    operations_department   varchar(30)       not null    default '' comment '运营部门',
    shipper   varchar(30)       not null    default '' comment '发货人',
    logistics_no   varchar(30)       not null    default '' comment '物流单号',
    income_type   tinyint(1) NOT NULL DEFAULT '0' COMMENT '收入类别',
    driver_name varchar(30)         not null comment '司机',
    car_no  varchar(30)         not null comment '车牌号',
    chartered_bus  tinyint(1)        not null    default 0  comment '是否包车',
    departure_station   varchar(30)       not null    default '' comment '始发站',
    destination_station   varchar(30)       not null    default '' comment '目的站',
    pieces  decimal(6, 2)    not null    default 0  comment '件数',
    loading_weight  decimal(8, 2)    not null    default 0  comment '装载重量',
    storage_weight  decimal(8, 2)    not null    default 0  comment '入库重量',
    un_stocked_weight  decimal(8, 2)    not null    default 0  comment '未入库重量',
    volume  decimal(8, 2)    not null    default 0  comment '体积',
    freight_income_amount  decimal(8, 2)    not null    default 0  comment '运费收入金额',
    payment_time     datetime          not null    comment '收款日期',
    actual_freight_amount  decimal(8, 2)    not null    default 0  comment '实收运费金额',
    warehousing_no   varchar(30)       not null    default '' comment '进仓单号',
    frozen_goods_discounts  decimal(8, 2)    not null    default 0  comment '进口冻品优惠',
    unpaid_amount  decimal(8, 2)    not null    default 0  comment '未收款金额',
    remark    varchar(200)      default '' comment '备注',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);
-- ALTER TABLE t_logistic_statistics_day_report ADD COLUMN chartered_bus  tinyint(1)        not null    default 0  comment '是否包车' AFTER car_no;
-- ALTER TABLE t_logistic_statistics_day_report MODIFY payment_time datetime comment '收款日期';
-- ALTER TABLE t_logistic_statistics_day_report ADD COLUMN customer_name varchar(50)     not null    default '' comment '客户名' AFTER report_date;
-- ALTER TABLE t_logistic_statistics_day_report DROP COLUMN customer_name;

drop table if exists t_dict_logistic;
create table t_dict_logistic
(
    id            bigint primary key auto_increment,
    `key`         varchar(50)     default '' not null comment 'key',
    name          varchar(30)     default '' not null comment '名称',
    category      tinyint(1)      default 0  not null comment '类型',
    attr1         varchar(50)                null comment '保留字段1',
    attr2         varchar(50)                null comment '保留字段2',
    sort          bigint unsigned default 0  not null comment '排序',
    is_disabled   tinyint(1)      default 0  not null comment '禁用',
    creator_id    bigint unsigned default 0  not null comment '创建人id',
    creator_name  varchar(30)     default '' not null comment '创建人姓名',
    create_time   datetime                   not null comment '创建时间',
    modifier_id   bigint unsigned default 0  not null comment '修改人id',
    modifier_name varchar(30)     default '' not null comment '修改人姓名',
    modified_time datetime                   not null comment '修改时间'
);

drop table if exists t_logistic_loader_settlement;
create table t_logistic_loader_settlement
(
    id   bigint primary key auto_increment,
    `date`                date                         not null comment '日期',
    freight_type          tinyint(1)      default 0    not null comment '装卸类型',
    customer_id           bigint unsigned default 0    not null comment '客户id',
    goods_type            tinyint(1)      default 0    not null comment '商品类型',
    transport_avg         decimal(10, 2)  default 0.00 not null comment '平均搬运量',
    transport_total       decimal(10, 2)  default 0.00 not null comment '总搬运量',
    participation_number  bigint unsigned default 0    not null comment '参与人数',
    freight_unit_price    decimal(10, 2)  default 0.00 not null comment '装卸单价',
    freight_total_amount  decimal(10, 2)  default 0.00 not null comment '装卸总金额',
    sorting_fee           decimal(10, 2)  default 0.00 not null comment '分拣费',
    pieces         bigint unsigned default 0    not null comment '件数',
    car_no                varchar(20)     default ''   not null comment '车牌',
    remark    varchar(100)      default '' comment '备注',
    driving_hours         decimal(10, 2)  default 0.00 not null comment '出车工时',
    meal_allowance_amount decimal(10, 2)  default 0.00 not null comment '餐补',
    subtotal_amount       decimal(10, 2)  default 0.00 not null comment '小计',
    total_amount          decimal(10, 2)  default 0.00 not null comment '合计',
    is_disabled           tinyint(1)      default 0    not null comment '禁用',
    creator_id            bigint unsigned default 0    not null comment '创建人id',
    creator_name          varchar(30)     default ''   not null comment '创建人姓名',
    create_time           datetime                     not null comment '创建时间',
    modifier_id           bigint unsigned default 0    not null comment '修改人id',
    modifier_name         varchar(30)     default ''   not null comment '修改人姓名',
    modified_time         datetime                     not null comment '修改时间'
);
-- ALTER TABLE t_logistic_loader_settlement ADD COLUMN remark    varchar(100)      default '' comment '备注' AFTER car_no;


drop table if exists t_logistic_loader_income;
create table t_logistic_loader_income
(
    id            bigint primary key auto_increment,
    date          date                         not null comment '日期',
    loader_number varchar(20) comment '搬运工编号',
    loader_name   varchar(60)     default ''   not null comment '搬运工姓名',
    settlement_id bigint unsigned default 0    not null comment '结算id',
    amount        decimal(10, 2)  default 0.00 not null comment '金额',
    is_disabled   tinyint(1)      default 0    not null comment '禁用',
    creator_id    bigint unsigned default 0    not null comment '创建人id',
    creator_name  varchar(30)     default ''   not null comment '创建人姓名',
    create_time   datetime                     not null comment '创建时间',
    modifier_id   bigint unsigned default 0    not null comment '修改人id',
    modifier_name varchar(30)     default ''   not null comment '修改人姓名',
    modified_time datetime                     not null comment '修改时间'
);

drop table if exists t_logistic_loader_income_day_report;
create table t_logistic_loader_income_day_report
(
    date          date                        not null comment '日期',
    loader_number varchar(20) comment '搬运工编号',
    loader_name   varchar(60)    default ''   not null comment '搬运工姓名',
    amount        decimal(10, 2) default 0.00 not null comment '金额',
    primary key (date, loader_number)
);

-- alter table t_logistic_loader_settlement add index idx_logistic_loader_date (`date`);

drop table if exists t_logistics_daily_order_delivery;
create table t_logistics_daily_order_delivery
(
    id                        bigint unsigned              not null auto_increment primary key ,
    order_delivery_no         varchar(20)     default ''   not null comment '配送单号',
    order_delivery_car_no     varchar(20)     default ''   not null comment '配送车牌',
    order_pieces              bigint unsigned default 0    not null comment '订单件数',
    order_total_kilograms     decimal(10, 2)  default 0.00 not null comment '订单总千克',
    order_time                date                         not null comment '订单时间',
    order_person              varchar(30)     default ''   not null comment '下单人',
    send_time                 date                         not null comment '送货时间',
    type                      tinyint(1)      default 0    not null comment '类型',
    pieces                    bigint unsigned default 0    not null comment '件数',
    kilograms                 decimal(10, 2)  default 0.00 not null comment '千克',
    freight_unit_price        decimal(10,2) default 0.00 not null  comment '运费单价',
    receivable_freight_charge decimal(10, 2)  default 0.00 not null comment '应收运费',
    received_freight_charge   decimal(10, 2)  default 0.00 not null comment '实收运费',
    discrepancy_amount        decimal(10, 2)  default 0.00 not null comment '差额',
    remark                    varchar(200)                 null comment '备注',
    is_disabled               tinyint(1)                   not null default 0 comment '禁用',
    creator_id                bigint unsigned              not null default 0 comment '创建人id',
    creator_name              varchar(30)                  not null default '' comment '创建人姓名',
    create_time               datetime                     not null comment '创建时间',
    modifier_id               bigint unsigned              not null default 0 comment '修改人id',
    modifier_name             varchar(30)                  not null default '' comment '修改人姓名',
    modified_time             datetime                     not null comment '修改时间'
);

drop table if exists t_logistics_freight;
create table t_logistics_freight
(
    id                         bigint unsigned              not null auto_increment primary key,
    operations_department      varchar(30)     default ''   not null comment '运营部门',
    sender_customer_name       varchar(60)     default ''   not null comment '寄件客户名',
    sender_customer_contact    varchar(20)                  not null default '' comment '寄件客户联系方式',
    sender_pieces              bigint unsigned default 0    not null comment '寄件数',
    sender_freight_charge      decimal(10, 2)  default 0.00 not null comment '寄件运费',
    sender_settlement_type     tinyint(1)      default 0    not null comment '寄件结算类型(1:到付;2:现付)',
    receiving_date             date                         null comment '收货日期',
    receiving_no               varchar(20)     default ''   not null comment '收货单号',
    receiving_customer_name    varchar(60)     default ''   not null comment '收货客户名',
    receiving_customer_address varchar(60)     default ''   not null comment '收货客户地址',
    receiving_customer_contact varchar(20)     default ''   not null comment '收货客户联系方式',
    transfer_logistics_company varchar(100)    default ''   not null comment '中转物流公司',
    transfer_logistics_no      varchar(20)     default ''   not null comment '中转物流单号',
    transfer_charge            decimal(10, 2)  default 0.00 not null comment '中转运费',
    profit_amount              decimal(10, 2)  default 0.00 not null comment '利润',
    remark                     varchar(200)                 null comment '备注',
    paid_amount     decimal(10, 2)               default 0.00 not null comment '支付金额',
    paid_time       datetime comment '支付时间',
    remote_order_id bigint unsigned     not null default 0 comment '订单id',
    status          tinyint unsigned    not null default 0 comment '账单状态：0初始化；1已支付',
    paid_remark      varchar(200)                 null comment '支付备注',
    toll_man_id     bigint unsigned              default '0' not null comment '收费员',
    is_disabled                tinyint(1)                   not null default 0 comment '禁用',
    creator_id                 bigint unsigned              not null default 0 comment '创建人id',
    creator_name               varchar(30)                  not null default '' comment '创建人姓名',
    create_time                datetime                     not null comment '创建时间',
    modifier_id                bigint unsigned              not null default 0 comment '修改人id',
    modifier_name              varchar(30)                  not null default '' comment '修改人姓名',
    modified_time              datetime                     not null comment '修改时间'
);
-- ALTER TABLE t_logistics_freight ADD COLUMN paid_amount     decimal(10, 2)               default 0.00 not null comment '支付金额' AFTER remark;
-- ALTER TABLE t_logistics_freight ADD COLUMN paid_time       datetime comment '支付时间' AFTER paid_amount;
-- ALTER TABLE t_logistics_freight ADD COLUMN remote_order_id bigint unsigned     not null default 0 comment '订单id' AFTER paid_time;
-- ALTER TABLE t_logistics_freight ADD COLUMN status          tinyint unsigned    not null default 0 comment '账单状态：0初始化；1已支付' AFTER remote_order_id;
-- ALTER TABLE t_logistics_freight ADD COLUMN paid_remark      varchar(200)                 null comment '支付备注' AFTER status;
-- ALTER TABLE t_logistics_freight ADD COLUMN toll_man_id     bigint unsigned              default '0' not null comment '收费员' AFTER paid_remark;


drop table if exists t_bicycle_charges_scheduled_task;
create table t_bicycle_charges_scheduled_task
(
    id            bigint auto_increment
        primary key,
    task_name     varchar(100)    default ''  not null comment '任务名',
    task_count    bigint unsigned default 0   not null comment '任务次数',
    model         tinyint(1)      default 0   not null comment '模式(0:增量更新;1:全量更新)',
    charges_id    bigint unsigned default 0   not null comment '费用标准id',
    start_time   datetime                     not null comment '开始时间',
    is_disabled   tinyint(1)      default 0   not null comment '禁用',
    creator_id    bigint unsigned default '0' not null comment '创建人id',
    creator_name  varchar(30)     default ''  not null comment '创建人姓名',
    create_time   datetime                    not null comment '创建时间',
    modifier_id   bigint unsigned default '0' not null comment '修改人id',
    modifier_name varchar(30)     default ''  not null comment '修改人姓名',
    modified_time datetime                    not null comment '修改时间'

);
-- alter table t_bicycle_charges_scheduled_task add index idx_charges_id (charges_id);

drop table if exists t_bicycle_charges_scheduled_task_merchant;
create table t_bicycle_charges_scheduled_task_merchant
(
    task_id     bigint unsigned not null comment '任务id',
    merchant_id bigint unsigned not null comment '商户id',
    primary key (task_id, merchant_id)
);

drop table if exists t_bicycle_payoff_charges;
create table t_bicycle_payoff_charges
(
    id            bigint unsigned auto_increment primary key,
    basic_amount  decimal(10, 4)         not null comment '基础金额',
    basic_pieces  decimal(10, 4)         not null comment '基础件数',
    excess_price  decimal(10, 4)         not null comment '超量价格',
    creator_id    bigint unsigned        not null comment '创建人id',
    creator_name  varchar(30) default '' not null comment '创建人姓名',
    create_time   datetime               not null comment '创建时间',
    modifier_id   bigint unsigned        not null comment '修改人id',
    modifier_name varchar(30) default '' not null comment '修改人姓名',
    modified_time datetime               not null comment '修改时间',
    is_disabled   tinyint(1)  default 0  not null comment '禁用（0：未禁用，1：已禁用）'
);

DROP TABLE IF EXISTS px_unloading_dict;
CREATE TABLE px_unloading_dict(
    id          bigint      not null        auto_increment,
    category  tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型 0冻品，1干货，2车型，3其他，4客户，5加急费',
    name varchar(20)       not null   comment '名称',
    unit     varchar(32) not null default ''  COMMENT '单位',
    `attr1` varchar(50) DEFAULT '' COMMENT '保留字段1',
    `attr2` varchar(50) DEFAULT '' COMMENT '保留字段2',
    `attr3` varchar(50) DEFAULT '' COMMENT '保留字段3',
    `attr4` varchar(50) DEFAULT '' COMMENT '保留字段4',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);
-- ALTER TABLE px_unloading_dict ADD   `attr1` varchar(50) DEFAULT '' COMMENT '保留字段1' AFTER unit;
-- ALTER TABLE px_unloading_dict ADD   `attr2` varchar(50) DEFAULT '' COMMENT '保留字段2' AFTER attr1;
-- ALTER TABLE px_unloading_dict ADD   `attr3` varchar(50) DEFAULT '' COMMENT '保留字段3' AFTER attr2;
-- ALTER TABLE px_unloading_dict ADD   `attr4` varchar(50) DEFAULT '' COMMENT '保留字段4' AFTER attr3;

DROP TABLE IF EXISTS px_unloading_worker;
CREATE TABLE px_unloading_worker(
    id bigint not null auto_increment,
    worker_no      varchar(20)       not null   default '' comment '搬运工编号',
    worker_sign  varchar(50) DEFAULT '' COMMENT '搬运工标识',
    name    varchar(50) DEFAULT '' COMMENT '姓名',
    born_date date DEFAULT NULL COMMENT '出生日期',
    contact varchar(11) DEFAULT '' COMMENT '联系方式',
    face_url varchar(200) NOT NULL default '' COMMENT '人脸',
    classes  tinyint unsigned default 0  not null comment '班次 0 早班; 1 晚班;',
    status  tinyint unsigned default 0  not null comment '状态 0 未挂牌; 1 已挂牌; 2 搬运中 3 请假',
    listing  tinyint(1)      default 0  not null comment '自动挂牌',
    order_num int NOT NULL DEFAULT '0' COMMENT '排序',
    round_num int NOT NULL DEFAULT '0' COMMENT '轮次',
    punish_round_num int NOT NULL DEFAULT '0' COMMENT '惩罚轮次',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);
-- ALTER TABLE px_unloading_worker ADD COLUMN classes  tinyint unsigned default 0  not null comment '班次 0 早班; 1 晚班;' AFTER status;
-- ALTER TABLE px_unloading_worker ADD COLUMN round_num int NOT NULL DEFAULT '0' COMMENT '轮次' AFTER order_num;
-- ALTER TABLE px_unloading_worker ADD COLUMN punish_round_num int NOT NULL DEFAULT '0' COMMENT '惩罚轮次' AFTER round_num;
-- ALTER TABLE px_unloading_worker ADD COLUMN listing  tinyint(1)      default 0  not null comment '自动挂牌' AFTER status;

DROP TABLE IF EXISTS px_unloading_worker_access;
CREATE TABLE px_unloading_worker_access(
    id               bigint unsigned             not null auto_increment,
    worker_id  bigint NOT NULL COMMENT '搬运工id',
    worker_sign  varchar(50) DEFAULT '' COMMENT '搬运工标识',
    device_id      bigint unsigned   not null  comment '设备id',
    device_ip varchar(20) NOT NULL DEFAULT '' COMMENT 'ip',
    access         tinyint(1)        not null   default 0 comment '拥有权限',
    state       tinyint(1)      default 0                 comment '是否生效（0：未生效，1：已生效）',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id     bigint unsigned   not null   default 0  comment '创建人id',
    creator_name   varchar(30)       not null   default '' comment '创建人姓名',
    create_time    datetime          not null   comment '创建时间',
    modifier_id    bigint unsigned   not null   default 0  comment '修改人id',
    modifier_name  varchar(30)       not null   default '' comment '修改人姓名',
    modified_time  datetime          not null   comment '修改时间',
    primary key (id)
);

DROP TABLE IF EXISTS px_unloading_attendance;
CREATE TABLE px_unloading_attendance(
    id bigint not null auto_increment,
    worker_id  bigint NOT NULL COMMENT '搬运工id',
    worker_name    varchar(50) DEFAULT '' COMMENT '搬运工姓名',
    worker_no      varchar(20)       not null   default '' comment '搬运工编号',
    operate_time datetime    not null    comment '操作时间',
    remark    varchar(100)      default '' comment '备注',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS px_unloading_order;
CREATE TABLE px_unloading_order(
    id          bigint      not null        auto_increment,
    order_no varchar(40)       not null   comment '订单编号',
    merchant_id bigint unsigned not null comment '客户id',
    customer_name  varchar(50)   not null default ''  comment '客户名',
    address        varchar(150)   not null  default '' comment '地址',
    license_plate      varchar(20)   not null  default '' comment '车牌',
    contact        varchar(20)   not null   comment '联系方式',
    remark    varchar(200)      default '' comment '备注',
    state tinyint(1)        not null   default 0  comment '状态 0：草稿 1：正常',
    worker_status tinyint(1)        not null   default 0  comment '搬运状态 0：初始化 1：搬运中 2: 已完成',
    carpool       tinyint(1)        not null    default 0  comment '是否拼车',
    car_category  tinyint(1) NOT NULL DEFAULT '0' COMMENT '车型',
    car_category_name  varchar(30) NOT NULL DEFAULT '' COMMENT '车型名',
    car_num      bigint  default 0    not null comment '车辆数',
    worker_num      bigint  default 0    not null comment '搬运工人数',
    reservation_order tinyint(1)        not null    default 0  comment '是否预约单',
    reservation_time datetime         comment '预约时间',
    order_time     datetime         comment '下单时间',
    work_time     datetime                      comment '搬运时间',
    finish_time     datetime                      comment '完成时间',
    urgent_order         tinyint(1)        not null    default 0  comment '是否加急单',
    urgent_amount         decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '加急金额',
    amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
    total_amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
    payoff_amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣金额',
    supplement       tinyint(1)        not null    default 0  comment '补录',
    openid   varchar(64)       not null    default '' comment '微信创建用户',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id),
    unique key uk_px_unloading_order_no(order_no)
);
-- ALTER TABLE px_unloading_order ADD COLUMN amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '金额' AFTER finish_time;
-- ALTER TABLE px_unloading_order ADD COLUMN payoff_amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣金额' AFTER amount;
-- ALTER TABLE px_unloading_order ADD COLUMN customer_name  varchar(50)   not null default ''   comment '客户名' AFTER order_no;
-- ALTER TABLE px_unloading_order ADD COLUMN carpool       tinyint(1)        not null    default 0  comment '是否拼车' AFTER worker_status;
-- ALTER TABLE px_unloading_order ADD COLUMN supplement       tinyint(1)        not null    default 0  comment '补录' AFTER payoff_amount;
-- ALTER TABLE px_unloading_order ADD COLUMN merchant_id bigint unsigned not null comment '客户id' AFTER order_no;
-- ALTER TABLE px_unloading_order ADD COLUMN address        varchar(150)   not null  default '' comment '地址' AFTER customer_name;
-- ALTER TABLE px_unloading_order ADD COLUMN reservation_order tinyint(1)        not null    default 0  comment '是否预约单' AFTER worker_num;
-- ALTER TABLE px_unloading_order ADD COLUMN reservation_time datetime         comment '预约时间' AFTER reservation_order;
-- ALTER TABLE px_unloading_order ADD COLUMN urgent_order         tinyint(1)        not null    default 0  comment '是否加急单' AFTER finish_time;
-- ALTER TABLE px_unloading_order ADD COLUMN urgent_amount         decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '加急金额' AFTER urgent_order;
-- ALTER TABLE px_unloading_order ADD COLUMN total_amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总金额' AFTER amount;

DROP TABLE IF EXISTS px_unloading_order_goods;
CREATE TABLE px_unloading_order_goods(
    id          bigint      not null        auto_increment,
    order_id  bigint NOT NULL COMMENT '订单id',
    category  tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型 0冻品，1干货',
    goods_name varchar(100)  NOT NULL default '' COMMENT '货物名',
    unit     varchar(32) not null default ''  COMMENT '单位',
    quantity     decimal(6,1) NOT NULL DEFAULT '0.0' COMMENT '数量',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS px_unloading_order_workers;
CREATE TABLE px_unloading_order_workers(
    id          bigint      not null        auto_increment,
    order_id  bigint NOT NULL COMMENT '订单id',
    worker_id      bigint NOT NULL COMMENT '搬运工id',
    worker_no      varchar(20)       not null   default '' comment '搬运工编号',
    worker_name    varchar(50) DEFAULT '' COMMENT '搬运工姓名',
    flag tinyint(1)        not null   default 1  comment '状态 0：非正常轮 1：正常轮',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);
-- ALTER TABLE px_unloading_order_workers ADD COLUMN flag tinyint(1)        not null   default 1  comment '状态 0：非正常轮 1：正常轮' AFTER worker_name;

DROP TABLE IF EXISTS px_unloading_order_bill;
CREATE TABLE px_unloading_order_bill(
    id          bigint      not null        auto_increment,
    bill_date date NOT NULL COMMENT '账单日',
    bill_year smallint unsigned NOT NULL COMMENT '年份',
    bill_month tinyint unsigned NOT NULL COMMENT '月份',
    order_no varchar(40)       not null   comment '订单编号',
    amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
    status          tinyint unsigned    not null default 0 comment '账单状态：0初始化；1已支付',
    paid_time       datetime comment '支付时间',
    toll_man_id     bigint unsigned              default '0' not null comment '收费员',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);
-- ALTER TABLE px_unloading_order_bill ADD COLUMN toll_man_id     bigint unsigned              default '0' not null comment '收费员' AFTER paid_time;

drop table if exists px_unloading_shares;
create table px_unloading_shares
(
    id          bigint      not null        auto_increment,
    name             varchar(100)    not null comment '名称',
    shares_rate      decimal(10, 2)           default 0.00 not null comment '分佣比例',
    effective_time   datetime        not null comment '生效时间',
    ineffective_time datetime        not null comment '失效时间',
    status           tinyint unsigned         default 0 not null comment '状态 0未生效 1已生效 2已失效',
    is_disabled      tinyint(1)       default 0  not null comment '禁用',
    creator_id       bigint unsigned not null default 0 comment '创建人id',
    creator_name     varchar(30)     not null default '' comment '创建人姓名',
    create_time      datetime        not null comment '创建时间',
    modifier_id      bigint unsigned not null default 0 comment '修改人id',
    modifier_name    varchar(30)     not null default '' comment '修改人姓名',
    modified_time    datetime        not null comment '修改时间',
    PRIMARY KEY (id)
);

DROP TABLE IF EXISTS px_unloading_order_payoff;
CREATE TABLE px_unloading_order_payoff(
    id          bigint      not null        auto_increment,
    payoff_date date NOT NULL COMMENT '应付日期',
    payoff_year                smallint unsigned                          not null comment '应付年份',
    payoff_month               tinyint unsigned                           not null comment '应付月份',
    order_id  bigint NOT NULL COMMENT '订单id',
    order_no varchar(40)       not null   comment '订单编号',
    worker_id      bigint NOT NULL COMMENT '搬运工id',
    worker_no      varchar(20)       not null   default '' comment '搬运工编号',
    worker_name    varchar(50) DEFAULT '' COMMENT '搬运工姓名',
    order_num int NOT NULL DEFAULT '0' COMMENT '排序',
    payoff_amount             decimal(10, 2)   default 0.00              not null comment '佣金',
    month_payoff_id bigint unsigned NOT NULL DEFAULT '0' COMMENT '月应付账单',
    status          tinyint unsigned    not null default 0 comment '应付状态：0初始化；1已支付',
    payoff_time       datetime comment '应付时间',
    is_disabled     tinyint(1)        not null    default 0  comment '禁用',
    creator_id      bigint unsigned   not null    default 0  comment '创建人id',
    creator_name    varchar(30)       not null    default '' comment '创建人姓名',
    create_time     datetime          not null    comment '创建时间',
    modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
    modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);
-- ALTER TABLE px_unloading_order_payoff ADD COLUMN order_id  bigint NOT NULL COMMENT '订单id' AFTER payoff_month;
-- ALTER TABLE px_unloading_order_payoff ADD COLUMN status          tinyint unsigned    not null default 0 comment '应付状态：0初始化；1已支付' AFTER payoff_amount;
-- ALTER TABLE px_unloading_order_payoff ADD COLUMN payoff_time       datetime comment '应付时间' AFTER status;
-- ALTER TABLE px_unloading_order_payoff ADD COLUMN month_payoff_id bigint unsigned NOT NULL DEFAULT '0' COMMENT '月应付账单' AFTER payoff_amount;

-- ALTER TABLE px_unloading_order_payoff ADD COLUMN order_num int NOT NULL DEFAULT '0' COMMENT '排序' AFTER worker_name;


DROP TABLE IF EXISTS px_unloading_order_month_payoff;
CREATE TABLE px_unloading_order_month_payoff(
  id          bigint      not null        auto_increment,
  payoff_year                smallint unsigned                          not null comment '应付年份',
  payoff_month               tinyint unsigned                           not null comment '应付月份',
  worker_id      bigint NOT NULL COMMENT '搬运工id',
  worker_no      varchar(20)       not null   default '' comment '搬运工编号',
  worker_name    varchar(50) DEFAULT '' COMMENT '搬运工姓名',
  payoff_amount             decimal(10, 2)   default 0.00              not null comment '佣金',
  share_amount             decimal(10, 2)   default 0.00              not null comment '收益',
  status          tinyint unsigned    not null default 0 comment '应付状态：0初始化；1已支付',
  payoff_time       datetime comment '应付时间',
  remark    varchar(100)      default '' comment '备注',
  is_disabled     tinyint(1)        not null    default 0  comment '禁用',
  creator_id      bigint unsigned   not null    default 0  comment '创建人id',
  creator_name    varchar(30)       not null    default '' comment '创建人姓名',
  create_time     datetime          not null    comment '创建时间',
  modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
  modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
  modified_time   datetime          not null    comment '修改时间',
  PRIMARY KEY (id)
);

DROP TABLE IF EXISTS px_unloading_night_schedule;
CREATE TABLE px_unloading_night_schedule(
  id          bigint      not null        auto_increment,
  schedule_date date NOT NULL COMMENT '排期日期',
  schedule_year smallint unsigned NOT NULL COMMENT '排期年份',
  schedule_month tinyint unsigned NOT NULL COMMENT '排期月份',
  worker_id      bigint NOT NULL COMMENT '搬运工id',
  worker_no      varchar(20)       not null   default '' comment '搬运工编号',
  worker_name    varchar(50) DEFAULT '' COMMENT '搬运工姓名',
  is_disabled     tinyint(1)        not null    default 0  comment '禁用',
  creator_id      bigint unsigned   not null    default 0  comment '创建人id',
  creator_name    varchar(30)       not null    default '' comment '创建人姓名',
  create_time     datetime          not null    comment '创建时间',
  modifier_id     bigint unsigned   not null    default 0  comment '修改人id',
  modifier_name   varchar(30)       not null    default '' comment '修改人姓名',
  modified_time   datetime          not null    comment '修改时间',
  PRIMARY KEY (id)
);

DROP TABLE IF EXISTS px_unloading_worker_log;
CREATE TABLE px_unloading_worker_log(
    id bigint not null auto_increment,
    worker_id  bigint NOT NULL COMMENT '搬运工id',
    worker_name    varchar(50) DEFAULT '' COMMENT '搬运工姓名',
    worker_no      varchar(20)       not null   default '' comment '搬运工编号',
    remark    varchar(100)      default '' comment '备注',
    modified_time   datetime          not null    comment '修改时间',
    PRIMARY KEY (id)
);

drop table if exists t_outgoing_ttl;
create table t_outgoing_ttl
(
    id                         bigint unsigned              not null auto_increment primary key,
    receiving_date             date                         null comment '收货日期',
    shipper_name      varchar(60)     default ''   not null comment '托运方客户',
    recipient_name      varchar(60)     default ''   not null comment '收货方客户',
    recipient_address varchar(60)     default ''   not null comment '收货方地址',
    recipient_contact varchar(20)     default ''   not null comment '收货方电话',
    logistics_company varchar(100)    default ''   not null comment '物流公司',
    logistics_no      varchar(20)     default ''   not null comment '物流单号',
    shipper_pieces    bigint unsigned default 0    not null comment '托运方件数',
    actual_freight_amount  decimal(8, 2)    not null    default 0  comment '实收运费',
    actual_shipping_amount  decimal(8, 2)    not null    default 0  comment '实发运费',
    profit_amount              decimal(10, 2)  default 0.00 not null comment '利润',
    settlement_type     tinyint(1)      default 1    not null comment '结算类型(1:到付;2:现付)',
    is_disabled                tinyint(1)                   not null default 0 comment '禁用',
    creator_id                 bigint unsigned              not null default 0 comment '创建人id',
    creator_name               varchar(30)                  not null default '' comment '创建人姓名',
    create_time                datetime                     not null comment '创建时间',
    modifier_id                bigint unsigned              not null default 0 comment '修改人id',
    modifier_name              varchar(30)                  not null default '' comment '修改人姓名',
    modified_time              datetime                     not null comment '修改时间'
);
drop table if exists t_logistic_transport_order;
create table t_logistic_transport_order
(
    id                   bigint auto_increment primary key,
    serial_no            varchar(10)     default ''   not null comment '编号',
    year_month_day       varchar(10)     default ''   not null comment '订单年月日',
    consignor_id         bigint          default 0    not null comment '发货人id',
    consignor_name       varchar(30)     default ''   not null comment '发货人姓名',
    consignor_code       varchar(10)     default ''   not null comment '发货人编码',
    consignor_phone      varchar(30)     default ''   not null comment '发货人电话',
    consignee_name       varchar(30)     default ''   not null comment '收货人姓名',
    consignee_phone      varchar(30)     default ''   not null comment '收货人电话',
    category             tinyint(1)      default 0    not null comment '类别',
    driver_name          varchar(30)     default ''   not null comment '司机姓名',
    driver_phone         varchar(20)     default ''   not null comment '司机手机',
    license_plate_number varchar(20)     default ''   not null comment '车牌号',
    is_charter           tinyint(1)      default 0    not null comment '是否包车',
    departure_station    varchar(100)    default ''   not null comment '始发站',
    destination_station  varchar(100)    default ''   not null comment '目的站',
    pieces               int             default 0    not null comment '件数',
    loading_weight       decimal(10, 2)  default 0.00 not null comment '装载重量(kg)',
    freight_charge       decimal(10, 2)  default 0.00 not null comment '运费',
    other_charge        decimal(10, 2) default 0.00 not null comment '其他费用',
    receivable_freight_charge decimal(10, 2) default 0.00 not null comment '应收运费',
    payer                tinyint(1)      default 0    not null comment '付款人',
    remark               varchar(2555)   default ''   not null comment '备注',
    audit_status         tinyint(1)      default 0    null comment '审核状态',
    auditor_id           bigint          default 0    null comment '审核人',
    auditor_name         varchar(30)     default ''   null comment '审核人姓名',
    audit_time           datetime                     null comment '审核时间',
    is_disabled          tinyint(1)      default 0    not null comment '禁用',
    creator_id           bigint unsigned default '0'  not null comment '创建人id',
    creator_name         varchar(30)     default ''   not null comment '创建人姓名',
    create_time          datetime                     not null comment '创建时间',
    modifier_id          bigint unsigned default '0'  not null comment '修改人id',
    modifier_name        varchar(30)     default ''   not null comment '修改人姓名',
    modified_time        datetime                     not null comment '修改时间'
);
-- create index idx_logistic_transport_order_serial_no on t_logistic_transport_order (serial_no);
-- create index idx_bicycle_order_year_month_day on t_logistic_transport_order (year_month_day);

drop table if exists t_logistic_transport_bill;
create table t_logistic_transport_bill
(
    id              bigint auto_increment
        primary key,
    settlement_id   bigint unsigned   default '0'  null comment '结算单id',
    bill_date       date                           not null comment '账单日期',
    bill_year       smallint unsigned              not null comment '年份',
    bill_month      tinyint unsigned               not null comment '月份',
    order_serial_no varchar(64)                    not null comment '订单编号',
    merchant_id     bigint unsigned   default '0'  null comment '商户id',
    amount          decimal(10, 2)    default 0.00 not null comment '金额',
    status          tinyint unsigned  default '0'  not null comment '账单状态：0初始化；1已支付',
    paid_time       datetime                       null comment '支付时间',
    pay_way         smallint unsigned default '0'  not null comment '支付方式',
    is_disabled     tinyint(1)        default 0    not null comment '禁用',
    creator_id      bigint unsigned   default '0'  not null comment '创建人id',
    creator_name    varchar(30)       default ''   not null comment '创建人姓名',
    create_time     datetime                       not null comment '创建时间',
    modifier_id     bigint unsigned   default '0'  not null comment '修改人id',
    modifier_name   varchar(30)       default ''   not null comment '修改人姓名',
    modified_time   datetime                       not null comment '修改时间',
    unique key uk_bill_date_order (bill_date, order_serial_no)
);
-- create index idx_logistic_transport_bill_order_serial_no on t_logistic_transport_bill (order_serial_no);
-- create index idx_logistic_transport_bill_merchant on t_logistic_transport_bill (merchant_id);
-- create index idx_logistic_transport_bill_year_month on t_logistic_transport_bill (bill_year, bill_month);
-- create index idx_logistic_transport_bill_year_mont_date  on t_logistic_transport_bill (bill_year, bill_month, bill_date);

drop table if exists t_logistic_transport_bill_settlement;
create table t_logistic_transport_bill_settlement
(
    id                bigint unsigned auto_increment
        primary key,
    bill_date         date                           not null comment '账单日期',
    bill_year_month   varchar(10)                    not null comment '账单年月',
    bill_year         smallint unsigned              not null comment '年份',
    bill_month        tinyint unsigned               not null comment '月份',
    merchant_id       bigint unsigned   default '0'  null comment '商户id',
    payer             tinyint(1)      default 0    not null comment '付款人',
    freight_charge       decimal(10, 2)  default 0.00 not null comment '运费',
    other_charge        decimal(10, 2) default 0.00 not null comment '其他费用',
    receivable_freight_charge decimal(10, 2) default 0.00 not null comment '应收运费',
    paid_amount       decimal(10, 2)    default 0.00 not null comment '支付金额',
    paid_still_amount decimal(10, 2)    default 0.00 not null comment '待支付金额',
    pay_way           smallint unsigned default '0'  not null comment '支付方式',
    paid_time         datetime                       null comment '支付时间',
    remote_order_id   bigint unsigned   default '0'  null comment '订单id',
    send              tinyint unsigned  default '0'  not null comment '下发状态：0初始化；1已下发',
    send_time         datetime                       null comment '下发时间',
    toll_man_id       bigint unsigned   default '0'  null comment '收费员',
    status            tinyint unsigned  default '0'  not null comment '账单状态：0初始化；1已支付',
    is_disabled       tinyint(1)        default 0    not null comment '禁用',
    creator_id        bigint unsigned   default '0'  not null comment '创建人id',
    creator_name      varchar(30)       default ''   not null comment '创建人姓名',
    create_time       datetime                       not null comment '创建时间',
    modifier_id       bigint unsigned   default '0'  not null comment '修改人id',
    modifier_name     varchar(30)       default ''   not null comment '修改人姓名',
    modified_time     datetime                       not null comment '修改时间'
);
-- create index idx_bicycle_bill_settlement_merchant on t_logistic_transport_bill_settlement (merchant_id);
-- create index idx_bicycle_bill_settlement_year_mont_date on t_logistic_transport_bill_settlement (bill_year, bill_month, bill_date);
-- create index idx_bicycle_bill_settlement_year_month on t_logistic_transport_bill_settlement (bill_year, bill_month);

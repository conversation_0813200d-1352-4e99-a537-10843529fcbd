server:
  port: 8081

spring:
  profiles:
    active: test
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:test
    username: sa
    password:
  sql:
    init:
      platform: h2
      encoding: UTF-8
      schema-locations: classpath:db/schema-h2.sql
      data-locations: classpath:db/schema-h2-data.sql
  redis:
    host: **************
    port: 6379
    password: WT0UdHepNsSnYb81
    timeout: 10000
    database: 2
  rabbitmq:
    addresses: *************:5672
    username: smart
    password: smart
    virtual-host: /smart
    connection-timeout: 15000
    consumer-threads: 5
  zipkin:
    enabled: false

logging:
  level:
   com.senox.tms.mapper : debug

package com.senox.tms.service;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.UnloadingBaseTest;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.domain.UnloadingAttendance;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.vo.UnloadingWorkerSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/24 14:39
 */
@Slf4j
public class UnloadingWorkerServiceTest extends UnloadingBaseTest {

    @Autowired
    private UnloadingWorkerService workerService;

    @Autowired
    private UnloadingAttendanceService attendanceService;

    @Test
    void addWorker() {
        List<UnloadingWorker> workers = workerMock(5);
        workers.forEach(worker -> workerService.addWorker(worker));
        UnloadingWorkerSearchVo searchVo = new UnloadingWorkerSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);
        List<UnloadingWorker> workerList = workerService.listWorker(searchVo);
        log.info("workerList ---- : {}", JsonUtils.object2Json(workerList));
        Assertions.assertNotNull(workerList);
    }

    //@Test
    void updateWorker() {
        List<UnloadingWorker> workers = workerMock(5);
        workers.forEach(worker -> workerService.addWorker(worker));
        UnloadingWorker dbWorker = workerService.findById(1L);
        log.info("dbWorker ---- : {}", JsonUtils.object2Json(dbWorker));
        //需要去取setting
        workerService.updateWorkerStatus(dbWorker.getId(), UnloadingWorkerStatus.ALREADY_LISTED.getNumber());
        UnloadingWorker updateWorker = workerService.findById(1L);
        log.info("updateWorker ---- : {}", JsonUtils.object2Json(updateWorker));
        Assertions.assertNotEquals(dbWorker, updateWorker);
        UnloadingAttendance dbAttendance = attendanceService.findById(1L);
        log.info("dbAttendance ---- : {}", JsonUtils.object2Json(dbAttendance));
        attendanceService.updateRemark(dbAttendance.getId(), "测试改备注");
        UnloadingAttendance updateAttendance = attendanceService.findById(1L);
        log.info("updateAttendance ---- : {}", JsonUtils.object2Json(updateAttendance));
        Assertions.assertNotEquals(dbAttendance, updateAttendance);
    }
}

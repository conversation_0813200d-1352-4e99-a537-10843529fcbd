package com.senox.tms.service;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.BaseTest;
import com.senox.tms.domain.LogisticOrderProduct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/28 11:48
 */
class LogisticOrderProductServiceTest extends BaseTest {

    private static final Logger logger = LoggerFactory.getLogger(LogisticOrderProductServiceTest.class);

    @Autowired
    private LogisticOrderProductService orderProductService;


    //@Test
    void batchSaveProductOrder() {
        List<LogisticOrderProduct> list = Arrays.asList(mockOrderProduct(), mockOrderProduct());
        orderProductService.saveProductOrderBatch(list, true);
        logger.info("list1: {}", JsonUtils.object2Json(list));

        // h2 查询浮点数唯一性判断有点问题
        list.get(0).setProductPrice(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(1000L), 2));
        orderProductService.saveProductOrderBatch(list, true);

        logger.info("list2: {}", JsonUtils.object2Json(list));
    }


    private LogisticOrderProduct mockOrderProduct() {
        LogisticOrderProduct result = new LogisticOrderProduct();
        result.setShipDate(LocalDate.now());
        result.setOrderNo(randNumStr(16));
        result.setMerchant(randStr(8));
        result.setProduct(randStr(28));
        result.setProductCount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(100L), 2));

        return result;
    }

}
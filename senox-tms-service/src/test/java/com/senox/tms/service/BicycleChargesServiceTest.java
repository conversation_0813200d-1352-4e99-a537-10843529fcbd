package com.senox.tms.service;

import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.BicycleChargesBaseTest;
import com.senox.tms.constant.BicycleOrderGoodsType;
import com.senox.tms.constant.BicycleStatus;
import com.senox.tms.convert.BicycleChargesDetailConvert;
import com.senox.tms.domain.BicycleCharges;
import com.senox.tms.domain.BicycleChargesDetail;
import com.senox.tms.domain.BicycleOrderGoodsDetail;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.BicycleChargesDetailVo;
import com.senox.tms.vo.BicycleChargesSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 收费标准 test case
 *
 * <AUTHOR>
 * @date 2023-9-13
 */
@Slf4j
class BicycleChargesServiceTest extends BicycleChargesBaseTest {
    private static final String SUFFIX = "-service";
    @Autowired
    private BicycleChargesDetailConvert chargesDetailConvert;

    @Test
    void addTest() {
        Set<BicycleCharges> mockChargesList = chargesMock(COUNT, SUFFIX);
        for (BicycleCharges charges : mockChargesList) {
            Set<BicycleChargesDetail> mockChargesDetailList = chargesDetailMock(1L, COUNT / 2);
            chargesService.add(charges, new ArrayList<>(mockChargesDetailList));
            Assertions.assertTrue(WrapperClassUtils.biggerThanLong(charges.getId(), 0));
            mockChargesDetailList.forEach(chargesDetail -> Assertions.assertEquals(chargesDetail.getChargesId(), charges.getId()));
        }

    }

    @Test
    void deleteTest() {
        BicycleCharges charges = randCharges();
        chargesService.delete(charges.getId());
        BicycleCharges deletedCharges = chargesService.getById(charges.getId());
        Assertions.assertNull(deletedCharges);
        List<BicycleChargesDetailVo> chargesDetails = chargesService.listDetailByCharges(charges.getId());
        Assertions.assertTrue(CollectionUtils.isEmpty(chargesDetails));

    }

    @Test
    void updateTest() {
        BicycleCharges charges = randCharges();
        BicycleCharges updateCharges = new BicycleCharges();
        updateCharges.setId(charges.getId());
        updateCharges.setName(charges.getName());
        updateCharges.setEffectiveTime(charges.getEffectiveTime().plusMinutes(randLong(1, 30)));
        updateCharges.setIneffectiveTime(updateCharges.getEffectiveTime().minusDays(randInt(5, 15)));
        updateCharges.setStatus(BicycleStatus.ENABLED_NOT_EFFECTIVE.getNumber());
        ContextUtils.initEntityModifier(updateCharges);
        List<BicycleChargesDetailVo> chargesDetails = chargesService.listDetailByCharges(charges.getId());
        Assertions.assertFalse(CollectionUtils.isEmpty(chargesDetails));
        BicycleChargesDetailVo chargesDetail = chargesDetails.get( chargesDetails.size() - 1);
        BicycleChargesDetail updateChargesDetail = new BicycleChargesDetail();
        updateChargesDetail.setId(chargesDetail.getId());
        updateChargesDetail.setChargesId(charges.getId());
        updateChargesDetail.setMinCount(randInt(1, 20));
        updateChargesDetail.setMaxCount(randInt(30, 50));
        updateChargesDetail.setUnitPrice(new BigDecimal(randInt(1, 10).toString()).setScale(2, RoundingMode.HALF_EVEN));
        chargesService.update(updateCharges, Collections.singletonList(updateChargesDetail));
        BicycleCharges dbCharges = chargesService.getById(updateCharges.getId());
        Assertions.assertEquals(updateCharges.getName(), dbCharges.getName());
        Assertions.assertEquals(updateCharges.getEffectiveTime(), dbCharges.getEffectiveTime());
        Assertions.assertEquals(updateCharges.getIneffectiveTime(), dbCharges.getIneffectiveTime());
        Assertions.assertEquals(updateCharges.getStatus(), dbCharges.getStatus());
        BicycleChargesDetail dbChargesDetail = chargesDetailService.getById(updateChargesDetail.getId());
        Assertions.assertEquals(dbChargesDetail.getChargesId(), chargesDetail.getChargesId());
        Assertions.assertEquals(updateChargesDetail.getMinCount(), dbChargesDetail.getMinCount());
        Assertions.assertEquals(updateChargesDetail.getMaxCount(), dbChargesDetail.getMaxCount());
        Assertions.assertEquals(updateChargesDetail.getUnitPrice(), dbChargesDetail.getUnitPrice());
    }

    @Test
    void listTest() {
        BicycleChargesSearchVo searchVo = new BicycleChargesSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(COUNT);
        searchVo.setName(SUFFIX);
        searchVo.setEffectiveStartTime(LocalDateTime.now().withDayOfMonth(1).with(LocalTime.MIN));
        searchVo.setEffectiveEndTime(searchVo.getEffectiveStartTime().minusDays(1));
        PageResult<BicycleCharges> pageResult = chargesService.list(searchVo);
        Assertions.assertNotNull(pageResult);
        List<BicycleCharges> dataList = pageResult.getDataList();
        Assertions.assertFalse(CollectionUtils.isEmpty(dataList));
    }

    @Test
    void chargesDetailAddTest() {
        BicycleCharges charges = randCharges();
        BicycleChargesDetail chargesDetail = new BicycleChargesDetail();
        chargesDetail.setChargesId(charges.getId());
        chargesDetail.setMinCount(randInt(1, 10));
        chargesDetail.setMaxCount(randInt(11, 30));
        chargesDetail.setUnitPrice(new BigDecimal(randInt(1, 20).toString()));
        chargesService.addDetailBatch(Collections.singletonList(chargesDetail));
        BicycleChargesDetail dbChargesDetail = chargesDetailService.getById(chargesDetail.getId());
        Assertions.assertNotNull(dbChargesDetail);
    }


    @Test
    void chargesDetailDeleteTest() {
        BicycleCharges charges = randCharges();
        List<BicycleChargesDetailVo> chargesDetailList = chargesService.listDetailByCharges(charges.getId());
        Assertions.assertFalse(CollectionUtils.isEmpty(chargesDetailList));
        BicycleChargesDetailVo chargesDetail = chargesDetailList.get(0);
        chargesService.deleteDetailBatch(Collections.singletonList(chargesDetail.getId()));
        BicycleChargesDetail deletedChargesDetail = chargesDetailService.getById(chargesDetail.getId());
        Assertions.assertNull(deletedChargesDetail);
    }

    @Test
    void chargesDetailUpdateTest() {
        List<BicycleCharges> chargesList = chargesService.list().stream().filter(c->c.getStatus()==BicycleStatus.DISABLED.getNumber()).collect(Collectors.toList());
        Assertions.assertFalse(CollectionUtils.isEmpty(chargesList));
        BicycleCharges charges1 = chargesList.get(0);
        BicycleCharges charges2 = chargesList.get(1);
        Assertions.assertTrue(WrapperClassUtils.biggerThanLong(charges1.getId(), 0));
        Assertions.assertTrue(WrapperClassUtils.biggerThanLong(charges2.getId(), 0));
        List<BicycleChargesDetailVo> chargesDetails1 = chargesDetailService.listVoByCharges(charges1.getId());
        Assertions.assertFalse(CollectionUtils.isEmpty(chargesDetails1));
        List<BicycleChargesDetailVo> chargesDetails2 = chargesDetailService.listVoByCharges(charges2.getId());
        Assertions.assertFalse(CollectionUtils.isEmpty(chargesDetails2));
        List<BicycleChargesDetailVo> chargesDetails = Stream.concat(chargesDetails1.stream(), chargesDetails2.stream()).collect(Collectors.toList());
        chargesDetails.forEach(chargesDetail -> {
            chargesDetail.setMinCount(randInt(1, 10));
            chargesDetail.setMaxCount(randInt(11, 30));
            chargesDetail.setUnitPrice(new BigDecimal(randInt(1, 20).toString()).setScale(2, RoundingMode.HALF_EVEN));
        });
        Map<Long, BicycleChargesDetailVo> chargesDetailsMap = chargesDetails.stream().collect(Collectors.toMap(BicycleChargesDetailVo::getId, Function.identity()));
        chargesService.updateDetailBatch(chargesDetailConvert.toDo(chargesDetails));
        List<BicycleChargesDetailVo> dbChargesDetails1 = chargesDetailService.listVoByCharges(charges1.getId());
        Assertions.assertFalse(CollectionUtils.isEmpty(dbChargesDetails1));
        List<BicycleChargesDetailVo> dbChargesDetails2 = chargesDetailService.listVoByCharges(charges2.getId());
        Assertions.assertFalse(CollectionUtils.isEmpty(dbChargesDetails2));
        List<BicycleChargesDetailVo> dbChargesDetails = Stream.concat(dbChargesDetails1.stream(), dbChargesDetails2.stream()).collect(Collectors.toList());
        dbChargesDetails.forEach(dbChargesDetail -> {
            BicycleChargesDetailVo chargesDetail = chargesDetailsMap.get(dbChargesDetail.getId());
            Assertions.assertEquals(chargesDetail.getMinCount(), dbChargesDetail.getMinCount());
            Assertions.assertEquals(chargesDetail.getMaxCount(), dbChargesDetail.getMaxCount());
            Assertions.assertEquals(chargesDetail.getUnitPrice(), dbChargesDetail.getUnitPrice());
        });

    }

    @Test
    void calculateChargesTest() {
        List<BicycleChargesDetail> chargesDetailList = new ArrayList<>();
        BicycleChargesDetail d1 = new BicycleChargesDetail();
        BicycleChargesDetail d2 = new BicycleChargesDetail();
        BicycleChargesDetail d3 = new BicycleChargesDetail();
        BicycleChargesDetail d4 = new BicycleChargesDetail();
        BicycleChargesDetail d5 = new BicycleChargesDetail();

        BicycleChargesDetail d6 = new BicycleChargesDetail();
        BicycleChargesDetail d7 = new BicycleChargesDetail();
        BicycleChargesDetail d8 = new BicycleChargesDetail();
        BicycleChargesDetail d9 = new BicycleChargesDetail();
        BicycleChargesDetail d10 = new BicycleChargesDetail();
        BicycleChargesDetail d11 = new BicycleChargesDetail();
        BicycleChargesDetail d12 = new BicycleChargesDetail();
        BicycleChargesDetail d13 = new BicycleChargesDetail();
        BicycleChargesDetail d14 = new BicycleChargesDetail();
        BicycleChargesDetail d15 = new BicycleChargesDetail();

        //普通类型货物
        d1.setMinCount(1);
        d1.setMaxCount(10);
        d1.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        d1.setMinUnit(BigDecimal.ZERO);
        d1.setMaxUnit(BigDecimal.ZERO);
        d1.setDefaultCharges(true);
        d1.setUnitPrice(new BigDecimal("0.5"));
        d1.setCalAnyway(true);

        d2.setMinCount(11);
        d2.setMaxCount(20);
        d2.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        d2.setMinUnit(BigDecimal.ZERO);
        d2.setMaxUnit(BigDecimal.ZERO);
        d2.setDefaultCharges(true);
        d2.setUnitPrice(new BigDecimal("0.45"));

        d3.setMinCount(21);
        d3.setMaxCount(30);
        d3.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        d3.setMinUnit(BigDecimal.ZERO);
        d3.setMaxUnit(BigDecimal.ZERO);
        d3.setDefaultCharges(true);
        d3.setUnitPrice(new BigDecimal("0.4"));

        d4.setMinCount(31);
        d4.setMaxCount(40);
        d4.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        d4.setMinUnit(BigDecimal.ZERO);
        d4.setMaxUnit(BigDecimal.ZERO);
        d4.setDefaultCharges(true);
        d4.setUnitPrice(new BigDecimal("0.35"));

        d5.setMinCount(41);
        d5.setMaxCount(0);
        d5.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        d5.setMinUnit(BigDecimal.ZERO);
        d5.setMaxUnit(BigDecimal.ZERO);
        d5.setDefaultCharges(true);
        d5.setUnitPrice(new BigDecimal("0.3"));



        //重货类型货物
        d6.setMinCount(1);
        d6.setMaxCount(10);
        d6.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        d6.setMinUnit(BigDecimal.ZERO);
        d6.setMaxUnit(BigDecimal.valueOf(10));
        d6.setDefaultCharges(false);
        d6.setUnitPrice(new BigDecimal("0.6"));
        d6.setCalAnyway(true);

        d7.setMinCount(11);
        d7.setMaxCount(20);
        d7.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        d7.setMinUnit(BigDecimal.ZERO);
        d7.setMaxUnit(BigDecimal.valueOf(10));
        d7.setDefaultCharges(false);
        d7.setUnitPrice(new BigDecimal("0.55"));

        d8.setMinCount(21);
        d8.setMaxCount(30);
        d8.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        d8.setMinUnit(BigDecimal.ZERO);
        d8.setMaxUnit(BigDecimal.valueOf(10));
        d8.setDefaultCharges(false);
        d8.setUnitPrice(new BigDecimal("0.5"));

        d9.setMinCount(31);
        d9.setMaxCount(40);
        d9.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        d9.setMinUnit(BigDecimal.ZERO);
        d9.setMaxUnit(BigDecimal.valueOf(10));
        d9.setDefaultCharges(false);
        d9.setUnitPrice(new BigDecimal("0.45"));

        d10.setMinCount(41);
        d10.setMaxCount(0);
        d10.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        d10.setMinUnit(BigDecimal.ZERO);
        d10.setMaxUnit(BigDecimal.valueOf(10));
        d10.setDefaultCharges(false);
        d10.setUnitPrice(new BigDecimal("0.4"));

        //重量下一档
        d11.setMinCount(1);
        d11.setMaxCount(10);
        d11.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        d11.setMinUnit(BigDecimal.valueOf(11));
        d11.setMaxUnit(BigDecimal.ZERO);
        d11.setUnitPrice(new BigDecimal("1"));
        d11.setDefaultCharges(false);
        d11.setCalAnyway(true);

        d12.setMinCount(11);
        d12.setMaxCount(20);
        d12.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        d12.setMinUnit(BigDecimal.valueOf(11));
        d12.setMaxUnit(BigDecimal.ZERO);
        d12.setDefaultCharges(false);
        d12.setUnitPrice(new BigDecimal("0.95"));

        d13.setMinCount(21);
        d13.setMaxCount(30);
        d13.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        d13.setMinUnit(BigDecimal.valueOf(11));
        d13.setMaxUnit(BigDecimal.ZERO);
        d13.setDefaultCharges(false);
        d13.setUnitPrice(new BigDecimal("0.9"));

        d14.setMinCount(31);
        d14.setMaxCount(40);
        d14.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        d14.setMinUnit(BigDecimal.valueOf(11));
        d14.setMaxUnit(BigDecimal.ZERO);
        d14.setDefaultCharges(false);
        d14.setUnitPrice(new BigDecimal("0.85"));

        d15.setMinCount(41);
        d15.setMaxCount(0);
        d15.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        d15.setMinUnit(BigDecimal.valueOf(11));
        d15.setMaxUnit(BigDecimal.ZERO);
        d15.setDefaultCharges(false);
        d15.setUnitPrice(new BigDecimal("0.8"));


        chargesDetailList.add(d1);
        chargesDetailList.add(d2);
        chargesDetailList.add(d3);
        chargesDetailList.add(d4);
        chargesDetailList.add(d5);

        chargesDetailList.add(d6);
        chargesDetailList.add(d7);
        chargesDetailList.add(d8);
        chargesDetailList.add(d9);
        chargesDetailList.add(d10);
        chargesDetailList.add(d11);
        chargesDetailList.add(d12);
        chargesDetailList.add(d13);
        chargesDetailList.add(d14);
        chargesDetailList.add(d15);
        log.info("chargesDetailList:{}", JsonUtils.object2Json(chargesDetailList));

        BigDecimal minAmount = new BigDecimal("5");

        // 基础消费
        BicycleOrderGoodsDetail detail1 = new BicycleOrderGoodsDetail();
        detail1.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        detail1.setPieces(randDecimal(new BigDecimal("1"), new BigDecimal("10"), 2));
        BigDecimal c1 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail1));
        Assertions.assertTrue(DecimalUtils.equals(minAmount, c1));

        // 基础消费重货重量第一阶段
        BicycleOrderGoodsDetail detail2 = new BicycleOrderGoodsDetail();
        detail2.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        detail2.setPieces(new BigDecimal("9"));
        detail2.setWeight(randDecimal(new BigDecimal("1"), new BigDecimal("10"), 2));
        BigDecimal c2 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail2));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("5.4"), c2));

        // 基础消费重货重量第二阶段
        BicycleOrderGoodsDetail detail3 = new BicycleOrderGoodsDetail();
        detail3.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        detail3.setPieces(new BigDecimal("9"));
        detail3.setWeight(randDecimal(new BigDecimal("11"), new BigDecimal("20"), 2));
        BigDecimal c3 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail3));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("9"), c3));


        // 第二个费段
        BicycleOrderGoodsDetail detail4 = new BicycleOrderGoodsDetail();
        detail4.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        detail4.setPieces(randDecimal(new BigDecimal("11"), new BigDecimal("20"), 2));
        BigDecimal c4 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail4));
        Assertions.assertTrue(DecimalUtils.equals(DecimalUtils.add(minAmount,
                DecimalUtils.multiple(d2.getUnitPrice(), new BigDecimal(detail4.getPieces().setScale(0, RoundingMode.CEILING).intValue() - d1.getMaxCount()))), c4));

        // 第二个费段重货重量第一阶段
        BicycleOrderGoodsDetail detail5 = new BicycleOrderGoodsDetail();
        detail5.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        detail5.setPieces(new BigDecimal("15"));
        detail5.setWeight(randDecimal(new BigDecimal("1"), new BigDecimal("10"), 2));
        BigDecimal c5 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail5));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("8.75"), c5));

        // 第二个费段重货重量第二阶段
        BicycleOrderGoodsDetail detail6 = new BicycleOrderGoodsDetail();
        detail6.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        detail6.setPieces(new BigDecimal("15"));
        detail6.setWeight(randDecimal(new BigDecimal("11"), new BigDecimal("20"), 2));
        BigDecimal c6 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail6));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("14.75"), c6));

        // 第三个费段
        BicycleOrderGoodsDetail detail7 = new BicycleOrderGoodsDetail();
        detail7.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        detail7.setPieces(randDecimal(new BigDecimal("21"), new BigDecimal("30"), 2));
        BigDecimal c7 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail7));
        Assertions.assertTrue(DecimalUtils.equals(DecimalUtils.add(minAmount,
                DecimalUtils.multiple(d3.getUnitPrice(), new BigDecimal(detail7.getPieces().setScale(0, RoundingMode.CEILING).intValue() - d1.getMaxCount()))), c7));

        // 第三个费段重货重量第一阶段
        BicycleOrderGoodsDetail detail8 = new BicycleOrderGoodsDetail();
        detail8.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        detail8.setPieces(randDecimal(new BigDecimal("21"), new BigDecimal("30"), 2));
        detail8.setWeight(randDecimal(new BigDecimal("1"), new BigDecimal("10"), 2));
        BigDecimal c8 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail8));
        Assertions.assertTrue(DecimalUtils.equals(DecimalUtils.add(DecimalUtils.multiple(d6.getUnitPrice(), new BigDecimal(d6.getMaxCount())),
                DecimalUtils.multiple(d8.getUnitPrice(), new BigDecimal(detail8.getPieces().setScale(0, RoundingMode.UP).intValue() - d6.getMaxCount()))), c8));

        // 第三个费段重货重量第二阶段
        BicycleOrderGoodsDetail detail9 = new BicycleOrderGoodsDetail();
        detail9.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        detail9.setPieces(randDecimal(new BigDecimal("21"), new BigDecimal("30"), 2));
        detail9.setWeight(randDecimal(new BigDecimal("11"), new BigDecimal("20"), 2));
        BigDecimal c9 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail9));
        Assertions.assertTrue(DecimalUtils.equals(DecimalUtils.add(DecimalUtils.multiple(d11.getUnitPrice(), new BigDecimal(d11.getMaxCount())),
                DecimalUtils.multiple(d13.getUnitPrice(), new BigDecimal(detail9.getPieces().setScale(0, RoundingMode.UP).intValue() - d6.getMaxCount()))), c9));

        // 最大费段
        BicycleOrderGoodsDetail detail10 = new BicycleOrderGoodsDetail();
        detail10.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        detail10.setPieces(new BigDecimal("48"));
        BigDecimal c10 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail10));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("16.4"), c10));

        // 最大费段重货重量第一阶段
        BicycleOrderGoodsDetail detail11 = new BicycleOrderGoodsDetail();
        detail11.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        detail11.setPieces(new BigDecimal("48"));
        detail11.setWeight(randDecimal(new BigDecimal("1"), new BigDecimal("10"), 2));
        BigDecimal c11 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail11));
        Assertions.assertTrue(DecimalUtils.equals(DecimalUtils.add(DecimalUtils.multiple(d6.getUnitPrice(), new BigDecimal(d6.getMaxCount())),
                DecimalUtils.multiple(d10.getUnitPrice(), new BigDecimal(detail11.getPieces().setScale(0, RoundingMode.UP).intValue() - d6.getMaxCount()))), c11));

        // 最大费段重货重量第一阶段
        BicycleOrderGoodsDetail detail12 = new BicycleOrderGoodsDetail();
        detail12.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        detail12.setPieces(new BigDecimal("48"));
        detail12.setWeight(randDecimal(new BigDecimal("10"), new BigDecimal("20"), 2));
        BigDecimal c12 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail12));
        Assertions.assertTrue(DecimalUtils.equals(DecimalUtils.add(DecimalUtils.multiple(d11.getUnitPrice(), new BigDecimal(d11.getMaxCount())),
                DecimalUtils.multiple(d15.getUnitPrice(), new BigDecimal(detail11.getPieces().setScale(0, RoundingMode.UP).intValue() - d11.getMaxCount()))), c12));

        d2.setCalAnyway(true);
        d3.setCalAnyway(true);
        d4.setCalAnyway(true);
        d5.setCalAnyway(true);
        d7.setCalAnyway(true);
        d8.setCalAnyway(true);
        d9.setCalAnyway(true);
        d10.setCalAnyway(true);
        d12.setCalAnyway(true);
        d13.setCalAnyway(true);
        d14.setCalAnyway(true);
        d15.setCalAnyway(true);
        chargesDetailList.clear();
        chargesDetailList.add(d1);
        chargesDetailList.add(d2);
        chargesDetailList.add(d3);
        chargesDetailList.add(d4);
        chargesDetailList.add(d5);

        chargesDetailList.add(d6);
        chargesDetailList.add(d7);
        chargesDetailList.add(d8);
        chargesDetailList.add(d9);
        chargesDetailList.add(d10);
        chargesDetailList.add(d11);
        chargesDetailList.add(d12);
        chargesDetailList.add(d13);
        chargesDetailList.add(d14);
        chargesDetailList.add(d15);
        log.info("chargesDetailList:{}", JsonUtils.object2Json(chargesDetailList));

        BicycleOrderGoodsDetail detail13 = new BicycleOrderGoodsDetail();
        detail13.setGoodsType(BicycleOrderGoodsType.OTHER.getNumber());
        detail13.setPieces(new BigDecimal("48"));
        BigDecimal c13 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail13));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("19.4"), c13));

        BicycleOrderGoodsDetail detail14 = new BicycleOrderGoodsDetail();
        detail14.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        detail14.setPieces(new BigDecimal("48"));
        detail14.setWeight(randDecimal(new BigDecimal("1"), new BigDecimal("10"), 2));
        BigDecimal c14 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail14));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("24.2"), c14));

        BicycleOrderGoodsDetail detail15 = new BicycleOrderGoodsDetail();
        detail15.setGoodsType(BicycleOrderGoodsType.HEAVY_GOODS.getNumber());
        detail15.setPieces(new BigDecimal("48"));
        detail15.setWeight(randDecimal(new BigDecimal("11"), new BigDecimal("20"), 2));
        BigDecimal c15 = chargesService.calculateCharges(minAmount, chargesDetailList, Collections.singletonList(detail15));
        Assertions.assertTrue(DecimalUtils.equals(new BigDecimal("43.4"), c15));
    }
}

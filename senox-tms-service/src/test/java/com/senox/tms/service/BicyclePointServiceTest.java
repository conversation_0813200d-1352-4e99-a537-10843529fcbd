package com.senox.tms.service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.senox.common.exception.BusinessException;
import com.senox.tms.BaseTest;
import com.senox.tms.domain.BicyclePoint;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/12 15:44
 */
@Slf4j
class BicyclePointServiceTest extends BaseTest {

    @Autowired
    private BicyclePointService bicyclePointService;


    @Test
    void modifyBicycleDeliveryPointTest() {
        // add
        BicyclePoint point = mockPoint();
        Long id = bicyclePointService.addBicyclePoint(point);
        Assertions.assertTrue(id > 0);

        BicyclePoint dbPoint = bicyclePointService.findById(id);
        Assertions.assertEquals(point, dbPoint);


        // update
        BicyclePoint upPoint = new BicyclePoint();
        upPoint.setId(id);
        upPoint.setName(randStr(8));
        upPoint.setModifierId(point.getModifierId());
        upPoint.setModifierName(point.getModifierName());
        bicyclePointService.updateBicyclePoint(upPoint);

        BicyclePoint dbPoint2 = bicyclePointService.findById(id);
        Assertions.assertNotEquals(point, dbPoint2);
        Assertions.assertEquals(upPoint, dbPoint2);

        // duplicated name update
        upPoint.setName("1号停车场");
        Assertions.assertThrows(BusinessException.class, () -> bicyclePointService.updateBicyclePoint(upPoint));

        // delete
        bicyclePointService.deleteBicyclePoint(id);
        Assertions.assertNull(bicyclePointService.findById(id));
    }

    @Test
    void bicycleDeliveryPointList() {
        List<BicyclePoint> points = bicyclePointService.listBicyclePoint();
        log.info("points:{}", points);
        Assertions.assertTrue(CollectionUtils.isNotEmpty(points));
    }


    private BicyclePoint mockPoint() {
        BicyclePoint parking = new BicyclePoint();
        parking.setName(randStr(5));
        parking.setCreatorId(randLong(1L, 100L));
        parking.setCreatorName(randStr(20));
        parking.setModifierId(parking.getModifierId());
        parking.setModifierName(parking.getModifierName());
        return parking;
    }
}

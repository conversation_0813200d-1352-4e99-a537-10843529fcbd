package com.senox.tms.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.BaseTest;
import com.senox.tms.constant.BicycleOrderSenderType;
import com.senox.tms.constant.BicycleOrderState;
import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.vo.BicycleOrderGoodsDetailVo;
import com.senox.tms.vo.BicycleOrderSearchVo;
import com.senox.tms.vo.BicycleOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18 11:42
 */
@Slf4j
class BicycleOrderServiceTest extends BaseTest {

    @Autowired
    private BicycleOrderService bicycleOrderService;

    //@Test
    void modifyBicycleDeliveryOrderTest() {
        // add
        BicycleOrder order = mockOrder();
        List<BicycleOrderGoodsDetailVo> goodsDetails = mockOrderGoodsDetail();
        Long id = bicycleOrderService.addBicycleOrder(order, goodsDetails, Collections.emptyList()).getId();
        Assertions.assertTrue(id > 0);

        BicycleOrderVo orderVo = bicycleOrderService.findOrderVoById(id, false);
        log.info("orderVo:{}", JsonUtils.object2Json(orderVo));
        Assertions.assertNotNull(orderVo);

        //update
        //bicycleOrderService.updateBicycleOrderState(id, BicycleOrderState.FINALIZE.getNumber());

        //exception
        Assertions.assertThrows(BusinessException.class, () -> bicycleOrderService.deleteBicycleOrder(id, true));


        BicycleOrder order1 = bicycleOrderService.findById(id);
        Assertions.assertNotNull(order1);
    }

    //@Test
    void listOrderTest() {
        mockOrder(10);
        BicycleOrderSearchVo searchVo = new BicycleOrderSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(5);
        searchVo.setState(BicycleOrderState.DRAFT.getNumber());
        PageResult<BicycleOrderVo> pageResult = bicycleOrderService.page(searchVo);
        Assertions.assertNotNull(pageResult);
        Assertions.assertTrue(CollectionUtils.isEmpty(pageResult.getDataList()));
        log.info("pageResult ---- :{}", JsonUtils.object2Json(pageResult));
    }

    private BicycleOrder mockOrder() {
        BicycleOrder order = new BicycleOrder();
        order.setSenderId(randLong(1L, 8L));
        order.setCreateOpenid(randStr(8));
        order.setStartPointId(randLong(1L, 100L));
        order.setStartPointName(randStr(5));
        order.setEndPointId(randLong(1L, 100L));
        order.setEndPointName(randStr(5));
        order.setStartPointDetailName(randStr(10));
        order.setEndPointDetailName(randStr(10));
        order.setSendTimeStart(LocalDateTime.now());
        order.setSendTimeEnd(LocalDateTime.now().plusMinutes(30));
        order.setSender(randStr(4));
        order.setSenderSerialNo(randStr(6));
        order.setSenderContact(randStr(11));
        order.setSenderType(BicycleOrderSenderType.REFRIGERATE_CUSTOMER.getNumber());
        order.setRecipient(randStr(4));
        order.setRecipientContact(randStr(11));
        //收费标准id和分佣标准id  暂时先mock 具体是获取实时的标准id
        order.setChargesId(1L);
        order.setCreatorId(randLong(1L, 100L));
        order.setCreatorName(randStr(20));
        order.setModifierId(order.getModifierId());
        order.setModifierName(order.getModifierName());
        return order;
    }

    private List<BicycleOrderGoodsDetailVo> mockOrderGoodsDetail() {
        List<BicycleOrderGoodsDetailVo> list = new ArrayList<>();
        for (int i = 0; i < randInt(1, 3); i++) {
            BicycleOrderGoodsDetailVo goodsDetail = new BicycleOrderGoodsDetailVo();
            goodsDetail.setGoodsName(randStr(6));
            goodsDetail.setPieces(randDecimal(BigDecimal.valueOf(0.1), BigDecimal.valueOf(3.0), 1));
            list.add(goodsDetail);
        }
        return list;
    }

    private void mockOrder(int count) {
        for (int i = 0; i < count; i++) {
            BicycleOrder order = mockOrder();
            List<BicycleOrderGoodsDetailVo> goodsDetails = mockOrderGoodsDetail();
            bicycleOrderService.addBicycleOrder(order, goodsDetails, Collections.emptyList());
        }
    }
}

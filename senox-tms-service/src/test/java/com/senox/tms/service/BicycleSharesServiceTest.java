package com.senox.tms.service;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.BicycleSharesBaseTest;
import com.senox.tms.domain.BicycleShares;
import com.senox.tms.vo.BicycleSharesSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-9-15
 */
@Slf4j
class BicycleSharesServiceTest extends BicycleSharesBaseTest {
    private static final String SUFFIX = "-service";

    @Test
    void addTest() {
        List<BicycleShares> sharesList = new ArrayList<>(sharesMock(COUNT,SUFFIX));
        BicycleShares shares = sharesList.get(0);
        sharesService.add(shares);
        Assertions.assertTrue(WrapperClassUtils.biggerThanLong(shares.getId(), 0));
        BicycleShares dbShares = sharesService.getById(shares.getId());
        Assertions.assertEquals(shares.getName(), dbShares.getName());
        Assertions.assertEquals(shares.getRiderShares(), dbShares.getRiderShares());
        Assertions.assertEquals(shares.getRiderDefaultShares(), dbShares.getRiderDefaultShares());
        Assertions.assertEquals(shares.getEffectiveTime(), dbShares.getEffectiveTime());
        Assertions.assertEquals(shares.getIneffectiveTime(), dbShares.getIneffectiveTime());
        Assertions.assertEquals(shares.getStatus(), dbShares.getStatus());
    }

    @Test
    void deleteTest() {
        BicycleShares shares = randShares();
        sharesService.delete(shares.getId());
        BicycleShares deletedShares = sharesService.getById(shares.getId());
        Assertions.assertNull(deletedShares);
    }

    @Test
    void updateTest() {
        BicycleShares shares = randShares();
        shares.setName(shares.getName().concat("-update"));
        shares.setRiderShares(new BigDecimal(randInt(12, 40)).setScale(2, RoundingMode.HALF_EVEN));
        shares.setRiderDefaultShares(new BigDecimal(randInt(12, 40)).setScale(2, RoundingMode.HALF_EVEN));
        shares.setEffectiveTime(shares.getEffectiveTime().plusMinutes(randInt(1, 2)));
        shares.setIneffectiveTime(shares.getIneffectiveTime().plusMinutes(randInt(3, 4)));
        sharesService.update(shares);
        BicycleShares dbShares = sharesService.getById(shares.getId());
        Assertions.assertEquals(shares.getName(), dbShares.getName());
        Assertions.assertEquals(shares.getRiderShares(), dbShares.getRiderShares());
        Assertions.assertEquals(shares.getRiderDefaultShares(), dbShares.getRiderDefaultShares());
        Assertions.assertEquals(shares.getStatus(), dbShares.getStatus());
        Assertions.assertEquals(shares.getEffectiveTime(), dbShares.getEffectiveTime());
        Assertions.assertEquals(shares.getIneffectiveTime(), dbShares.getIneffectiveTime());

    }

    @Test
    void listTest() {
        BicycleSharesSearchVo searchVo = new BicycleSharesSearchVo();
        searchVo.setName(SUFFIX);
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);
        PageResult<BicycleShares> pageResult = sharesService.list(searchVo);
        Assertions.assertNotNull(pageResult);
        List<BicycleShares> sharesList = pageResult.getDataList();
        Assertions.assertFalse(CollectionUtils.isEmpty(sharesList));
    }

}

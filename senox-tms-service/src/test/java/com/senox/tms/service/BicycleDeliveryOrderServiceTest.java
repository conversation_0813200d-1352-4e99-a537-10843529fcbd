package com.senox.tms.service;

import com.senox.common.utils.JsonUtils;
import com.senox.common.utils.StringUtils;
import com.senox.tms.BaseTest;
import com.senox.tms.constant.BicycleDeliveryOrderStatus;
import com.senox.tms.constant.BicycleOrderSenderType;
import com.senox.tms.constant.BicycleOrderState;
import com.senox.tms.domain.BicycleDeliveryOrder;
import com.senox.tms.domain.BicycleDeliveryOrderDetail;
import com.senox.tms.domain.BicycleDeliveryOrderJob;
import com.senox.tms.domain.BicycleOrder;
import com.senox.tms.vo.BicycleDeliveryOrderDetailVo;
import com.senox.tms.vo.BicycleDeliveryOrderVo;
import com.senox.tms.vo.BicycleOrderGoodsDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/20 11:58
 */
@Slf4j
class BicycleDeliveryOrderServiceTest extends BaseTest {

    @Autowired
    private BicycleDeliveryOrderService bicycleDeliveryOrderService;
    @Autowired
    private BicycleOrderService bicycleOrderService;
    @Autowired
    private BicycleDeliveryOrderDetailService bicycleDeliveryOrderDetailService;
    @Autowired
    private BicycleDeliveryOrderJobService bicycleDeliveryOrderJobService;
    @Autowired
    private BicycleBillService bicycleBillService;
    @Autowired
    private BicycleBillDetailService bicycleBillDetailService;

    //@Test
    void modifyBicycleDeliveryOrderTest() {
        //构造订单
        BicycleOrder order = mockOrder();
        List<BicycleOrderGoodsDetailVo> goodsDetails = mockOrderGoodsDetail();
        Long orderId = bicycleOrderService.addBicycleOrder(order, goodsDetails, Collections.emptyList()).getId();
        BicycleOrder order1 = bicycleOrderService.findById(orderId);

        BicycleDeliveryOrder deliveryOrder = mockDeliveryOrder();
        BicycleDeliveryOrderDetailVo deliveryOrderDetail = mockOrderDetail(order1);
        Long id = bicycleDeliveryOrderService.addBicycleDeliveryOrder(deliveryOrder, Collections.singletonList(deliveryOrderDetail), null);
        Assertions.assertTrue(id > 0);

        BicycleDeliveryOrder orderDb = bicycleDeliveryOrderService.findById(id);
        log.info("orderDb ---- {}", JsonUtils.object2Json(orderDb));
        Assertions.assertNotNull(orderDb);
        BicycleDeliveryOrderVo deliveryOrderVo = bicycleDeliveryOrderService.findDeliveryOrderById(id);
        log.info("deliveryOrderVo ---- {}", JsonUtils.object2Json(deliveryOrderVo));
        Assertions.assertNotNull(deliveryOrderVo);

        BicycleDeliveryOrderDetail orderDetail = bicycleDeliveryOrderDetailService.findByDeliveryOrderSerialNo(orderDb.getDeliveryOrderSerialNo());
        log.info("orderDetail ---- {}", JsonUtils.object2Json(orderDetail));
        Assertions.assertNotNull(orderDetail);

        BicycleDeliveryOrderDetailVo detail = bicycleDeliveryOrderDetailService.findDeliveryOrderDetailById(orderDetail.getId());
        log.info("detail ---- {}", JsonUtils.object2Json(detail));
        Assertions.assertNotNull(detail);

        BicycleDeliveryOrderJob orderJob = mockDeliveryOrderJob(orderDetail);
        List<String> mockMedia = mockMedia();
        Long jobId = bicycleDeliveryOrderJobService.addBicycleDeliveryOrderJob(orderJob, mockMedia, 5, StringUtils.EMPTY);
        Assertions.assertTrue(jobId > 0);

        BicycleDeliveryOrderVo deliveryOrderVo1 = bicycleDeliveryOrderService.findDeliveryOrderById(id);
        log.info("deliveryOrderVo1 ---- {}", JsonUtils.object2Json(deliveryOrderVo1));
        Assertions.assertNotNull(deliveryOrderVo1);

        BicycleDeliveryOrderDetailVo detail1 = bicycleDeliveryOrderDetailService.findDeliveryOrderDetailById(orderDetail.getId());
        log.info("detail1 ---- {}", JsonUtils.object2Json(detail1));
        Assertions.assertNotNull(detail1);
    }

    //@Test
    void mergedTest() {
        //构造订单
        BicycleOrder order = mockOrder();
        List<BicycleOrderGoodsDetailVo> goodsDetails = mockOrderGoodsDetail();
        Long orderId = bicycleOrderService.addBicycleOrder(order, goodsDetails, Collections.emptyList()).getId();
        BicycleOrder bicycleOrder = bicycleOrderService.findById(orderId);

        BicycleDeliveryOrder deliveryOrder = mockDeliveryOrder();
        BicycleDeliveryOrderDetailVo deliveryOrderDetail = mockOrderDetail(bicycleOrder);
        deliveryOrderDetail.setRiderId(2L);
        Long id = bicycleDeliveryOrderService.addBicycleDeliveryOrder(deliveryOrder, Collections.singletonList(deliveryOrderDetail), null);
        Assertions.assertTrue(id > 0);

        //构造订单
        BicycleOrder order1 = mockOrder();
        List<BicycleOrderGoodsDetailVo> goodsDetails1 = mockOrderGoodsDetail();
        Long orderId1 = bicycleOrderService.addBicycleOrder(order1, goodsDetails1, Collections.emptyList()).getId();
        BicycleOrder bicycleOrder1 = bicycleOrderService.findById(orderId1);

        BicycleDeliveryOrder deliveryOrder1 = mockDeliveryOrder();
        deliveryOrder1.setMerged(true);
        BicycleDeliveryOrderDetailVo deliveryOrderDetail1 = mockOrderDetail(bicycleOrder1);
        deliveryOrderDetail1.setRiderId(2L);
        Long id1 = bicycleDeliveryOrderService.addBicycleDeliveryOrder(deliveryOrder1, Collections.singletonList(deliveryOrderDetail1), null);
        Assertions.assertTrue(id1 > 0);

        BicycleDeliveryOrder order2 = bicycleDeliveryOrderService.findById(id1);
        log.info("order2:{}", JsonUtils.object2Json(order2));
    }

    private BicycleDeliveryOrderDetailVo mockOrderDetail(BicycleOrder order) {
        BicycleDeliveryOrderDetailVo orderDetail = new BicycleDeliveryOrderDetailVo();
        orderDetail.setOrderSerialNo(order.getOrderSerialNo());
        orderDetail.setStatus(BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber());
        orderDetail.setRiderId(randLong(2L,4L));
        return orderDetail;
    }

    private BicycleDeliveryOrder mockDeliveryOrder() {
        BicycleDeliveryOrder order = new BicycleDeliveryOrder();
        order.setStatus(BicycleDeliveryOrderStatus.UNPICKED_GOODS.getNumber());
        order.setCreatorId(randLong(1L, 100L));
        order.setCreatorName(randStr(20));
        order.setModifierId(order.getModifierId());
        order.setModifierName(order.getModifierName());
        return order;
    }

    private BicycleOrder mockOrder() {
        BicycleOrder order = new BicycleOrder();
        order.setSenderId(randLong(1L, 8L));
        order.setState(BicycleOrderState.FINALIZE.getNumber());
        order.setCreateOpenid(randStr(8));
        order.setStartPointId(randLong(1L, 100L));
        order.setStartPointName(randStr(5));
        order.setEndPointId(randLong(1L, 100L));
        order.setEndPointName(randStr(5));
        order.setStartPointDetailName(randStr(10));
        order.setEndPointDetailName(randStr(10));
        order.setSendTimeStart(LocalDateTime.now());
        order.setSendTimeEnd(LocalDateTime.now().plusMinutes(30));
        order.setSender(randStr(4));
        order.setSenderSerialNo(randStr(6));
        order.setSenderContact(randStr(11));
        order.setSenderType(BicycleOrderSenderType.REFRIGERATE_CUSTOMER.getNumber());
        order.setRecipient(randStr(4));
        order.setRecipientContact(randStr(11));
        //收费标准id和分佣标准id  暂时先mock 具体是获取实时的标准id
        order.setChargesId(1L);
        order.setCreatorId(randLong(1L, 100L));
        order.setCreatorName(randStr(20));
        order.setModifierId(order.getModifierId());
        order.setModifierName(order.getModifierName());
        return order;
    }

    private List<BicycleOrderGoodsDetailVo> mockOrderGoodsDetail() {
        List<BicycleOrderGoodsDetailVo> list = new ArrayList<>();
        for (int i = 0; i < randInt(1, 3); i++) {
            BicycleOrderGoodsDetailVo goodsDetail = new BicycleOrderGoodsDetailVo();
            goodsDetail.setGoodsName(randStr(6));
            goodsDetail.setPieces(randDecimal(BigDecimal.valueOf(0.1), BigDecimal.valueOf(3.0), 1));
            list.add(goodsDetail);
        }
        return list;
    }

    private BicycleDeliveryOrderJob mockDeliveryOrderJob(BicycleDeliveryOrderDetail orderDetail) {
        BicycleDeliveryOrderJob orderJob = new BicycleDeliveryOrderJob();
        orderJob.setStatus(5);
        orderJob.setRemark(randStr(6));
        orderJob.setDeliveryOrderDetailId(orderDetail.getId());
        return orderJob;
    }

    private List<String> mockMedia() {
        List<String> list = new ArrayList<>();
        for (int i = 0; i < randInt(1, 5); i++) {
            list.add(randStr(10));
        }
        return list;
    }
}

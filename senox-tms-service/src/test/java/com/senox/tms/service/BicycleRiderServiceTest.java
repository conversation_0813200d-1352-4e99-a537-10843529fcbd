package com.senox.tms.service;

import com.senox.common.exception.BusinessException;
import com.senox.common.utils.JsonUtils;
import com.senox.common.vo.PageResult;
import com.senox.tms.BaseTest;
import com.senox.tms.constant.BicycleRiderStatus;
import com.senox.tms.domain.BicycleRider;
import com.senox.tms.domain.BicycleRiderAttendance;
import com.senox.tms.vo.BicycleRiderAttendanceSearchVo;
import com.senox.tms.vo.BicycleRiderAttendanceVo;
import com.senox.tms.vo.BicycleRiderChangerPwdVo;
import com.senox.tms.vo.BicycleRiderSearchVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/14 8:55
 */
@Slf4j
class BicycleRiderServiceTest extends BaseTest {

    @Autowired
    private BicycleRiderService bicycleRiderService;

    @Autowired
    private BicycleRiderAttendanceService bicycleRiderAttendanceService;

    @BeforeEach
    void setUp() {
        List<BicycleRider> riderList = mockRiderList(10);
        List<BicycleRiderAttendance> riderAttendanceList = new ArrayList<>(10);
        for (BicycleRider rider : riderList) {
            Long id = bicycleRiderService.addBicycleRider(rider);
            BicycleRiderAttendance attendance = mockRiderAttendance(id);
            riderAttendanceList.add(attendance);
        }
        for (BicycleRiderAttendance attendance : riderAttendanceList) {
            bicycleRiderAttendanceService.riderOnline(attendance.getRiderId());
        }
    }

    //@Test
    void modifyBicycleDeliveryRiderTest() {
        //add
        BicycleRider rider = mockRider();
        Long id = bicycleRiderService.addBicycleRider(rider);
        Assertions.assertTrue(id > 0);

        BicycleRider dbRider = bicycleRiderService.findById(id);
        Assertions.assertEquals(rider, dbRider);

        // duplicated contact add
        BicycleRider rider1 = mockRider();
        rider1.setContact(rider.getContact());
        Assertions.assertThrows(BusinessException.class, () -> bicycleRiderService.addBicycleRider(rider1));

        //update
        BicycleRider updateRider = mockRider();
        updateRider.setId(id);
        updateRider.setAvatar(randStr(8));
        updateRider.setBirthday(LocalDate.now());
        String password = updateRider.getPassword();
        bicycleRiderService.updateBicycleRider(updateRider);

        BicycleRider dbRider2 = bicycleRiderService.findById(id);
        updateRider.setRiderNo(dbRider2.getRiderNo());
        Assertions.assertNotEquals(rider, dbRider2);
        Assertions.assertEquals(updateRider, dbRider2);

        //check password
        BicycleRiderChangerPwdVo pwdVo = new BicycleRiderChangerPwdVo();
        pwdVo.setContact(dbRider2.getContact());
        pwdVo.setOldPassword(password);
        pwdVo.setNewPassword(randStr(6));
        bicycleRiderService.changePassword(pwdVo);

        BicycleRider dbRider3 = bicycleRiderService.findById(id);
        Assertions.assertNotEquals(dbRider2, dbRider3);

        Assertions.assertNull(dbRider3.getReferralCode());
        bicycleRiderService.generateReferralCode(dbRider3.getId());

        BicycleRider dbRider4 = bicycleRiderService.findById(dbRider3.getId());
        Assertions.assertNotNull(dbRider4.getReferralCode());
    }

    @Test
    void deleteBicycleDeliveryRiderTest() {
        BicycleRider dbRider = bicycleRiderService.findById(1L, false);
        // delete
        bicycleRiderService.deleteBicycleRider(1L);

        BicycleRider dbRider2 = bicycleRiderService.findById(1L, false);
        Assertions.assertNotEquals(dbRider, dbRider2);
    }

    @Test
    void listBicycleDeliveryRiderTest() {
        BicycleRiderSearchVo searchVo = new BicycleRiderSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(5);
        PageResult<BicycleRider> result = bicycleRiderService.list(searchVo);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(CollectionUtils.isEmpty(result.getDataList()));
    }

    @Test
    void modifyBicycleDeliveryRiderAttendanceTest() {
        BicycleRiderAttendance attendance = bicycleRiderAttendanceService.findLastUnOfflineAttendanceByRiderId(randLong(4L, 10L));
        log.info("attendance:{}", JsonUtils.object2Json(attendance));
        Assertions.assertNotNull(attendance);

//        bicycleRiderAttendanceService.riderOffline(attendance.getRiderId());
//        BicycleRiderAttendance attendance2 = bicycleRiderAttendanceService.findLastUnOfflineAttendanceByRiderId(attendance.getRiderId());
//        log.info("attendance2:{}", JsonUtils.object2Json(attendance2));
//        Assertions.assertNull(attendance2);
//
//        BicycleRiderAttendance attendance3 = bicycleRiderAttendanceService.findById(attendance.getId());
//        log.info("attendance3:{}", JsonUtils.object2Json(attendance3));
//        Assertions.assertNotNull(attendance3);
    }

    @Test
    void listRiderAttendanceTest() {
        BicycleRiderAttendanceSearchVo searchVo = new BicycleRiderAttendanceSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(5);
        searchVo.setOnlineTimeStart(LocalDateTime.of(2023,9,15,10,34,53));
        searchVo.setOnlineTimeEnd(LocalDateTime.now());
        PageResult<BicycleRiderAttendanceVo> result = bicycleRiderAttendanceService.listRiderAttendance(searchVo);
        log.info("result---------:{}", result);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(CollectionUtils.isEmpty(result.getDataList()));
    }

    private List<BicycleRider> mockRiderList(int count) {
        List<BicycleRider> riderList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            BicycleRider rider = mockRider();
            riderList.add(rider);
        }
        return riderList;
    }

    private BicycleRider mockRider() {
        BicycleRider rider = new BicycleRider();
        rider.setName(randStr(3));
        rider.setReferralCode(null);
        rider.setContact(randStr(11));
        rider.setPassword(randStr(6));
        rider.setStatus(BicycleRiderStatus.OFFLINE.getNumber());
        rider.setCreatorId(randLong(1L, 100L));
        rider.setCreatorName(randStr(10));
        rider.setModifierId(rider.getCreatorId());
        rider.setModifierName(rider.getCreatorName());
        return rider;
    }

    private BicycleRiderAttendance mockRiderAttendance(Long riderId) {
        BicycleRiderAttendance attendance = new BicycleRiderAttendance();
        attendance.setRiderId(riderId);
        return attendance;
    }
}

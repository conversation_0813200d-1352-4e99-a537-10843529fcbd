package com.senox.tms;

import com.senox.tms.domain.UnloadingDict;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.utils.ContextUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/23 16:49
 */
@Slf4j
public class UnloadingBaseTest extends BaseTest {

    protected List<UnloadingDict> dictMock(int count)  {
        List<UnloadingDict> dictList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            UnloadingDict dict = new UnloadingDict();
            dict.setName(randStr(10));
            dict.setCategory(randInt(0, 3));
            dict.setUnit(randStr(4));
            dict.setCreateTime(LocalDateTime.now());
            dict.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityCreator(dict);
            ContextUtils.initEntityModifier(dict);
            dictList.add(dict);
        }
        return dictList;
    }

    protected List<UnloadingWorker> workerMock(int count) {
        List<UnloadingWorker> workerList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            UnloadingWorker worker = new UnloadingWorker();
            worker.setWorkerNo(randStr(5));
            workerList.add(worker);
        }
        return workerList;
    }

}

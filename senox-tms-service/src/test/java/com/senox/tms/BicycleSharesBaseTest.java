package com.senox.tms;

import com.senox.tms.constant.BicycleStatus;
import com.senox.tms.domain.BicycleShares;
import com.senox.tms.service.BicycleSharesService;
import com.senox.tms.utils.ContextUtils;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-9-18
 */
public class BicycleSharesBaseTest extends BaseTest {

    @Autowired
    protected BicycleSharesService sharesService;

    protected static final int COUNT = 10;


    protected Set<BicycleShares> sharesMock(int count, String suffix) {
        Set<BicycleShares> mockDataList = new LinkedHashSet<>();
        for (int i = 0; i < count; i++) {
            mockDataList.add(sharesMock(suffix));
        }
        return mockDataList;
    }

    protected BicycleShares sharesMock(String suffix) {
        BicycleShares shares = new BicycleShares();
        shares.setName(randStr(50).concat(suffix));
        shares.setRiderShares(new BigDecimal(randInt(1, 50)).setScale(2, RoundingMode.HALF_EVEN));
        shares.setRiderDefaultShares(new BigDecimal(randInt(1, 50)).setScale(2, RoundingMode.HALF_EVEN));
        LocalDateTime now = LocalDateTime.now();
        shares.setEffectiveTime(LocalDateTime.of(now.getYear(), now.getMonth(), randInt(1, 28), 0, 0));
        shares.setIneffectiveTime(shares.getEffectiveTime().minusDays(randInt(5, 15)));
        shares.setStatus(BicycleStatus.DISABLED.getNumber());
        ContextUtils.initEntityCreator(shares);
        ContextUtils.initEntityModifier(shares);
        return shares;
    }

    protected BicycleShares randShares() {
        List<BicycleShares> chargesList = sharesService.list().stream().filter(s->s.getStatus()==BicycleStatus.DISABLED.getNumber()).collect(Collectors.toList());
        Assertions.assertFalse(CollectionUtils.isEmpty(chargesList));
        return chargesList.size() == 1 ? chargesList.get(0) : chargesList.get(randInt(0, chargesList.size() - 1));
    }
}

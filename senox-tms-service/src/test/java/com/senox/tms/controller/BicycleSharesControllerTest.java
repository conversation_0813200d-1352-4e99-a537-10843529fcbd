package com.senox.tms.controller;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.BicycleSharesBaseTest;
import com.senox.tms.constant.BicycleStatus;
import com.senox.tms.convert.BicycleSharesConvert;
import com.senox.tms.domain.BicycleShares;
import com.senox.tms.vo.BicycleSharesSearchVo;
import com.senox.tms.vo.BicycleSharesVo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2023-9-18
 */
@AutoConfigureMockMvc
class BicycleSharesControllerTest extends BicycleSharesBaseTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private BicycleSharesConvert sharesConvert;
    private static final String SUFFIX = "-view";

    @Test
    void addTest() throws Exception {
      List<BicycleShares> sharesList = new ArrayList<>(sharesMock(COUNT, SUFFIX));
        for (BicycleShares shares : sharesList) {
            BicycleSharesVo sharesVo = sharesConvert.toVo(shares);
            MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                    .post("/bicycle/shares/add")
                    .accept(MediaType.APPLICATION_JSON)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JsonUtils.object2Json(sharesVo));
            mockMvc.perform(requestBuilder)
                    .andExpect(MockMvcResultMatchers.status().isOk());
        }
    }

    @Test
    void deleteTest() throws Exception {
        BicycleShares shares = randShares();
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get(String.format("/bicycle/shares/delete/%s", shares.getId()))
                .accept(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    void  listTest() throws Exception {
        BicycleSharesSearchVo searchVo = new BicycleSharesSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);
        searchVo.setName(SUFFIX);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/bicycle/shares/list")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.object2Json(searchVo));
        ResultActions resultActions = mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON));
        String content = resultActions.andReturn().getResponse().getContentAsString();
        Assertions.assertNotNull(content);
    }

    @Test
    void updateTest() throws Exception {
        BicycleShares shares = randShares();
        BicycleSharesVo sharesVo = sharesConvert.toVo(shares);
        sharesVo.setName(sharesVo.getName().concat("-update"));
        sharesVo.setStatus(BicycleStatus.ENABLED);
        sharesVo.setEffectiveTime(LocalDateTime.now().plusMinutes(randInt(10,15)));
        sharesVo.setIneffectiveTime(sharesVo.getEffectiveTime().plusMinutes(randInt(1,10)));
        sharesVo.setRiderShares(new BigDecimal(randInt(10,20)).setScale(2, RoundingMode.HALF_EVEN));
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/bicycle/shares/update")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.object2Json(sharesVo));
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

}





package com.senox.tms.controller;

import com.senox.common.utils.DecimalUtils;
import com.senox.common.utils.JsonUtils;
import com.senox.tms.BaseControllerTest;
import com.senox.tms.vo.LogisticOrderVo;
import org.junit.jupiter.api.Assertions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 14:47
 */
class LogisticOrderControllerTest extends BaseControllerTest {

    private static final Logger logger = LoggerFactory.getLogger(LogisticOrderControllerTest.class);

    //@Test
    void saveLogisticOrderBatch() {
        List<LogisticOrderVo> list = Collections.singletonList(mockLogisticOrder());
        HttpEntity<List<LogisticOrderVo>> entity1 = new HttpEntity<>(list);
        ResponseEntity<Void> responseEntity1 = restTemplate.exchange("/logistic/order/saveBatch", HttpMethod.POST,
                entity1, new ParameterizedTypeReference<Void>() {});
        Assertions.assertEquals(HttpStatus.OK, responseEntity1.getStatusCode());
        logger.info("response: {}", JsonUtils.object2Json(responseEntity1));
    }

    private LogisticOrderVo mockLogisticOrder() {
        LogisticOrderVo result = new LogisticOrderVo();
        result.setMerchant(randStr(10));
        result.setOrderNo(randNumStr(18));
        result.setShipDate(LocalDate.now());
        result.setProduct(randStr(12));
        result.setProductCount(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(100L), 2));
        result.setProductPrice(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(1000L), 2));
        result.setProductAmount(DecimalUtils.multiple(result.getProductCount(), result.getProductAmount()).setScale(2, RoundingMode.HALF_UP));
        result.setProductWeight(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(1000L), 2));
        result.setProductSize(randDecimal(BigDecimal.ONE, BigDecimal.valueOf(500L), 2));
        return result;
    }
}
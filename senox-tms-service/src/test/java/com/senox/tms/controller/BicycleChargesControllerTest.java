package com.senox.tms.controller;

import com.senox.common.utils.JsonUtils;
import com.senox.tms.BicycleChargesBaseTest;
import com.senox.tms.constant.BicycleStatus;
import com.senox.tms.convert.BicycleChargesConvert;
import com.senox.tms.domain.BicycleCharges;
import com.senox.tms.domain.BicycleChargesDetail;
import com.senox.tms.vo.BicycleChargesDetailVo;
import com.senox.tms.vo.BicycleChargesSearchVo;
import com.senox.tms.vo.BicycleChargesVo;
import com.senox.tms.vo.BicyclePointVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2023-9-18
 */
@Slf4j
@AutoConfigureMockMvc
class BicycleChargesControllerTest extends BicycleChargesBaseTest {
    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private BicycleChargesConvert chargesConvert;
    private static final String SUFFIX = "-view";

    @Test
    void addTest() throws Exception {
        List<BicycleCharges> chargesList = new ArrayList<>(chargesMock(COUNT, SUFFIX));
        List<BicycleChargesVo> chargesVoList = chargesConvert.toVo(chargesList);
        for (BicycleChargesVo chargesVo : chargesVoList) {
            init();
            List<BicycleChargesDetailVo> updateChargesDetails = new ArrayList<>(chargesDetailVoMock(1L, COUNT));
            chargesVo.setChargesDetails(updateChargesDetails);
            MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                    .post("/bicycle/charges/add")
                    .accept(MediaType.APPLICATION_JSON)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JsonUtils.object2Json(chargesVo));
            ResultActions resultActions = mockMvc.perform(requestBuilder)
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON));
            String content = resultActions.andReturn().getResponse().getContentAsString();
            Assertions.assertNotNull(content);
        }
    }

    @Test
    void deleteTest() throws Exception {
        BicycleCharges charges = randCharges();
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get(String.format("/bicycle/charges/delete/%s", charges.getId()))
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON);
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    void updateTest() throws Exception {
        BicycleCharges charges = randCharges();
        List<BicycleChargesDetailVo> chargesDetailList = listChargesDetailByCharges(charges.getId());
        List<BicycleChargesDetailVo> updateChargesDetails = new ArrayList<>(chargesDetailList.size());
        BicyclePointVo pointVo = new BicyclePointVo();
        pointVo.setId(2L);
        pointVo.setName("1期冷库");
        chargesDetailList.forEach(chargesDetail -> {
            BicycleChargesDetailVo updateChargesDetailVo = new BicycleChargesDetailVo();
            updateChargesDetailVo.setId(chargesDetail.getId());
            updateChargesDetailVo.setMinCount(randInt(1, 10));
            updateChargesDetailVo.setMaxCount(randInt(11, 30));
            updateChargesDetailVo.setUnitPrice(new BigDecimal(randInt(1, 20).toString()));
            updateChargesDetails.add(updateChargesDetailVo);
        });
        BicycleChargesVo updateChargesVo = new BicycleChargesVo();
        updateChargesVo.setId(charges.getId());
        updateChargesVo.setName(charges.getName().concat("-").concat("view").concat("-").concat("update"));
        updateChargesVo.setEffectiveTime(charges.getEffectiveTime().plusMinutes(randLong(1, 30)));
        updateChargesVo.setIneffectiveTime(updateChargesVo.getEffectiveTime().minusDays(randInt(5, 15)));
        updateChargesVo.setStatus(BicycleStatus.ENABLED_NOT_EFFECTIVE);
        updateChargesVo.setChargesDetails(updateChargesDetails);
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/bicycle/charges/update")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.object2Json(updateChargesVo));
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());

    }

    @Test
    void listTest() throws Exception {
        BicycleChargesSearchVo searchVo = new BicycleChargesSearchVo();
        searchVo.setPageNo(1);
        searchVo.setPageSize(10);
        searchVo.setName("-mock");
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/bicycle/charges/list")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.object2Json(searchVo));
        ResultActions resultActions = mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON));
        String content = resultActions.andReturn().getResponse().getContentAsString();
        Assertions.assertNotNull(content);
    }

    @Test
    void addDetailTest() throws Exception {
        BicycleCharges charges = randCharges();
        BicycleChargesDetail chargesDetail = chargesDetailMock(2L);
        BicycleChargesDetailVo chargesDetailVo = new BicycleChargesDetailVo();
        chargesDetailVo.setChargesId(charges.getId());
        BicyclePointVo pointVo = new BicyclePointVo();
        chargesDetailVo.setMinCount(chargesDetail.getMinCount());
        chargesDetailVo.setMaxCount(chargesDetail.getMaxCount());
        chargesDetailVo.setUnitPrice(chargesDetail.getUnitPrice());
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/bicycle/charges/detail/add")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.object2Json(chargesDetailVo));
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }


    @Test
    void updateDetailTest() throws Exception {
        BicycleCharges charges = randCharges();
        List<BicycleChargesDetailVo> chargesDetails = listChargesDetailByCharges(charges.getId());
        Assertions.assertFalse(CollectionUtils.isEmpty(chargesDetails));
        BicycleChargesDetailVo chargesDetail = chargesDetails.get(chargesDetails.size() - 1);
        BicycleChargesDetailVo updateChargesDetailVo = new BicycleChargesDetailVo();
        updateChargesDetailVo.setId(chargesDetail.getId());
        updateChargesDetailVo.setChargesId(charges.getId());
        updateChargesDetailVo.setMinCount(randInt(1, 10));
        updateChargesDetailVo.setMaxCount(randInt(11, 30));
        updateChargesDetailVo.setUnitPrice(new BigDecimal(randInt(1, 20).toString()));
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/bicycle/charges/detail/update")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.object2Json(updateChargesDetailVo));
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    void deleteDetailTest() throws Exception {
        BicycleCharges charges = randCharges();
        List<BicycleChargesDetailVo> chargesDetails = listChargesDetailByCharges(charges.getId());
        Assertions.assertFalse(CollectionUtils.isEmpty(chargesDetails));
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .post("/bicycle/charges/detail/delete")
                .accept(MediaType.APPLICATION_JSON)
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.object2Json(chargesDetails.stream().map(BicycleChargesDetailVo::getId).collect(Collectors.toList())));
        mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    void listDetailByChargesTest() throws Exception {
        BicycleCharges charges = randCharges();
        MockHttpServletRequestBuilder requestBuilder = MockMvcRequestBuilders
                .get(String.format("/bicycle/charges/detail/list/%s", charges.getId()))
                .accept(MediaType.APPLICATION_JSON);
        ResultActions resultActions = mockMvc.perform(requestBuilder)
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType(MediaType.APPLICATION_JSON));
        String content = resultActions.andReturn().getResponse().getContentAsString();
        Assertions.assertNotNull(content);
    }


    protected Set<BicycleChargesDetailVo> chargesDetailVoMock(Long pointId, int count) {
        Set<BicycleChargesDetailVo> mockDataList = new LinkedHashSet<>(count);
        for (int i = 0; i < count; i++) {
            mockDataList.add(chargesDetailVoMock(pointId));
        }
        return mockDataList;

    }

    protected BicycleChargesDetailVo chargesDetailVoMock(Long pointId) {
        BicycleChargesDetailVo chargesDetailVo = new BicycleChargesDetailVo();
        BicyclePointVo pointVo = new BicyclePointVo();
        pointVo.setId(pointId);
        chargesDetailVo.setMinCount(randInt(1, 10));
        chargesDetailVo.setMaxCount(randInt(11, 30));
        chargesDetailVo.setUnitPrice(new BigDecimal(randInt(1, 20).toString()));
        return chargesDetailVo;
    }
}

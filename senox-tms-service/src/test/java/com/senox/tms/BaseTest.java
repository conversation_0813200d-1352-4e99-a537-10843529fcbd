package com.senox.tms;

import com.senox.context.AdminContext;
import com.senox.context.AdminUserDto;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @Date 2020/12/15 10:00
 */
@SpringBootTest(classes = TmsApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BaseTest {


    private static final String LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final String NUMBERS = "0123456789";

    @PostConstruct
    public void init() {
        AdminUserDto adminUser = new AdminUserDto();
        adminUser.setUserId(1L);
        adminUser.setUsername("admin");
        adminUser.setToken(randStr(20));
        AdminContext.setUser(adminUser);

    }

    public static String randStr(int length) {
        Random random = new Random();
        StringBuilder builder = new StringBuilder();
        while (length > 0) {
            builder.append(LETTERS.charAt(random.nextInt(LETTERS.length())));
            length--;
        }
        return builder.toString();
    }

    public static String randNumStr(int length) {
        Random random = new Random();
        StringBuilder builder = new StringBuilder();
        while (length > 0) {
            builder.append(NUMBERS.charAt(random.nextInt(NUMBERS.length())));
            length--;
        }
        return builder.toString();
    }

    public static Integer randInt(int start, int end) {
        return ThreadLocalRandom.current().nextInt(start, end);
    }

    public static Long randLong(long start, long end) {
        return ThreadLocalRandom.current().nextLong(start, end);
    }

    public static BigDecimal randDecimal(BigDecimal start, BigDecimal end, int scale) {
        BigDecimal randResult = start.add(new BigDecimal(Math.random()).multiply(end.subtract(start)));
        return randResult.setScale(scale, BigDecimal.ROUND_HALF_UP);
    }
}

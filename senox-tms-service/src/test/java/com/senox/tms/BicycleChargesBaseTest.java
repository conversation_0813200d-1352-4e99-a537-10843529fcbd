package com.senox.tms;

import com.senox.common.utils.WrapperClassUtils;
import com.senox.tms.constant.BicycleStatus;
import com.senox.tms.domain.BicycleCharges;
import com.senox.tms.domain.BicycleChargesDetail;
import com.senox.tms.service.BicycleChargesDetailService;
import com.senox.tms.service.BicycleChargesService;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.BicycleChargesDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-9-18
 */
@Slf4j
public class BicycleChargesBaseTest extends BaseTest {

    @Autowired
    protected BicycleChargesService chargesService;
    @Autowired
    protected BicycleChargesDetailService chargesDetailService;
    protected static final int COUNT = 10;

    protected Set<BicycleCharges> chargesMock(int count, String suffix) {
        Set<BicycleCharges> mockDataList = new LinkedHashSet<>(count);
        for (int i = 0; i < count; i++) {
            BicycleCharges charges = new BicycleCharges();
            charges.setName(randStr(50).concat("-").concat(String.valueOf(i)).concat(suffix));
            LocalDateTime now = LocalDateTime.now();
            charges.setEffectiveTime(LocalDateTime.of(now.getYear(), now.getMonth(), randInt(1, 28), 0, 0));
            charges.setIneffectiveTime(charges.getEffectiveTime().minusDays(randInt(5, 15)));
            charges.setStatus(BicycleStatus.DISABLED.getNumber());
            charges.setMinAmount(new BigDecimal(randInt(5, 15).toString()));
            ContextUtils.initEntityCreator(charges);
            ContextUtils.initEntityModifier(charges);
            mockDataList.add(charges);
        }
        return mockDataList;

    }

    protected Set<BicycleChargesDetail> chargesDetailMock(Long pointId, int count) {
        Set<BicycleChargesDetail> mockDataList = new LinkedHashSet<>(count);
        for (int i = 0; i < count; i++) {
            mockDataList.add(chargesDetailMock(pointId));
        }
        return mockDataList;

    }

    protected BicycleChargesDetail chargesDetailMock(Long pointId) {
        BicycleChargesDetail chargesDetail = new BicycleChargesDetail();
        chargesDetail.setMinCount(randInt(1, 10));
        chargesDetail.setMaxCount(randInt(11, 30));
        chargesDetail.setUnitPrice(new BigDecimal(randInt(1, 20).toString()));
        return chargesDetail;
    }

    protected BicycleCharges randCharges() {
        List<BicycleCharges> chargesList = chargesService.list().stream().filter(c -> c.getStatus() == BicycleStatus.DISABLED.getNumber()).collect(Collectors.toList());
        Assertions.assertFalse(CollectionUtils.isEmpty(chargesList));
        return chargesList.size() == 1 ? chargesList.get(0) : chargesList.get(randInt(0, chargesList.size() - 1));
    }

    protected List<BicycleChargesDetailVo> listChargesDetailByCharges(Long chargesId) {
        if (!WrapperClassUtils.biggerThanLong(chargesId, 0)) {
            return Collections.emptyList();
        }
        return chargesService.listDetailByCharges(chargesId);
    }
}
